---
description: 'Project rules for SmartTV'
alwaysApply: true
---

# Project Profile

- **Language/Framework**: TypeScript + React
- **Target Devices**: Smart TVs (webOS, Tizen, HbbTV, older browsers with limited APIs)
- **Formatting**: Handled automatically by IDE — never change formatting in suggestions
- **Testing**: Vitest for unit tests

# TypeScript Guidelines

- Always use **strict typing**, never use `any`.
- Prefer **`type` aliases** over `interface` unless extension is required.
- Do not use `@ts-ignore` unless explicitly approved.
- Avoid type assertions like `as any`.

# React Guidelines

- Use **functional components with hooks** only — no class components.
- Props must be **explicitly typed** with `type`.
- Avoid inline styles — prefer SCSS modules or styled components.
- Do not introduce default exports — always use named exports.
- Avoid useMemo and useEffect unless strictly required

# Smart TV–Specific Rules

- Code must run on **low‑power Smart TV browsers** with limited API support.
- Avoid desktop‑only or experimental APIs (e.g. WebUSB, Service Workers).
- Primary navigation is via RCU directional keys, or up/down/left/right on keyboard.
- Focus management is provided by https://github.com/NoriginMedia/Norigin-Spatial-Navigation
- Keep performance in mind — Smart TVs have limited CPU/GPU.

# Safe Editing Rules

- **Never change existing code without explicit user approval.**
- Do not auto‑format or adjust whitespace — IDE handles this.
- Always explain suggested changes before applying.
- When unsure, ask for clarification instead of making assumptions.

# Additional Notes

- Large media, build outputs, and Smart TV SDK binaries are excluded via `.cursorignore`.
- Keep code lightweight, efficient, and robust for TV environments.

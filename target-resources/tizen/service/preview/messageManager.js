const logger = require('./logger.js');
var localMsgPort;
var listenerId;

function init(config) {
  // port for receiving messages
  localMsgPort = tizen.messageport.requestLocalMessagePort('BG_SERVICE_PORT');

  // register a handler for incoming messages
  listenerId = localMsgPort.addMessagePortListener(config.onMessageReceived);

  // port for sending messages
  try {
    tizen.messageport.requestRemoteMessagePort(config.calleeAppId, config.remoteMessagePortName);
  } catch (error) {
    logger.log('Requesting remote port in background service failed, ' + error.message);
  }
}

function removeListeners() {
  localMsgPort.removeMessagePortListener(listenerId);
}

module.exports = {
  init: init,
  removeListeners: removeListeners,
};

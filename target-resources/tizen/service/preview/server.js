const http = require('http');
const querystring = require('querystring');
const port = require('./config.json').port;

var server = http.createServer(function (request) {
  if (request.method === 'POST') {
    let data = '';
    request.on('data', (chunk) => {
      data += chunk.toString();
    });
    request.on('end', () => {
      const postData = querystring.parse(data);
      console.log('log:', postData.msg);
    });
  } else {
    console.log('Undefined request.');
  }
});

server.listen(port, () => {
  console.log(`Server is running on port ${port}`);
});

const http = require('http');
const config = require('./config.json');

module.exports.log = function (msg) {
  if (!config.enabled) {
    return;
  }

  function httpPost(body, hostname, port) {
    const req = http.request({
      method: 'POST',
      hostname: hostname,
      port: port,
    });
    if (body) {
      req.write(body);
    }
    req.end();
  }

  httpPost(`msg=${msg}`, config.host, config.port);
};

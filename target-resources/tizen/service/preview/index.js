const logger = require('./logger.js');
const messageManager = require('./messageManager');

function onMessageReceived(data) {
  logger.log('onMessageReceived ' + JSON.stringify(data));
  const msg = data[0];

  if (msg.key === 'lang') {
    webapis.preview.setPreviewData(
      msg.value,
      function () {
        logger.log('setPreviewData success');
        messageManager.removeListeners();
      },
      function (e) {
        logger.log('setPreviewData failed : ' + e.message);
      }
    );
  }
}

var messageManagerConfig = {
  remoteMessagePortName: 'CALLER_PORT',
  calleeAppId: 'aLlph042JZ.arte',
  onMessageReceived: onMessageReceived,
};

module.exports.onStart = function () {
  logger.log('Start Callback');
};

module.exports.onRequest = function () {
  logger.log('Request Callback');

  var reqAppControl = tizen.application.getCurrentApplication().getRequestedAppControl();
  if (reqAppControl && reqAppControl.appControl.operation === 'http://tizen.org/appcontrol/operation/pick') {
    var data = reqAppControl.appControl.data;

    if (data[0].value[0] == 'ForegroundApp') {
      messageManager.init(messageManagerConfig);
    }
  }
};

module.exports.onExit = function () {
  logger.log('Exit Callback');
};

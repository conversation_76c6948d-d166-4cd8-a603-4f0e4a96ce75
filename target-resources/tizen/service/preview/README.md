By using Personal Preview we can change the preview tiles when users change the in-app language.
--

Resources
---
Samsung docs on personal preview https://developer.samsung.com/smarttv/develop/guides/smart-hub-preview/implementing-personal-preview.html

The code below is based on Samsung's code sample at https://github.com/SamsungDForum/SampleWebApps-PersonalPreview

MessagePort API to communicate between foreground and background apps - https://developer.samsung.com/smarttv/develop/api-references/tizen-web-device-api-references/messageport-api.html

How to debug this code?
---
- In the folder `target-resources/tizen/service/preview/`, edit `config.json` and use node to start `server.js`. It will listen to messages coming from the background service
- Add the following code in the index html file for tizen. It will print js logs to screen. The logger we have in React kicks in too late to debug the preview feature.
```
<textarea id="onScreenDebug"
      rows="64"
      style="background-color:#FFFFFF; width: 1920px; opacity: 0.5; position:absolute; z-index:1000000">
</textarea>
<script>
    function printToScreen(message) {
        var onScreenDebug = document.getElementById("onScreenDebug");
        onScreenDebug.value += message + "\n";
        onScreenDebug.scrollTop = onScreenDebug.scrollHeight;
    }
    console.log = printToScreen;
    window.onerror = printToScreen;
</script>
```
How does it work?
---
At startup, the foreground app launches the background service. Upon success, the foreground app requests a preview feed from the middleware. The feed is then passed to the background service which calls `webapis.preview.setPreviewData` to update the tiles. The preview feed will also be updated when the foreground app gains focus.

Future development
---
- After `lang` message has been received, the communication will be closed by calling `localMsgPort.removeMessagePortListener(listenerId)`. This is due to the fact that the foreground app gets reloaded when a language is changed. We might consider another approach in the future in case we need to keep the connection alive after sending a message. I'm thinking personalised feed for logged in users.
- According to Samsung's sample code we should close the background service before closing the foreground app. However, it requires an application kill privilege that's only available to Samsung partners (partner level certificate is needed)
- For convenience, the js files for the preview service are not bundled. Thanks to that they can be modified after importing a widget file. It could be useful in case the QA team is needed to debug the service.
<?xml version="1.0" encoding="UTF-8"?>
<widget xmlns="http://www.w3.org/ns/widgets" xmlns:tizen="http://tizen.org/ns/widgets" id="http://arte.tv/arte" version="${VERSION}" viewmodes="maximized">
    <access origin="*" subdomains="true"></access>

    <tizen:application id="aLlph042JZ.arte" package="aLlph042JZ" required_version="2.3"/>

    <content src="index.html"/>

    <feature name="http://tizen.org/feature/screen.size.all"/>

    <icon src="wgt-icon.png"/>

    <tizen:metadata key="http://samsung.com/tv/metadata/devel.api.version" value="2.4"/>
    <tizen:metadata key="http://samsung.com/tv/metadata/multitasking.support" value="true"/>
    <tizen:metadata key="http://samsung.com/tv/metadata/prelaunch.support" value="true"/>
    <tizen:metadata key='http://samsung.com/tv/metadata/use.preview' value='bg_service'></tizen:metadata>

    <tizen:service id='aLlph042JZ.preview' auto-restart="false">
        <tizen:content src='service/preview/index.js'></tizen:content>
        <tizen:name>preview</tizen:name>
        <tizen:description>Preview Service</tizen:description>
        <tizen:metadata key='meta-key' value='meta-value'></tizen:metadata>
        <tizen:category name='http://tizen.org/category/service'></tizen:category>
    </tizen:service>

    <tizen:app-control>
        <tizen:src name='index.html' reload='disable'></tizen:src>
        <tizen:operation name='http://samsung.com/appcontrol/operation/eden_resume'></tizen:operation>
    </tizen:app-control>

    <name>ARTE</name>

    <tizen:profile name="tv-samsung"/>

    <tizen:privilege name="http://tizen.org/privilege/application.launch"/>
    <tizen:privilege name="http://tizen.org/api/filesystem"/>
    <tizen:privilege name="http://tizen.org/privilege/filesystem.read"/>
    <tizen:privilege name="http://tizen.org/privilege/filesystem.write"/>
    <tizen:privilege name="http://tizen.org/privilege/internet"/>
    <tizen:privilege name="http://tizen.org/privilege/tv.inputdevice"/>
    <tizen:privilege name='http://developer.samsung.com/privilege/network.public'/>

    <tizen:setting pointing-device-support='disable'/>
    <tizen:setting screen-orientation="landscape"/>
</widget>


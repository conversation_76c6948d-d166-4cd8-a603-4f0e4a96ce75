module.exports = {
  'no-direct-target-imports': {
    meta: {
      type: 'problem',
      docs: {
        description: 'disallow imports directly from target',
        category: 'Possible Errors',
        recommended: true,
      },
      schema: [],
    },
    create(context) {
      return {
        ImportDeclaration(node) {
          const importPath = node.source.value;
          const isTargetPath = /(\.\.\/)*target\/(hbbtv|html5|tizen|webos)/.test(importPath);

          if (isTargetPath) {
            context.report({
              node,
              message: `Avoid importing directly from target '${importPath}' (target/<target-name>)`,
            });
          }
        },
      };
    },
  },
};

#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

npm test run

# Get the branch name
branch_name=$(git rev-parse --abbrev-ref HEAD)

# regular expression for the branch name format
# format: feat|fix|docs|refactor|test|dependencies|chore|minor: /<ticket-id>-<subject>
regex="^(feat|fix|docs|refactor|test|dependencies|chore|minor)\/[A-Za-z0-9]+-[a-z0-9\-]+$"

# Check if the branch name matches the regex
if [[ ! "$branch_name" =~ $regex ]]; then
    echo "Error: Branch name '$branch_name' does not match the required pattern."
    echo "The branch name should follow the regex: <type>/<ticket-id>-<subject>"
    echo "<type> = feat|fix|docs|refactor|test|dependencies|chore|minor"
    echo "<ticket-id> = Alphanumeric ticket or task identifier"
    echo "<subject> = Short description in lowercase with '-' instead of special characters"
    exit 1
fi

# Successful push
 exit 0

#!/bin/sh

# Commit message file
commit_msg_file=$1

# Get the commit message
commit_msg=$(cat "$commit_msg_file")

# check if the commit message contains the skip-hooks flag
if echo "$commit_msg" | grep -q "#skip-hooks"; then
  echo "Commit hook skipped due to #skip-hooks flag."
  # exit 0 # Exit successfully, skipping the hook
  exit 1
fi

# regular expression for commit format
# format: feat|fix|docs|refactor|test|dependencies|chore|minor: <subject> (JIRA-Ticket-ID)
regex="^[[:space:]]*(feat|fix|docs|refactor|test|dependencies|chore|minor): [a-z].* \([A-Z]+-[0-9]+\)[[:space:]]*$"

# check if format of message matches the pattern
if ! echo "$commit_msg" | grep -Eq "$regex"; then
  echo "### bash file used. ###"
  echo "Error: Commit message '$commit_msg' does not match the required pattern."
  echo ""
  echo "Required format: <type>: <subject> (<ticket-id>)"
  echo "<type> must be either feat|fix|docs|refactor|test|dependencies|chore|minor."
  echo "<subject> must be in the imperative mood, without a capital letter at the beginning, and without a period at the end."
  echo "Example: feat: add new login page (PROJ-123)"
  exit 1
fi

# Successful commit
echo "commit message format is correct."
exit 0

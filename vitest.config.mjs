import path from 'path';
import tsconfigPaths from 'vite-tsconfig-paths';
import { defineConfig } from 'vitest/config';

import { LOG_PREFIX } from './src/errors/errorLogging';

export default defineConfig({
  test: {
    environment: 'happy-dom',
    include: ['**/*.test.ts'],
    globals: true,
    setupFiles: ['./test/setup.ts'],
    onConsoleLog(log) {
      if (log.includes(LOG_PREFIX)) return false;
    },
  },
  plugins: [tsconfigPaths()],
  resolve: {
    alias: {
      target: path.resolve(__dirname, 'src/types/target.d.ts'),
    },
  },
});

{"parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2020, "sourceType": "module", "project": "./tsconfig.eslint.json", "ecmaFeatures": {"jsx": true}}, "extends": ["plugin:@typescript-eslint/recommended", "prettier"], "plugins": ["react", "react-hooks", "@typescript-eslint", "prettier", "unused-imports", "simple-import-sort", "local-rules"], "env": {"browser": true, "node": true, "es6": true}, "settings": {"react": {"version": "detect"}}, "rules": {"prettier/prettier": ["error", {"endOfLine": "auto"}], "unused-imports/no-unused-imports": "error", "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "error", "@typescript-eslint/no-explicit-any": ["error"], "@typescript-eslint/no-unused-vars": ["error"], "simple-import-sort/imports": "error", "local-rules/no-direct-target-imports": "error", "no-debugger": ["error"], "react/jsx-no-useless-fragment": ["error"], "react/no-array-index-key": ["error"], "no-nested-ternary": ["warn"], "no-param-reassign": ["error"]}, "overrides": [{"files": ["*.js"], "rules": {"@typescript-eslint/no-var-requires": "off"}, "parserOptions": {"project": null}}]}
import { EVENTS } from '../src/constants';
import EventBus from '../src/util/EventBus';

const STANDARD_HIDDEN = 'hidden';
const STANDARD_VISIBILITY_CHANGE = 'visibilitychange';
const WEBKIT_HIDDEN = 'webkitHidden';
const WEBKIT_VISIBILITY_CHANGE = 'webkitvisibilitychange';

function getProperties() {
  const properties = {
    hidden: '',
    visibilityChange: '',
  };

  // For backward compatibility, it is recommended you add an event listener for the standard web engine.

  // to support the standard web browser engine
  if (typeof document.hidden !== 'undefined') {
    properties.hidden = STANDARD_HIDDEN;
    properties.visibilityChange = STANDARD_VISIBILITY_CHANGE;
  }
  // to support the webkit engine
  if (typeof document.webkitHidden !== 'undefined') {
    properties.hidden = WEBKIT_HIDDEN;
    properties.visibilityChange = WEBKIT_VISIBILITY_CHANGE;
  }

  return properties;
}

/**
 *
 * After an app is launched, it can be either visible or hidden depending on user interactions and system events.
 * When a launched app receives a visibilityChange event (hidden) , its state becomes suspended and its visibility
 * becomes hidden
 */
export function onVisibilityChange() {
  const { hidden, visibilityChange } = getProperties();

  if (!hidden || !visibilityChange) {
    console.warn('visibilitychange not supported');
    return;
  }

  document.addEventListener(visibilityChange, () => {
    if (document[hidden]) {
      EventBus.emit(EVENTS.APP_TO_BACKGROUND, {});
    } else {
      EventBus.emit(EVENTS.APP_TO_FOREGROUND, {});
    }
  });
}

import { EVENTS } from '../../../src/constants';
import { UTM_MEDIUM_VALUE, UTM_SOURCE_VALUE } from '../../../src/features/queryParams/queryParamsConsts';
import { getRouteFromQueryString, isVideoRoute } from '../../../src/routes/route';
import EventBus from '../../../src/util/EventBus';
import { reloadApp } from '..';
import { setDeeplinkFound, setInitialDeeplinkRead, wasInitialDeeplinkRead } from '../storage/sessionStorage';
import { getDeeplink } from './getDeeplink';

export const initDeeplinks = () => {
  window.addEventListener('load', () => {
    if (wasInitialDeeplinkRead()) {
      return;
    }

    setInitialDeeplinkRead();

    const deeplink = getDeeplink();

    // common params for starting the app with or without a deeplink
    const commonLaunchParams = `utm_source=${UTM_SOURCE_VALUE.TIZEN}`;

    if (deeplink && !getRouteFromQueryString()) {
      setDeeplinkFound();

      const type = isVideoRoute(deeplink.id) ? 'player' : 'collection';
      // reload the app with a deeplink
      reloadApp(
        `${commonLaunchParams}&utm_medium=${deeplink.utmMedium}&utm_campaign=${deeplink.utmCampaign}&type=${type}&id=${deeplink.id}&lang=${deeplink.language}`,
      );
    } else {
      // reload the app without a deeplink
      reloadApp(`${commonLaunchParams}&utm_medium=${UTM_MEDIUM_VALUE.SMARTHUB}&utm_campaign=thumbnail_app`);
    }
  });

  document.addEventListener('appcontrol', () => {
    const deeplink = getDeeplink();
    if (deeplink) {
      setDeeplinkFound();
      EventBus.emit(EVENTS.NATIVE_DEEPLINK, { [EVENTS.NATIVE_DEEPLINK]: deeplink });
    }
  });
};

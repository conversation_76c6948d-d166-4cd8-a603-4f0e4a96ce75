import { UTM_MEDIUM_VALUE } from '../../../src/features/queryParams/queryParamsConsts';
import { NativeDeeplink } from '../../../src/types/deeplink';
import { isJSON } from '../../../src/util/json';

type Payload = {
  /**
   * Payload value string with the structure below
   * contentID={{programId}_{{language}}_{{contentType}}_{{utm_campaign}}
   * e.g content_id=RC-024597_de_show_smartHubPreview
   */
  value: string;
  /**
   * Flag indicating if payload has a json structure
   */
  json: boolean;
};

/**
 * Utm params to be extracted from a deeplink
 */
type UtmParams = {
  utmMedium: string;
  utmCampaign: string;
};

export const getDeeplink = (): NativeDeeplink | undefined => {
  const payload = getPayload();
  if (payload) {
    return payloadToDeeplink(payload);
  }
  return undefined;
};

const getPayload = (): Payload | undefined => {
  try {
    const reqAppControl = window['tizen'].application.getCurrentApplication().getRequestedAppControl();
    if (reqAppControl && reqAppControl.appControl) {
      const data = reqAppControl.appControl.data;

      /**
       * TEST DATA
       */
      // const data = [];
      // data.push({
      // key: 'PAYLOAD',

      // OLD LOGIC
      /**
       * FOR TESTING SMARTHUB PREVIEW DEEPLINK
       */
      // value: ['{\n   "values" : "{\\"assetId\\":\\"content_id=RC-024597_en\\"}"\n}\n'],
      /**
       * FOR TESTING GLOBAL SEARCH DEEPLINK
       */
      // value: ['{ "values" : "content_id=RC-024597_en" }'],

      // NEW LOGIC
      /**
       * FOR TESTING NEW CONTENT ID
       * contentID={{programId}_{{language}}_{{contentType}}_{{utm_campaign}}
       */
      // value: ['{\n   "values" : "{\\"assetId\\":\\"content_id=RC-024597_de_show_smartHubPreview\\"}"\n}\n'],

      //});

      for (let i = 0; i < data.length; i++) {
        if (data[i].key == 'PAYLOAD') {
          const payloadJsonString = data[i].value[0];
          const payload = JSON.parse(payloadJsonString);
          const regex = /content_id=(\w|-)+/gim;

          if (regex.test(payload.values)) {
            return {
              value: payload.values?.match(regex)?.[0],
              json: isJSON(payload.values),
            };
          }
        }
      }
    }
  } catch (e) {
    console.warn('Error reading app deeplink: ' + e);
  }

  return undefined;
};

/**
 * Returns utm params given id and utm campaign
 */
const getUtmParamsFromPayloadValue = (id: string, utmCampaign: string): UtmParams => {
  return {
    utmMedium: utmCampaign,
    utmCampaign: `${utmCampaign}_${id}`,
  };
};

/**
 * Returns utm params based on stucture of a payload
 */
const getUtmParamsFromPayloadStructure = (id: string, json: boolean): UtmParams => {
  if (json) {
    return {
      utmMedium: UTM_MEDIUM_VALUE.SMARTHUB_PREVIEW,
      utmCampaign: `preview_${id}`,
    };
  } else {
    return {
      utmMedium: UTM_MEDIUM_VALUE.UNIVERSAL_SEARCH,
      utmCampaign: `search_${id}`,
    };
  }
};

const payloadToDeeplink = (payload: Payload): NativeDeeplink => {
  const params = payload.value.split('=')[1].split('_');
  const id = params[0];
  const lang = params[1];
  // params[2] is only needed in legacy tizen app
  const campaign = params[3];

  let utmParams: UtmParams;
  if (campaign) {
    // new logic
    // Fill utm params from utm campaign if provided
    utmParams = getUtmParamsFromPayloadValue(id, campaign);
  } else {
    // old logic
    // Fill utm params based on structure of the payload
    utmParams = getUtmParamsFromPayloadStructure(id, payload.json);
  }

  return { id: id, language: lang, ...utmParams };
};

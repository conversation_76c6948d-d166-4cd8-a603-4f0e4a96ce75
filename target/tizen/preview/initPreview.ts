import { middlewareUrl } from '../../../src/util/url';

let bgServiceLaunched = false;
let remoteMsgPort;
const localMessagePortName = 'CALLER_PORT';
const localMsgPort = window['tizen'].messageport.requestLocalMessagePort(localMessagePortName);
const serviceId = 'aLlph042JZ.preview'; // Application ID for background service application

const updatePreview = () => {
  window['tizen'].application.launchAppControl(
    new window['tizen'].ApplicationControl('http://tizen.org/appcontrol/operation/pick', null, null, null, [
      new window['tizen'].ApplicationControlData('caller', ['ForegroundApp']),
    ]),
    serviceId,
    // success callback
    function () {
      bgServiceLaunched = true;

      // load preview feed and pass it to the background service
      const url = middlewareUrl(`/pmr/tizen`);
      fetch(url)
        .then((response) => response.json())
        .then((data) => {
          const msg = {
            key: 'lang',
            value: JSON.stringify(data),
          };
          sendMessage([msg]);
        });
    },
    // fail calback
    function (error) {
      bgServiceLaunched = false;
      console.log(`Failed to launch the preview service. ${JSON.stringify(error)}`);
    },
  );
};

const visibilityHandler = () => {
  // Update preview when the app gains focus
  if (!document.hidden) {
    updatePreview();
  }
};

export const resetPreview = () => {
  document.removeEventListener('visibilitychange', visibilityHandler);
};

export const initPreview = () => {
  updatePreview();
  document.addEventListener('visibilitychange', visibilityHandler);
};

function requestRemotePort() {
  // get the port for sending messages
  try {
    remoteMsgPort = window['tizen'].messageport.requestRemoteMessagePort('aLlph042JZ.preview', 'BG_SERVICE_PORT');
  } catch (error) {
    console.error(`Failed to request a message port: ${error.message}`);
  }
}

const sendMessage = (msg) => {
  if (!bgServiceLaunched) {
    console.warn('Background service is NOT launched');
  } else {
    try {
      requestRemotePort();
      // sends message using remote port and indicates the local port for eventual reply message
      remoteMsgPort.sendMessage(msg, localMsgPort);
    } catch (error) {
      console.error('Error occured while sending message: ', error);
    }
  }
};

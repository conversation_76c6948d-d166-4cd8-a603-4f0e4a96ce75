const getDeeplinkFound = () => !!window.sessionStorage.getItem('deeplinkFound');

const setDeeplinkFound = () => {
  window.sessionStorage.setItem('deeplinkFound', 'true');
};

const getPreviewServiceInitialized = () => !!window.sessionStorage.getItem('previewServiceInitialized');

const setPreviewServiceInitialized = () => {
  window.sessionStorage.setItem('previewServiceInitialized', 'true');
};

const wasInitialDeeplinkRead = () => !!window.sessionStorage.getItem('initialDeeplinkRead');

const setInitialDeeplinkRead = () => {
  window.sessionStorage.setItem('initialDeeplinkRead', 'true');
};

export {
  getDeeplinkFound,
  setDeeplinkFound,
  getPreviewServiceInitialized,
  setPreviewServiceInitialized,
  wasInitialDeeplinkRead,
  setInitialDeeplinkRead,
};

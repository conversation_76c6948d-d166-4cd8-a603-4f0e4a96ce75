import { createHashRouter } from 'react-router-dom';

import germanTranslation from '../../locales/de/translation.json';
import englishTranslation from '../../locales/en/translation.json';
import spanishTranslation from '../../locales/es/translation.json';
import frenchTranslation from '../../locales/fr/translation.json';
import italianTranslation from '../../locales/it/translation.json';
import polishTranslation from '../../locales/pl/translation.json';
import { handleOffline, handleOnline } from '../../src/util/offlineEvent';
import {
  defaultGetSearchParams,
  defaultIsExitEnabled,
  defaultNormalizeParams,
  defaultShowMessageOnReload,
  defaultThirdPartyAuth,
  defaultUpdateKeys,
} from '../defaults';
import { ITarget } from '../ITarget';
import * as keys from '../keys';
import { onVisibilityChange } from '../lifecycle';
import { initDeeplinks } from './deeplink/initDeeplink';
import { initPreview, resetPreview } from './preview/initPreview';
import { getDeeplinkFound, wasInitialDeeplinkRead } from './storage/sessionStorage';

const REGISTERED_NUMBER_KEYS = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
const REGISTERED_COLOUR_KEYS = ['ColorF1Green', 'ColorF2Yellow'];
const REGISTERED_MEDIA_KEYS = [
  'MediaRewind',
  'MediaPause',
  'MediaFastForward',
  'MediaPlay',
  'MediaStop',
  'MediaPlayPause',
  'MediaTrackPrevious',
  'MediaTrackNext',
];
const REGISTERED_KEYS = [...REGISTERED_NUMBER_KEYS, ...REGISTERED_COLOUR_KEYS, ...REGISTERED_MEDIA_KEYS];

let keysRegistered = false;

const printError = (apiCalled: string, errorReceived) => {
  console.warn(`Error calling ${apiCalled}. Is it a tizen device? ${errorReceived}`);
};

const registerKeys = () => {
  if (!keysRegistered) {
    try {
      window['tizen'].tvinputdevice.registerKeyBatch(
        // keys
        REGISTERED_KEYS,
        // success handler
        () => {
          keysRegistered = true;
        },
        // error handler
        (err) => {
          console.log('The registration of keys failed:', err);
        },
      );
    } catch (e) {
      printError("window['tizen'].tvinputdevice.registerKeyBatch", e);
    }
  }
};

export const getKeyMap: ITarget['getKeyMap'] = () => {
  registerKeys();

  return {
    // directional navigation
    [keys.KEY_ENTER]: 13,
    [keys.KEY_UP]: 38,
    [keys.KEY_RIGHT]: 39,
    [keys.KEY_DOWN]: 40,
    [keys.KEY_LEFT]: 37,
    [keys.KEY_BACK]: 10009,

    // media keys
    [keys.KEY_PLAY]: 415,
    [keys.KEY_PAUSE]: 19,
    [keys.KEY_STOP]: 413,
    [keys.KEY_FAST_FWD]: 417,
    [keys.KEY_REWIND]: 412,
    [keys.KEY_PLAY_PAUSE]: 10252,
    [keys.KEY_PREV]: 10232,
    [keys.KEY_NEXT]: 10233,

    // number keys
    [keys.KEY_0]: 48,
    [keys.KEY_1]: 49,
    [keys.KEY_2]: 50,
    [keys.KEY_3]: 51,
    [keys.KEY_4]: 52,
    [keys.KEY_5]: 53,
    [keys.KEY_6]: 54,
    [keys.KEY_7]: 55,
    [keys.KEY_8]: 56,
    [keys.KEY_9]: 57,

    // color keys
    [keys.KEY_GREEN]: 404,
    [keys.KEY_YELLOW]: 405,
  };
};

export const updateKeys: ITarget['updateKeys'] = defaultUpdateKeys;

// Use a hash router because Tizen builds load index.html from a file system as opposed to a web server
export const createRouter: ITarget['createRouter'] = createHashRouter;

export const getSearchParams: ITarget['getSearchParams'] = () => {
  const queryStringInHashExists = window.location.hash.includes('?');

  // Reading and modifying the query string works differently with hash router.
  // Initially, the tizen app is started with a query string e.g ?param1=value1
  // Navigating to a new page using the navigate function repeats the query string e.g ?param1=value1#/page/ARTE_CONCERT?param1=value1
  // In case one of the parameters needs to be modified, the new query string is passed to the navigate function
  // and it is reflected only in the 'second' query string e.g ?param1=value1#/page/ARTE_CONCERT?param1=NEW_VALUE_HERE
  // To factor in the behaviour above, the app first reads from the 'second' query string if it exists.

  if (queryStringInHashExists) {
    const queryStringInHash = window.location.hash.substring(window.location.hash.indexOf('?'));
    return new URLSearchParams(queryStringInHash);
  }

  return defaultGetSearchParams();
};

export const confirmExitOnBack: ITarget['confirmExitOnBack'] = () => !getDeeplinkFound();

export const exitApp: ITarget['exitApp'] = () => {
  try {
    resetPreview();
    // TODO strongly type
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (window as any).tizen.application.getCurrentApplication().exit();
  } catch (e) {
    printError('(window as any).tizen.application.getCurrentApplication().exit()', e);
  }
};

export const isExitEnabled: ITarget['isExitEnabled'] = defaultIsExitEnabled;

export const isReady: ITarget['isReady'] = () => wasInitialDeeplinkRead();

export const reloadApp: ITarget['reloadApp'] = (relaunchQueryParams?: string) => {
  resetPreview();

  const location = window.location;

  // construct the url
  let newUrl = location.protocol + '//' + location.host + location.pathname;
  if (relaunchQueryParams) newUrl = `${newUrl}?${relaunchQueryParams}`;

  // reload
  window.history.pushState({ path: newUrl }, '', newUrl);
  location.reload();
};

export const showMessageOnReload: ITarget['showMessageOnReload'] = defaultShowMessageOnReload;

export const config: ITarget['config'] = {
  cookieModal: { acceptCookiesOnPressingBack: true },
  i18n: {
    resources: {
      de: {
        translation: germanTranslation,
      },
      en: {
        translation: englishTranslation,
      },
      es: {
        translation: spanishTranslation,
      },
      fr: {
        translation: frenchTranslation,
      },
      it: {
        translation: italianTranslation,
      },
      pl: {
        translation: polishTranslation,
      },
    },
  },
  offline: { goBackFromPlayback: true, handleOfflineEventInVideo: true },
  hasColorButtonSupport: true,
  lifecycle: { goBackFromPlayback: true },
};

const tizenNetworkListener = (value) => {
  if (value == window?.webapis?.network.NetworkState.GATEWAY_DISCONNECTED) {
    handleOffline();
  } else if (value == window?.webapis?.network.NetworkState.GATEWAY_CONNECTED) {
    handleOnline();
  }
};

export const addOnlineOfflineListener: ITarget['onlineOfflineListener'] = () => {
  window?.webapis?.network.addNetworkStateChangeListener(tizenNetworkListener);
};

export const onLanguageDetected: ITarget['onLanguageDetected'] = initPreview;

export const thirdPartyAuth: ITarget['thirdPartyAuth'] = defaultThirdPartyAuth;

export const normalizeParams: ITarget['normalizeParams'] = defaultNormalizeParams;

initDeeplinks();

onVisibilityChange();

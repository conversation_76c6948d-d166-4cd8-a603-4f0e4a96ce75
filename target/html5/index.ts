import { createBrowserRouter } from 'react-router-dom';

import { REMAP_COLOR_KEYS_QUERY_PARAM } from '../../src/features/queryParams/queryParamsConsts';
import { isSky, isSkyDe } from '../../src/features/queryParams/queryParamsLookup';
import { isMagentaTv, isPanasonic, isRoku, isTitanos, isVidaa } from '../../src/util/platform';
import {
  defaultConfig,
  defaultConfirmExitOnBack,
  defaultGetSearchParams,
  defaultIsExitEnabled,
  defaultIsReady,
  defaultNormalizeParams,
  defaultOnLanguageDetected,
  defaultOnlineOfflineListener,
  defaultReload,
  defaultShowMessageOnReload,
  defaultThirdPartyAuth,
  defaultTimeBasedVideoEnd,
  defaultUpdateKeys,
} from '../defaults';
import { ITarget } from '../ITarget';
import { keyMap, remapColorKeys } from './keyMap';
import { magentaTvKeyMap } from './magentatv/keyMap';
import { panasonicTvKeyMap } from './panasonic/keyMap';
import { rokuKeyMap } from './roku/keyMap';
import { skyTvKeyMap } from './sky/keyMap';
import { titanosKeyMap } from './titanos/keyMap';
import { vidaaTvKeyMap } from './vidaa/keyMap';

export const getKeyMap: ITarget['getKeyMap'] = () => {
  switch (true) {
    case isMagentaTv():
      return magentaTvKeyMap;
    case isRoku():
      return rokuKeyMap;
    case isPanasonic():
      return panasonicTvKeyMap;
    case isTitanos():
      return titanosKeyMap;
    case isVidaa():
      return vidaaTvKeyMap;
    case isSky():
    case isSkyDe():
      return skyTvKeyMap;
    default:
      return keyMap;
  }
};

export const updateKeys: ITarget['updateKeys'] = defaultUpdateKeys;

export const createRouter: ITarget['createRouter'] = createBrowserRouter;

export const getSearchParams: ITarget['getSearchParams'] = defaultGetSearchParams;

export const confirmExitOnBack: ITarget['confirmExitOnBack'] = defaultConfirmExitOnBack;

export const exitApp: ITarget['exitApp'] = () => window.close();

export const isExitEnabled: ITarget['isExitEnabled'] = defaultIsExitEnabled;

export const isReady: ITarget['isReady'] = defaultIsReady;

export const reloadApp: ITarget['reloadApp'] = defaultReload;

export const showMessageOnReload: ITarget['showMessageOnReload'] = defaultShowMessageOnReload;

export const addOnlineOfflineListener: ITarget['onlineOfflineListener'] = defaultOnlineOfflineListener;

export const onLanguageDetected: ITarget['onLanguageDetected'] = defaultOnLanguageDetected;

export const thirdPartyAuth: ITarget['thirdPartyAuth'] = defaultThirdPartyAuth;

const isTimeBasedVideoEnd: boolean | undefined = !isMagentaTv() ? defaultTimeBasedVideoEnd : true;

export const normalizeParams: ITarget['normalizeParams'] = defaultNormalizeParams;

// remap colour keys to use keyboard (b, g, r, y) when testing in browser
if (getSearchParams().get(REMAP_COLOR_KEYS_QUERY_PARAM.key) === REMAP_COLOR_KEYS_QUERY_PARAM.value) remapColorKeys();

export const config: ITarget['config'] = {
  ...defaultConfig,
  timeBasedVideoEnd: isTimeBasedVideoEnd,
  hasColorButtonSupport: !isRoku(),
};

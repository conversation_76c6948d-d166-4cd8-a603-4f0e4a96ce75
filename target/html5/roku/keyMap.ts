import * as keys from '../../keys';
import { keyMap } from '../keyMap';

// Start with general html5 key mapping
const rokuKeyMap = { ...keyMap };
// And override the keys below
rokuKeyMap[keys.KEY_BACK] = typeof VKEY_ESCAPE != 'undefined' ? VKEY_ESCAPE : 27;
rokuKeyMap[keys.KEY_FAST_FWD] = typeof VKEY_OEM_104 != 'undefined' ? VKEY_OEM_104 : 228;
rokuKeyMap[keys.KEY_REWIND] = typeof VKEY_OEM_103 != 'undefined' ? VKEY_OEM_103 : 227;
rokuKeyMap[keys.KEY_PLAY_PAUSE] = typeof VKEY_MEDIA_PLAY_PAUSE != 'undefined' ? VKEY_MEDIA_PLAY_PAUSE : 179;

export { rokuKeyMap };

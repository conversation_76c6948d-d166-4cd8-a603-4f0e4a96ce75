import * as keys from '../../keys';
import { keyMap } from '../keyMap';

const skyTvKeyMap = { ...keyMap };

skyTvKeyMap[keys.KEY_BACK] = typeof VK_BACK != 'undefined' ? VK_BACK : 27;
skyTvKeyMap[keys.KEY_PLAY_PAUSE] = typeof VK_PLAY_PAUSE != 'undefined' ? VK_PLAY_PAUSE : 179;
skyTvKeyMap[keys.KEY_FAST_FWD] = typeof VK_FAST_FWD != 'undefined' ? VK_FAST_FWD : 228;
skyTvKeyMap[keys.KEY_REWIND] = typeof VK_REWIND != 'undefined' ? VK_REWIND : 227;

export { skyTvKeyMap };

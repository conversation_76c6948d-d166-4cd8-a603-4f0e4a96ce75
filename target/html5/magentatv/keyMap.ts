import * as keys from '../../keys';
import { keyMap } from '../keyMap';

const magentaTvKeyMap = { ...keyMap };

magentaTvKeyMap[keys.KEY_RED] = typeof HK_RED != 'undefined' ? HK_RED : 403;
magentaTvKeyMap[keys.KEY_GREEN] = typeof HK_GREEN != 'undefined' ? HK_GREEN : 404;
magentaTvKeyMap[keys.KEY_YELLOW] = typeof HK_YELLOW != 'undefined' ? HK_YELLOW : 405;
magentaTvKeyMap[keys.KEY_BLUE] = typeof HK_BLUE != 'undefined' ? HK_BLUE : 406;
magentaTvKeyMap[keys.KEY_BACK] = typeof VK_BACK != 'undefined' ? VK_BACK : 461;

export { magentaTvKeyMap };

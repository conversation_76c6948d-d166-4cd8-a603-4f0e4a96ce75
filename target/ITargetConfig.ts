export interface AuthErrorMessage {
  title: string;
  description: string;
}

export interface ITargetConfig {
  hasMouseSupport?: boolean;
  cookieModal?: { acceptCookiesOnPressingBack?: boolean };
  i18n?: { backend?: { loadPath: string }; resources?: unknown };
  offline?: { goBackFromPlayback?: boolean; handleOfflineEventInVideo?: boolean };
  timeBasedVideoEnd?: boolean;
  hasColorButtonSupport?: boolean;
  hasAuthErrorQueryParams: boolean;
  authErrorMessage: AuthErrorMessage | null;
  lifecycle?: { goBackFromPlayback?: boolean };
}

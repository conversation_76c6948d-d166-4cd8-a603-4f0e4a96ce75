import {
  UTM_CAMPAIGN_KEY,
  UTM_MEDIUM_KEY,
  UTM_SOURCE_KEY,
  UTM_SOURCE_VALUE,
} from '../../src/features/queryParams/queryParamsConsts';

const DEFAULT_UTM_CAMPAIGN_VALUE = 'thumbnail_app';
const DEFAULT_UTM_MEDIUM_VALUE = 'portal';

export function hasUtmParams() {
  const url = new URL(window.location.href);
  return !!(
    url.searchParams.get(UTM_SOURCE_KEY) &&
    url.searchParams.get(UTM_MEDIUM_KEY) &&
    url.searchParams.get(UTM_CAMPAIGN_KEY)
  );
}

/**
 * The application should be launched with default utm params e.g.
 * http://path/to/app/?utm_source=webos&utm_medium=portal&utm_campaign=thumbnail_app
 *
 * For webos the initial url is baked into the ipk as a redirect url, however, we want to
 * push an update without submitting a new ipk with a new url containing the default utm
 * values. As a result we ensure we add them at startup here.
 */
export function addDefaultUtmParams() {
  const url = new URL(window.location.href);
  url.searchParams.set(UTM_CAMPAIGN_KEY, DEFAULT_UTM_CAMPAIGN_VALUE);
  url.searchParams.set(UTM_MEDIUM_KEY, DEFAULT_UTM_MEDIUM_VALUE);
  url.searchParams.set(UTM_SOURCE_KEY, UTM_SOURCE_VALUE.WEBOS);
  window.history.pushState(null, '', url.toString());
}

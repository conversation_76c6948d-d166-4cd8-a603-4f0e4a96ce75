import { create<PERSON>rowser<PERSON>outer } from 'react-router-dom';

import {
  defaultConfig,
  defaultConfirmExitOnBack,
  defaultGetSearchParams,
  defaultIsExitEnabled,
  defaultIsReady,
  defaultNormalizeParams,
  defaultOnLanguageDetected,
  defaultOnlineOfflineListener,
  defaultReload,
  defaultThirdPartyAuth,
  defaultUpdateKeys,
} from '../defaults';
import { ITarget } from '../ITarget';
import * as keys from '../keys';
import { onVisibilityChange } from '../lifecycle';
import { initDeeplink } from './deeplink/initDeeplink';
import { addDefaultUtmParams, hasUtmParams } from './utm';

/**
 * see: https://webostv.developer.lge.com/develop/guides/magic-remote#media-playback-keys
 */
export const getKeyMap: ITarget['getKeyMap'] = () => {
  return {
    [keys.KEY_ENTER]: 13,
    [keys.KEY_UP]: 38,
    [keys.KEY_RIGHT]: 39,
    [keys.KEY_DOWN]: 40,
    [keys.KEY_LEFT]: 37,
    [keys.KEY_BACK]: 461,
    [keys.KEY_RED]: 403,
    [keys.KEY_GREEN]: 404,
    [keys.KEY_YELLOW]: 405,
    [keys.KEY_BLUE]: 406,
    [keys.KEY_PLAY]: 415,
    [keys.KEY_PAUSE]: 19,
    [keys.KEY_STOP]: 413,
    [keys.KEY_FAST_FWD]: 417,
    [keys.KEY_REWIND]: 412,
    [keys.KEY_0]: 48,
    [keys.KEY_1]: 49,
    [keys.KEY_2]: 50,
    [keys.KEY_3]: 51,
    [keys.KEY_4]: 52,
    [keys.KEY_5]: 53,
    [keys.KEY_6]: 54,
    [keys.KEY_7]: 55,
    [keys.KEY_8]: 56,
    [keys.KEY_9]: 57,
  };
};

export const updateKeys: ITarget['updateKeys'] = defaultUpdateKeys;

export const createRouter: ITarget['createRouter'] = createBrowserRouter;

export const getSearchParams: ITarget['getSearchParams'] = defaultGetSearchParams;

export const confirmExitOnBack: ITarget['confirmExitOnBack'] = defaultConfirmExitOnBack;

export const exitApp: ITarget['exitApp'] = () => window.close();

export const isExitEnabled: ITarget['isExitEnabled'] = defaultIsExitEnabled;

export const isReady: ITarget['isReady'] = defaultIsReady;

export const reloadApp: ITarget['reloadApp'] = defaultReload;

/**
 * webOS 4 and below exhibit slow reloading behaviour when changing language, render a message to the
 * user to indicate the app will reload. Note that we do this for all webos versions right now as it really
 * has no impact on UX for the faster devices. If required we could restrict this by version at a later date.
 */
export const showMessageOnReload: ITarget['showMessageOnReload'] = () => true;

export const addOnlineOfflineListener: ITarget['onlineOfflineListener'] = defaultOnlineOfflineListener;

export const onLanguageDetected: ITarget['onLanguageDetected'] = defaultOnLanguageDetected;

export const config: ITarget['config'] = { ...defaultConfig, hasMouseSupport: true };

export const thirdPartyAuth: ITarget['thirdPartyAuth'] = defaultThirdPartyAuth;

export const normalizeParams: ITarget['normalizeParams'] = defaultNormalizeParams;

onVisibilityChange();

/**
 * check if params already exist before adding defaults
 * this can occur when deep-linking for example
 */
if (!hasUtmParams()) addDefaultUtmParams();

initDeeplink();

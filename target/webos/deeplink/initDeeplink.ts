import { EVENTS } from '../../../src/constants';
import EventBus from '../../../src/util/EventBus';

// Note that deeplink params are declared in: target-resources/webos/appinfo.json

type DeepLinkParams = {
  id: string;
  utm_campaign: string;
  utm_medium: string;
  utm_source: string;
};

type LaunchParams = {
  contentTarget: string;
};

declare global {
  interface Window {
    webOSSystem?: {
      launchParams: LaunchParams;
    };
    PalmSystem?: {
      launchParams: LaunchParams;
    };
  }
}

/**
 * NOTE the following from `webOS TV Deep Linking Development Guide`:
 * In webOS TV 4.0 or lower, only PalmSystem is supported, and in webOS TV 5.0 or higher,
 * both PalmSystem and webOSSystem are supported. Although PalmSystem is still supported
 * for backward compatibility, future support is not guaranteed, and accordingly it is
 * strongly recommended that you use webOSSystem in webOS TV 5.0 or higher.
 */
function getWebOSLaunchParams(): LaunchParams | undefined {
  // use webOSSystem if available (webOS TV 5.0 or higher),
  // otherwise fallback to PalmSystem for older devices (webOS TV 4.0 or lower)
  const params = window.webOSSystem?.launchParams || window.PalmSystem?.launchParams;
  if (!params) return;

  try {
    return JSON.parse(params.toString());
  } catch (error) {
    console.error('[webos] failed to parse launchParams:', error);
  }
}

/**
 * returns `contentTarget` from webos launch params
 * `contentTarget` is a string of deep link parameters that the app expects
 * e.g. type=player&id=110868-001-A&utm_source=webos&utm_medium=editorial_campaign&utm_campaign=edito_110868-001-A
 *
 * @returns {string | undefined} - The deep link parameters as a string.
 */
function getContentTarget(): string | undefined {
  const params = getWebOSLaunchParams();
  return params?.contentTarget;
}

function getUrl(params: string) {
  return `${location.protocol}//${location.host}${location.pathname}?${params}`;
}

function getQueryParamsAsObject(query: string): Record<string, string> {
  return query.split('&').reduce((acc, pair) => {
    const [key, value] = pair.split('=').map(decodeURIComponent);
    if (key) {
      acc[key] = value ?? '';
    }
    return acc;
  }, {} as Record<string, string>);
}

function parseAndValidateContentTarget(contentTarget: string | undefined): DeepLinkParams | undefined {
  if (!contentTarget) return;

  const { id, utm_campaign, utm_medium, utm_source } = getQueryParamsAsObject(contentTarget);

  if (!id || !utm_campaign || !utm_medium || !utm_source) {
    console.warn('[webos] missing required parameters for deep linking');
    return;
  }

  return { id, utm_campaign, utm_medium, utm_source };
}

/**
 * check for deeplink params from webOSSystem on initial launch
 *
 * @param {string | undefined} contentTarget - The deep link parameters as a string.
 */
function handleLaunch(contentTarget: string | undefined) {
  if (!contentTarget) return;

  const params = parseAndValidateContentTarget(contentTarget);
  if (!params) return;

  const url = getUrl(contentTarget);
  window.history.pushState({ path: url }, '', url);
}

/**
 * If deep linking is requested while the app is already running, we receive a `webOSRelaunch` event.
 * By handling webOSRelaunch event, the app can process the deep linking request without app reloading.
 *
 * @param {string | undefined} contentTarget - The deep link parameters as a string.
 */
function handleWebOSRelaunch(contentTarget: string | undefined) {
  const params = parseAndValidateContentTarget(contentTarget);
  if (!params) return;

  const payload = { id: params.id, utmCampaign: params.utm_campaign, utmMedium: params.utm_medium };
  EventBus.emit(EVENTS.NATIVE_DEEPLINK, { [EVENTS.NATIVE_DEEPLINK]: payload });
}

export function initDeeplink() {
  handleLaunch(getContentTarget());
  document.addEventListener('webOSRelaunch', () => handleWebOSRelaunch(getContentTarget()));
}

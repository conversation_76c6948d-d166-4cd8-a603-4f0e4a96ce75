import { Params } from 'react-router-dom';

import { handleOffline, handleOnline } from '../src/util/offlineEvent';
import { ITarget } from './ITarget';
import { ITargetConfig } from './ITargetConfig';

export const defaultUpdateKeys: ITarget['updateKeys'] = () => true;

export const defaultGetSearchParams: ITarget['getSearchParams'] = () => new URLSearchParams(window.location.search);

export const defaultConfirmExitOnBack: ITarget['confirmExitOnBack'] = () => true;

export const defaultIsExitEnabled: ITarget['isExitEnabled'] = () => true;

export const defaultIsReady: ITarget['isReady'] = () => true;

export const defaultReload: ITarget['reloadApp'] = (relaunchQueryParams?: string) => {
  let url = `${process.env.REACT_ROUTER_BASE_PATH}`;
  if (relaunchQueryParams) url = `${url}?${relaunchQueryParams}`;
  window.location.replace(url);
};

export const defaultShowMessageOnReload: ITarget['showMessageOnReload'] = () => false;

export const defaultConfig: ITarget['config'] = {
  i18n: {
    backend: {
      loadPath: `${process.env.REACT_ROUTER_BASE_PATH}locales/{{lng}}/{{ns}}.json`,
    },
  },
  offline: { handleOfflineEventInVideo: true, goBackFromPlayback: true },
  hasColorButtonSupport: true,
  /**
   * hasAuthErrorQueryParams and authErrorMessage are orange specific and relate
   * to failed authentication on the orange network, if the app is requested with
   * these params render an error UI and prevent the user from seeing any content.
   *
   */
  hasAuthErrorQueryParams: false,
  authErrorMessage: null,
};

export const defaultOnlineOfflineListener: ITarget['onlineOfflineListener'] = () => {
  window.addEventListener('offline', handleOffline);
  window.addEventListener('online', handleOnline);
};

export const defaultOnLanguageDetected: ITarget['onLanguageDetected'] = () => true;

export const defaultTimeBasedVideoEnd: ITargetConfig['timeBasedVideoEnd'] = false;

export const defaultThirdPartyAuth: ITarget['thirdPartyAuth'] = {
  auth: () => Promise.resolve(true),
  enabled: false,
};

export const defaultNormalizeParams: ITarget['normalizeParams'] = (params: Readonly<Params>) => params;

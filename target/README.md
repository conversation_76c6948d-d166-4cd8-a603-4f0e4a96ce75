# Target module

Even though we have a single codebase, we still need code specific to each target, such as key mappings. We achieve this by loading a target-specific module via an alias.

The main benefits of this approach include:

1. Keeps the bundle size smaller by only including modules for the given target.
1. Provides a clean separation of concerns for target-specific code.

The main goal is to demonstrate how to import target-specific modules using aliases and ensure type safety. We will cover how to:

1. Add additional targets
1. Add additional functions to a target module

## File Structure

The given file structure is organized into target folders, each containing an `index.ts` and an `index.html` file:

```
target
├── ITarget.ts
├── hbbtv
│   ├── index.html
│   └── index.ts
├── html5
│   ├── index.html
│   └── index.ts
├── tizen
│   ├── index.html
│   └── index.ts
└── webos
    ├── index.html
    └── index.ts
```

## Interface definition (ITarget.ts)

We define an interface `ITarget` that contains a `getKeymap` function. This interface ensures type safety for target-specific modules by enforcing a specific structure for each target:

```typescript
export interface ITarget {
  getKeymap: () => void;
}
```

## Target implementation (index.ts)

Each target-specific module must implement the `ITarget` interface. Here's an example implementation for `getKeyMap` in an `index.ts` file:

```typescript
import { ITarget } from '../ITarget';

export const getKeyMap: ITarget['getKeyMap'] = () => {
  // return keymap
};
```

## Webpack alias configuration

To simplify importing target-specific modules, we use an alias in the Webpack configuration. The alias points to the specific target directory:

```javascript
alias: {
  target: path.resolve(__dirname, `../target/${target}/`),
},
```

## Adding additional targets

To add a new target, follow these steps:

1. Create a new folder under the `target` directory with the target's name (e.g., `newTarget`).
1. Add an `index.html` and an `index.ts` file in the new target folder.
1. In the `index.ts` file, import the `ITarget` interface and implement the required functions as specified in the interface.
1. Update the `package.json` file with a new script entry for the new target:

```json
"scripts": {
  "dev:hbbtv": "TARGET=hbbtv npm run dev",
  "dev:tizen": "TARGET=tizen npm run dev",
  "dev:webos": "TARGET=webos npm run dev",
  "dev:newTarget": "TARGET=newTarget npm run dev"
}
```

Run the new target as follows:

```bash
npm run dev:newTarget
```

## Adding additional functions to a target module

To add a new function to a target module, follow these steps:

1. Update the `ITarget` interface in `ITarget.ts` with the new function's signature.
1. Add the new function implementation to each target's `index.ts` file, ensuring the implementation adheres to the updated `ITarget` interface.
1. Import and use the new function in the app, relying on the alias for a seamless import.

## Using the module

```typescript
import { getKeymap } from 'target';
```

import { createBrowserRouter } from 'react-router-dom';

import { isChannel77 } from '../../src/features/queryParams/queryParamsLookup';
import {
  defaultConfig,
  defaultConfirmExitOnBack,
  defaultGetSearchParams,
  defaultIsReady,
  defaultNormalizeParams,
  defaultOnLanguageDetected,
  defaultOnlineOfflineListener,
  defaultReload,
  defaultShowMessageOnReload,
  defaultThirdPartyAuth,
  defaultTimeBasedVideoEnd,
} from '../defaults';
import { keyMap as html5KeyMap, remapColorKeys } from '../html5/keyMap';
import { ITarget } from '../ITarget';
import { ITargetConfig } from '../ITargetConfig';
import * as keys from '../keys';
import { exit } from './exit';
import { setKeyMask } from './keymask';

export type HbbTVKeyEvent = {
  VK_LEFT: number;
  VK_UP: number;
  VK_RIGHT: number;
  VK_DOWN: number;
  VK_ENTER: number;
  VK_RED: number;
  VK_GREEN: number;
  VK_YELLOW: number;
  VK_BLUE: number;
  VK_PLAY: number;
  VK_PAUSE: number;
  VK_STOP: number;
  VK_PLAY_PAUSE: number;
  VK_FAST_FWD: number;
  VK_REWIND: number;
  VK_NEXT: number;
  VK_PREV: number;
  VK_BACK: number;
  VK_0: number;
  VK_1: number;
  VK_2: number;
  VK_3: number;
  VK_4: number;
  VK_5: number;
  VK_6: number;
  VK_7: number;
  VK_8: number;
  VK_9: number;
};

declare const KeyEvent: HbbTVKeyEvent;

/**
 * Returns true if the current environment is NOT an HbbTV device.
 * This is used to detect when we are running in a browser,
 * so we can use the html5 key map for development/testing.
 */
function isNonHbbtvUserAgent() {
  return !window.navigator.userAgent.toLowerCase().includes('hbbtv');
}

/**
 * Returns the html5 key map for browser/dev environments.
 * This remaps color keys to keyboard keys (b, g, r, y) for easier testing.
 * Only used when not running on a real HbbTV device.
 */
function getHtml5KeyMapForTesting() {
  remapColorKeys();
  return html5KeyMap;
}

export const getKeyMap: ITarget['getKeyMap'] = () => {
  // If not running on a real HbbTV device, use the html5 key map for development/testing.
  if (isNonHbbtvUserAgent()) {
    // This is intentional: we want browser/dev environments to use the html5 key map.
    // This enables keyboard navigation and color key testing in the browser and avoids
    // using a browser extension to simulate an hbbtv environment
    console.warn('[hbbtv]: using html5 keymap for hbbtv in non-hbbtv env');
    return getHtml5KeyMapForTesting();
  }

  let VK_LEFT;
  let VK_UP;
  let VK_RIGHT;
  let VK_DOWN;
  let VK_ENTER;
  let VK_RED;
  let VK_GREEN;
  let VK_YELLOW;
  let VK_BLUE;
  let VK_PLAY;
  let VK_PAUSE;
  let VK_STOP;
  let VK_PLAY_PAUSE;
  let VK_FAST_FWD;
  let VK_REWIND;
  let VK_NEXT;
  let VK_PREV;
  let VK_BACK;
  let VK_0;
  let VK_1;
  let VK_2;
  let VK_3;
  let VK_4;
  let VK_5;
  let VK_6;
  let VK_7;
  let VK_8;
  let VK_9;

  // Make sure VK_ constants are defined
  if (typeof KeyEvent !== 'undefined') {
    if (typeof KeyEvent.VK_LEFT !== 'undefined') {
      VK_LEFT = KeyEvent.VK_LEFT;
      VK_UP = KeyEvent.VK_UP;
      VK_RIGHT = KeyEvent.VK_RIGHT;
      VK_DOWN = KeyEvent.VK_DOWN;
    }

    if (typeof KeyEvent.VK_ENTER !== 'undefined') {
      VK_ENTER = KeyEvent.VK_ENTER;
    }

    if (typeof KeyEvent.VK_RED !== 'undefined') {
      VK_RED = KeyEvent.VK_RED;
      VK_GREEN = KeyEvent.VK_GREEN;
      VK_YELLOW = KeyEvent.VK_YELLOW;
      VK_BLUE = KeyEvent.VK_BLUE;
    }

    if (typeof KeyEvent.VK_PLAY !== 'undefined') {
      VK_PLAY = KeyEvent.VK_PLAY;
      VK_PAUSE = KeyEvent.VK_PAUSE;
      VK_STOP = KeyEvent.VK_STOP;
    }

    if (typeof KeyEvent.VK_PLAY_PAUSE !== 'undefined') {
      VK_PLAY_PAUSE = KeyEvent.VK_PLAY_PAUSE;
    }

    if (typeof KeyEvent.VK_FAST_FWD !== 'undefined') {
      VK_FAST_FWD = KeyEvent.VK_FAST_FWD;
      VK_REWIND = KeyEvent.VK_REWIND;
    }

    if (typeof KeyEvent.VK_NEXT !== 'undefined') {
      VK_NEXT = KeyEvent.VK_NEXT;
      VK_PREV = KeyEvent.VK_PREV;
    }

    if (typeof KeyEvent.VK_BACK !== 'undefined') {
      VK_BACK = KeyEvent.VK_BACK;
    }

    if (typeof KeyEvent.VK_0 !== 'undefined') {
      VK_0 = KeyEvent.VK_0;
      VK_1 = KeyEvent.VK_1;
      VK_2 = KeyEvent.VK_2;
      VK_3 = KeyEvent.VK_3;
      VK_4 = KeyEvent.VK_4;
      VK_5 = KeyEvent.VK_5;
      VK_6 = KeyEvent.VK_6;
      VK_7 = KeyEvent.VK_7;
      VK_8 = KeyEvent.VK_8;
      VK_9 = KeyEvent.VK_9;
    }
  }
  if (typeof KeyEvent.VK_LEFT === 'undefined') {
    VK_LEFT = 0x25;
    VK_UP = 0x26;
    VK_RIGHT = 0x27;
    VK_DOWN = 0x28;
  }

  if (typeof KeyEvent.VK_ENTER === 'undefined') {
    VK_ENTER = 0x0d;
  }

  if (typeof KeyEvent.VK_RED === 'undefined') {
    VK_RED = 0x74;
    VK_GREEN = 0x75;
    VK_YELLOW = 0x76;
    VK_BLUE = 0x77;
  }

  if (typeof KeyEvent.VK_PLAY === 'undefined') {
    VK_PLAY = 0x50;
    VK_PAUSE = 0x51;
    VK_STOP = 0x53;
  }

  if (typeof KeyEvent.VK_PLAY_PAUSE === 'undefined') {
    VK_PLAY_PAUSE = -1;
  }

  if (typeof KeyEvent.VK_FAST_FWD === 'undefined') {
    VK_FAST_FWD = 0x46;
    VK_REWIND = 0x52;
  }

  if (typeof KeyEvent.VK_NEXT === 'undefined') {
    VK_NEXT = 0x1a9;
    VK_PREV = 0x1a8;
  }

  if (typeof KeyEvent.VK_BACK === 'undefined') {
    VK_BACK = 0xa6;
  }

  if (typeof KeyEvent.VK_0 === 'undefined') {
    VK_0 = 0x30;
    VK_1 = 0x31;
    VK_2 = 0x32;
    VK_3 = 0x33;
    VK_4 = 0x34;
    VK_5 = 0x35;
    VK_6 = 0x36;
    VK_7 = 0x37;
    VK_8 = 0x38;
    VK_9 = 0x39;
  }

  return {
    [keys.KEY_ENTER]: VK_ENTER,
    [keys.KEY_UP]: VK_UP,
    [keys.KEY_RIGHT]: VK_RIGHT,
    [keys.KEY_DOWN]: VK_DOWN,
    [keys.KEY_LEFT]: VK_LEFT,
    [keys.KEY_BACK]: VK_BACK,
    [keys.KEY_YELLOW]: VK_YELLOW,
    [keys.KEY_RED]: VK_RED,
    [keys.KEY_BLUE]: VK_BLUE,
    [keys.KEY_GREEN]: VK_GREEN,
    [keys.KEY_PLAY]: VK_PLAY,
    [keys.KEY_PAUSE]: VK_PAUSE,
    [keys.KEY_STOP]: VK_STOP,
    [keys.KEY_FAST_FWD]: VK_FAST_FWD,
    [keys.KEY_REWIND]: VK_REWIND,
    [keys.KEY_PLAY_PAUSE]: VK_PLAY_PAUSE,
    [keys.KEY_NEXT]: VK_NEXT,
    [keys.KEY_PREV]: VK_PREV,
    [keys.KEY_0]: VK_0,
    [keys.KEY_1]: VK_1,
    [keys.KEY_2]: VK_2,
    [keys.KEY_3]: VK_3,
    [keys.KEY_4]: VK_4,
    [keys.KEY_5]: VK_5,
    [keys.KEY_6]: VK_6,
    [keys.KEY_7]: VK_7,
    [keys.KEY_8]: VK_8,
    [keys.KEY_9]: VK_9,
  };
};

export const updateKeys: ITarget['updateKeys'] = setKeyMask;

export const createRouter: ITarget['createRouter'] = createBrowserRouter;

export const getSearchParams: ITarget['getSearchParams'] = defaultGetSearchParams;

export const confirmExitOnBack: ITarget['confirmExitOnBack'] = defaultConfirmExitOnBack;

export const exitApp: ITarget['exitApp'] = () => exit();

export const isExitEnabled: ITarget['isExitEnabled'] = () => !isChannel77();

export const isReady: ITarget['isReady'] = defaultIsReady;

export const reloadApp: ITarget['reloadApp'] = defaultReload;

export const showMessageOnReload: ITarget['showMessageOnReload'] = defaultShowMessageOnReload;

export const addOnlineOfflineListener: ITarget['onlineOfflineListener'] = defaultOnlineOfflineListener;

export const onLanguageDetected: ITarget['onLanguageDetected'] = defaultOnLanguageDetected;

export const timeBasedVideoEnd: ITargetConfig['timeBasedVideoEnd'] = defaultTimeBasedVideoEnd;

export const config: ITarget['config'] = defaultConfig;

export const thirdPartyAuth: ITarget['thirdPartyAuth'] = defaultThirdPartyAuth;

export const normalizeParams: ITarget['normalizeParams'] = defaultNormalizeParams;

<!DOCTYPE html PUBLIC "-//HbbTV//1.3.1//EN" "http://www.hbbtv.org/dtd/HbbTV-1.3.1.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <title>ARTE</title>
    <meta http-equiv="content-type" content="text/html; charset=utf-8" />
    <meta name="target" content="hbbtv" />
    <%= htmlWebpackPlugin.options.headFragment %>

    <script>
      function init() {
        try {
          var appManager = document.getElementById('oipfApplicationManager');
          var appObject = appManager.getOwnerApplication(document);
          if (appObject === null) {
            console.log('error acquiring the Application object!');
          } else {
            appObject.show();
            var mask = 0x1 + 0x2 + 0x4 + 0x8 + 0x10 + 0x20 + 0x40 + 0x80 + 0x100;
            appObject.privateData.keyset.setValue(mask);
          }
        } catch (e) {
          console.log('this is not an HbbTV client. Error: ' + e);
        }
      }
    </script>
    <style>
      object#oipfApplicationManager, object#oipfCapabilities {
        position: absolute;
        left: 0;
        top: 0;
        width: 0;
        height: 0;
      }
    </style>
  </head>

  <body onload="init()">
    <div>
      <object type="application/oipfApplicationManager" id="oipfApplicationManager"></object>
      <object type="application/oipfCapabilities" id="oipfCapabilities"></object>
    </div>
    <%= htmlWebpackPlugin.options.bodyFragment %>
  </body>
</html>

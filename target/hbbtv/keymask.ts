const setKeyMask = () => {
  try {
    const appManager = document.getElementById('oipfApplicationManager') as OIPF.ApplicationManagerObject;
    const appObject = appManager.getOwnerApplication(document);
    if (appObject === null) {
      console.log('setKeyMask: error acquiring the Application object!');
    } else {
      const mask = 0x1 + 0x2 + 0x4 + 0x8 + 0x10 + 0x20 + 0x40 + 0x80 + 0x100;
      appObject.privateData.keyset.setValue(mask);
    }
  } catch (e) {
    console.log('setKeyMask: this is not an HbbTV client. Error: ' + e);
  }
};

export { setKeyMask };

function getHeadFragment() {
  return `
      <link rel="preload" href="${process.env.REACT_ROUTER_BASE_PATH}img/arte-logo.png" as="image">
      <link rel="preload" href="${process.env.REACT_ROUTER_BASE_PATH}gifnocache/hbbtv_load_start.gif" as="image">
      <link rel="preload" href="${process.env.REACT_ROUTER_BASE_PATH}static/fonts/Barna-Regular.ttf" as="font" type="font/ttf" crossorigin />
      <link rel="preload" href="${process.env.REACT_ROUTER_BASE_PATH}static/fonts/Barna-Bold.ttf" as="font" type="font/ttf" crossorigin />
      <style>
          body {
            background-color: #151515; /* $primary-background */
          }
          #splash {
              z-index: 999;
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              background-image: url("${process.env.REACT_ROUTER_BASE_PATH}img/arte-logo.png");
              background-repeat: no-repeat;
              background-position: center;
              background-color: #151515;
          }
          #load-start{
            width: 0;
            height: 0;
            top: -10px; 
            left: -10px;
            position: absolute;
            background-image: url("${process.env.REACT_ROUTER_BASE_PATH}gifnocache/hbbtv_load_start.gif");
          }
      </style>
    `;
}

/**
 * Returns metadata block for the splash screen.
 * Does not render in prod env.
 * Useful for debugging app start issues when stuck at splash screen.
 *
 * @param {Object} [metadata] - Metadata object.
 * @param {string} metadata.hash - Commit hash.
 * @param {string} metadata.branch - Branch name.
 * @param {string} metadata.target - Target environment or build identifier.
 * @returns {string} HTML string for the metadata block or empty string if no metadata.
 */
function renderMetadata(metadata) {
  if (!metadata) return '';
  const { hash, branch, target } = metadata;
  return `<div style="padding:80px; font-size:32px; font-weight:bold; font-family:sans-serif">
    ${hash}<br/>${branch}<br/>${target}
  </div>`;
}

/**
 * Builds the full body fragment containing video, root, and splash containers.
 *
 * @param {Object} [metadata] - Metadata object passed to renderMetadata.
 * @returns {string} Complete HTML string for the body fragment.
 */
function getBodyFragment(metadata) {
  return `
    <div id="video"></div>
    <div id="root"></div>
    <div id="splash">${renderMetadata(metadata)}</div>
  `;
}

module.exports = {
  getHeadFragment,
  getBodyFragment,
};

import { Router } from '@remix-run/router';
import { Params, RouteObject } from 'react-router-dom';

import { ITargetConfig } from './ITargetConfig';

export interface ITarget {
  getKeyMap: () => void;
  /**
   * Only needed for hbbtv to re-register keys.
   */
  updateKeys: () => void;
  createRouter: (
    routes: RouteObject[],
    opts?: {
      basename?: string;
    },
  ) => Router;
  /**
   * A function returning URLSearchParams.
   */
  getSearchParams: () => URLSearchParams;
  confirmExitOnBack: () => boolean;
  exitApp: () => void;
  isExitEnabled: () => boolean;
  isReady: () => boolean;
  reloadApp: (relaunchQueryParams?: string) => void;
  showMessageOnReload: () => boolean;
  onlineOfflineListener: () => void;
  onLanguageDetected: () => void;
  config: ITargetConfig;
  thirdPartyAuth: {
    auth: () => Promise<boolean | undefined>;
    enabled: boolean;
  };
  normalizeParams?: (params: Readonly<Params>) => Readonly<Params>;
}

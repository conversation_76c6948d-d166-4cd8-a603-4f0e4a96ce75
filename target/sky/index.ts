import { createBrowserRouter } from 'react-router-dom';

import {
  defaultConfig,
  defaultConfirmExitOnBack,
  defaultGetSearchParams,
  defaultIsExitEnabled,
  defaultIsReady,
  defaultNormalizeParams,
  defaultOnLanguageDetected,
  defaultOnlineOfflineListener,
  defaultReload,
  defaultShowMessageOnReload,
  defaultThirdPartyAuth,
  defaultUpdateKeys,
} from '../defaults';
import { ITarget } from '../ITarget';

// TODO sky specific implementation

export const getKeyMap: ITarget['getKeyMap'] = () => {
  return {};
};

export const updateKeys: ITarget['updateKeys'] = defaultUpdateKeys;

export const createRouter: ITarget['createRouter'] = createBrowserRouter;

export const getSearchParams: ITarget['getSearchParams'] = defaultGetSearchParams;

export const confirmExitOnBack: ITarget['confirmExitOnBack'] = defaultConfirmExitOnBack;

export const exitApp: ITarget['exitApp'] = () => window.close();

export const isExitEnabled: ITarget['isExitEnabled'] = defaultIsExitEnabled;

export const isReady: ITarget['isReady'] = defaultIsReady;

export const reloadApp: ITarget['reloadApp'] = defaultReload;

export const showMessageOnReload: ITarget['showMessageOnReload'] = defaultShowMessageOnReload;

export const addOnlineOfflineListener: ITarget['onlineOfflineListener'] = defaultOnlineOfflineListener;

export const onLanguageDetected: ITarget['onLanguageDetected'] = defaultOnLanguageDetected;

export const config: ITarget['config'] = defaultConfig;

export const thirdPartyAuth: ITarget['thirdPartyAuth'] = defaultThirdPartyAuth;

export const normalizeParams: ITarget['normalizeParams'] = defaultNormalizeParams;

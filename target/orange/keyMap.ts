import * as keys from '../keys';

const keyMap = {
  [keys.KEY_ENTER]: typeof VK_ENTER != 'undefined' ? VK_ENTER : 13,
  [keys.KEY_UP]: typeof VK_UP != 'undefined' ? VK_UP : 38,
  [keys.KEY_RIGHT]: typeof VK_RIGHT != 'undefined' ? VK_RIGHT : 39,
  [keys.KEY_DOWN]: typeof VK_DOWN != 'undefined' ? VK_DOWN : 40,
  [keys.KEY_LEFT]: typeof VK_LEFT != 'undefined' ? VK_LEFT : 37,
  [keys.KEY_BACK]: typeof VK_BACK != 'undefined' ? VK_BACK : 166,

  [keys.KEY_STOP]: typeof VK_STOP != 'undefined' ? VK_STOP : 178,
  [keys.KEY_FAST_FWD]: typeof VK_FAST_FWD != 'undefined' ? VK_FAST_FWD : 228,
  [keys.KEY_REWIND]: typeof VK_REWIND != 'undefined' ? VK_REWIND : 227,
  [keys.KEY_PLAY_PAUSE]: typeof VK_PLAY_PAUSE != 'undefined' ? VK_PLAY_PAUSE : 179,

  [keys.KEY_0]: typeof VK_0 != 'undefined' ? VK_0 : 48,
  [keys.KEY_1]: typeof VK_1 != 'undefined' ? VK_1 : 49,
  [keys.KEY_2]: typeof VK_2 != 'undefined' ? VK_2 : 50,
  [keys.KEY_3]: typeof VK_3 != 'undefined' ? VK_3 : 51,
  [keys.KEY_4]: typeof VK_4 != 'undefined' ? VK_4 : 52,
  [keys.KEY_5]: typeof VK_5 != 'undefined' ? VK_5 : 53,
  [keys.KEY_6]: typeof VK_6 != 'undefined' ? VK_6 : 54,
  [keys.KEY_7]: typeof VK_7 != 'undefined' ? VK_7 : 55,
  [keys.KEY_8]: typeof VK_8 != 'undefined' ? VK_8 : 56,
  [keys.KEY_9]: typeof VK_9 != 'undefined' ? VK_9 : 57,
};

export { keyMap };

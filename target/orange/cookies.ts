import { Target } from '../../src/constants';
import { eraseAppData, readAppData, writeAppData } from '../../src/features/appdata/appdata';

export const COOKIE_ORANGE_PREFIX = 'orange';

export const COOKIE_ORANGE_AUTH_CODE = `${COOKIE_ORANGE_PREFIX}CodeToken`;
export const COOKIE_ORANGE_ACCESS_TOKEN = `${COOKIE_ORANGE_PREFIX}AccessToken`;
export const COOKIE_ORANGE_USER_TOKEN_EXPIRE = `${COOKIE_ORANGE_PREFIX}UserTokenExpire`;
export const COOKIE_ORANGE_REFRESH_TOKEN = `${COOKIE_ORANGE_PREFIX}RefreshToken`;

export const COOKIE_ORANGE_LOGGED_IN = `${COOKIE_ORANGE_PREFIX}LoggedIn`;

export const COOKIE_ORANGE_RECOMMENDATIONS = `${COOKIE_ORANGE_PREFIX}Recommendations`;

export function getOrangeAuthCode() {
  return readAppData(COOKIE_ORANGE_AUTH_CODE);
}

export function setOrangeAuthCode(pCode: string) {
  return writeAppData(COOKIE_ORANGE_AUTH_CODE, pCode);
}

export function deleteOrangeAuthCode() {
  eraseAppData(COOKIE_ORANGE_AUTH_CODE);
}

export function setOrangeAccessToken(pCode: string) {
  return writeAppData(COOKIE_ORANGE_ACCESS_TOKEN, pCode);
}

export function getOrangeAccessToken() {
  return readAppData(COOKIE_ORANGE_ACCESS_TOKEN);
}

export function deleteOrangeAccessToken() {
  eraseAppData(COOKIE_ORANGE_ACCESS_TOKEN);
}

export function getOrangeRefreshToken() {
  return readAppData(COOKIE_ORANGE_REFRESH_TOKEN);
}

export function setOrangeRefreshToken(pCode: string) {
  return writeAppData(COOKIE_ORANGE_REFRESH_TOKEN, pCode);
}

export function getOrangeTokenExpire() {
  return readAppData(COOKIE_ORANGE_USER_TOKEN_EXPIRE);
}

export function setOrangeTokenExpire(pExpire: string) {
  return writeAppData(COOKIE_ORANGE_USER_TOKEN_EXPIRE, pExpire);
}

export function deleteOrangeTokenExpire() {
  eraseAppData(COOKIE_ORANGE_USER_TOKEN_EXPIRE);
}

export function deleteOrangeRefreshToken() {
  eraseAppData(COOKIE_ORANGE_REFRESH_TOKEN);
}

export function setOrangeLoggedIn(pLoggedIn: boolean) {
  writeAppData(COOKIE_ORANGE_LOGGED_IN, pLoggedIn);
}

export function setOrangeRecommandations(pRecommandations: boolean) {
  writeAppData(COOKIE_ORANGE_RECOMMENDATIONS, pRecommandations);
}

export function getOrangeRecommandations() {
  return readAppData(COOKIE_ORANGE_RECOMMENDATIONS);
}

export function isOrangeLoggedIn() {
  // DELETE ME ! only to hide the menu buttons, store cookies
  // return true;
  return readAppData(COOKIE_ORANGE_LOGGED_IN);
}

export function isThirdPartyLoggedIn(pTarget: string) {
  if (pTarget === Target.ORANGE) {
    return getOrangeAuthCode() !== null && getOrangeRefreshToken() !== null && isOrangeLoggedIn() === 'true';
  }
}

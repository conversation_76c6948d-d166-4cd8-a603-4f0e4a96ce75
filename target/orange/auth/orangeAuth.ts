import { eraseAppData } from '../../../src/features/appdata/appdata';
import {
  acceptCookies,
  acceptTechnicalCookies,
  COOKIE_ANON_TOKEN,
  getUuid,
  rejectCookies,
  setCookiesNotified,
} from '../../../src/util/cookies';
import GetEnvFromURL from '../../../src/util/getEnvFromURL';
import isTokenExpired from '../../../src/util/isTokenExpired';
import { CODE, ORANGE_AUTH_API_URL, ORANGE_REDIRECT_URI, ORANGE_STATIC_CLIENT_ID } from '../constants';
import {
  deleteOrangeAuthCode,
  getOrangeAccessToken,
  getOrangeTokenExpire,
  isOrangeLoggedIn,
  setOrangeAccessToken,
  setOrangeAuthCode,
  setOrangeLoggedIn,
  setOrangeRecommandations,
  setOrangeRefreshToken,
  setOrangeTokenExpire,
} from '../cookies';
import { getSearchParams } from '../index';
import { getTvMidForOrange } from '../utils';
import { getOrangeAuthToken, getOrangeUserToken, refreshOrangeAuthToken } from './api';

const REFRESH_TOKEN_INTERVAL = 10000; // 10 seconds
let refreshTokenInterval: ReturnType<typeof setInterval> | undefined;

interface IAccessToken {
  content: {
    accessToken: string;
    expiresIn: number;
    redirectUri: string;
    refreshToken: string;
    refreshTokenExpiresIn: number;
    tokenType: string;
  };
  crc: number;
  type: string;
  version: string;
}

interface IOrangeUserData {
  type: string;
  content: {
    hasAccess: boolean;
    userId: string;
    consents: [
      {
        id: string;
        enabled: boolean;
        userId: string;
      },
    ];
    rlecontents: [
      {
        contentId: string;
        timecode: number;
        resumeTimecode: number;
        timecodeUpdateDate: number;
        watchingState: string;
      },
    ];
  };
}

enum OrangeConsentTypes {
  STATS = 'STATS',
  RECOS = 'RECOS',
}

const orangeAuth = async () => {
  const orangeCode = getSearchParams().get(CODE);

  if (isOrangeLoggedIn()) {
    return await refreshToken();
  }

  if (orangeCode) {
    return await getOrangeToken(orangeCode);
  }

  if (!orangeCode && !isOrangeLoggedIn()) {
    deleteOrangeAuthCode();
    redirect();
    return false;
  }
};

const Auth = {
  auth: orangeAuth,
  enabled: true,
};

const redirect = () => {
  const redirectQueryParams = {
    response_type: 'code',
    client_id: ORANGE_STATIC_CLIENT_ID,
    scope: 'replay_ott_userdata',
    state: getUuid(),
    redirect_uri: `https://${ORANGE_REDIRECT_URI[GetEnvFromURL()]}${getTvMidForOrange()}`,
  };

  const queryParamsString = new URLSearchParams(redirectQueryParams).toString();
  const redirectUrl = `${ORANGE_AUTH_API_URL}?${queryParamsString}`;

  window.location.href = redirectUrl;
};

const refreshToken = async () => {
  const refreshTokenData = (await refreshOrangeAuthToken()) as IAccessToken;
  if (refreshTokenData && refreshTokenData?.content) {
    processTokenData(refreshTokenData);
    startRefreshTokenInterval();
    return true;
  }

  return false;
};

const getOrangeToken = async (code: string) => {
  setOrangeAuthCode(code);

  const accessToken = (await getOrangeAuthToken(code)) as IAccessToken;
  if (accessToken?.type === 'orange_token') {
    eraseAppData(COOKIE_ANON_TOKEN);
    processTokenData(accessToken);
    startRefreshTokenInterval();
  }
  const searchParams = getSearchParams();
  searchParams.delete(CODE);
  return true;
};

/**
 * We need to refresh the token every accessToken?.content?.expiresIn time set in the orange token response, usually set to 1hr
 * We are testing to see if the token has expired, every 10 seconds and we compare it to the expire time - 60s
 */
const startRefreshTokenInterval = () => {
  stopRefreshTokenInterval();

  refreshTokenInterval = setInterval(async () => {
    if (isTokenExpired(Number(getOrangeTokenExpire()) - 60 * 1000)) {
      stopRefreshTokenInterval();
      await refreshToken();
    }
  }, REFRESH_TOKEN_INTERVAL);
};

const stopRefreshTokenInterval = () => {
  refreshTokenInterval && clearInterval(refreshTokenInterval);
};

const processTokenData = async (accessToken: IAccessToken) => {
  setOrangeAccessToken(accessToken?.content?.accessToken);
  setOrangeRefreshToken(accessToken?.content?.refreshToken);
  const expiryDate = new Date();

  expiryDate.setTime(expiryDate.getTime() + accessToken?.content?.expiresIn * 1000); // 60*60*1000);

  setOrangeTokenExpire(expiryDate.getTime() as unknown as string);

  const orangeUserData = (await getOrangeUserData()) as IOrangeUserData;
  console.log('orangeUserData', orangeUserData);
  // delete me
  setOrangeLoggedIn(true);

  // setOrangeLoggedIn(orangeUserData.type !== 'error');

  setCookiesNotified();

  // const consents = orangeUserData?.content?.consents;

  // delete me
  const consents = [
    {
      id: 'STATS',
      enabled: true,
    },
    {
      id: 'RECOS',
      enabled: true,
    },
  ];
  for (const consent of consents) {
    if (consent.id === OrangeConsentTypes.STATS) {
      consent.enabled === true ? acceptCookies() : rejectCookies();
    }
    if (consent.id === OrangeConsentTypes.RECOS) {
      setOrangeRecommandations(consent.enabled);
    }
  }

  /**
   * data not provided by Orange, will accept by default
   */
  acceptTechnicalCookies();
};

const getOrangeUserData = async () => {
  if (getOrangeAccessToken()) {
    return await getOrangeUserToken();
  }
  return null;
};

export { Auth };

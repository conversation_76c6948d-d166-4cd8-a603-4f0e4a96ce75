import { apiResource } from '../../../src/data';
import { middlewareUrl } from '../../../src/util/url';
import { getOrangeAccessToken, getOrangeRefreshToken } from '../cookies';
import { getTvMidForOrange } from '../utils';

export const getOrangeAuthToken = (pCode: string) =>
  apiResource(middlewareUrl(`/orange/auth`, { query: `code=${pCode}`, omitLang: true }));

export const refreshOrangeAuthToken = async () =>
  apiResource(middlewareUrl(`/orange/refresh`, { omitLang: true }), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      access_token: getOrangeAccessToken(),
      refresh_token: getOrangeRefreshToken(),
    }),
  });

export const getOrangeUserToken = async () => {
  return apiResource(
    middlewareUrl(`/orange/user`, {
      query: getTvMidForOrange() === '?tvmid=dev' ? 'orangeenv=preprod' : '',
      omitLang: true,
    }),
    {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${getOrangeAccessToken()}`,
      },
    },
  );
};

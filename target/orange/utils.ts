import { Params } from 'react-router-dom';

import GetEnvFromURL from '../../src/util/getEnvFromURL';

/**
 * Orange specific normalization of params.
 * Will get rid of the ATE_ prefix in arteId, programId and collectionId.
 * @param params
 */
const orangeNormalizeParams = (params: Readonly<Params>) => {
  return {
    ...params,
    videoId: params.videoId ? normalizeArteId(params.videoId) : undefined,
    programId: params.programId ? normalizeArteId(params.programId) : undefined,
    collectionId: params.collectionId ? normalizeArteId(params.collectionId) : undefined,
  };
};

const normalizeArteId = (arteId: string): string => {
  if (typeof arteId !== 'string') return arteId;
  return arteId.startsWith('ATE_') ? arteId.slice(4) : arteId;
};

const getTvMidForOrange = () => {
  const detectedEnv = GetEnvFromURL();

  if (detectedEnv === 'DEV') {
    return '?tvmid=dev';
  }

  if (detectedEnv === 'PREPROD') {
    return '?tvmid=preprod';
  }

  return '';
};

export { getTvMidForOrange, orangeNormalizeParams };

import { createBrowserRouter } from 'react-router-dom';

import {
  defaultConfig,
  defaultConfirmExitOnBack,
  defaultGetSearchParams,
  defaultIsExitEnabled,
  defaultIsReady,
  defaultOnLanguageDetected,
  defaultOnlineOfflineListener,
  defaultReload,
  defaultShowMessageOnReload,
  defaultUpdateKeys,
} from '../defaults';
import { ITarget } from '../ITarget';
import { AuthErrorMessage } from '../ITargetConfig';
import { Auth } from './auth/orangeAuth';
import { DESCRIPTION_KEY, ERROR_KEY } from './constants';
import { keyMap } from './keyMap';
import { orangeNormalizeParams } from './utils';

export const getKeyMap: ITarget['getKeyMap'] = () => keyMap;

export const updateKeys: ITarget['updateKeys'] = defaultUpdateKeys;

export const createRouter: ITarget['createRouter'] = createBrowserRouter;

export const getSearchParams: ITarget['getSearchParams'] = defaultGetSearchParams;

export const confirmExitOnBack: ITarget['confirmExitOnBack'] = defaultConfirmExitOnBack;

export const exitApp: ITarget['exitApp'] = () => window.close();

export const isExitEnabled: ITarget['isExitEnabled'] = defaultIsExitEnabled;

export const isReady: ITarget['isReady'] = defaultIsReady;

export const reloadApp: ITarget['reloadApp'] = defaultReload;

export const showMessageOnReload: ITarget['showMessageOnReload'] = defaultShowMessageOnReload;

export const addOnlineOfflineListener: ITarget['onlineOfflineListener'] = defaultOnlineOfflineListener;

export const onLanguageDetected: ITarget['onLanguageDetected'] = defaultOnLanguageDetected;

const error = getSearchParams().get(ERROR_KEY);
const description = getSearchParams().get(DESCRIPTION_KEY);

function getAuthErrorMessage(): AuthErrorMessage {
  return {
    title: error ? decodeURIComponent(error) : '',
    description: description ? decodeURIComponent(description) : '',
  };
}

export const config: ITarget['config'] = {
  ...defaultConfig,
  hasColorButtonSupport: false,
  hasAuthErrorQueryParams: !!error,
  authErrorMessage: getAuthErrorMessage(),
};

export const thirdPartyAuth: ITarget['thirdPartyAuth'] = Auth;

export const normalizeParams: ITarget['normalizeParams'] = orangeNormalizeParams;

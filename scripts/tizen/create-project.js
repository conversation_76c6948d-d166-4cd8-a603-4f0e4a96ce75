require('dotenv').config();
const shell = require('shelljs');
const path = require('path');
const replaceInFile = require('replace-in-file');
const paths = require('../paths');
const packageJson = require('../../package.json');

const tizenResources = path.resolve(__dirname, '..', `${paths.TARGET_RESOURCES}/tizen`);
const previewDist = `${paths.DIST}/service/preview`;

// copy resources to dist
const resources = [
  `${tizenResources}/config.xml`,
  `${tizenResources}/wgt-icon.png`,
  `${tizenResources}/.project`,
  `${tizenResources}/.tproject`,
];
shell.cp(resources, `${paths.DIST}`);

// copy preview resources
const previewResources = [`${tizenResources}/service/preview/*.*`];
shell.mkdir('-p', previewDist);
shell.cp(previewResources, previewDist);

// environment variable substitutions for config.xml
// TODO version number should be substituted here
const options = {
  files: [`${paths.DIST}/config.xml`],
  from: [/\${VERSION}/g],
  to: [packageJson.version],
};

try {
  replaceInFile.sync(options);
} catch (error) {
  console.error(error);
}

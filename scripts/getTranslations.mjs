// script to access www.lokalise.com (Arte SmartTV project), download translations for all six languages
// and save them in current folder as follows:
//
// /locales/de/translation.json
//         /en/translation.json
//         /es/translation.json
//         /fr/translation.json
//         /it/translation.json
//         /pl/translation.json
//
// note: this script expects the value of ARTE_LOKALISE_KEY set as a environment variable
//
// alternative: save ARTE_LOKALISE_KEY in .env file and uncomment the following two lines:
import dotenv from 'dotenv';
dotenv.config();

import { LokaliseApi } from '@lokalise/node-api';

import fs from 'fs';
import axios from 'axios';
import unzip from 'unzip-stream';

const PROJECT_ID = '3037003364be44e6f25fd0.58552531';
// Check if the lokalise key is set as a env variable, if not, exit
const lokaliseApiKey = process.env.ARTE_LOKALISE_KEY;

if (!lokaliseApiKey) {
  console.log('ARTE_LOKALISE_KEY is missing in the environment variables');
  process.exit(1);
}

const lokaliseApi = new LokaliseApi({ apiKey: lokaliseApiKey });

let fileName;

lokaliseApi
  .files()
  .download(PROJECT_ID, {
    format: 'json',
    original_filenames: false,
    bundle_structure: 'locales/%LANG_ISO%/translation.json',
    all_platforms: true,
    plural_format: 'i18next',
    placeholder_format: 'i18n',
    export_empty_as: 'base'
  })
  .then((result) => {
    fileName = result.bundle_url.split('/')[result.bundle_url.split('/').length - 1];
    return axios.get(result.bundle_url, { responseType: 'stream' });
  })
  .then((response) => {
    const file = fs.createWriteStream(fileName);
    const stream = response.data.pipe(file);
    // This is here incase any errors occur
    stream.on('error', (err) => {
      console.log('write error', err);
    });
    stream.on('finish', () => {
      const readStream = fs.createReadStream(fileName);
      const unzipExtractorArteVp = unzip.Extract({ path: '.' });
      readStream.pipe(unzipExtractorArteVp);
      readStream.on('error', (err) => {
        console.log('read error', err);
      });
      unzipExtractorArteVp.on('close', () => {
        fs.unlinkSync(fileName);
      });
    });
  })
  .catch((e) => {
    console.log(e);
  });

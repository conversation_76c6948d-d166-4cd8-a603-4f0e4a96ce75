# Scripts

Scripts here are intended to be utilised for various purposes, such as application packaging, post-build processing, and other targeted operations that a given target may require. For example, the webOS target requires a package in the form of an `.ipk`; this can be achieved using a custom script which is invoked as part of the build process.

## webOS

For webos packaging you will require the CLI tools.

> The webOS TV CLI (Command Line Interface) provides a collection of commands used for creating, packaging, installing, and launching web apps in the command line environment. The CLI allows you to develop and test your app without using a specific IDE. - https://webostv.developer.lge.com/develop/tools/cli-installation

## Packaged app vs. hosted App

In addition to packaging the webOS TV app as a .ipk file, it's important to note that webOS supports two different app types: [packaged apps and hosted apps](https://webostv.developer.lge.com/develop/getting-started/web-app-types). For ARTE we will use the hosted approach but both approaches are outlined below. 

### Packaged app
A packaged app is a self-contained webOS app that is bundled as a .ipk file. It includes all the necessary code, assets, and dependencies within the package itself. When a packaged app is installed on a webOS device, it runs locally, providing an isolated and independent experience.

Packaged apps are suitable for scenarios where the app's content and functionality are static and do not require frequent updates. They offer faster performance as the app's resources are readily available on the device. However, updating a packaged app requires releasing a new version of the .ipk file and distributing it to users.

### Hosted app
A hosted app, on the other hand, is a web app that resides on a remote server and is accessed by webOS devices through a URL. Instead of packaging all the app's resources within the .ipk file, a hosted app fetches its content dynamically from the server at runtime. This allows for more flexibility and easier app updates.

By using the hosted app solution, you can make changes to your app's content, UI, or functionality without requiring users to download and install a new version of the app. Users can simply access the updated app through its URL, eliminating the need for distribution and installation processes.

## Lokalise

To update translations in the app run the `getTranslations` script also available through `npm run translate`. It downloads the translation for each language used in the app and puts it in the `locales` folder. Note that an env variable, `ARTE_LOKALISE_KEY`, needs to be added in the `.env` file for the script to run.

/**
Generate ipk's for all environments and resolutions
Note these are only intended for dev and QA use
ipks are written to /ipk
Assuming you have ares cli setup you can install all as follows...

ares-install -d <device_name> ipk/arte-webos-dev-1080.ipk
ares-install -d <device_name> ipk/arte-webos-dev-720.ipk
ares-install -d <device_name> ipk/arte-webos-preprod-1080.ipk
ares-install -d <device_name> ipk/arte-webos-preprod-720.ipk
ares-install -d <device_name> ipk/arte-webos-prod-1080.ipk
ares-install -d <device_name> ipk/arte-webos-prod-720.ipk
ares-install -d <device_name> ipk/arte-webos-qa-1080.ipk
ares-install -d <device_name> ipk/arte-webos-qa-720.ipk
*/

require('dotenv').config();
const shell = require('shelljs');
const path = require('path');
const replaceInFile = require('replace-in-file');
const paths = require('../paths');
const packageJson = require('../../package.json');

const webosResources = path.resolve(__dirname, '..', `${paths.TARGET_RESOURCES}/webos`);

const apps = [
  {
    icon: 'logo-qa-720.png',
    id: 'com.arte.app.qa.720',
    packageName: 'arte-webos-qa',
    resolution: '720',
    splashBackground: 'splash-720.png',
    url: `http://smarttv-dev.arte.tv/static/smartTV-webos-720-qa/current/`,
  },
  {
    icon: 'logo-qa-1080.png',
    id: 'com.arte.app.qa.1080',
    packageName: 'arte-webos-qa',
    resolution: '1080',
    splashBackground: 'splash-1080.png',
    url: `http://smarttv-dev.arte.tv/static/smartTV-webos-1080-qa/current/`,
  },
  {
    icon: 'logo-dev-720.png',
    id: 'com.arte.app.dev.720',
    packageName: 'arte-webos-dev',
    resolution: '720',
    splashBackground: 'splash-720.png',
    url: `http://smarttv-dev.arte.tv/static/smartTV-webos-720/current/`,
  },
  {
    icon: 'logo-dev-1080.png',
    id: 'com.arte.app.dev.1080',
    packageName: 'arte-webos-dev',
    resolution: '1080',
    splashBackground: 'splash-1080.png',
    url: `http://smarttv-dev.arte.tv/static/smartTV-webos-1080/current/`,
  },
  {
    icon: 'logo-preprod-720.png',
    id: 'com.arte.app.preprod.720',
    packageName: 'arte-webos-preprod',
    resolution: '720',
    splashBackground: 'splash-720.png',
    url: `http://smarttv-preprod.arte.tv/static/smartTV-webos-720/current/`,
  },
  {
    icon: 'logo-preprod-1080.png',
    id: 'com.arte.app.preprod.1080',
    packageName: 'arte-webos-preprod',
    resolution: '1080',
    splashBackground: 'splash-1080.png',
    url: `http://smarttv-preprod.arte.tv/static/smartTV-webos-1080/current/`,
  },
  {
    icon: 'logo-prod-720.png',
    id: 'com.arte.app.prod.720',
    packageName: 'arte-webos-prod',
    resolution: '720',
    splashBackground: 'splash-720.png',
    url: `http://smarttv.arte.tv/static/smartTV-webos-720/current/`,
  },
  {
    icon: 'logo-prod-1080.png',
    id: 'com.arte.app.prod.1080',
    packageName: 'arte-webos-prod',
    resolution: '1080',
    splashBackground: 'splash-1080.png',
    url: `http://smarttv.arte.tv/static/smartTV-webos-1080/current/`,
  },
];

function generateIpk({ icon, id, packageName, url, splashBackground, resolution }) {
  const appInfoResolution = resolution === '720' ? '1280x720' : '1920x1080';

  shell.mkdir('-p', `${paths.BIN}/app`);
  shell.mkdir('-p', `${paths.IPK}`);

  const resources = [
    `${webosResources}/index.html`,
    `${webosResources}/appinfo.json`,
    `${webosResources}/${icon}`,
    `${webosResources}/${splashBackground}`,
  ];
  shell.cp(resources, `${paths.BIN}/app`);

  // Rename logo-<env>.png to logo.png as that name is required by `ares-package`
  shell.mv(`${paths.BIN}/app/${icon}`, `${paths.BIN}/app/logo.png`);

  const options = {
    files: [`${paths.BIN}/app/appinfo.json`, `${paths.BIN}/app/index.html`],
    from: [/\${ID}/g, /\${SPLASH_BACKGROUND}/g, /\${RESOLUTION}/g, /\${VERSION}/g, /\${WEBOS_HOSTED_URL}/g],
    to: [id, splashBackground, appInfoResolution, packageJson.version, url],
  };

  try {
    replaceInFile.sync(options);
  } catch (error) {
    console.error(error);
  }

  shell.exec(
    `ares-package ${paths.BIN}/app -v -n -o ${paths.BIN} && mv bin/*.ipk ${paths.IPK}/${packageName}-${resolution}.ipk`,
  );

  shell.rm('-rf', `${paths.BIN}/app`);
}

shell.rm('-rf', paths.BIN);
shell.rm('-rf', paths.IPK);

for (const app of apps) {
  generateIpk(app);
}

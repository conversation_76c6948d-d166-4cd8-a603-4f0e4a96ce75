require('dotenv').config();
const shell = require('shelljs');
const path = require('path');
const replaceInFile = require('replace-in-file');
const paths = require('../paths');
const packageJson = require('../../package.json');

const appID = 'com.arte.app';
const webosPackageName = 'arte-webos';
const webosResources = path.resolve(__dirname, '..', `${paths.TARGET_RESOURCES}/webos`);

// delete bin directory and all contents
shell.rm('-r', paths.BIN);

const appInfoResolution = process.env.RESOLUTION === '720' ? '1280x720' : '1920x1080';
const appInfoSplashBackground = `splash-${process.env.RESOLUTION}.png`;
const appInfoVersion = packageJson.version;

shell.mkdir(paths.BIN);
shell.mkdir(`${paths.BIN}/app`);

// copy resources to bin
const resources = [
  `${webosResources}/index.html`,
  `${webosResources}/appinfo.json`,
  `${webosResources}/logo.png`,
  `${webosResources}/${appInfoSplashBackground}`,
];
shell.cp(resources, `${paths.BIN}/app`);

// environment variable substitutions for appinfo
const options = {
  files: [`${paths.BIN}/app/appinfo.json`, `${paths.BIN}/app/index.html`],
  from: [/\${ID}/g, /\${SPLASH_BACKGROUND}/g, /\${RESOLUTION}/g, /\${VERSION}/g, /\${WEBOS_HOSTED_URL}/g],
  to: [appID, appInfoSplashBackground, appInfoResolution, appInfoVersion, process.env.WEBOS_HOSTED_URL],
};

try {
  replaceInFile.sync(options);
} catch (error) {
  console.error(error);
}

// package the app to get an .ipk
shell.exec(
  `ares-package ${paths.BIN}/app -v -n -o ${paths.BIN} && mv bin/*.ipk bin/${webosPackageName}-${process.env.RESOLUTION}.ipk`,
);

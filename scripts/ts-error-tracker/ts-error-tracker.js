const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');
const c = require('yoctocolors-cjs');

const projectRoot = process.cwd();
const baselinePath = path.join(projectRoot, 'scripts', 'ts-error-tracker/error-baseline.txt');

const baselineErrorCount = parseInt(fs.readFileSync(baselinePath, 'utf8').trim(), 10);

console.log(`\n${c.bold(c.cyan('--- TypeScript Error Tracker ---'))}\n`);

// run TypeScript compiler and capture output
let compilerOutput = '';
try {
  console.log(c.dim('Running TypeScript compiler...'));
  compilerOutput = execSync('npx tsc --noEmit', { encoding: 'utf8' });
} catch (error) {
  compilerOutput = error.stdout || '';
}

const currentErrorCount = (compilerOutput.match(/error TS\d+/g) || []).length;
const errorDifference = currentErrorCount - baselineErrorCount;

console.log(`${c.bold('Current TypeScript errors:')} ${c.yellow(currentErrorCount.toString())}`);
console.log(`${c.bold('Baseline TypeScript errors:')} ${c.blue(baselineErrorCount.toString())}`);
console.log('');

if (errorDifference > 0) {
  console.error(c.bold(c.red(`✖ TypeScript errors increased by ${errorDifference}`)));
  console.error(c.red(`  From ${baselineErrorCount} to ${currentErrorCount}`));
  console.error('');
  console.error(c.yellow('Please address new errors before committing.'));
  console.error('');
  process.exit(1);
} else if (errorDifference < 0) {
  console.log(c.bold(c.green(`✓ Nice! TypeScript errors reduced by ${errorDifference}`)));
  console.log(c.green(`  From ${baselineErrorCount} to ${currentErrorCount}`));
  console.log('');
  console.log(c.dim('Updating baseline file...'));
  // update baseline error count
  fs.writeFileSync(baselinePath, `${currentErrorCount}`);
  console.log(c.green(`Baseline updated to ${currentErrorCount}`));
} else {
  console.log(c.bold(c.cyan(`• TypeScript error count unchanged at ${currentErrorCount}`)));
}

console.log('\n');

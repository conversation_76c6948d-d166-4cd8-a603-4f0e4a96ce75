# TypeScript error tracker

A pre-commit hook tool that tracks TypeScript errors and ensures they do not increase over time. Helps maintain code quality by enforcing a baseline of TypeScript errors and encouraging their gradual reduction.


1. maintains a baseline count of TypeScript errors in `error-baseline.txt`
2. runs on pre-commit to check current TypeScript errors against the baseline
3. fails the commit if errors have increased
4. automatically updates the baseline when errors are reduced
5. integrates with husky for automatic pre-commit execution

## Usage

Automatically run as part of the pre-commit hook. You can also run it manually...

```bash
npm run ts:error-tracker
```

## Behaviour

- errors increase then commit is blocked with an error message
- errors decrease then `error-baseline.txt` is automatically updated and staged for commit
- errors remain the same then commit proceeds normally


# =====================================================================
# Build configs =======================================================
# =====================================================================
# TODO: pass env variables through github and remove jobs in this section

# Target Server for QA Deployments
qaServer=arte-static-web1.sdv.fr
# Target Server for Dev Deployments
devServer=arte-static-web1.sdv.fr
# Target Server for Preprod Deployments
preprodServer=arte-static-web1.sdv.fr
# Target Server for Prod Deployments
prodServer=arte-static-web1.sdv.fr

# Default Shell
SHELL:=/bin/bash

# Dot Env file name
configFile=".env"

set-build-config:
	@IFS=- read -r ACTION FLAVOR RES_OR_ENV ENV <<< $(MAKECMDGOALS); \
	echo '==[ Build Config Context ]=='; \
	echo ' » action = '$$ACTION; \
	echo ' » flavor = '$$FLAVOR; \
	echo ' » resolution (or environment) = '$$RES_OR_ENV; \
	echo ' » environment (if resolution) = '$$ENV; \
	[[ $$ENV == '' ]] \
	&& cat .infrastructure/env/.env .infrastructure/env/.env.$$FLAVOR.$$RES_OR_ENV       > ${configFile} \
	|| cat .infrastructure/env/.env .infrastructure/env/.env.$$FLAVOR.$$RES_OR_ENV.$$ENV > ${configFile}

delete-build-config:
	rm ${configFile}

# =====================================================================
# SSH =================================================================
# =====================================================================
get-target-server-qa:
	@echo $(qaServer)

get-target-server-dev:
	@echo $(devServer)

get-target-server-preprod:
	@echo $(preprodServer)

get-target-server-prod:
	@echo $(prodServer)

# =====================================================================
# Dependencies ========================================================
# =====================================================================

install-dependencies:
	npm install

# =====================================================================
# Builds ============================================================
# =====================================================================

# ~-~> Flavor HbbTV
# ~ qa - same as dev but with a different name
build-hbbtv-qa:
	npm run build:hbbtv
# ~ dev
build-hbbtv-dev:
	npm run build:hbbtv
# ~ preprod
build-hbbtv-preprod:
	npm run build:hbbtv:preproduction
# ~ prod
build-hbbtv-prod:
	npm run build:hbbtv:production

# ~-~> Flavor Tizen
# ~ qa - same as dev but with a different name
build-tizen-qa:
	npm run build:tizen
# ~ dev
build-tizen-dev:
	npm run build:tizen
# ~ preprod
build-tizen-preprod:
	npm run build:tizen:preproduction
# ~ prod
build-tizen-prod:
	npm run build:tizen:production

# ~-~> Flavor WebOS 720
# ~ qa - same as dev but with a different name
build-webos-720-qa:
	npm run build:webos:720
# ~ dev
build-webos-720-dev:
	npm run build:webos:720
# ~ preprod
build-webos-720-preprod:
	npm run build:webos:preproduction:720
# ~ prod
build-webos-720-prod:
	npm run build:webos:production:720

# ~-~> Flavor WebOS 1080
# ~ qa - same as dev but with a different name
build-webos-1080-qa:
	npm run build:webos:1080
# ~ dev
build-webos-1080-dev:
	npm run build:webos:1080
# ~ preprod
build-webos-1080-preprod:
	npm run build:webos:preproduction:1080
# ~ prod
build-webos-1080-prod:
	npm run build:webos:production:1080

# ~-~> Flavor HTML5
# ~ qa - same as dev but with a different name
build-html5-qa:
	npm run build:html5
# ~ dev
build-html5-dev:
	npm run build:html5
# ~ preprod
build-html5-preprod:
	npm run build:html5:preproduction
# ~ prod
build-html5-prod:
	npm run build:html5:production

# ~-~> Flavor Sky
# ~ qa - same as dev but with a different name
build-sky-qa:
	npm run build:sky
# ~ dev
build-sky-dev:
	npm run build:sky
# ~ preprod
build-sky-preprod:
	npm run build:sky:preproduction
# ~ prod
build-sky-prod:
	npm run build:sky:production

# ~-~> Flavor Orange
# ~ qa - same as dev but with a different name
build-orange-qa:
	npm run build:orange
# ~ dev
build-orange-dev:
	npm run build:orange
# ~ preprod
build-orange-preprod:
	npm run build:orange:preproduction
# ~ prod
build-orange-prod:
	npm run build:orange:production

# ~-~> Flavor Philips
# ~ qa - same as dev but with a different name
build-philips-qa:
	npm run build:philips
# ~ dev
build-philips-dev:
	npm run build:philips
# ~ preprod
build-philips-preprod:
	npm run build:philips:preproduction
# ~ prod
build-philips-prod:
	npm run build:philips:production

# ~-~> Flavor Panasonic
# ~ qa - same as dev but with a different name
build-panasonic-qa:
	npm run build:panasonic
# ~ dev
build-panasonic-dev:
	npm run build:panasonic
# ~ preprod
build-panasonic-preprod:
	npm run build:panasonic:preproduction
# ~ prod
build-panasonic-prod:
	npm run build:panasonic:production

# =====================================================================
# Deployments =========================================================
# =====================================================================

deploy-qa: ## deploys static HbbTV files to qa
	@echo "Deploying static HbbTV files to qa"
	./ci/deploy.sh arte-static-web1.sdv.fr /data/www/root/static-dev.arte.tv/static/hbbtvv11-qa

deploy-dev: ## deploys static HbbTV files to dev
	@echo "Deploying static HbbTV files to dev"
	./ci/deploy.sh arte-static-web1.sdv.fr /data/www/root/static-dev.arte.tv/static/hbbtvv11

# ~-~> Flavor HbbTV
# ~ qa
deploy-hbbtv-qa:
	@echo "Deploying static Flavor HbbTV files to qa"
	./ci/deploy.sh $(qaServer) /data/www/root/static-dev.arte.tv/static/hbbtvv11-qa
# ~ dev
deploy-hbbtv-dev:
	@echo "Deploying static Flavor HbbTV files to dev"
	./ci/deploy.sh $(devServer) /data/www/root/static-dev.arte.tv/static/hbbtvv11
# ~ preprod
deploy-hbbtv-preprod:
	@echo "Deploying static Flavor HbbTV files to preprod"
	./ci/deploy.sh $(preprodServer) /data/www/root/static-preprod.arte.tv/static/hbbtvv11
# ~ prod
deploy-hbbtv-prod:
	@echo "Deploying static Flavor HbbTV files to prod"
	./ci/deploy.sh $(prodServer) /data/www/root/static.arte.tv/static/hbbtvv11

# ~-~> Flavor WebOS 720
# ~ qa
deploy-webos-720-qa:
	@echo "Deploying static Flavor WebOS 720 files to qa"
	./ci/deploy.sh $(qaServer) /data/www/root/static-dev.arte.tv/static/smartTV-webos-720-qa
# ~ dev
deploy-webos-720-dev:
	@echo "Deploying static Flavor WebOS 720 files to dev"
	./ci/deploy.sh $(devServer) /data/www/root/static-dev.arte.tv/static/smartTV-webos-720
# ~ preprod
deploy-webos-720-preprod:
	@echo "Deploying static Flavor WebOS 720 files to preprod"
	./ci/deploy.sh $(preprodServer) /data/www/root/static-preprod.arte.tv/static/smartTV-webos-720
# ~ prod
deploy-webos-720-prod:
	@echo "Deploying static Flavor WebOS 720 files to prod"
	./ci/deploy.sh $(prodServer) /data/www/root/static.arte.tv/static/smartTV-webos-720

# ~-~> Flavor WebOS 1080
# ~ qa
deploy-webos-1080-qa:
	@echo "Deploying static Flavor WebOS 1080 files to qa"
	./ci/deploy.sh $(qaServer) /data/www/root/static-dev.arte.tv/static/smartTV-webos-1080-qa
# ~ dev
deploy-webos-1080-dev:
	@echo "Deploying static Flavor WebOS 1080 files to dev"
	./ci/deploy.sh $(devServer) /data/www/root/static-dev.arte.tv/static/smartTV-webos-1080
# ~ preprod
deploy-webos-1080-preprod:
	@echo "Deploying static Flavor WebOS 1080 files to preprod"
	./ci/deploy.sh $(preprodServer) /data/www/root/static-preprod.arte.tv/static/smartTV-webos-1080
# ~ prod
deploy-webos-1080-prod:
	@echo "Deploying static Flavor WebOS 1080 files to prod"
	./ci/deploy.sh $(prodServer) /data/www/root/static.arte.tv/static/smartTV-webos-1080

# ~-~> Flavor Tizen
# ~ qa
deploy-tizen-qa:
	@echo "Deploying static Flavor Tizen files to qa"
	./ci/deploy.sh $(qaServer) /data/www/root/static-dev.arte.tv/static/smartTV-tizen-qa
# ~ dev
deploy-tizen-dev:
	@echo "Deploying static Flavor Tizen files to dev"
	./ci/deploy.sh $(devServer) /data/www/root/static-dev.arte.tv/static/smartTV-tizen
# ~ preprod
deploy-tizen-preprod:
	@echo "Deploying static Flavor Tizen files to preprod"
	./ci/deploy.sh $(preprodServer) /data/www/root/static-preprod.arte.tv/static/smartTV-tizen
# ~ prod
deploy-tizen-prod:
	@echo "Deploying static Flavor Tizen files to prod"
	@echo "Disabled for security reasons."
#	./ci/deploy.sh $(prodServer) /data/www/root/static.arte.tv/static/smartTV-tizen

# ~-~> Flavor HTML5
# ~ qa
deploy-html5-qa:
	@echo "Deploying static Flavor HTML5 files to qa"
	./ci/deploy.sh $(qaServer) /data/www/root/static-dev.arte.tv/static/smartTV-html5-subset-qa
# ~ dev
deploy-html5-dev:
	@echo "Deploying static Flavor HTML5 files to dev"
	./ci/deploy.sh $(devServer) /data/www/root/static-dev.arte.tv/static/smartTV-html5-subset
# ~ preprod
deploy-html5-preprod:
	@echo "Deploying static Flavor HTML5 files to preprod"
	./ci/deploy.sh $(preprodServer) /data/www/root/static-preprod.arte.tv/static/smartTV-html5-subset
# ~ prod
deploy-html5-prod:
	@echo "Deploying static Flavor HTML5 files to prod"
	@echo "Disabled for security reasons."
	./ci/deploy.sh $(prodServer) /data/www/root/static.arte.tv/static/smartTV-html5-subset

# ~-~> Flavor Sky
# ~ qa
deploy-sky-qa:
	@echo "Deploying static Flavor Sky files to qa"
	./ci/deploy.sh $(qaServer) /data/www/root/static-dev.arte.tv/static/smartTV-sky-qa
# ~ dev
deploy-sky-dev:
	@echo "Deploying static Flavor Sky files to dev"
	./ci/deploy.sh $(devServer) /data/www/root/static-dev.arte.tv/static/smartTV-sky
# ~ preprod
deploy-sky-preprod:
	@echo "Deploying static Flavor Sky files to preprod"
	./ci/deploy.sh $(preprodServer) /data/www/root/static-preprod.arte.tv/static/smartTV-sky
# ~ prod
deploy-sky-prod:
	@echo "Deploying static Flavor Sky files to prod"
	@echo "Disabled for security reasons."
#	./ci/deploy.sh $(prodServer) /data/www/root/static.arte.tv/static/smartTV-sky

# ~-~> Flavor Orange
# ~ qa
deploy-orange-qa:
	@echo "Deploying static Flavor Orange TV files to qa"
	./ci/deploy.sh $(qaServer) /data/www/root/static-dev.arte.tv/static/smartTV-orange-qa
# ~ dev
deploy-orange-dev:
	@echo "Deploying static Flavor Orange TV files to dev"
	./ci/deploy.sh $(devServer) /data/www/root/static-dev.arte.tv/static/smartTV-orange
# ~ preprod
deploy-orange-preprod:
	@echo "Deploying static Flavor Oranage TV files to preprod"
	./ci/deploy.sh $(preprodServer) /data/www/root/static-preprod.arte.tv/static/smartTV-orange
# ~ prod
deploy-orange-prod:
	@echo "Deploying static Flavor Orange TV files to prod"
	./ci/deploy.sh $(prodServer) /data/www/root/static.arte.tv/static/smartTV-orange

# ~-~> Flavor Philips
# ~ qa
deploy-philips-qa:
	@echo "Deploying static Flavor Philips files to qa"
	./ci/deploy.sh $(qaServer) /data/www/root/static-dev.arte.tv/static/smartTV-html5v2
# ~ dev
deploy-philips-dev:
	@echo "Deploying static Flavor Philips files to dev"
	./ci/deploy.sh $(devServer) /data/www/root/static-dev.arte.tv/static/smartTV-html5v2
# ~ preprod
deploy-philips-preprod:
	@echo "Deploying static Flavor Philips files to preprod"
	./ci/deploy.sh $(preprodServer) /data/www/root/static-preprod.arte.tv/static/smartTV-html5v2-preprod
# ~ prod
deploy-philips-prod:
	@echo "Deploying static Flavor Philips files to prod"
	@echo "Disabled for security reasons."
#	./ci/deploy.sh $(prodServer) /data/www/root/static.arte.tv/static/smartTV-html5v2

# ~-~> Flavor Panasonic
# ~ qa
deploy-panasonic-qa:
	@echo "Deploying static Flavor Panasonic files to qa"
	./ci/deploy.sh $(qaServer) /data/www/root/static-dev.arte.tv/static/smartTV-panasonic-qa
# ~ dev
deploy-panasonic-dev:
	@echo "Deploying static Flavor Panasonic files to dev"
	./ci/deploy.sh $(devServer) /data/www/root/static-dev.arte.tv/static/smartTV-panasonic
# ~ preprod
deploy-panasonic-preprod:
	@echo "Deploying static Flavor Panasonic files to preprod"
	./ci/deploy.sh $(preprodServer) /data/www/root/static-preprod.arte.tv/static/smartTV-panasonic
# ~ prod
deploy-panasonic-prod:
	@echo "Deploying static Flavor Panasonic files to prod"
	@echo "Disabled for security reasons."
#	./ci/deploy.sh $(prodServer) /data/www/root/static.arte.tv/static/smartTV-panasonic

# =====================================================================
# Releases ============================================================
# =====================================================================

# Release configuration
release_dir = release
release_output_file = release/hbbtv_$(shell git rev-parse --short HEAD).tar.gz

clear-release:
	rm -f $(release_dir)/*.tar.gz

release: clear-release ## Create final compressed file of the build
	mkdir -p $(release_dir)
	cp ./Makefile ./dist
	cp -R ./ci ./dist
	(cd ./dist && tar --create -z --file="../$(release_output_file)" *)
	@echo "Built $(release_output_file)"

purge-varnish-qa:
	@echo "Cache should be purged automatically on the qa environment"

purge-varnish-dev:
	@echo "Cache should be purged automatically on the dev environment"

purge-varnish-preprod:
	@echo "Cache should be purged automatically on the preprod environment"

purge-varnish-prod:
	@echo "Cache should be purged automatically on the prod environment"

# ~-~> Flavor HbbTV
# ~ qa
release-hbbtv-qa: install-dependencies set-build-config build-hbbtv-dev release delete-build-config
# ~ dev
release-hbbtv-dev: install-dependencies set-build-config build-hbbtv-dev release delete-build-config
# ~ preprod
release-hbbtv-preprod: install-dependencies set-build-config build-hbbtv-preprod release delete-build-config
# ~ prod
release-hbbtv-prod: install-dependencies set-build-config build-hbbtv-prod release delete-build-config

# ~-~> Flavor Tizen
# ~ qa
release-tizen-qa: install-dependencies set-build-config build-tizen-dev release delete-build-config
# ~ dev
release-tizen-dev: install-dependencies set-build-config build-tizen-dev release delete-build-config
# ~ preprod
release-tizen-preprod: install-dependencies set-build-config build-tizen-preprod release delete-build-config
# ~ prod
release-tizen-prod: install-dependencies set-build-config build-tizen-prod release delete-build-config

# ~-~> Flavor WebOS 720
# ~ qa
release-webos-720-qa: install-dependencies set-build-config build-webos-720-dev release delete-build-config
# ~ dev
release-webos-720-dev: install-dependencies set-build-config build-webos-720-dev release delete-build-config
# ~ preprod
release-webos-720-preprod: install-dependencies set-build-config build-webos-720-preprod release delete-build-config
# ~ prod
release-webos-720-prod: install-dependencies set-build-config build-webos-720-prod release delete-build-config

# ~-~> Flavor WebOS 1080
# ~ qa
release-webos-1080-qa: install-dependencies set-build-config build-webos-1080-dev release delete-build-config
# ~ dev
release-webos-1080-dev: install-dependencies set-build-config build-webos-1080-dev release delete-build-config
# ~ preprod
release-webos-1080-preprod: install-dependencies set-build-config build-webos-1080-preprod release delete-build-config
# ~ prod
release-webos-1080-prod: install-dependencies set-build-config build-webos-1080-prod release delete-build-config

# ~-~> Flavor HTML5
# ~ qa
release-html5-qa: install-dependencies set-build-config build-html5-dev release delete-build-config
# ~ dev
release-html5-dev: install-dependencies set-build-config build-html5-dev release delete-build-config
# ~ preprod
release-html5-preprod: install-dependencies set-build-config build-html5-preprod release delete-build-config
# ~ prod
release-html5-prod: install-dependencies set-build-config build-html5-prod release delete-build-config

# ~-~> Flavor Sky
# ~ qa
release-sky-qa: install-dependencies set-build-config build-sky-dev release delete-build-config
# ~ dev
release-sky-dev: install-dependencies set-build-config build-sky-dev release delete-build-config
# ~ preprod
release-sky-preprod: install-dependencies set-build-config build-sky-preprod release delete-build-config
# ~ prod
release-sky-prod: install-dependencies set-build-config build-sky-prod release delete-build-config

# ~-~> Flavor Orange
# ~ qa
release-orange-qa: install-dependencies set-build-config build-orange-dev release delete-build-config
# ~ dev
release-orange-dev: install-dependencies set-build-config build-orange-dev release delete-build-config
# ~ preprod
release-orange-preprod: install-dependencies set-build-config build-orange-preprod release delete-build-config
# ~ prod
release-orange-prod: install-dependencies set-build-config build-orange-prod release delete-build-config

# ~-~> Flavor Philips
# ~ qa
release-philips-qa: install-dependencies set-build-config build-philips-dev release delete-build-config
# ~ dev
release-philips-dev: install-dependencies set-build-config build-philips-dev release delete-build-config
# ~ preprod
release-philips-preprod: install-dependencies set-build-config build-philips-preprod release delete-build-config
# ~ prod
release-philips-prod: install-dependencies set-build-config build-philips-prod release delete-build-config

# ~-~> Flavor Panasonic
# ~ qa
release-panasonic-qa: install-dependencies set-build-config build-panasonic-dev release delete-build-config
# ~ dev
release-panasonic-dev: install-dependencies set-build-config build-panasonic-dev release delete-build-config
# ~ preprod
release-panasonic-preprod: install-dependencies set-build-config build-panasonic-preprod release delete-build-config
# ~ prod
release-panasonic-prod: install-dependencies set-build-config build-panasonic-prod release delete-build-config

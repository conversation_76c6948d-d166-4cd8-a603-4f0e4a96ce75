import { ROUTES } from './constants';

const EXPECTED_ROUTES = {
  ROOT: '/',
  PAGE: '/page',
  PROGRAM: '/program',
  COLLECTION: '/collection',
  TV_GUIDE: '/page/tv_guide',
  VERIFICATION: '/verification',
  VIDEO: '/video',
  MYARTE: {
    ROOT: '/page/myarte',
    AUTHENTICATE: '/page/myarte/authenticate',
    LOGIN: '/page/myarte/login',
  },
  SETTINGS: {
    ROOT: '/page/settings',
    INTERFACE: '/page/settings/interface',
    PERSONALISATION: '/page/settings/personalisation',
    TUTORIAL: '/page/settings/tutorial',
    PRIVACY: '/page/settings/privacy',
    INFORMATION: '/page/settings/information',
  },
};

describe('ROUTES object', () => {
  it('should have the correct ROOT route', () => {
    expect(ROUTES.ROOT).toBe(EXPECTED_ROUTES.ROOT);
  });

  it('should have the correct PAGE route', () => {
    expect(ROUTES.PAGE).toBe(EXPECTED_ROUTES.PAGE);
  });

  it('should have the correct PROGRAM route', () => {
    expect(ROUTES.PROGRAM).toBe(EXPECTED_ROUTES.PROGRAM);
  });

  it('should have the correct COLLECTION route', () => {
    expect(ROUTES.COLLECTION).toBe(EXPECTED_ROUTES.COLLECTION);
  });

  it('should have the correct TV_GUIDE route', () => {
    expect(ROUTES.TV_GUIDE).toBe(EXPECTED_ROUTES.TV_GUIDE);
  });

  it('should have the correct VERIFICATION route', () => {
    expect(ROUTES.VERIFICATION).toBe(EXPECTED_ROUTES.VERIFICATION);
  });

  it('should have the correct VIDEO route', () => {
    expect(ROUTES.VIDEO).toBe(EXPECTED_ROUTES.VIDEO);
  });

  describe('MYARTE sub-routes', () => {
    it('should have the correct MYARTE ROOT route', () => {
      expect(ROUTES.MYARTE.ROOT).toBe(EXPECTED_ROUTES.MYARTE.ROOT);
    });

    it('should have the correct MYARTE AUTHENTICATE route', () => {
      expect(ROUTES.MYARTE.AUTHENTICATE).toBe(EXPECTED_ROUTES.MYARTE.AUTHENTICATE);
    });

    it('should have the correct MYARTE LOGIN route', () => {
      expect(ROUTES.MYARTE.LOGIN).toBe(EXPECTED_ROUTES.MYARTE.LOGIN);
    });
  });

  describe('SETTINGS sub-routes', () => {
    it('should have the correct SETTINGS ROOT route', () => {
      expect(ROUTES.SETTINGS.ROOT).toBe(EXPECTED_ROUTES.SETTINGS.ROOT);
    });

    it('should have the correct SETTINGS INTERFACE route', () => {
      expect(ROUTES.SETTINGS.INTERFACE).toBe(EXPECTED_ROUTES.SETTINGS.INTERFACE);
    });

    it('should have the correct SETTINGS PERSONALISATION route', () => {
      expect(ROUTES.SETTINGS.PERSONALISATION).toBe(EXPECTED_ROUTES.SETTINGS.PERSONALISATION);
    });

    it('should have the correct SETTINGS TUTORIAL route', () => {
      expect(ROUTES.SETTINGS.TUTORIAL).toBe(EXPECTED_ROUTES.SETTINGS.TUTORIAL);
    });

    it('should have the correct SETTINGS PRIVACY route', () => {
      expect(ROUTES.SETTINGS.PRIVACY).toBe(EXPECTED_ROUTES.SETTINGS.PRIVACY);
    });

    it('should have the correct SETTINGS INFORMATION route', () => {
      expect(ROUTES.SETTINGS.INFORMATION).toBe(EXPECTED_ROUTES.SETTINGS.INFORMATION);
    });
  });
});

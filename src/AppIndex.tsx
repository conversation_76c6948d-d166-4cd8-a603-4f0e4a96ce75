import { setPersonalisationByDefault } from '@util/cookies';
import { getRootElement } from '@util/getRootElement';
import { setUserLanguage } from '@util/languageDetection';
import { isReady, onLanguageDetected } from 'target';

import { App } from './App';
import { i18nInit } from './i18n';

setPersonalisationByDefault();

function renderApp() {
  const root = getRootElement();
  if (root) root.render(<App />);
}

export function init() {
  if (isReady()) {
    setUserLanguage().then(() => {
      i18nInit().then(() => {
        onLanguageDetected();
        renderApp();
      });
    });
  }
}

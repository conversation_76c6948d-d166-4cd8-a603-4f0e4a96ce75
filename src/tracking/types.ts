enum TrackingEventType {
  NAVIGATION = 'NAVIGATION',
  PLAYBACK = 'PLAYBACK',
  ACTION = 'ACTION',
}

enum TrackingEventAction {
  PAGE_VIEWED = 'PAGE_VIEWED',
  CONTROL_CLICKED = 'CONTROL_CLICKED',
  TEASER_CLICKED = 'TEASER_CLICKED',
  VIDEO_PLAYED = 'VIDEO_PLAYED',
  VIDEO_PAUSED = 'VIDEO_PAUSED',
  VIDEO_STARTED = 'VIDEO_STARTED',
  VIDEO_STOPPED = 'VIDEO_STOPPED',
}

type TrackingClient = {
  id: string;
  abv: null;
  language: string;

  app: {
    name: string;
    version: string;
    build: null;
    deviceType: string;
    os: string;
  };

  user: {
    type: string;
    id: string | null;
    ageVerification: string | null;
  };

  consent: {
    audience: boolean;
    push: boolean;
    technical: boolean;
  };
};

type TrackingEmacType = {
  page: {
    id: string;
    language: string;
    url: string;
    abv: string;
    query: string;
    category: string;
    subcategories: string;
  };
  content?: {
    id: string;
    slug: string;
    category: string;
    subcategory?: string;
    kind: string;
  };
  teaser: TrackingTeaserType | null;
};

type TrackingTeaserType = {
  trackingPixel?: string;
};

type TrackingSourceBase = {
  deeplink?: string;
  referrer: string;
};

type TrackingSourceUtmParams = {
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
};

type TrackingSourceType = TrackingSourceBase & TrackingSourceUtmParams;

type TrackingPlayerContextType = {
  associatedCollections: string[];
  category: string;
  duration: number;
  genre: string;
  id: string;
  imageFormat: 'HORIZONTAL' | 'VERTICAL';
  kind: string;
  programType: string;
  slug: string;
  subcategory: string;
};

type TrackingPlayerType = {
  timecode: number;
  previousTimeCode: number;
  audioTrackLanguage: string;
  audioTrackType: string;
  subtitlesTrackLanguage: string;
  subtitlesTrackType: string;
  streamUrl: string;
  soundMuted: null;
  playbackMode: string;
};

type TrackingApiContext = {
  emac: TrackingEmacType | null;
  player: TrackingPlayerContextType | null;
};

type TrackingEventBody = {
  time: string;
  type: TrackingEventType;
  action: TrackingEventAction;

  client: TrackingClient;
  frontendContext?: null;
  apiContext: TrackingApiContext;
  source: TrackingSourceType;
};

enum SubtitleTrackType {
  FULL = 'FULL',
  FORCED = 'FORCED',
  STM = 'STM',
}

enum AudioTrackType {
  STANDARD = 'STANDARD',
  AUDIO_DESCRIPTION = 'AUDIO_DESCRIPTION',
}

enum ControlGroupType {
  PROGRAM_ACTIONS = 'program_actions',
  COLLECTION_ACTIONS = 'collection_actions',
  TEASER_ACTIONS = 'teaser_actions',
  PLAYER_ACTIONS = 'player_actions',
  GREEN_BUTTON = 'green_button',
  SLIDER_MENU = 'slider_menu',
  SMARTTV_COLOR_BUTTONS = 'smarttv_color_buttons',
}

enum ControlGroupName {
  MORE_INFO = 'more_info',
  ADD_TO_FAVORITES = 'add_to_favorites',
  REMOVE_FROM_FAVORITES = 'remove_from_favorites',
  WATCH_TRAILER = 'watch_trailer',
}

export {
  TrackingEventType,
  TrackingEventAction,
  ControlGroupType,
  ControlGroupName,
  SubtitleTrackType,
  AudioTrackType,
};
export type {
  TrackingEventBody,
  TrackingClient,
  TrackingApiContext,
  TrackingSourceUtmParams,
  TrackingSourceBase,
  TrackingSourceType,
  TrackingEmacType,
  TrackingPlayerContextType,
  TrackingPlayerType,
};

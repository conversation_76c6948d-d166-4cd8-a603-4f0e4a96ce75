export const getStaticUserPageData = (id: 'LOGIN' | 'MY_FAVORITES', lang: string) => {
  const url =
    id === 'LOGIN'
      ? `https://www.arte.tv/${lang}/profile/auth/landing/`
      : `https://www.arte.tv/${lang}/profile/myvideos/favorite/`;

  return {
    stats: {
      serverSideTracking: {
        page: {
          id: id,
          language: lang,
          url: url,
          abv: 'A',
          query: null,
          category: null,
          subcategories: null,
        },
      },
    },
  };
};

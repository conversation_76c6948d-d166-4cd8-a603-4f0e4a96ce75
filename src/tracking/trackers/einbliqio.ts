import { TrackingVideoData } from '@apptypes/Tracking';
import { loadScript } from '@util/loadScript';

interface EinbliqSessionConfiguration {
  customerId: string;
  mediaUrl: string;
  mediaId?: string;
  mediaTitle?: string;
  mediaSeries?: string;
  mediaCategory?: string;
  applicationName?: string;
  applicationVersion?: string;
  customData?: Record<string, unknown>;
  versionSettings?: { version: string; transfer: string }[];
}

const tracker = function Einbliqio() {
  // Defining constant variables
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const global: any = window;
  const trackerName = tracker.name;
  const libraryUrl = `${process.env.REACT_ROUTER_BASE_PATH}libraries/einbliqio.hbbtv.v1.4.0.min.js`;
  const getSessionConfiguration = function (): EinbliqSessionConfiguration {
    return {
      customerId: 'wldy3ru6',
      mediaUrl: '',
      mediaId: '',
      mediaTitle: '',
      mediaCategory: '',
      applicationVersion: process.env.VERSION,
      applicationName: 'ARTE SmartTV App',
      versionSettings: [
        {
          version: '1.X',
          transfer: 'secure',
        },
      ],
    };
  };

  // Defining session based variables
  let loaded: boolean = false;
  let userOptedOut: boolean = false;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let einbliqIoDashJsAdater: null | any = null;
  let sessionConfiguration: EinbliqSessionConfiguration = getSessionConfiguration();

  const init = () => {
    // loading EINBLIQ.IO script
    loadScript(libraryUrl, true)
      .then(() => {
        if (global.EinbliqIo) {
          loaded = true;
          console.log(`[tracker] [${trackerName}] loaded & ready`);
        } else {
          console.log(`[tracker] [${trackerName}] UNKNOWN ERROR !`);
        }
      })
      .catch((error) => {
        console.error(`[tracker] [${trackerName}]`, error);
      });
  };

  const setVideoData = (data: TrackingVideoData) => {
    if (userOptedOut || !loaded) return;

    // getting default session configuration
    sessionConfiguration = getSessionConfiguration();
    // setting configuration values
    sessionConfiguration.mediaUrl = data?.stream?.url;
    sessionConfiguration.mediaId = data?.data?.attributes?.metadata?.providerId;
    sessionConfiguration.mediaTitle = data?.data?.attributes?.metadata?.title;
    sessionConfiguration.mediaCategory = data?.data?.attributes?.stat?.serverSideTracking?.category;
    sessionConfiguration.customData = {
      'hbbtv.language': data?.data?.attributes?.metadata?.language,
    };
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const trackDashjs = (player: any) => {
    // checking if session should be tracked
    if (userOptedOut || !loaded || !player) return;

    try {
      // checking if another session is active
      if (einbliqIoDashJsAdater) {
        // terminating session, if still active
        // this happens on the SKIP button in the Video UI
        einbliqIoDashJsAdater.terminateSession();
        // resetting adapter
        einbliqIoDashJsAdater = null;
      }
      // creating new adapter
      einbliqIoDashJsAdater = global.EinbliqIo.adapter.dashjs(player, sessionConfiguration);
    } catch (exception) {
      console.log(`[tracker] [${trackerName}] trackDashjs exception`, exception);
    }
  };

  const trackPageView = () => {
    // checking if session should be tracked
    if (userOptedOut || !loaded || !einbliqIoDashJsAdater) return;

    if (einbliqIoDashJsAdater) {
      einbliqIoDashJsAdater.terminateSession();
      einbliqIoDashJsAdater = null;
    }
    console.log(`[tracker] [${trackerName}] trackPageView`);
  };

  const optOut = (pOptOut: boolean) => {
    userOptedOut = pOptOut;
    console.log(`[tracker] [${trackerName}] userOptedOut: ${userOptedOut}`);
  };

  return {
    init,
    optOut,
    setVideoData,
    trackDashjs,
    trackPageView,
  };
};

const Einbliq = tracker();

export { Einbliq };

import { TrackingVideoData } from '@apptypes/Tracking';
import { SegmentType } from '@components/Video/types/Segmets';
import { Tracks } from '@components/Video/types/Tracks';
import { VideoLoaderResponse } from '@routes/videoLoader';
import { hasCookieConsent } from '@util/cookies';
import { loadScript } from '@util/loadScript';

import { Stream } from '../../types';

const dashVersion = '4.7.3';

enum EStatTrackStates {
  PLAY = 'play',
  PAUSE = 'pause',
  STOP = 'stop',
}

const PORTAL_FLAG = 'portailHBBTVFR';

const tracker = function Estat() {
  const trackerName = tracker.name;
  const libraryUrl = `${process.env.REACT_ROUTER_BASE_PATH}libraries/mu-7.2.js`;

  // TODO strongly type
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let eStatTag: any;

  let debugMode = true;
  let ready = false;
  let confStreamingAnalytics = null;
  let contentStreamTag = null;
  let currentPos = -1;
  let waitForRealTimeupdateInterval = null;
  let segmentTriggered: SegmentType | null = null;
  let ml3Backup = '';
  let ml5Backup = '- / -';

  const init = (pDebug = false) => {
    debugMode = pDebug;

    loadScript(libraryUrl, true)
      .then(() => {
        if (window.eStatTag) {
          ready = true;
          eStatTag = window.eStatTag;
          console.log(`[tracker] [${trackerName}] loaded & ready`);
        } else {
          console.log(`[tracker] [${trackerName}] UNKNOWN ERROR !`);
        }
      })
      .catch((error) => {
        console.error(`[tracker] [${trackerName}]`, error);
      });
  };

  const setVideoData = ({ data, stream, options }: TrackingVideoData) => {
    if (!ready) return;

    const {
      attributes: { live },
    } = data;

    const { isChannel77 } = options;

    live ? composeLive(data, stream) : composeVod(data, stream, isChannel77);
  };

  const composeVod = (data: VideoLoaderResponse, stream: Stream, isChannel77: boolean) => {
    console.log(`[tracker] [${trackerName}] Composing vod data stream`);

    currentPos = -1;
    const {
      attributes: {
        stat: { eStat },
      },
    } = data;
    const { versions } = stream;

    confStreamingAnalytics = {
      /**
       * Using hardcoded serial for now instead of the one from eStat object from player
       */
      serial: '235035215566', // safeGuardVar(eStat, 'serial'),
      measure: 'streaming',
      consentType: getConsentType(),
      debug: debugMode,

      streaming: {
        diffusion: 'replay',
        playerName: `dash.js_${dashVersion}_smarttv`,
        streamName: safeGuardVar(eStat, 'streamName'),
        streamDuration: safeGuardVar(eStat, 'streamDuration'),
        streamGenre: safeGuardVar(eStat, 'streamGenre'),
        playerVersion: '1',
        streamURL: safeGuardVar(stream, 'url'),
        callbackPosition: getPos,
        playerObj: document.getElementById('video-object'),
      },
      levels: {
        level_1: safeGuardVar(eStat, 'level3'),
        level_2: safeGuardVar(eStat, 'level2'),
        level_3: safeGuardVar(eStat, 'level1'),
        level_4: safeGuardVar(eStat, 'level4'),
        level_5: safeGuardVar(eStat, 'level5'),
      },
      newLevels: {
        newLevel_1: safeGuardVar(eStat, 'newLevel1'),
        newLevel_2: safeGuardVar(eStat, 'newLevel2'),
        newLevel_3: safeGuardVar(eStat, 'newLevel3'),
        newLevel_4: safeGuardVar(eStat, 'newLevel4'),
        newLevel_5: safeGuardVar(versions[0].eStat, 'ml5'),
        newLevel_6: null,
        newLevel_7: null,
        newLevel_8: null,
        newLevel_9: 'TV',
        newLevel_10: isChannel77 ? PORTAL_FLAG : process.env.TARGET,
        newLevel_11: safeGuardVar(eStat, 'newLevel11'),
      },
      mediaInfo: {
        mediaContentId: safeGuardVar(eStat, 'mediaContentId'),
        mediaDiffMode: safeGuardVar(eStat, 'mediaDiffMode'),
        mediaChannel: safeGuardVar(eStat, 'mediaChannel'),
      },
    };

    contentStreamTag = new eStatTag(confStreamingAnalytics);

    waitForRealTimeupdateInterval = setInterval(() => {
      if (currentPos > -1) {
        clearInterval(waitForRealTimeupdateInterval);
        trackPlay();
      }
    }, 50);
  };

  const trackSkipButton = (type: SegmentType) => {
    segmentTriggered = type;
    ml3Backup = confStreamingAnalytics.newLevels.newLevel_3;
    confStreamingAnalytics.newLevels.newLevel_3 = segmentTriggered;
    contentStreamTag.set(confStreamingAnalytics);
  };

  const getConsentType = () => (!hasCookieConsent() ? 'exempted' : 'optin');

  const getPos = () => currentPos;

  const trackTimeUpdate = (event: Event) => {
    if (!ready) return;

    const customEvent = event as CustomEvent;
    const { time } = customEvent?.detail;
    if (time) currentPos = time;
  };

  const trackPlay = () => {
    if (!ready) return;

    currentPos !== -1 && contentStreamTag.notifyPlayer(EStatTrackStates.PLAY, currentPos);

    if (currentPos !== -1 && segmentTriggered !== null) {
      segmentTriggered = null;
      confStreamingAnalytics.newLevels.newLevel_3 = ml3Backup;
      contentStreamTag.set(confStreamingAnalytics);
    }

    currentPos !== -1 && console.log(`[tracker] [${trackerName}] trackPlay`);
  };

  const trackPause = () => {
    if (!ready) return;

    contentStreamTag.notifyPlayer(EStatTrackStates.PAUSE, currentPos);

    console.log(`[tracker] [${trackerName}] trackPause`);
  };

  const trackEnded = () => {
    if (!ready) return;

    contentStreamTag.notifyPlayer(EStatTrackStates.STOP, currentPos);

    console.log(`[tracker] [${trackerName}] trackEnded`);
  };

  const trackStop = () => {
    if (!ready) return;

    currentPos !== -1 && contentStreamTag?.notifyPlayer(EStatTrackStates.STOP, currentPos);

    console.log(`[tracker] [${trackerName}] trackStop`);
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const composeLive = (data: VideoLoaderResponse, stream: Stream) => {
    console.log(`[tracker] [${trackerName}] Composing live data stream`);
  };

  const safeGuardVar = (pData, pVar: string) => (pData[pVar] ? pData[pVar] : '-');

  const triggerOptOut = () => {
    if (!confStreamingAnalytics) return;

    confStreamingAnalytics.consentType = getConsentType();
    contentStreamTag.set(confStreamingAnalytics);
    console.log(`[tracker] [${trackerName}] userOptedOut changed to ${confStreamingAnalytics.consentType}`);
  };

  const trackAudioSubtitleChange = (tracks: Tracks) => {
    if (!ready) return;

    const audioTrackLabel = tracks?.audio?.labels[0]?.text || '-';
    const subtitleTrackLabel = tracks?.text?.labels[0]?.text || '-';

    const ml5Constructed = `${audioTrackLabel} / ${subtitleTrackLabel}`;
    if (ml5Constructed !== ml5Backup) {
      ml5Backup = ml5Constructed;
      confStreamingAnalytics.newLevels.newLevel_5 = ml5Constructed;
      contentStreamTag.set(confStreamingAnalytics);
    }
  };

  return {
    init,
    triggerOptOut,
    setVideoData,
    trackTimeUpdate,
    trackSkipButton,
    trackPlay,
    trackPause,
    trackStop,
    trackEnded,
    trackAudioSubtitleChange,
  };
};

const Estat = tracker();

export { Estat };

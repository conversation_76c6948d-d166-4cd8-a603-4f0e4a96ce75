import { Tracks } from '@components/Video/types/Tracks';
import { Target } from '@constants';
import { getUtmSource } from '@features/queryParams/queryParamsLookup';
import { VideoLoaderResponse } from '@routes/videoLoader';
import { NIELSEN_EVENTS } from '@tracking/trackers/common';
import { getSHA, hasCookieConsent, isLoggedIn } from '@util/cookies';
import { v4 as uuidv4 } from 'uuid';

const APP_ID = 'PA109C5E7-FF98-4C34-8AEB-07D3FC8F1345';
const INSTANCE_NAME = `ArteHbbTV ${Math.floor(Math.random() * 100000)}`;
/**
 * Nielsen Mediametrie specific tracker implementation
 */
const tracker = function Mediametrie() {
  const trackerName = tracker.name;
  const sessionUuid = uuidv4();
  let debugMode = true;
  let ready = false;
  let nSdkInstance = null;
  let currentPos = -1;
  let userOptedOut = false;
  let mediametrieObject: null = null;

  const getConsentType = () => (!hasCookieConsent() ? 'true' : 'false');
  const getFPid = () => (!hasCookieConsent() ? 'false' : 'true');

  const init = async (data: VideoLoaderResponse) => {
    if (window.NOLBUNDLE) {
      ready = true;
      let sha = null;
      if (isLoggedIn()) {
        sha = await getSHA();
      }
      const config = {
        optout: getConsentType(),
        enableFpid: getFPid(),
        uid2: sessionUuid,
      };

      if (sha) {
        config.hem_sha1 = sha;
      }

      if (debugMode) {
        config.nol_sdkDebug = 'DEBUG';
      }

      nSdkInstance = window.NOLBUNDLE.nlsQ(APP_ID, INSTANCE_NAME, config);
      console.log(`[tracker] [${trackerName}] loaded & ready`);

      setVideoData(data);
    } else {
      console.log(`[tracker] [${trackerName}] UNKNOWN ERROR !`);
    }
  };

  const setDebugMode = (pDebug: boolean) => {
    debugMode = pDebug;
  };

  const setVideoData = (data: VideoLoaderResponse) => {
    if (!ready) return;
    if (!nSdkInstance.ggPM) return;
    if (userOptedOut) return;

    currentPos = -1;
    const {
      attributes: {
        stat: { mediametrie },
      },
    } = data;
    const getAppName = (): string => {
      const target = process.env.TARGET;
      const name = (() => {
        switch (target) {
          case Target.HBBTV:
            return 'HbbTV';
          case Target.HTML5:
            // abc => ABC
            return target.toUpperCase();
          case Target.TIZEN:
          case Target.WEBOS:
            // abc -> Abc
            return target.charAt(0).toUpperCase() + target.substring(1);
        }
      })();
      return `SmartTV-${name}`;
    };

    let customFields = {
      nol_p5: 'p5,TV',
      nol_p6: 'p6,n',
      nol_p7: `p7,${getAppName()}`,
      nol_p9: `p9,${process.env.VERSION}`,
      nol_p11: 'p11,n',
      nol_p13: 'p13,n',
    };

    if (getUtmSource()) {
      customFields = {
        ...customFields,
        nol_p8: `p8,${getUtmSource()}`,
      };
    }
    mediametrieObject = { ...mediametrie, ...customFields };

    mediametrieObject && nSdkInstance.ggPM(NIELSEN_EVENTS.LOAD_METADATA, mediametrieObject);
  };

  const trackTimeUpdate = (event: Event) => {
    if (!ready) return;
    if (!nSdkInstance.ggPM) return;
    if (userOptedOut) return;

    const customEvent = event as CustomEvent;
    const { time } = customEvent?.detail;
    let newPos = -1;

    if (time) {
      newPos = Math.round(time);
    }

    if (newPos !== -1 && newPos !== currentPos) {
      currentPos = newPos;
      nSdkInstance.ggPM('setPlayheadPosition', currentPos);
    }
  };

  const trackEnded = () => {
    if (!ready) return;
    if (!nSdkInstance.ggPM) return;
    if (userOptedOut) return;

    nSdkInstance.ggPM(NIELSEN_EVENTS.END, currentPos);
    console.log(`[tracker] [${trackerName}] trackEnded`);

    ready = false;
  };

  const trackStop = () => {
    if (!ready) return;
    if (!nSdkInstance.ggPM) return;
    if (userOptedOut) return;

    nSdkInstance.ggPM(NIELSEN_EVENTS.STOP, currentPos);
    console.log(`[tracker] [${trackerName}] trackStop`);
  };

  const trackPause = () => {
    if (!ready) return;
    if (!nSdkInstance.ggPM) return;
    if (userOptedOut) return;

    nSdkInstance.ggPM(NIELSEN_EVENTS.STOP, currentPos);
    console.log(`[tracker] [${trackerName}] trackStop`);
  };

  const trackAudioSubtitleChange = (tracks: Tracks) => {
    const audioTrackLabel = tracks?.audio?.labels[0]?.text || '-';
    const subtitleTrackLabel = tracks?.text?.labels[0]?.text || '-';

    mediametrieObject = { ...mediametrieObject, nol_p3: `p3,${audioTrackLabel}`, nol_p4: `p4,${subtitleTrackLabel}` };
  };

  const optOut = (pOptOut: boolean) => {
    userOptedOut = pOptOut;

    console.log(`[tracker] [${trackerName}] userOptedOut: ${userOptedOut}`);
  };

  return {
    init,
    optOut,
    setVideoData,
    trackTimeUpdate,
    trackStop,
    trackEnded,
    trackPause,
    trackAudioSubtitleChange,
    setDebugMode,
  };
};

const Mediametrie = tracker();

export { Mediametrie };

import { AgfType } from '@apptypes/VideoResponseBody';
import { ROLES, Roles, Tracks } from '@components/Video/types/Tracks';
import { readAppData } from '@features/appdata/appdata';
import { VideoLoaderResponse } from '@routes/videoLoader';
import { NIELSEN_EVENTS } from '@tracking/trackers/common';
import { DASHJS_TEXT_SETTINGS_COOKIE, hasCookieConsent } from '@util/cookies';

declare global {
  interface Window {
    NOLBUNDLE?: {
      nlsQ: (
        appId: string,
        instanceName: string,
        config: Record<string, unknown>,
      ) => { ggPM: (event: string, data: number | object) => void };
    };
  }
}

const APP_ID = 'P09785E42-8881-4A40-89C1-AEC09FFBF0AF';
const INSTANCE_NAME = 'ArteHbbTV';

type C19Fields = {
  lng: string;
  cty: string;
  scr: 'TV';
  brd: 'unknown';
  bct: string;
  pgt: string;
  atl: string;
  stl: string;
  vft: 'H' | 'V';
  cst: '-' | 'Y' | 'N';
};

function getTextLang(roles: Roles, lang: string) {
  if (roles?.value === ROLES.FORCED_SUBTITLE) {
    return `${lang}-fcd`;
  }

  if (roles?.value === ROLES.CAPTION) {
    return `${lang}-stm`;
  }

  if (roles?.value === ROLES.SUBTITLE) {
    return `${lang}`;
  }

  return '-';
}

/**
 * Nielsen AGF specific tracker implementation
 */
const tracker = function Agf() {
  const trackerName = tracker.name;
  const libraryUrl = `${process.env.REACT_ROUTER_BASE_PATH}libraries/nielsen.js`;
  let debugMode = true;
  let ready = false;
  let nSdkInstance: { ggPM: (arg0: string, arg1: number | object) => void } | null = null;
  let currentPos = -1; // move this to Tracking.ts in the future !
  let userOptedOut = false;
  let agfObject: AgfType | undefined = undefined;
  let c19Object: C19Fields;
  let firstLoad = true;

  function isTrackerReady() {
    return ready && !!nSdkInstance && !userOptedOut;
  }

  const getConsentType = () => (!hasCookieConsent() ? '1' : '0');

  const init = (data: VideoLoaderResponse) => {
    if (window.NOLBUNDLE) {
      ready = true;
      const config: Record<string, unknown> = {
        optout: getConsentType(),
      };

      if (debugMode) {
        config.nol_sdkDebug = 'DEBUG';
      }

      nSdkInstance = window.NOLBUNDLE.nlsQ(APP_ID, INSTANCE_NAME, config);
      console.log(`[tracker] [${trackerName}] loaded & ready`);

      setVideoData(data);
    } else {
      console.log(`[tracker] [${trackerName}] UNKNOWN ERROR !`);
    }
  };

  const setDebugMode = (pDebug: boolean) => {
    debugMode = pDebug;
  };

  const updateC19Field = (data: VideoLoaderResponse | Tracks) => {
    if ('attributes' in data) {
      // update C19 fields based on player response
      const serverSideTracking = data?.attributes?.stat?.serverSideTracking;

      c19Object = {
        ...c19Object,
        lng: data?.attributes?.metadata?.language,
        cty: data?.attributes?.restriction?.geoblocking?.userCountryCode || '-',
        scr: 'TV',
        brd: 'unknown',
        bct: serverSideTracking?.programType,
        pgt: serverSideTracking?.kind,
        vft: serverSideTracking?.imageFormat === 'HORIZONTAL' ? 'H' : 'V',
        cst: '-',
      };
    }

    if ('audio' in data) {
      // update audio lang
      c19Object = {
        ...c19Object,
        atl: data?.audio?.lang || '-',
      };
    }

    const dashTextSettings = readAppData(DASHJS_TEXT_SETTINGS_COOKIE);
    const dashTextSettingsObj = dashTextSettings ? JSON.parse(dashTextSettings) : null;
    let roles;
    let lang;
    if (dashTextSettingsObj?.settings) {
      roles = dashTextSettingsObj?.settings?.role;
      lang = dashTextSettingsObj?.settings?.lang;
    }

    c19Object = {
      ...c19Object,
      stl: getTextLang(roles, lang),
    };
  };

  const setVideoData = (data: VideoLoaderResponse) => {
    if (!isTrackerReady()) return;

    currentPos = -1;
    const {
      attributes: {
        stat: { agf },
      },
    } = data;

    agfObject = agf;
    updateC19Field(data);
    firstLoad = true;

    trackMetadata(NIELSEN_EVENTS.LOAD_METADATA);
  };

  const trackPlay = () => {
    if (!isTrackerReady()) return;

    !firstLoad && trackMetadata(NIELSEN_EVENTS.LOAD_METADATA);

    if (firstLoad) {
      firstLoad = false;
    }
  };

  const trackAudioSubtitleChange = (tracks: Tracks) => {
    updateC19Field(tracks);
  };

  const trackMetadata = (event: typeof NIELSEN_EVENTS.LOAD_METADATA) => {
    function convertC19FieldsToString(fields: C19Fields): string {
      return Object.entries(fields)
        .map(([key, value]) => `${key}=${value}`)
        .join('|');
    }
    const metadata = {
      ...agfObject,
      nol_c19: `p19,${convertC19FieldsToString(c19Object)}`,
    };
    agfObject && nSdkInstance?.ggPM(event, metadata);
  };

  const trackTimeUpdate = (event: Event) => {
    if (!isTrackerReady()) return;

    const customEvent = event as CustomEvent;
    const { time } = customEvent?.detail;
    let newPos = -1;

    if (time) {
      newPos = Math.round(time);
    }

    if (newPos !== -1 && newPos !== currentPos) {
      currentPos = newPos;
      nSdkInstance?.ggPM('setPlayheadPosition', currentPos);
    }
  };

  const trackEnded = () => {
    if (!isTrackerReady()) return;

    nSdkInstance?.ggPM(NIELSEN_EVENTS.END, currentPos);
    console.log(`[tracker] [${trackerName}] trackEnded`);

    ready = false;
  };

  const trackStop = () => {
    if (!isTrackerReady()) return;

    nSdkInstance?.ggPM(NIELSEN_EVENTS.STOP, currentPos);
    console.log(`[tracker] [${trackerName}] trackStop`);
  };

  const trackPause = () => {
    if (!isTrackerReady()) return;

    nSdkInstance?.ggPM(NIELSEN_EVENTS.STOP, currentPos);
    console.log(`[tracker] [${trackerName}] trackStop`);
  };

  const optOut = (pOptOut: boolean) => {
    userOptedOut = pOptOut;

    console.log(`[tracker] [${trackerName}] userOptedOut: ${userOptedOut}`);
  };

  return {
    init,
    optOut,
    trackPlay,
    setVideoData,
    trackTimeUpdate,
    trackStop,
    trackEnded,
    trackPause,
    trackAudioSubtitleChange,
    getLibraryUrl: () => libraryUrl,
    setDebugMode,
  };
};

const Agf = tracker();

export { Agf };

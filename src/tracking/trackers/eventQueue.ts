import { protocol } from '@util/protocol';

import { TrackingEventAction, TrackingEventBody } from '../types';

const eventQueue: TrackingEventBody[] = [];
let eventProcessing = false;
let eventRetryCount = 0;
let eventQueueTimeout: ReturnType<typeof setTimeout> | null = null;
const MAX_RETRIES = 3; // If an event fails to be sent, it will be retried up to MAX_RETRIES times
const DELAY_PROCESS_QUEUE = 3000; // Events will only be sent after a delay to allow for batching and ease page load times
const HIT_SERVERSIDE_ENDPOINT = `${protocol}event.arte.tv/api/server-side-tracking/v1/tracking`; // The endpoint to send the events to

/**
 * Will add events to queue and only processQueue if there are no events being processed and timeout executes
 * Will process all events if EXIT modal is triggered
 * @param event
 */
const addEventToQueue = (event: TrackingEventBody) => {
  eventQueue.push(event);
  if (!eventProcessing) {
    eventQueueTimeout && clearTimeout(eventQueueTimeout);
    if (event.action === TrackingEventAction.CONTROL_CLICKED && event.frontendContext?.control?.name === 'QUIT') {
      processEvents();
    } else {
      eventQueueTimeout = setTimeout(() => {
        processEvents();
      }, DELAY_PROCESS_QUEUE);
    }
  }
};

/**
 * Will process the event queue and send events to the server
 */
const processEvents = () => {
  if (!eventProcessing && eventQueue.length > 0) {
    sendEvent(eventQueue[0]);
  } else {
    eventProcessing = false;
  }
};

/**
 * Will send the event to API endpoint and callback processEvents to process the next event
 * if events are left in the queue
 * @param event
 */
const sendEvent = (event: TrackingEventBody) => {
  eventProcessing = true;
  eventRetryCount++;

  const eventJSON = JSON.stringify(event);

  // console.log(`[tracker] sendingEvent ${eventJSON} attempt no: ${eventRetryCount}`);

  fetch(HIT_SERVERSIDE_ENDPOINT, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: eventJSON,
  })
    .then(() => {
      eventQueue.shift();
      eventRetryCount = 0;
      eventProcessing = false;
      processEvents();
    })
    .catch(() => {
      if (eventRetryCount >= MAX_RETRIES) {
        eventQueue.shift();
        eventRetryCount = 0;
      }
      eventProcessing = false;
      processEvents();
    });
};

export { addEventToQueue };

import { NativeDeeplink } from '@apptypes/deeplink';
import { UserData } from '@apptypes/SSOResponse';
import { TrackingVideoData } from '@apptypes/Tracking';
import { Tracks } from '@components/Video/types/Tracks';
import { CHANNEL_77_QUERY_PARAM } from '@features/queryParams/queryParamsConsts';
import { getUtmCampaign, getUtmMedium, getUtmSource } from '@features/queryParams/queryParamsLookup';
import { capitalizeFirstLetter } from '@util/capitalizeFirstLetter';
import { getUuid, hasCookieConsent, hasTechnicalCookieConsent, isLoggedIn } from '@util/cookies';
import EventBus, { EventData } from '@util/EventBus';
import i18n from 'i18next';

import { EVENTS, Target } from '../../constants';
import {
  Bookmark,
  ITeaserProperties,
  ITeaserResponse,
  ITvGuideTeaserProperties,
  PageResponseBody,
  Zone,
} from '../../types';
import {
  AudioTrackType,
  ControlGroupName,
  ControlGroupType,
  SubtitleTrackType,
  TrackingApiContext,
  TrackingEmacType,
  TrackingEventAction,
  TrackingEventBody,
  TrackingEventType,
  TrackingPlayerContextType,
  TrackingPlayerType,
  TrackingSourceBase,
  TrackingSourceType,
  TrackingSourceUtmParams,
} from '../types';
import { addEventToQueue } from './eventQueue';

const AUDIO_SCHEME_ID_URI = 'urn:tva:metadata:cs:AudioPurposeCS:2007';

const tracker = function ServerSide() {
  const trackerName = tracker.name;

  let verificationToken: string | null = null;
  let userUid: string | null = null;

  let currentSourceBase: TrackingSourceBase;
  let currentSourceUtmParams: TrackingSourceUtmParams = {};
  let currentApiContext: TrackingApiContext | null = null;

  let authenticated: boolean = false;
  let authRecorded: boolean = false;

  let currentPos: number = -1;
  let pausedPosition: number = -1;
  let currentVideoData: TrackingVideoData | null = null;
  let currentTracks: Tracks | null = null;
  let videoSessionStart: Date = new Date();

  let zonesInUsage: Zone[] = [];
  let videoStartTracked: boolean = false;

  const init = async () => {
    console.log(`[tracker] [${trackerName}] ready`);
    EventBus.on(EVENTS.NATIVE_DEEPLINK, updateUtmParamsFromDeeplink);
  };

  const setupNewUserData = (userData: UserData | undefined) => {
    if (userData && userData?.data && userData?.data.length > 0) {
      const { ageVerificationToken, uid } = userData?.data[0];
      verificationToken = ageVerificationToken;
      userUid = uid;
    }
    authRecorded = true;
  };

  function getOS() {
    const utmSource = getUtmSource();
    const target = process.env.TARGET;
    const uppercasedTarget = target?.toUpperCase();

    if (target === Target.HTML5) {
      return utmSource ? utmSource.toUpperCase() : uppercasedTarget;
    }

    if (target === Target.HBBTV) {
      const isChannel77 = utmSource === CHANNEL_77_QUERY_PARAM.value;
      if (isChannel77) return 'HBBTV77';
      return uppercasedTarget;
    }

    // other targets
    return uppercasedTarget;
  }

  const getClient = async () => {
    authenticated = isLoggedIn();

    if (!authenticated && authRecorded) {
      verificationToken = null;
      userUid = null;
      authRecorded = false;
    }

    return {
      id: getUuid(),
      abv: null,
      language: i18n.language.toLowerCase(),

      app: {
        name: `SmartTV-${capitalizeFirstLetter(process.env.TARGET.toLowerCase())}`,
        version: process.env.VERSION,
        build: null,
        deviceType: 'TV',
        os: getOS(),
      },

      user: {
        type: authenticated ? 'AUTHENTICATED' : 'ANONYMOUS',
        id: authenticated ? userUid : null,
        ageVerification: authenticated ? verificationToken : null,
      },
      consent: {
        audience: hasCookieConsent(),
        push: false,
        technical: hasTechnicalCookieConsent(),
      },
    };
  };

  const initCurrentSource = (data: PageResponseBody | null) => {
    if (!currentSourceBase) {
      currentSourceBase = getSourceBase(data);

      currentSourceUtmParams = {
        utm_source: getUtmSource() || undefined,
        utm_medium: getUtmMedium() || undefined,
        utm_campaign: getUtmCampaign() || undefined,
      };
    }
  };

  const getSourceBase = (data: PageResponseBody | null): TrackingSourceBase => {
    return {
      deeplink: data?.id || null,
      referrer: window.location.href,
    } as TrackingSourceBase;
  };

  const getSource = (): TrackingSourceType => {
    return { ...currentSourceBase, ...currentSourceUtmParams };
  };

  const resetCurrentUtmParams = () => {
    currentSourceUtmParams = {};
  };

  function updateUtmParamsFromDeeplink<T>(eventData: EventData<T>) {
    const data: unknown = eventData[EVENTS.NATIVE_DEEPLINK];
    const deeplink = data as NativeDeeplink;
    currentSourceUtmParams = {
      utm_source: getUtmSource(),
      utm_campaign: deeplink.utmCampaign,
      utm_medium: deeplink.utmMedium,
    } as TrackingSourceUtmParams;
  }

  const trackPageView = async (data: PageResponseBody | ITvGuideTeaserProperties[] | null) => {
    const apiContextData = data?.stats?.serverSideTracking;
    if (!apiContextData) return;
    const time: string = new Date().toISOString();
    const type: TrackingEventType = TrackingEventType.NAVIGATION;
    const action: TrackingEventAction = TrackingEventAction.PAGE_VIEWED;
    const client = await getClient();
    initCurrentSource(data);
    const frontendContext = null;

    currentApiContext = {
      emac: {
        ...apiContextData,
        teaser: null,
      } as TrackingEmacType,
      player: null,
    };

    addEventToQueue({
      time,
      type,
      action,
      client,
      frontendContext,
      apiContext: currentApiContext,
      source: getSource(),
    } as unknown as TrackingEventBody);

    resetCurrentUtmParams();
  };

  const trackTeaserClick = async (
    teaser: ITeaserResponse,
    controlGroup: ControlGroupType | undefined,
    controlName: ControlGroupName | undefined,
  ) => {
    const time: string = new Date().toISOString();
    const type: TrackingEventType = TrackingEventType.ACTION;
    const action: TrackingEventAction = TrackingEventAction.TEASER_CLICKED;
    const client = await getClient();

    const zoneIndex = zonesInUsage.findIndex((zone) =>
      zone.teaserList.some(
        (teaserInZone) =>
          (teaserInZone?.item_id && teaser?.item_id && teaserInZone.item_id === teaser.item_id) ||
          (teaserInZone?.id && teaser?.id && teaserInZone.id === teaser.id),
      ),
    );

    const currentZone = zoneIndex !== -1 ? zonesInUsage[zoneIndex] : null;

    const teaserInZoneIndex = currentZone
      ? currentZone.teaserList.findIndex(
          (teaserInZone) =>
            (teaserInZone?.item_id && teaser?.item_id && teaserInZone.item_id === teaser.item_id) ||
            (teaserInZone?.id && teaser?.id && teaserInZone.id === teaser.id),
        )
      : 0;

    let frontendContext = {
      zone: {
        index: zoneIndex,
        code: currentZone?.code,
        template: currentZone?.template,
        title: currentZone?.title,
      },
      teaser: {
        index: teaserInZoneIndex,
        id: teaser.item_id || teaser.id,
        programId: teaser.program_id || teaser.id || teaser.item_id,
        title: teaser.title,
      },
    };

    if (!!controlGroup && !!controlName) {
      frontendContext = {
        ...frontendContext,
        control: {
          name: controlName,
          group: controlGroup,
        },
      };
    }

    addEventToQueue({
      time,
      type,
      action,
      client,
      frontendContext,
      apiContext: currentApiContext,
      source: getSource(),
    } as unknown as TrackingEventBody);
  };

  const trackControlClick = async (
    controlGroup: ControlGroupType,
    controlName: string,
    teaserBookmark: Bookmark | null | ITeaserProperties = null,
  ) => {
    const time: string = new Date().toISOString();
    const type: TrackingEventType = TrackingEventType.ACTION;
    const action: TrackingEventAction = TrackingEventAction.CONTROL_CLICKED;
    const client = await getClient();

    let frontendContext = {
      control: {
        name: controlName,
        group: controlGroup,
      },
      content: null,
    };

    if (teaserBookmark) {
      frontendContext = {
        ...frontendContext,
        content: {
          id: teaserBookmark.program_id,
          title: teaserBookmark.title,
        },
      };
    }

    currentApiContext = { ...currentApiContext, teaser: null } as TrackingApiContext;

    addEventToQueue({
      time,
      type,
      action,
      client,
      frontendContext,
      apiContext: currentApiContext,
      source: getSource(),
    } as unknown as TrackingEventBody);
  };

  const setVideoData = (data: TrackingVideoData) => {
    const apiContextData = data?.data?.attributes?.stat?.serverSideTracking;
    if (!apiContextData) return;
    currentVideoData = data;
    videoSessionStart = new Date();
    currentPos = pausedPosition = -1;
    videoStartTracked = false;
    currentApiContext = {
      emac: null,
      player: {
        ...(apiContextData as TrackingPlayerContextType),
      },
    };
  };

  const storeTimeUpdate = (event: Event) => {
    const customEvent = event as CustomEvent;
    const { time } = customEvent?.detail;
    if (time) currentPos = Math.round(time);
  };

  const storeAudioSubtitleChange = (tracks: Tracks) => {
    currentTracks = tracks;
  };

  const trackPlay = async (currentTime: number | undefined) => {
    currentPos = currentTime || currentPos;
    currentPos = Math.round(currentPos);

    const time: string = new Date().toISOString();
    const type: TrackingEventType = TrackingEventType.PLAYBACK;
    const action: TrackingEventAction = videoStartTracked
      ? TrackingEventAction.VIDEO_PLAYED
      : TrackingEventAction.VIDEO_STARTED;
    const client = await getClient();
    const subtitleRoles = currentTracks?.text?.roles || [];
    initCurrentSource(null);
    videoStartTracked = true;

    const frontendContext = {
      player: {
        timecode: currentPos,
        previousTimeCode: pausedPosition !== -1 ? pausedPosition : currentPos,
        audioTrackLanguage: getAudioTrackLanguage(currentTracks),
        audioTrackType: getAudioTrackType(currentTracks),
        subtitlesTrackLanguage: getSubtitleTrackLanguage(currentTracks),
        subtitlesTrackType: getSubtitleTrackType(subtitleRoles),
        streamUrl: currentVideoData?.stream.url,
        soundMuted: null,
        playbackMode: 'DEVICE',
      } as TrackingPlayerType,
    };

    addEventToQueue({
      time,
      type,
      action,
      client,
      frontendContext,
      apiContext: currentApiContext,
      source: getSource(),
    } as unknown as TrackingEventBody);

    resetCurrentUtmParams();
  };

  const trackPause = async () => {
    const time: string = new Date().toISOString();
    const type: TrackingEventType = TrackingEventType.PLAYBACK;
    const action: TrackingEventAction = TrackingEventAction.VIDEO_PAUSED;
    const client = await getClient();
    const subtitleRoles = currentTracks?.text?.roles || [];

    pausedPosition = currentPos;

    const frontendContext = {
      player: {
        timecode: currentPos,
        previousTimeCode: pausedPosition,
        audioTrackLanguage: getAudioTrackLanguage(currentTracks),
        audioTrackType: getAudioTrackType(currentTracks),
        subtitlesTrackLanguage: getSubtitleTrackLanguage(currentTracks),
        subtitlesTrackType: getSubtitleTrackType(subtitleRoles),
        streamUrl: currentVideoData?.stream.url,
        soundMuted: null,
        playbackMode: 'DEVICE',
      } as TrackingPlayerType,
    };

    addEventToQueue({
      time,
      type,
      action,
      client,
      frontendContext,
      apiContext: currentApiContext,
      source: getSource(),
    } as unknown as TrackingEventBody);
  };

  const trackStop = async () => {
    videoStartTracked = false;
    const videoSessionDuration: number = new Date().getTime() - videoSessionStart.getTime();
    /**
     * if video session duration is less than 1 second, we don't track it
     */
    if (videoSessionDuration < 1000) return;

    const time: string = new Date().toISOString();
    const type: TrackingEventType = TrackingEventType.PLAYBACK;
    const action: TrackingEventAction = TrackingEventAction.VIDEO_STOPPED;
    const client = await getClient();
    const subtitleRoles = currentTracks?.text?.roles || [];

    const frontendContext = {
      player: {
        timecode: currentPos,
        previousTimeCode: currentPos,
        audioTrackLanguage: getAudioTrackLanguage(currentTracks),
        audioTrackType: getAudioTrackType(currentTracks),
        subtitlesTrackLanguage: getSubtitleTrackLanguage(currentTracks),
        subtitlesTrackType: getSubtitleTrackType(subtitleRoles),
        streamUrl: currentVideoData?.stream.url,
        soundMuted: null,
        playbackMode: 'DEVICE',
      } as TrackingPlayerType,
    };

    addEventToQueue({
      time,
      type,
      action,
      client,
      frontendContext,
      apiContext: currentApiContext,
      source: getSource(),
    } as unknown as TrackingEventBody);
  };

  const trackEnded = () => {
    trackStop();
  };

  const getSubtitleTrackType = (subtitleRoles: string[]) => {
    let subtitlesTrackType = SubtitleTrackType.FULL;
    if (subtitleRoles.includes('forced-subtitle')) {
      subtitlesTrackType = SubtitleTrackType.FORCED;
    } else if (subtitleRoles.includes('caption')) {
      subtitlesTrackType = SubtitleTrackType.STM;
    }
    return subtitlesTrackType;
  };

  const getAudioTrackType = (currentTracks: Tracks | null) => {
    let audioTrackType = AudioTrackType.STANDARD;
    const audioChannelConfiguration = currentTracks?.audio?.audioChannelConfiguration?.[0];
    if (audioChannelConfiguration?.schemeIdUri === AUDIO_SCHEME_ID_URI && audioChannelConfiguration?.value === '1') {
      audioTrackType = AudioTrackType.AUDIO_DESCRIPTION;
    }

    return audioTrackType;
  };

  const getAudioTrackLanguage = (currentTracks: Tracks | null) => {
    return currentTracks?.audio?.labels[0]?.text || '';
  };

  const getSubtitleTrackLanguage = (currentTracks: Tracks | null) => {
    return currentTracks?.text?.labels[0]?.text || '';
  };

  const setZonesInUsage = (zones: Zone[]) => {
    zonesInUsage = zones.map((zone) => {
      return { ...zone };
    });
  };

  const updateZonesInUsageTeasers = (teasers: ITeaserResponse[], zoneId: string) => {
    const zone = zonesInUsage.find((zone) => zone.id === zoneId);
    if (zone) {
      zone.teaserList = teasers;
    }
  };

  return {
    init,
    trackPageView,
    trackTeaserClick,
    trackControlClick,
    setVideoData,
    storeTimeUpdate,
    trackPlay,
    storeAudioSubtitleChange,
    trackPause,
    trackStop,
    trackEnded,
    setupNewUserData,
    setZonesInUsage,
    updateZonesInUsageTeasers,
  };
};

const ServerSide = tracker();

export { ServerSide };

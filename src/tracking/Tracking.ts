import { UserData } from '@apptypes/SSOResponse';
import { SegmentType } from '@components/Video/types/Segmets';
import { Tracks } from '@components/Video/types/Tracks';
import { Mediametrie } from '@tracking/trackers/nielsen/mediametrie';
import { COOKIE_USER_CONSENT, hasCookieConsent } from '@util/cookies';
import { loadScript } from '@util/loadScript';

import {
  Bookmark,
  ITeaserProperties,
  ITeaserResponse,
  ITvGuideTeaserProperties,
  PageResponseBody,
  Zone,
} from '../types';
import { Me } from '../types/SSOResponse';
import { TrackingVideoData } from '../types/Tracking';
import EventBus from '../util/EventBus';
import { ClickType } from './clickType';
import { Einbliq } from './trackers/einbliqio';
import { Estat } from './trackers/estat';
import { Agf } from './trackers/nielsen/agf';
import { ServerSide } from './trackers/serverSide';
import { ControlGroupName, ControlGroupType } from './types';

const Tracking = (() => {
  let debugMode = false;
  let ready = false;
  let userOptedOut = !hasCookieConsent();

  const enabledTrackers = {
    estat: true,
    agf: true,
    mediametrie: true,
    serverSide: true,
    einbliq: true,
  };

  const init = async (pDebug = false) => {
    debugMode = pDebug;
    if (ready) return;

    enabledTrackers.estat && Estat.init(debugMode);

    enabledTrackers.serverSide && ServerSide.init();

    enabledTrackers.einbliq && Einbliq.init();

    ready = true;
  };

  const setupUserData = (userData: UserData | Me | undefined) => {
    enabledTrackers.serverSide && ServerSide.setupNewUserData(userData);
  };

  const setVideoData = (trackingVideoData: TrackingVideoData) => {
    enabledTrackers.estat && Estat.setVideoData(trackingVideoData);
    enabledTrackers.serverSide && ServerSide.setVideoData(trackingVideoData);
    enabledTrackers.einbliq && Einbliq.setVideoData(trackingVideoData);

    if (enabledTrackers.agf || enabledTrackers.mediametrie) {
      const headScripts = document.head.querySelectorAll('script');
      headScripts.forEach((script) => {
        if (script.src && (script.src.includes(Agf.getLibraryUrl()) || script.src.includes('nmrodam.com'))) {
          script.remove();
        }
      });

      window.NOLBUNDLE = null;

      loadScript(Agf.getLibraryUrl(), true)
        .then(() => {
          const nielsenDebugMode = process.env.NODE_ENV !== 'production';
          enabledTrackers.agf && Agf.setDebugMode(nielsenDebugMode);
          enabledTrackers.mediametrie && Mediametrie.setDebugMode(nielsenDebugMode);
          enabledTrackers.agf && Agf.init(trackingVideoData.data);
          enabledTrackers.mediametrie && Mediametrie.init(trackingVideoData.data);
        })
        .catch((error) => {
          console.error(`[tracker] [Nielsen]`, error);
        });
    }
  };

  const trackTimeUpdate = (event: Event) => {
    enabledTrackers.estat && Estat.trackTimeUpdate(event);
    enabledTrackers.agf && Agf.trackTimeUpdate(event);
    enabledTrackers.mediametrie && Mediametrie.trackTimeUpdate(event);
    enabledTrackers.serverSide && ServerSide.storeTimeUpdate(event);
  };

  const trackPlay = (currentTime: number | undefined) => {
    enabledTrackers.estat && Estat.trackPlay();
    enabledTrackers.agf && Agf.trackPlay();
    enabledTrackers.serverSide && ServerSide.trackPlay(currentTime);
  };

  const trackPause = () => {
    enabledTrackers.estat && Estat.trackPause();
    enabledTrackers.agf && Agf.trackPause();
    enabledTrackers.mediametrie && Mediametrie.trackPause();
    enabledTrackers.serverSide && ServerSide.trackPause();
  };

  const trackEnded = () => {
    enabledTrackers.estat && Estat.trackEnded();
    enabledTrackers.agf && Agf.trackEnded();
    enabledTrackers.mediametrie && Mediametrie.trackEnded();
    enabledTrackers.serverSide && ServerSide.trackEnded();
  };

  const trackStop = () => {
    enabledTrackers.estat && Estat.trackStop();
    enabledTrackers.agf && Agf.trackEnded(); // only for Nielsen implementation, need trackEnded on stop event
    enabledTrackers.mediametrie && Mediametrie.trackEnded(); // only for Nielsen implementation, need trackEnded on stop event
    enabledTrackers.serverSide && ServerSide.trackStop();
  };

  const trackPageView = (data: PageResponseBody | ITvGuideTeaserProperties[] | null) => {
    enabledTrackers.serverSide && ServerSide.trackPageView(data);
    enabledTrackers.einbliq && Einbliq.trackPageView();
  };

  /**
   * Leaving this function empty for now, as it's properly targeted by the app right now.'
   * Will be used in the future to track clicks on the app.
   * @param type
   * @param data
   */
  const trackClick = (type: ClickType, properties: ITeaserProperties) => {
    enabledTrackers.serverSide &&
      ServerSide.trackControlClick(ControlGroupType.SMARTTV_COLOR_BUTTONS, type, properties);
  };

  const trackTeaserClick = (data: ITeaserResponse, controlGroup?: ControlGroupType, controlName?: ControlGroupName) => {
    enabledTrackers.serverSide && ServerSide.trackTeaserClick(data, controlGroup, controlName);
  };

  const trackSkipButton = (type: SegmentType) => {
    enabledTrackers.estat && Estat.trackSkipButton(type);
  };

  const trackAudioSubtitleChange = (tracks: Tracks) => {
    enabledTrackers.estat && Estat.trackAudioSubtitleChange(tracks);
    enabledTrackers.serverSide && ServerSide.storeAudioSubtitleChange(tracks);
    enabledTrackers.agf && Agf.trackAudioSubtitleChange(tracks);
    enabledTrackers.mediametrie && Mediametrie.trackAudioSubtitleChange(tracks);
  };

  const trackControlClick = (
    controlGroup: ControlGroupType,
    controlName: string,
    teaserBookmark: Bookmark | null = null,
  ) => {
    enabledTrackers.serverSide && ServerSide.trackControlClick(controlGroup, controlName, teaserBookmark);
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const trackDashjs = (player: any) => {
    Einbliq.trackDashjs(player);
  };

  EventBus.on(COOKIE_USER_CONSENT, (data) => {
    const { cookieValue } = data;
    userOptedOut = !cookieValue;

    enabledTrackers.estat && Estat.triggerOptOut();
    enabledTrackers.agf && Agf.optOut(userOptedOut);
    enabledTrackers.mediametrie && Mediametrie.optOut(userOptedOut);
    enabledTrackers.einbliq && Einbliq.optOut(userOptedOut);
  });

  const setZonesInUsage = (zones: Zone[]) => {
    enabledTrackers.serverSide && ServerSide.setZonesInUsage(zones);
  };

  const updateZonesInUsageTeasers = (teasers: ITeaserResponse[], zoneId: string) => {
    enabledTrackers.serverSide && ServerSide.updateZonesInUsageTeasers(teasers, zoneId);
  };

  return {
    init,
    setVideoData,
    trackSkipButton,
    trackTimeUpdate,
    trackPlay,
    trackPause,
    trackStop,
    trackEnded,
    trackPageView,
    trackClick,
    trackAudioSubtitleChange,
    trackTeaserClick,
    trackControlClick,
    trackDashjs,
    setupUserData,
    setZonesInUsage,
    updateZonesInUsageTeasers,
  };
})();

export { Tracking };

import { DefaultPageResults, IInitialResults, IPageResults } from '@apptypes/pagination';
import { getNextZonePage } from '@data/source';
import { usePagination } from '@features/pagination/usePagination';
import React, { createContext, ReactNode, useEffect, useState } from 'react';

import { DefaultSearchResults, ISearchResults } from '../types';

interface ISearchProviderProperties {
  children: ReactNode;
}

interface ISearchContextState {
  searchResults: ISearchResults;
  searchPageResults: IPageResults;
  fetchNextPage: () => void;
  setSearchResults: (searchResults: ISearchResults) => void;
  busyFetchingPage: boolean;
  currentGridItemIndex: number | undefined;
  setCurrentGridItemIndex: (index: number | undefined) => void;
  setSearchPageResults: (searchPageResults: IPageResults) => void;
  setCurrentPaginationPage: (page: number) => void;
}

const defaultContext = {
  searchResults: DefaultSearchResults,
  searchPageResults: DefaultPageResults,
  fetchNextPage: () => {},
  setSearchResults: () => {},
  busyFetchingPage: false,

  /**
   * temp usage of current grid item index
   * Should be handled by nav history later !
   */
  currentGridItemIndex: undefined,
  setCurrentGridItemIndex: () => {},
  setSearchPageResults: () => {},
  setCurrentPaginationPage: () => {},
};

export const SearchContext = createContext<ISearchContextState>(defaultContext);

const getQueryParams = (results: IInitialResults): URLSearchParams => {
  const queryParams = new URLSearchParams();

  queryParams.set('query', (results as ISearchResults).query);
  if (!!(results as ISearchResults).genre.value) {
    queryParams.set('genre', (results as ISearchResults)?.genre?.value.toString());
  }

  return queryParams;
};

export const SearchContextProvider: React.FC<ISearchProviderProperties> = ({ children }) => {
  const [currentGridItemIndex, setCurrentGridItemIndex] = useState<number | undefined>();

  const {
    initialResults,
    pageResults,
    fetchNextPage,
    setInitialResults,
    busyFetchingPage,
    setPageResults,
    setCurrentPaginationPage,
    setPageId,
  } = usePagination<IPageResults, ISearchResults>(getNextZonePage, DefaultSearchResults, getQueryParams);

  useEffect(() => {
    setPageId('SEARCH_HOME');
  }, [setPageId]);

  return (
    <SearchContext.Provider
      value={{
        searchResults: initialResults,
        searchPageResults: pageResults,
        fetchNextPage,
        setSearchResults: setInitialResults,
        busyFetchingPage,
        setSearchPageResults: setPageResults,
        setCurrentPaginationPage,
        currentGridItemIndex,
        setCurrentGridItemIndex,
      }}
    >
      {children}
    </SearchContext.Provider>
  );
};

export default SearchContextProvider;

import React, { ReactNode, useCallback, useMemo } from 'react';

import { ModalLayer } from '../components/Modal/ModalLayer/ModalLayer';
import { trackAppLoadingEnds } from '../tracking/appstart/appstart';

export interface IModalState {
  content: ReactNode;
}

export interface IModalContext {
  modalOpen?: boolean;
  hideModal: () => void;
  showModal: (options: IModalState) => void;
}

interface IModalProviderProperties {
  onHide?: () => void;
  children: ReactNode;
}

const noop = () => false;

export const ModalContext = React.createContext<IModalContext>({
  modalOpen: false,
  hideModal: noop,
  showModal: noop,
});

export const ModalProvider: React.FC<IModalProviderProperties> = ({ children, onHide }) => {
  const [{ content }, toggleModal] = React.useState<IModalState>({
    content: null,
  });

  const showModal = useCallback(({ content }: IModalState) => {
    trackAppLoadingEnds();
    toggleModal({
      content,
    });
  }, []);

  const hideModal = useCallback(() => {
    toggleModal(() => ({
      content: null,
    }));
    onHide?.();
  }, [onHide]);

  const contextValue = useMemo(
    () => ({
      modalOpen: !!content,
      hideModal,
      showModal,
    }),
    [content, hideModal, showModal],
  );

  return (
    <ModalContext.Provider value={contextValue}>
      <>
        {!!content && <ModalLayer>{content}</ModalLayer>}
        {children}
      </>
    </ModalContext.Provider>
  );
};

ModalProvider.displayName = 'ModalProvider';

export const useModalContext = (): IModalContext => React.useContext(ModalContext);

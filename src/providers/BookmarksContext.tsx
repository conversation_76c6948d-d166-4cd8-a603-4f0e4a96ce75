import { logError } from '@errors/errorLogging';
import { SsoRequestUnauthorizedError } from '@errors/SsoRequestUnauthorizedError';
import { useErrors } from '@errors/useErrors';
import { addToFavourites, removeFromFavourites } from '@features/usercontent/userContentData';
import { isCollectionRoute } from '@routes/route';
import { Tracking } from '@tracking/Tracking';
import { ControlGroupName, ControlGroupType } from '@tracking/types';
import { insertIf } from '@util/array';
import { compareBookmarks } from '@util/bookmarks';
import { isCollectionAndItemIdIsCollection } from '@util/isCollection';
import { storeMyVideosPageUpdate } from '@util/NavHistory';
import React, { createContext, ReactNode, useCallback, useState } from 'react';

import { Bookmark } from '../types';

interface IBookmarksProviderProperties {
  children: ReactNode;
}

interface IBookmarksContextState {
  bookmarks?: Bookmark[];
  bookmarksTimestamp: number;
  bookmarksZoneIndex: number;
  setBookmarksZoneIndex: (index: number) => void;
  initBookmarks: (value: Bookmark[]) => void;
  updateExistingBookmarks: (value: Bookmark[]) => void;
  addBookmark: (teaser: Bookmark) => void;
  removeBookmark: (value: Bookmark) => void;
  isBookmarked: (value: Bookmark) => boolean;
  resetBookmarksTimestamp: () => void;
  addNextBookmarksPage: (nextPageBookmarks: Bookmark[]) => void;
}

const defaultContext = {
  bookmarks: undefined,
  bookmarksTimestamp: -1,
  bookmarksZoneIndex: -1,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  setBookmarksZoneIndex: (index: number) => false,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  initBookmarks: (value: Bookmark[]) => false,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  updateExistingBookmarks: (value: Bookmark[]) => false,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  addBookmark: (teaser: Bookmark) => false,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  removeBookmark: (value: Bookmark) => false,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  isBookmarked: (value: Bookmark) => false,
  resetBookmarksTimestamp: () => false,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  addNextBookmarksPage: (nextPageBookmarks: Bookmark[]) => false,
};

export const BookmarksContext = createContext<IBookmarksContextState>(defaultContext);

export const BookmarksContextProvider: React.FC<IBookmarksProviderProperties> = ({ children }) => {
  const [bookmarks, setBookmarks] = useState<Bookmark[]>();
  const [bookmarksTimestamp, setBookmarksTimestamp] = useState<number>(-1);
  const [bookmarksZoneIndex, setBookmarksZoneIndex] = useState<number>(-1);
  const { setError } = useErrors();

  const resetTimestamp = useCallback(() => {
    setBookmarksTimestamp(-1);
  }, []);

  const updateTimestamp = useCallback(() => {
    setBookmarksTimestamp(new Date().getTime());
  }, []);

  const initBookmarks = setBookmarks;

  /**
   * Updates the following fields of existing bookmarks:
   * - duration
   *
   * Some fields need to be updated in case the sso response has data that was not available at the time when a teaser was bookmarked
   */
  const updateExistingBookmarks = useCallback(
    (ssoBookmarks: Bookmark[]) => {
      ssoBookmarks.forEach((ssoBookmark) => {
        const existingBookmarkToUpdate = bookmarks?.find(
          (existingBookmark) => existingBookmark.program_id === ssoBookmark.program_id,
        );
        if (existingBookmarkToUpdate && !existingBookmarkToUpdate.duration) {
          existingBookmarkToUpdate.duration = ssoBookmark.duration;
        }
      });
    },
    [bookmarks],
  );

  const addBookmark = useCallback(
    (teaser: Bookmark) => {
      const currentBookmarks = bookmarks || [];

      // id can refer to a COLLECTION or a SHOW
      const id = isCollectionAndItemIdIsCollection(teaser) ? teaser.item_id : teaser.program_id;

      addToFavourites(id)
        .then(() => {
          Tracking.trackTeaserClick(teaser, ControlGroupType.TEASER_ACTIONS, ControlGroupName.ADD_TO_FAVORITES);

          const clone = { ...teaser };
          delete clone.focusKey;
          delete clone.isFocused;
          delete clone.isActive;
          delete clone.isDisabled;
          delete clone.playable;
          clone.showItemTitle = true;
          clone.image = clone.landscapeImage as string;
          clone.imageWidth = clone.landscapeImageWidth;
          clone.imageHeight = clone.landscapeImageHeight;

          if (isCollectionRoute(clone.program_id)) {
            // no duration for collections
            // we need to remove duration in case it's a collection trailer
            clone.duration = '';
          }

          const updatedBookmarks = [clone, ...currentBookmarks];

          setBookmarks(updatedBookmarks);
          updateTimestamp();
        })
        .catch((error) => {
          switch (true) {
            case error instanceof SsoRequestUnauthorizedError:
              setError(error);
              break;
            default:
              logError(error, 'WARNING');
          }
        });
    },
    [bookmarks, setError, updateTimestamp],
  );

  const removeBookmark = useCallback(
    (value: Bookmark) => {
      storeMyVideosPageUpdate();

      const currentBookmarks = bookmarks || [];
      const bookmarkToRemove = currentBookmarks?.find((bookmark) => compareBookmarks(bookmark, value));
      if (bookmarkToRemove) {
        const bookmarkedRemovedAtIndex = currentBookmarks?.findIndex((bookmark) =>
          compareBookmarks(bookmark, bookmarkToRemove),
        );
        removeFromFavourites(bookmarkToRemove.program_id)
          .then(() => {
            Tracking.trackTeaserClick(
              bookmarkToRemove,
              ControlGroupType.TEASER_ACTIONS,
              ControlGroupName.REMOVE_FROM_FAVORITES,
            );

            const updatedBookmarks = [...currentBookmarks];
            if (bookmarkedRemovedAtIndex != null) {
              updatedBookmarks.splice(bookmarkedRemovedAtIndex, 1);
            }
            setBookmarks(updatedBookmarks);
          })
          .catch((error) => {
            switch (true) {
              case error instanceof SsoRequestUnauthorizedError:
                setError(error);
                break;
              default:
                logError(error, 'WARNING');
            }
          });

        updateTimestamp();
      }
    },
    [bookmarks, setError, updateTimestamp],
  );

  const isBookmarked = useCallback(
    (value: Bookmark) => bookmarks?.some((bookmark) => compareBookmarks(bookmark, value)) || false,
    [bookmarks],
  );

  /**
   * Used in pagination to add bookmarks from next pages
   */
  const addNextBookmarksPage = useCallback(
    (nextPageBookmarks: Bookmark[]) => {
      let bookmarksToAdd: Bookmark[] = [];
      nextPageBookmarks.forEach(
        (nextBookmark) =>
          (bookmarksToAdd = [...bookmarksToAdd, ...insertIf(!isBookmarked(nextBookmark), nextBookmark)]),
      );
      if (bookmarksToAdd.length) {
        initBookmarks([...(bookmarks as Bookmark[]), ...bookmarksToAdd]);
      }
    },
    [bookmarks, initBookmarks, isBookmarked],
  );

  return (
    <BookmarksContext.Provider
      value={{
        bookmarks,
        bookmarksTimestamp,
        bookmarksZoneIndex,
        setBookmarksZoneIndex,
        initBookmarks,
        updateExistingBookmarks,
        addBookmark,
        removeBookmark,
        isBookmarked,
        resetBookmarksTimestamp: resetTimestamp,
        addNextBookmarksPage,
      }}
    >
      {children}
    </BookmarksContext.Provider>
  );
};

export default BookmarksContextProvider;

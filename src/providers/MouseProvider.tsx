import EventBus from '@util/EventBus';
import React, { createContext, ReactNode, SetStateAction, useEffect, useState } from 'react';
import { config } from 'target';

import { EVENTS } from '../constants';

interface IMouseContextState {
  isMouseActive: boolean;
}

const defaultContext = {
  isMouseActive: false,
};

export const MouseContext = createContext<IMouseContextState>(defaultContext);

interface CursorStateChangeEvent extends Event {
  detail: {
    visibility: boolean;
  };
}

function onCursorStateChange(event: CursorStateChangeEvent, setIsMouseActive: React.Dispatch<SetStateAction<boolean>>) {
  setIsMouseActive(event.detail && event.detail.visibility);
}

function handleMousemove(setIsMouseActive: React.Dispatch<React.SetStateAction<boolean>>) {
  setIsMouseActive(true);
}

function handleMousemoveOnce(setIsMouseActive: React.Dispatch<React.SetStateAction<boolean>>) {
  document.addEventListener('mousemove', () => handleMousemove(setIsMouseActive), { once: true });
}

function MouseProvider({ children }: { children: ReactNode }) {
  const [isMouseActive, setIsMouseActive] = useState(false);

  useEffect(() => {
    if (!config.hasMouseSupport) {
      return;
    }

    const handleCursorStateChange = (event: Event) => {
      onCursorStateChange(event as CursorStateChangeEvent, setIsMouseActive);
    };

    /**
     * `cursorStateChange` is an event specific to webOS. Although this module
     * could potentially be more generic, it makes sense to leave it here instead
     * of abstracting it out to a device-specific module because mouse support
     * is only available for webOS.
     *
     * If mouse support is expanded to other platforms in the future, it might
     * be worth considering refactoring the module to be more device-agnostic.
     */
    document.addEventListener('cursorStateChange', handleCursorStateChange);

    /**
     * Note that `cursorStateChange` only fires when there is an actual change between
     * states as the name suggests. This means at application startup, if the mouse is active
     * in the first instance no `cursorStateChange` has occurred and the app will
     * not know it is in mouse mode at this point. Thus, using the mouse at startup will not
     * work as expected, we have to use arrow keys to invoke a cursorStateChange, then use the
     * mouse again to invoke another cursorStateChange in order to set isMouseActive to true.
     *
     * This is also the case when the application is running in the background and has been
     * moved to the foreground.
     *
     * As a result we need to listen once for `mousemove` and set `isMouseActive` to `true`,
     * this will allow us to be in the correct state in the first instance if the mouse is
     * active when the application starts or is moved to foreground.
     */
    handleMousemoveOnce(setIsMouseActive);

    return () => {
      document.removeEventListener('cursorStateChange', handleCursorStateChange);
    };
  }, []);

  /**
   * When the app is moved to the background a `cursorStateChange` is fired causing
   * `isMouseActive` to be set to `true`.
   *
   * NOTE that the `cursorStateChange` event fires before the `visibilitychange` event
   * which allows us to determine if the app is visible or not (foreground/background).
   *
   * This means if we move the app to the background using key navigation, then bring
   * to the foreground again using key navigation, `isMouseActive` is still true and
   * there will be no focused item in the UI.
   *
   * Thus when the app is sent to the background we set `isMouseActive` to `false`
   *
   */
  EventBus.on(EVENTS.APP_TO_BACKGROUND, () => setIsMouseActive(false));

  EventBus.on(EVENTS.APP_TO_FOREGROUND, () => handleMousemoveOnce(setIsMouseActive));

  return <MouseContext.Provider value={{ isMouseActive }}>{children}</MouseContext.Provider>;
}

export default MouseProvider;

import { PAGE_IDS } from '@constants';
import { UseFocusableResult } from '@noriginmedia/norigin-spatial-navigation';
import { createContext, ReactNode } from 'react';

import {
  myArteTeaserFocusGuard,
  searchTeaserFocusGuard,
  teaserFocusGuard,
  TeaserFocusGuardOptions,
  tvGuideTeaserFocusGuard,
} from '../focus';

interface IPageProviderProperties {
  pageId: string;
  children: ReactNode;
}

interface IPageContextState {
  listFocusedOnMount: boolean;
  minListLengthForMoreLink: number | null;
  teaserFocusGuard: (
    direction: string,
    zoneIndex: number,
    teaserIndex: number,
    setFocus: UseFocusableResult['setFocus'],
    options: TeaserFocusGuardOptions,
  ) => boolean;
}

const defaultContext = {
  listFocusedOnMount: false,
  minListLengthForMoreLink: 0,
  teaserFocusGuard: teaserFocusGuard,
};

export const PageContext = createContext<IPageContextState>(defaultContext);

const getFocusGuard = (pageId: string): IPageContextState['teaserFocusGuard'] => {
  switch (pageId) {
    case PAGE_IDS.SEARCH_HOME:
      return searchTeaserFocusGuard;
    case PAGE_IDS.TV_GUIDE:
      return tvGuideTeaserFocusGuard;
    case PAGE_IDS.MYARTE:
      return myArteTeaserFocusGuard;
    default:
      return teaserFocusGuard;
  }
};

const MIN_LIST_LENGTH_FOR_MORE_LINK = 20;

/**
 * The initial purpose of the page context is to make nested components act differently based on a page type
 */
export const PageContextProvider: React.FC<IPageProviderProperties> = ({ children, pageId }) => {
  const focusGuard = getFocusGuard(pageId);
  const listFocusedOnMount = pageId === PAGE_IDS.SEARCH_HOME || pageId === PAGE_IDS.MYARTE ? false : true;
  const minListLengthForMoreLink =
    pageId === PAGE_IDS.MY_VIDEOS || pageId === PAGE_IDS.HOME ? MIN_LIST_LENGTH_FOR_MORE_LINK : null;
  return (
    <PageContext.Provider value={{ teaserFocusGuard: focusGuard, listFocusedOnMount, minListLengthForMoreLink }}>
      {children}
    </PageContext.Provider>
  );
};

export default PageContextProvider;

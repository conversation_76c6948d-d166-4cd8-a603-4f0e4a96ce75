import { FeatureFlagsApiResponse, UserData } from '@apptypes/SSOResponse';
import { Me } from '@apptypes/SSOResponse';
import { getMe } from '@features/usercontent/userContentData';
import { Tracking } from '@tracking/Tracking';
import { isLoggedIn } from '@util/cookies';
import React, { createContext, ReactNode, useCallback, useEffect } from 'react';

interface IGlobalProviderProperties {
  children: ReactNode;
}

interface IGlobalContextState {
  showSplashScreen: boolean;
  setShowSplashScreen: (show: boolean) => void;
  setUserData: (userData: UserData | Me | undefined) => void;
  userData: UserData | Me | undefined;
  featureFlags: FeatureFlagsApiResponse | undefined;
  setFeatureFlags: (featureFlags: FeatureFlagsApiResponse | undefined) => void;
  fetchUserData: () => Promise<void>;
}

const defaultContext = {
  showSplashScreen: true,
  setShowSplashScreen: () => {},
  fetchUserData: async () => {},
};

export const GlobalContext = createContext<IGlobalContextState>(defaultContext);

export const GlobalContextProvider: React.FC<IGlobalProviderProperties> = ({ children }) => {
  const [showSplashScreen, setShowSplashScreen] = React.useState(true);
  const [userData, setUserData] = React.useState<UserData | Me | undefined>(undefined);
  const [featureFlags, setFeatureFlags] = React.useState<FeatureFlagsApiResponse | undefined>(undefined);

  useEffect(
    function hideSplashScreen() {
      if (!showSplashScreen) {
        const element = document.getElementById('splash');
        if (element) element.remove();
      }
    },
    [showSplashScreen],
  );

  const fetchUserData = useCallback(async () => {
    const userData = isLoggedIn() ? await getMe() : undefined;
    setUserData(userData);
    Tracking.setupUserData(userData);
  }, [setUserData]);

  useEffect(() => {
    fetchUserData();
  }, [setUserData, fetchUserData]);

  return (
    <GlobalContext.Provider
      value={{
        showSplashScreen,
        setShowSplashScreen,
        userData,
        setUserData,
        featureFlags,
        setFeatureFlags,
        fetchUserData,
      }}
    >
      {children}
    </GlobalContext.Provider>
  );
};

export default GlobalContextProvider;

import { FeatureFlagsApiResponse } from '@apptypes/SSOResponse';
import { getBaseUrl } from '@routes/utils';

const FEATURE_FLAG = {
  SMARTTV_FEATURE_FLAGS_LIVESTREAM: 'SMARTTV_FEATURE_FLAGS_LIVESTREAM',
  SMARTTV_FEATURE_FLAGS_MY_ACCOUNT: 'SMARTTV_FEATURE_FLAGS_MY_ACCOUNT',
};

const getFeatureFlag = (featureFlags: FeatureFlagsApiResponse | undefined, feature: string) => {
  if (!featureFlags) return false;
  const baseUrl = getBaseUrl('middleware');

  if (!featureFlags['feature-flags']) return false;

  if (baseUrl.includes('smarttv-dev')) {
    return featureFlags['feature-flags'][feature]?.DEV || false;
  }

  if (baseUrl.includes('smarttv-preprod')) {
    return featureFlags['feature-flags'][feature]?.PREPROD || false;
  }

  if (baseUrl.includes('smarttv.arte.tv')) {
    return featureFlags['feature-flags'][feature]?.PROD || false;
  }
};

export { getFeatureFlag, FEATURE_FLAG };

import { Target } from '@constants';
import {
  hasPanasonicTvQueryParam as hasPanasonicTvQueryParam,
  hasTitanosQueryParam,
  hasVidaaTvQueryParam,
  isMagentaTv as hasMagentaTvQueryParam,
  isRoku as hasRokuQueryParam,
} from '@features/queryParams/queryParamsLookup';

// Every subset (e.g hbbtv, webos) can have additional flavours (platforms).
// A good example is the html5 target which has multiple platforms e.g magenta_tv or roku.
// Some platforms are determined by sniffing the UA and some by doing a query string lookup.
// Add a function below if you need to check for a specific platform.

const PLATFORMS = {
  PANASONIC: 'panasonic',
};

const currentUserAgent = window.navigator?.userAgent.toLowerCase();

const isHosted = () => window.location.protocol.startsWith('http');

const isMagentaTv = () => hasMagentaTvQueryParam();

const isPanasonic = () => hasPanasonicTvQueryParam();

const isRoku = () => hasRokuQueryParam();

const isTitanos = () => hasTitanosQueryParam();

const isVidaa = () => hasVidaaTvQueryParam();

const isOrange = () => process.env.TARGET === Target.ORANGE;

export { PLATFORMS, isHosted, isMagentaTv, isPanasonic, isRoku, isTitanos, isVidaa, currentUserAgent, isOrange };

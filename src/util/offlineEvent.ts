import { EVENTS } from '../constants';
import EventBus from './EventBus';

export function emitOfflineEvent(isOffline: boolean) {
  EventBus.emit(EVENTS.OFFLINE, { [EVENTS.OFFLINE]: isOffline });
}

export function handleOffline() {
  emitOfflineEvent(true);
}

export function handleOnline() {
  emitOfflineEvent(false);
}

export async function isOnlineBackupCheck(): Promise<boolean> {
  return new Promise((resolve) => {
    const img = new Image();

    img.onload = () => {
      resolve(true);
    };

    img.onerror = () => {
      resolve(false);
    };

    const cacheBuster = `?rand=${Math.random().toString(36).substring(7)}`;
    img.src = `${process.env.REACT_ROUTER_BASE_PATH}gifnocache/online_offline_check.gif${cacheBuster}`;
  });
}

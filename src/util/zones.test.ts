import { describe } from 'vitest';

import { zonesWithFavourite, zonesWithFavouriteAndTeaserList, zonesWithoutFavourite } from '../../test-data/zones';
import { Zone } from '../types';
import { getTeasersFromFavouritesZone, hasFavouritesZone } from './zones';

describe('hasFavouritesZone', () => {
  it('should return true if a favourites zone exists', () => {
    const actual = hasFavouritesZone(zonesWithFavourite as Zone[]);
    const expected = true;
    expect(actual).toEqual(expected);
  });

  it('should return false if a favourites zone does not exist', () => {
    const actual = hasFavouritesZone(zonesWithoutFavourite as Zone[]);
    const expected = false;
    expect(actual).toEqual(expected);
  });
});

describe('getTeasersFromFavouritesZone', () => {
  it('should get teasers array from favourites zone if they exist', () => {
    const actual = getTeasersFromFavouritesZone(zonesWithFavouriteAndTeaserList as Zone[]);
    // @ts-expect-error possibly undefined
    expect(actual.length).toBeGreaterThan(0);
  });

  it('should return undefined if no teaser list', () => {
    const actual = getTeasersFromFavouritesZone(zonesWithFavourite as Zone[]);
    const expected = undefined;
    expect(actual).toEqual(expected);
  });
});

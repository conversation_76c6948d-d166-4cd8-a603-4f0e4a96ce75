import { getSearchParams } from 'target';

import { protocol } from './protocol';

const SSO_API_PATH = '/api/sso/v3';

const SSO_DOMAIN = {
  PROD: 'smarttv.arte.tv',
  PREPROD: 'smarttv-preprod.arte.tv',
} as const;

const SSO_QUERY_PARAM = {
  KEY: 'sso',
  VALUE: 'preprod',
} as const;

function shouldUseSsoPreprod() {
  return getSearchParams?.().get(SSO_QUERY_PARAM.KEY) === SSO_QUERY_PARAM.VALUE;
}

function getDomain(preprod: boolean) {
  return preprod ? SSO_DOMAIN.PREPROD : SSO_DOMAIN.PROD;
}

export function getSsoUrl(path: string): string {
  const domain = getDomain(shouldUseSsoPreprod());

  return `${protocol}${domain}${SSO_API_PATH}${path}`;
}

import { Bookmark } from '@apptypes/bookmarks';
import { isCollection } from '@util/isCollection';

export function compareBookmarks(stored: Bookmark, challenge: Bookmark) {
  if (isCollection(stored) !== isCollection(challenge)) return false;

  if (isCollection(stored) && isCollection(challenge)) {
    return (
      stored?.program_id === challenge?.program_id ||
      stored?.program_id === challenge?.item_id ||
      stored?.item_id === challenge?.program_id
    );
  }

  return stored?.program_id === challenge?.program_id;
}

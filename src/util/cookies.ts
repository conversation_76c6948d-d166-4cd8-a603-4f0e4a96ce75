import { generateSHA256 } from '@util/generateSHA256';
import { v4 as uuidv4 } from 'uuid';

import { eraseAppData, readAppData, writeAppData } from '../features/appdata/appdata';
import EventBus from './EventBus';
import { VideoQuality } from './videoQuality';

export const COOKIE_PREFIX = 'arte';
export const COOKIE_USER_VIEW_HISTORY = `${COOKIE_PREFIX}UserViewHistory`;
export const COOKIE_ANON_TOKEN = `${COOKIE_PREFIX}AnonymousToken`;
export const COOKIE_USER_TOKEN = `${COOKIE_PREFIX}UserToken`;
export const COOKIE_SHA = `${COOKIE_PREFIX}Sha`;
export const COOKIE_USER_CONSENT = `${COOKIE_PREFIX}UserConsent`;
export const COOKIE_VIDEO_QUALITY = `${COOKIE_PREFIX}UserQuality`;
export const COOKIE_USER_LANGUAGE = `${COOKIE_PREFIX}Lang`;
export const COOKIE_PERSONALISATION = `${COOKIE_PREFIX}Personalisation`;
export const COOKIE_TOKENS_TRANSFERRED = `${COOKIE_PREFIX}TokensTransferred`;

export const COOKIE_UUID = `${COOKIE_PREFIX}Uuid`;
export const COOKIE_SUBTITLE_PREFERENCE = `${COOKIE_PREFIX}SubtitlePreference`;

const COOKIE_USER_CONSENT_TECHNICAL = `${COOKIE_PREFIX}UserConsentTechnical`;
const COOKIE_USER_NOTIFIED = `${COOKIE_PREFIX}UserNotified`;
const COOKIE_AUTOPLAY = `${COOKIE_PREFIX}Autoplay`;
const COOKIE_EXPIRY_SIX_MONTHS = 183; // approx 6 months in days
const COOKIE_EXPIRY_1_MONTH = 30; // approx 1 month in days

// Legacy app cookies
export const COOKIE_LEGACY_VIDEO_QUALITY = 'userQuality';
export const COOKIE_LEGACY_USER_LANGUAGE = 'userLang';
export const COOKIE_LEGACY_ANON_TOKEN = 'anonymousTokenv3';
export const COOKIE_LEGACY_USER_TOKEN = 'userTokenv3';

// text settings set in cookies by dash directly
export const DASHJS_TEXT_SETTINGS_COOKIE = 'dashjs_text_settings';

/**
 * Personalisation features are allowed for anonymous users by default
 * */
export function setPersonalisationByDefault() {
  if (readAppData(COOKIE_PERSONALISATION) === null || undefined) {
    acceptPersonalisationCookies();
  }
}

export function acceptCookies() {
  writeAppData(COOKIE_USER_CONSENT, true, COOKIE_EXPIRY_SIX_MONTHS);
  EventBus.emit(COOKIE_USER_CONSENT, { cookieValue: hasCookieConsent() });
}

export function rejectCookies() {
  writeAppData(COOKIE_USER_CONSENT, false, COOKIE_EXPIRY_SIX_MONTHS);
  EventBus.emit(COOKIE_USER_CONSENT, { cookieValue: hasCookieConsent() });
}

export function hasCookieConsent() {
  return readAppData(COOKIE_USER_CONSENT) === 'true';
}

export function acceptTechnicalCookies() {
  writeAppData(COOKIE_USER_CONSENT_TECHNICAL, true, COOKIE_EXPIRY_SIX_MONTHS);
}

export function rejectTechnicalCookies() {
  writeAppData(COOKIE_USER_CONSENT_TECHNICAL, false, COOKIE_EXPIRY_SIX_MONTHS);
}

export function hasTechnicalCookieConsent() {
  return readAppData(COOKIE_USER_CONSENT_TECHNICAL) === 'true';
}

export function acceptPersonalisationCookies() {
  writeAppData(COOKIE_PERSONALISATION, true, COOKIE_EXPIRY_SIX_MONTHS);
  EventBus.emit(COOKIE_PERSONALISATION, { cookieValue: hasPersonalisationCookieConsent() });
}

export function rejectPersonalisationCookies() {
  writeAppData(COOKIE_PERSONALISATION, false, COOKIE_EXPIRY_SIX_MONTHS);
  EventBus.emit(COOKIE_PERSONALISATION, { cookieValue: false });
}

export function hasPersonalisationCookieConsent() {
  return readAppData(COOKIE_PERSONALISATION) === 'true';
}

export function hasAnonymousToken() {
  return readAppData(COOKIE_ANON_TOKEN) !== null;
}

export function setAnonymousToken(value: string) {
  writeAppData(COOKIE_ANON_TOKEN, value);
}

export function hasUserToken() {
  return readAppData(COOKIE_USER_TOKEN) !== null;
}

export function setUserToken(value: string) {
  writeAppData(COOKIE_USER_TOKEN, value);
  EventBus.emit(COOKIE_USER_TOKEN, { cookieValue: hasUserToken() });
}

export function cookiesNotified() {
  return readAppData(COOKIE_USER_NOTIFIED);
}

export function setCookiesNotified() {
  writeAppData(COOKIE_USER_NOTIFIED, true, COOKIE_EXPIRY_SIX_MONTHS);
}

export function autoplayEnabled(): boolean {
  return readAppData(COOKIE_AUTOPLAY) === 'true';
}

export function setAutoplayEnabledCookie(value: boolean) {
  writeAppData(COOKIE_AUTOPLAY, value, COOKIE_EXPIRY_SIX_MONTHS);
}

export function setVideoQualityCookie(value: VideoQuality) {
  writeAppData(COOKIE_VIDEO_QUALITY, value, COOKIE_EXPIRY_SIX_MONTHS);
}

export function getVideoQualityCookie() {
  return readAppData(COOKIE_VIDEO_QUALITY);
}

export function hasLanguage() {
  return !!readAppData(COOKIE_USER_LANGUAGE);
}

export function getLanguage() {
  return readAppData(COOKIE_USER_LANGUAGE);
}

export function setLanguage(value: string) {
  // strip the region if it exists e.g. `de-DE` becomes `de`
  const lang = value.split('-')[0];
  writeAppData(COOKIE_USER_LANGUAGE, lang);
}

export function isLoggedIn() {
  return !!readAppData(COOKIE_USER_TOKEN);
}

export async function getSHA() {
  let sha = readAppData(COOKIE_SHA);
  if (sha) {
    return sha;
  } else {
    sha = await generateSHA256(readAppData(COOKIE_USER_TOKEN) || '');
  }
  return sha;
}

export function getSubtitlePreference() {
  return readAppData(COOKIE_SUBTITLE_PREFERENCE);
}

export function setSubtitlePreference(value: string) {
  writeAppData(COOKIE_SUBTITLE_PREFERENCE, value, COOKIE_EXPIRY_SIX_MONTHS);
}

export function resetSubtitlePreference() {
  eraseAppData(COOKIE_SUBTITLE_PREFERENCE);
}

export function areTokensTransferred() {
  return readAppData(COOKIE_TOKENS_TRANSFERRED);
}

export function setTokensTransferred() {
  const expiryDays = 20 * 365; // 20 years
  writeAppData(COOKIE_TOKENS_TRANSFERRED, true, expiryDays);
}

// Legacy app cookies
export function getLegacyVideoQualityCookie() {
  return readAppData(COOKIE_LEGACY_VIDEO_QUALITY);
}

export function deleteLegacyVideoQualityCookie() {
  eraseAppData(COOKIE_LEGACY_VIDEO_QUALITY);
}

export function getLegacyLanguage() {
  return readAppData(COOKIE_LEGACY_USER_LANGUAGE);
}

export function deleteLegacyLanguage() {
  return eraseAppData(COOKIE_LEGACY_USER_LANGUAGE);
}

export function getLegacyAnonToken() {
  return readAppData(COOKIE_LEGACY_ANON_TOKEN);
}

export function deleteLegacyAnonToken() {
  return eraseAppData(COOKIE_LEGACY_ANON_TOKEN);
}

export function getLegacyUserToken() {
  return readAppData(COOKIE_LEGACY_USER_TOKEN);
}

export function deleteLegacyUserToken() {
  return eraseAppData(COOKIE_LEGACY_USER_TOKEN);
}

export function setUserViewHistory(value: string) {
  writeAppData(COOKIE_USER_VIEW_HISTORY, value, COOKIE_EXPIRY_SIX_MONTHS);
}

export function getUserViewHistory() {
  return readAppData(COOKIE_USER_VIEW_HISTORY);
}

export function getUuid() {
  const uuid = readAppData(COOKIE_UUID);
  if (!uuid) {
    const newUuid = uuidv4();
    writeAppData(COOKIE_UUID, newUuid, COOKIE_EXPIRY_1_MONTH);
    return newUuid;
  }

  return uuid;
}

import { Environment } from '@constants';

const getEnvFromURL = (): Environment => {
  const hostname = window.location.hostname;

  let environment: Environment = 'DEV';

  if (hostname.includes('smarttv-dev')) {
    environment = 'DEV';
  } else if (hostname.includes('smarttv-preprod')) {
    environment = 'PREPROD';
  } else if (hostname === 'smarttv.arte.tv') {
    environment = 'PROD';
  }

  return environment;
};

export default getEnvFromURL;

import { ITeaserProperties } from '../types';
import { isPartnerKind } from './isPartnerKind';

describe('isPartnerKind', () => {
  it('should return false for no properties', () => {
    // @ts-expect-error no args provided test
    const actual = isPartnerKind();
    const expected = false;
    expect(actual).toEqual(expected);
  });

  it('should return false for no `kind`', () => {
    const properties = {};
    const actual = isPartnerKind(properties as ITeaserProperties);
    const expected = false;
    expect(actual).toEqual(expected);
  });

  it('should return false for no `code`', () => {
    const properties = { kind: {} };
    const actual = isPartnerKind(properties as ITeaserProperties);
    const expected = false;
    expect(actual).toEqual(expected);
  });

  it('should return false when `code` is `undefined`', () => {
    const properties = { kind: { code: undefined } };
    const actual = isPartnerKind(properties as ITeaserProperties);
    const expected = false;
    expect(actual).toEqual(expected);
  });

  it('should return false when `code` is `null`', () => {
    const properties = { kind: { code: null } };
    // @ts-expect-error null test
    const actual = isPartnerKind(properties as ITeaserProperties);
    const expected = false;
    expect(actual).toEqual(expected);
  });

  it('should return false when `code` is not `PARTNER`', () => {
    const properties = { kind: { code: 'TEST' } };
    const actual = isPartnerKind(properties as ITeaserProperties);
    const expected = false;
    expect(actual).toEqual(expected);
  });

  it('should return true when `code` is `PARTNER`', () => {
    const properties = { kind: { code: 'PARTNER' } };
    const actual = isPartnerKind(properties as ITeaserProperties);
    const expected = true;
    expect(actual).toEqual(expected);
  });
});

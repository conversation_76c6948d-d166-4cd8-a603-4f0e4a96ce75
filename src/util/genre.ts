import { Genre, ITeaserProperties } from '@apptypes/ITeaserProperties';

const shouldDisplayGenreLabel = (theme: string | undefined) => theme !== undefined && theme === 'genre';

const getGenreLabel = (properties: ITeaserProperties) => getGenreName(properties?.genre);

const getGenreName = (pGenre: Genre | undefined) => {
  if (pGenre && pGenre.genreName) {
    return pGenre.genreName;
  }
  return '';
};

export { shouldDisplayGenreLabel, getGenreLabel, getGenreName };

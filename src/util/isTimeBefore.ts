import { ITvGuideTeaserProperties } from '@apptypes/ITvGuideTeaserProperties';
import { beginsAfterToday } from '@components/Teaser/getAvailabilityInformation';

export const isTimeBefore = (teaser: ITvGuideTeaserProperties): boolean => {
  const [inputHour, inputMinute] = teaser.start.split(':').map(Number);
  const inputTime = new Date();
  inputTime.setHours(inputHour, inputMinute, 0, 0);

  const now = new Date();
  const currentHour = now.getHours();
  const currentMinute = now.getMinutes();
  if (beginsAfterToday(teaser)) {
    return true;
  }
  const isBefore =
    currentHour < inputTime.getHours() ||
    (currentHour === inputTime.getHours() && currentMinute <= inputTime.getMinutes());

  return isBefore;
};

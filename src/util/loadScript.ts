const loadScript = (url: string, async = false) =>
  new Promise((resolve, reject) => {
    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.src = url;
    script.async = async;

    script.addEventListener('load', () => {
      console.log(`script loaded: ${url}`);
      resolve();
    });

    script.addEventListener('error', () => {
      reject(new Error('Error loading script'));
    });

    const scriptsTag = document.getElementsByTagName('script')[0];
    scriptsTag.parentNode.insertBefore(script, scriptsTag);
  });

export { loadScript };

import { logError } from '../errors';

const EventTeaserTexts = {
  TITLE: 'EventTeaserTitle',
  SUBTITLE: 'EventTeaserSubtitle',
} as const;

const TopTeaserTexts = {
  TITLE: 'TopTeaserTitle',
  SUBTITLE: 'TopTeaserSubtitle',
  DESCRIPTION: 'TopTeaserDescription',
} as const;

const LandscapeTeaserTexts = {
  TITLE: 'LandscapeTeaserTitle',
  SUBTITLE: 'LandscapeTeaserSubtitle',
} as const;

const LandscapeBigTeaserTexts = {
  TITLE: 'LandscapeBigTeaserTitle',
  SUBTITLE: 'LandscapeBigTeaserSubtitle',
} as const;

type EventTeaserTextType = (typeof EventTeaserTexts)[keyof typeof EventTeaserTexts];
type TopTeaserTextType = (typeof TopTeaserTexts)[keyof typeof TopTeaserTexts];
type LandscapeTeaserTextType = (typeof LandscapeTeaserTexts)[keyof typeof LandscapeTeaserTexts];
type LandscapeBigTeaserTextType = (typeof LandscapeBigTeaserTexts)[keyof typeof LandscapeBigTeaserTexts];

export type TextType = EventTeaserTextType | TopTeaserTextType | LandscapeTeaserTextType | LandscapeBigTeaserTextType;

const charactersPerLine = new Map<TextType, number>([
  [EventTeaserTexts.TITLE, 22],
  [EventTeaserTexts.SUBTITLE, 35],
  [TopTeaserTexts.TITLE, 24],
  [TopTeaserTexts.SUBTITLE, 38],
  [TopTeaserTexts.DESCRIPTION, 61],
  [LandscapeTeaserTexts.TITLE, 26],
  [LandscapeTeaserTexts.SUBTITLE, 29],
  [LandscapeBigTeaserTexts.TITLE, 39],
  [LandscapeBigTeaserTexts.SUBTITLE, 42],
]);

function getNumberOfLines(text: string, type: TextType): number {
  if (!charactersPerLine.has(type)) {
    logError(new Error('missing text type ' + type), 'WARNING');
    return 0;
  }
  return Math.ceil(text?.length / (charactersPerLine.get(type) as number));
}

export { getNumberOfLines, EventTeaserTexts, TopTeaserTexts, LandscapeTeaserTexts, LandscapeBigTeaserTexts };

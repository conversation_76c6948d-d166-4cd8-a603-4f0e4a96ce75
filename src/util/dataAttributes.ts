import { ITeaserProperties } from '@apptypes/ITeaserProperties';
import { ITeaserResponse } from '@apptypes/ITeaserResponse';
import { hasAvailability, isLivestreamWeb } from '@components/Teaser/getAvailabilityInformation';

/**
 * returns the data-kind attribute value for a given teaser for e2e testing
 * note we only add this if it is VOD content so that the automated tests can
 * differentiate between VOD and LIVE content
 *
 * automated tests can locate VOD content in order to directly play video when
 * code is SHOW, or assert that when code is TV_SERIES we navigate to a
 * collection page for example
 *
 * Example
 * data-kind="code:TV_SERIES"
 *
 */
export function getDataKind(teaser: ITeaserResponse) {
  if (hasAvailability(teaser as ITeaserProperties) && isLivestreamWeb(teaser as ITeaserProperties)) return undefined;
  return teaser?.kind?.code ? `code:${teaser.kind.code}` : undefined;
}

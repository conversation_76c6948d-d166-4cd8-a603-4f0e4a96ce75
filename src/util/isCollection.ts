import { ITeaserProperties } from '@apptypes/ITeaserProperties';
import { ITeaserResponse } from '@apptypes/ITeaserResponse';
import { ITvGuideTeaserProperties } from '@apptypes/ITvGuideTeaserProperties';
import { ROUTE_PREFIX_COLLECTION } from '@routes/route';

const isCollection = (item: ITeaserResponse | ITvGuideTeaserProperties | ITeaserProperties): boolean =>
  (item.kind && item.kind.isCollection) || false;

const isCollectionAndItemIdIsCollection = (teaser: ITeaserProperties) =>
  isCollection(teaser) && teaser?.item_id?.startsWith(ROUTE_PREFIX_COLLECTION);

export { isCollection, isCollectionAndItemIdIsCollection };

/**
 * Replaces placeholders in a template string with values from an object.
 * Simple usage like this:
 * const template: string = "Livestream on {{date}} at {{hour}}";
 * const values: { [key: string]: string } = {
 *     date: "February 07, 1987",
 *     hour: "11:00"
 * };
 * const result: string = formatTString(template, values);
 * console.log(result); // Livestream on February 07, 1987 at 11:00
 *
 * const result: string = formatTString("Livestream on {{date}} at {{hour}}, venue: {{venue}}", {
 *     date: "09/12",
 *     hour: "16:30",
 *     venue: "Paris"
 * });
 *
 * console.log(result); // Outputs: "Livestream on 09/12 at 16:30, venue: Paris"
 *
 * @param template
 * @param values
 */
const formatTString = (template: string, replacements: { [placeholder: string]: string }): string => {
  return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
    return key in replacements ? replacements[key] : match;
  });
};

export { formatTString };

import { getSearchParams } from 'target';

export type ApiEnv = 'dev' | 'preprod' | 'prod';
const VALID_ENVS: Array<ApiEnv> = ['dev', 'preprod', 'prod'];

type ApiParam = 'tvmid' | 'playerapi';

const apiOverrides: Map<ApiParam, ApiEnv> = new Map();

export function overrideApiEnv() {
  const getApiEnvFromUrl = (param: ApiParam): ApiEnv => getSearchParams().get(param) as ApiEnv;

  const setEnv = (apiParam: ApiParam) => {
    const apiEnv = getApiEnvFromUrl(apiParam);
    if (VALID_ENVS.includes(apiEnv)) apiOverrides.set(apiParam, apiEnv);
  };

  setEnv('tvmid');
  setEnv('playerapi');
}

export function getOverriddenApiEnv(param: ApiParam) {
  return apiOverrides.get(param);
}

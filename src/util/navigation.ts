import { FocusDetails } from '@noriginmedia/norigin-spatial-navigation/dist/SpatialNavigation';

const focusDetails = {
  scroll: true,
};

type NavigateByDirection = (direction: string, focusDetails: FocusDetails) => void;

export function navigateByDirectionUp(navigateByDirection: NavigateByDirection) {
  navigateByDirection('up', focusDetails);
}

export function navigateByDirectionDown(navigateByDirection: NavigateByDirection) {
  navigateByDirection('down', focusDetails);
}

// We need to run onFocus callbacks through this wrapper so that we can check that it has been invoked
// for navigation via a key press or mousewheel event. Without this, the onFocus callback can be invoked
// via other states such as on mouse over, resulting in unintended behaviour where mouse over events
// can cause list/grid to scroll
export function handleOnFocus(focusDetails: FocusDetails, index: number, setIndex: (index: number) => void) {
  if (focusDetails.scroll || focusDetails.event?.type === 'keydown') {
    setIndex(index);
  }
}

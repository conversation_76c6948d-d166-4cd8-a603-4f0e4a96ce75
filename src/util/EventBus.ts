export interface EventData<T> {
  [key: string]: T;
}

type Callback = <T>(data: EventData<T>) => void;

const createEventBus = () => {
  const events: Record<string, Callback[]> = {};

  const on = (event: string, callback: Callback): void => {
    events[event] = events[event] ? [...events[event], callback] : [callback];
  };

  const off = (event: string, callback: Callback): void => {
    if (events[event]) {
      events[event] = events[event].filter((cb) => cb !== callback);
    }
  };

  const emit = <T>(event: string, data: EventData<T>): void => {
    if (events[event]) {
      events[event].forEach((callback) => callback(data));
    }
  };

  return {
    on,
    off,
    emit,
  };
};

const EventBus = createEventBus();

export default EventBus;

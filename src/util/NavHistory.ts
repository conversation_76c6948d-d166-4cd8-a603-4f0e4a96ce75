import { IPageResults } from '@apptypes/pagination';
import { PAGE_IDS, PageId } from '@constants';

import { IGenre, ISearchResponse, ITeaserResponse, Zone } from '../types';

type NavHistorySearchParams = {
  query: string;
  genre: IGenre;
};

type NavHistoryItem = {
  routeId: string;
  zones: Zone[];
  zoneId: string;
  verticalIndex: number;
  currentDay?: number;
  search?: NavHistorySearchParams;

  paginationCurrentPage?: number;
  paginationData?: IPageResults;

  gridFocusedItem?: number;
  horizontalDiff?: number;
  response?: object;

  exitKeyRight?: string;
  exitKeyLeft?: string;

  offset?: number;
  savedHorizontalPosition?: number;
};

const navHistory: Map<string, NavHistoryItem> = new Map();

const findZone = (item: NavHistoryItem, zoneId: string) => {
  if (!item?.zones) return -1;

  return item.zones.findIndex((zone) => zone.id === zoneId);
};

const resetNavHistory = () => {
  navHistory.clear();
};

const setSearchKeyboardNavExitKeyRight = (key: string) => {
  const targetItem = navHistory.get('SEARCH_HOME');
  if (targetItem) {
    targetItem.exitKeyRight = key;
  }
};

const setSearchKeyboardNavExitKeyLeft = (key: string) => {
  const targetItem = navHistory.get('SEARCH_HOME');
  if (targetItem) {
    targetItem.exitKeyLeft = key;
  }
};

const getSearchKeyboardNavExitKeyRight = () => {
  const targetItem = navHistory.get('SEARCH_HOME');
  return targetItem ? targetItem.exitKeyRight : undefined;
};

const getSearchKeyboardNavExitKeyLeft = () => {
  const targetItem = navHistory.get('SEARCH_HOME');
  return targetItem ? targetItem.exitKeyLeft : undefined;
};

const setGridFocusedItem = (pageId: string, pGridFocusedItem: number) => {
  const targetItem = navHistory.get(pageId);
  if (targetItem) {
    targetItem.gridFocusedItem = pGridFocusedItem;
  }
};

const setGridHorizontalDiff = (pageId: string, pHorizontalDiff: number) => {
  const targetItem = navHistory.get(pageId);
  if (targetItem) {
    targetItem.horizontalDiff = pHorizontalDiff;
  }
};

const getGridHorizontalDiff = (pageId: string) => {
  const targetItem = navHistory.get(pageId);
  return targetItem ? targetItem.horizontalDiff : null;
};

const getGridFocusedItem = (pageId: string) => {
  const targetItem = navHistory.get(pageId);
  return targetItem ? targetItem.gridFocusedItem : null;
};

const storePaginationFullResult = (query: string, genre: IGenre, response: ISearchResponse | void, pageId: string) => {
  const targetItem = navHistory.get(pageId);
  if (targetItem) {
    targetItem.search = {
      query,
      genre,
    };
    targetItem.response = response as ISearchResponse;
    targetItem.paginationData = <IPageResults>{ data: [] };
    targetItem.paginationCurrentPage = 1;
    targetItem.gridFocusedItem = 0;
  }
};

const storePaginationResult = (response: IPageResults, pageId: string) => {
  const targetItem = navHistory.get(pageId);
  if (targetItem) {
    targetItem.paginationData = response;
  }
};

const storePaginationCurrentPage = (paginationCurrentPage: number, pageId: string) => {
  const targetItem = navHistory.get(pageId);
  if (targetItem) {
    targetItem.paginationCurrentPage = paginationCurrentPage + 1;
  }
};

const getStoredPaginationResult = (pageId: string) => {
  const targetItem = navHistory.get(pageId);
  return targetItem || null;
};

const resetSearchResult = () => {
  const targetItem = navHistory.get('SEARCH_HOME');
  if (targetItem) {
    targetItem.search = undefined;
    targetItem.response = undefined;
  }
};

const processNewPageLoad = (pageId: string, zones: Zone[]) => {
  if (zones.length === 0) return;

  if (navHistory.has(pageId)) {
    let deleteFollowing = false;
    for (const key of navHistory.keys()) {
      if (deleteFollowing) {
        navHistory.delete(key);
      }
      if (key === pageId) {
        deleteFollowing = true;
      }
    }
  } else {
    const navHistoryZones = zones.map((zone) => ({
      id: zone.id,
      horizontalIndex: 0,
      authenticatedConent: zone.authenticatedContent,
    }));
    navHistory.set(pageId, {
      routeId: pageId,
      zones: navHistoryZones,
      verticalIndex: 0,
    } as unknown as NavHistoryItem);
  }
};

/**
 * For a dynamic list, like the bookmarks, we need to store the horizontal index of the teaser
 * when we update the bookmarks, particularly useful when removing a bookmark
 */
const storeMyVideosPageUpdate = () => {
  const targetItem = navHistory.get(PAGE_IDS.MY_VIDEOS);
  if (targetItem) {
    const zone = targetItem.zones[targetItem.verticalIndex];
    if (zone) {
      targetItem.savedHorizontalPosition = zone.horizontalIndex;
    }
  }
};

/**
 * Recover the horizontal position of the teaser in the bookmarks page upon refreshing the bookmarks
 * very useful when removing a bookmark
 * @param pLimit
 */
const recoverMyVideosHorizontalPosition = (pLimit: number) => {
  if (pLimit !== 0) {
    const targetItem = navHistory.get(PAGE_IDS.MY_VIDEOS);
    if (targetItem) {
      if (targetItem.savedHorizontalPosition === pLimit) {
        const zone = targetItem.zones[targetItem.verticalIndex];
        zone.horizontalIndex = targetItem.savedHorizontalPosition;
      }
      return targetItem.savedHorizontalPosition || 0;
    }
  }
  return 0;
};

const storeGuidePageLoad = (pageId: string, verticalIndex: number, currentDay: number) => {
  if (navHistory.has(pageId)) {
    let deleteFollowing = false;
    for (const key of navHistory.keys()) {
      if (deleteFollowing) {
        navHistory.delete(key);
      }
      if (key === pageId) {
        deleteFollowing = true;
      }
    }
  }

  navHistory.set(pageId, {
    routeId: pageId,
    currentDay,
    verticalIndex,
  } as NavHistoryItem);
};

const deletePage = (pageId: string) => navHistory.delete(pageId);

const getPage = (pageId: string) => navHistory.get(pageId);

const setNavHistoryVerticalOffset = (pageId: string, offset: number) => {
  const targetItem = navHistory.get(pageId);

  if (!targetItem) return;

  targetItem.offset = offset;
};

const setNavHistoryVerticalIndex = (selectedItemIndex: number, pageId: string) => {
  if (selectedItemIndex === -1) return;

  const targetItem = navHistory.get(pageId);

  if (!targetItem) return;

  targetItem.verticalIndex = selectedItemIndex;
};

const setNavHistoryZoneId = (pageId: string, zoneId: string): void => {
  const targetItem = navHistory.get(pageId);
  if (!targetItem) return;

  targetItem.zoneId = zoneId;
};

const setNavHistoryHorizontalIndex = (pageId: string, zoneId: string, focusedTeaserIndex: number) => {
  const targetItem = navHistory.get(pageId);
  if (!targetItem) return;
  targetItem.savedHorizontalPosition = 0;
  const zoneFoundAt = findZone(targetItem, zoneId);
  if (zoneFoundAt === -1) return;

  targetItem.zones[zoneFoundAt].horizontalIndex = focusedTeaserIndex;
};

const getNavHistoryVerticalOffset = (pageId: string): number | undefined => {
  const targetItem = navHistory.get(pageId);

  return targetItem?.offset;
};

const getVerticalNavIndex = (pageId: string) => {
  const targetItem = navHistory.get(pageId);

  return targetItem ? targetItem.verticalIndex : 0;
};

const getNavHistoryZoneId = (pageId: string) => {
  const targetItem = navHistory.get(pageId);
  return targetItem?.zoneId;
};

const getHorizontalNavIndex = (pageId: string, zoneId: string) => {
  const targetItem = navHistory.get(pageId);

  if (!targetItem) return 0;

  const zoneFoundAt = findZone(targetItem, zoneId);

  if (zoneFoundAt === -1) return 0;

  return targetItem.zones[zoneFoundAt].horizontalIndex;
};

const storeZoneTeasers = (pageId: string, zoneId: string, teasers: ITeaserResponse[]) => {
  const targetItem = navHistory.get(pageId);
  if (!targetItem) return;

  const zoneIndex = findZone(targetItem, zoneId);
  targetItem.zones[zoneIndex].teaserList = [...teasers];
};

const getStoredZoneTeasers = (pageId: string, zoneId: string): ITeaserResponse[] | undefined => {
  const targetItem = navHistory.get(pageId);
  if (!targetItem) return undefined;
  const zoneIndex = findZone(targetItem, zoneId);
  return targetItem?.zones[zoneIndex]?.teaserList;
};

const getRootPage = () => {
  const mainPageIds = Object.values(PAGE_IDS);

  const historyArray = Array.from(navHistory.values()).reverse();

  for (const item of historyArray) {
    if (mainPageIds.includes(item.routeId as PageId)) {
      return item;
    }
  }
  return null;
};

const getNavHistory = () => navHistory;

const getNavHistoryLength = () => Array.from(getNavHistory().values()).length;

export {
  resetNavHistory,
  getHorizontalNavIndex,
  processNewPageLoad,
  setNavHistoryVerticalOffset,
  setNavHistoryVerticalIndex,
  setNavHistoryHorizontalIndex,
  setNavHistoryZoneId,
  getNavHistoryVerticalOffset,
  getVerticalNavIndex,
  getNavHistoryZoneId,
  storePaginationFullResult,
  getStoredPaginationResult,
  setGridFocusedItem,
  getGridFocusedItem,
  storePaginationResult,
  storePaginationCurrentPage,
  resetSearchResult,
  setSearchKeyboardNavExitKeyRight,
  setSearchKeyboardNavExitKeyLeft,
  getSearchKeyboardNavExitKeyRight,
  getSearchKeyboardNavExitKeyLeft,
  storeGuidePageLoad,
  storeZoneTeasers,
  getStoredZoneTeasers,
  deletePage,
  getPage,
  getRootPage,
  setGridHorizontalDiff,
  getGridHorizontalDiff,
  getNavHistory,
  getNavHistoryLength,
  storeMyVideosPageUpdate,
  recoverMyVideosHorizontalPosition,
};

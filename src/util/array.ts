/**
 * splits an array into chunks of given size
 *
 * @param array
 * @param size
 * @returns
 */
export function chunk<T>(array: T[], size: number) {
  const chunks = [];
  const arrayLength = array.length;
  let index = 0;

  while (index < arrayLength) {
    chunks.push(array.slice(index, (index += size)));
  }

  return chunks;
}

/**
 * A utility function to conditionally add new elements to an array
 * Usage: [...insertIf(condition, elements)];
 * @param condition if met, elements will be added
 * @param elements elements to add
 */
export const insertIf = <T>(condition: string | boolean | undefined, ...elements: T[]) => (condition ? elements : []);

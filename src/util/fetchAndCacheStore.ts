import { failOnPageResponseError } from '@errors/errorHandling';
import { fetchCachedData, fetchUrl, getCachedResponse, storeCachedResponse } from '@util/CacheHistory';

import { PageResponseBody } from '../types';
import { middlewareUrl } from './url';

const fetchAndCacheStore = async (page: string) => {
  const url = middlewareUrl(`/skeletons/pages/${page}`);
  const cachedResponse = getCachedResponse(url);
  let pageData: PageResponseBody | null = cachedResponse ? await fetchCachedData(cachedResponse) : null;

  if (!pageData) {
    pageData = await fetchUrl(url);
    failOnPageResponseError(pageData);
    storeCachedResponse(url, pageData);
  }

  storeCachedResponse(url, pageData);

  return pageData;
};

export { fetchAndCacheStore };

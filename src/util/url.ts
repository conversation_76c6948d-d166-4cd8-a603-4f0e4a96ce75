import { getBaseUrl } from '@routes/utils';
import { protocol } from '@util/protocol';
import i18n from 'i18next';
import { getSearchParams } from 'target';

import { insertIf } from './array';

type MiddlewareUrlOptions = {
  query?: string;
  omitLang?: boolean;
};

const middlewareParams = (query?: string) => {
  const searchParams = getSearchParams();

  const queryParams = [
    `target=${process.env.TARGET}`,
    ...insertIf(!!searchParams.get('emac'), `emac=${searchParams.get('emac')}`),
    ...insertIf(query, query),
  ].join('&');
  return `?${queryParams}`;
};

const middlewareUrl = (api: string, options?: MiddlewareUrlOptions): string => {
  const apiBaseUrl = getBaseUrl('middleware');
  const lang = options?.omitLang ? '' : `/${i18n.language}`;
  const query = middlewareParams(options?.query);
  return `${protocol}${apiBaseUrl}${lang}${api}${query}`;
};

const playerUrl = (api: string, id: string): string => {
  const playerBaseUrl = getBaseUrl('player');
  return `${protocol}${playerBaseUrl}${api}/${i18n.language}/${id}`;
};

export { middlewareUrl, playerUrl };

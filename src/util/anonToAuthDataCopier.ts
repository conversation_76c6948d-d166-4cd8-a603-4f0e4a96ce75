import { MeDataLastViewed } from '@apptypes/SSOResponse';
import { addToFavourites, getMeData } from '@features/usercontent/userContentData';
import { anonPersonalisationAllowed } from '@util/anonPersonalisationAllowed';
import { userHistoryScheduler } from '@util/userHistoryScheduler';

let anonLastViewedItems: MeDataLastViewed[];
let anonBookmarks: string[];

/**
 * This will reset the anonymous last views and favorite data
 */
const resetAnonStoredData = () => {
  anonBookmarks = [];
  anonLastViewedItems = [];
};

/**
 * This will locally save the anonymous last views and favorite data before true user login
 */
export const temporarySaveAnonymousData = async () => {
  if (!anonPersonalisationAllowed()) {
    resetAnonStoredData();
    return;
  }

  const anonMeData = await getMeData();

  anonLastViewedItems = anonMeData.lastvieweds;
  anonBookmarks = anonMeData.favorites as string[];
};

/**
 * after user logs in, the anonymous data will be transferred to the authenticated user
 */
export const transferAnonymousDataToAuthenticatedUser = async () => {
  if (!anonPersonalisationAllowed()) {
    resetAnonStoredData();
    return;
  }

  for (const anonLastViewedItem of anonLastViewedItems) {
    anonLastViewedItem?.programId &&
      anonLastViewedItem?.timecode &&
      userHistoryScheduler.addToQueue(anonLastViewedItem?.programId, anonLastViewedItem?.timecode, 0);
  }

  for (const anonBookmarkItem of anonBookmarks) {
    await addToFavourites(anonBookmarkItem);
  }

  resetAnonStoredData();
};

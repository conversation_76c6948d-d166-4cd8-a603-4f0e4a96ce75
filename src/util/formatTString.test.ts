import { describe } from 'vitest';

import { formatTString } from './formatTString';

describe('formatTString', () => {
  it('replaces all placeholders with their corresponding values', () => {
    const template = 'Livestream on {{date}} at {{hour}}';
    const values = {
      date: 'February 07, 1987',
      hour: '11:00',
    };
    const result = formatTString(template, values);
    expect(result).toBe('Livestream on February 07, 1987 at 11:00');
  });

  it('handles multiple and different placeholders', () => {
    const result = formatTString('Livestream on {{date}} at {{hour}}, venue: {{venue}}', {
      date: '09/12',
      hour: '16:30',
      venue: 'Paris',
    });
    expect(result).toBe('Livestream on 09/12 at 16:30, venue: Paris');
  });

  it('leaves placeholder if no matching key in replacements', () => {
    const template = 'Event on {{date}} at {{time}}';
    const values = {
      date: 'April 1, 2024',
    };
    const result = formatTString(template, values);
    expect(result).toBe('Event on April 1, 2024 at {{time}}');
  });

  it('works with empty strings as replacements', () => {
    const template = 'Hello, {{name}}!';
    const values = {
      name: '',
    };
    const result = formatTString(template, values);
    expect(result).toBe('Hello, !');
  });

  it('processes a template with no placeholders', () => {
    const template = 'Just a normal string';
    const result = formatTString(template, {});
    expect(result).toBe('Just a normal string');
  });
});

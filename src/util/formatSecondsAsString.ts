const formatSecondsAsString = (seconds: number, pad = true) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor(seconds / 60) % 60;
  const remainingSeconds = Math.floor(seconds) % 60;

  // only show hours if greater than 0 resulting in 0:00:00 or 00:00
  let result = hours >= 1 ? hours + ':' : '';
  result += (result || pad ? minutes.toString().padStart(2, '0') : minutes) + ':';
  result += remainingSeconds.toString().padStart(2, '0');

  return result;
};

export default formatSecondsAsString;

import { ITeaserProperties } from '@apptypes/ITeaserProperties';
import { anonPersonalisationAllowed } from '@util/anonPersonalisationAllowed';
import { hasUserToken } from '@util/cookies';
import { isPartnerKind } from '@util/isPartnerKind';
import { t } from 'i18next';

export function canBookmark(properties: ITeaserProperties): boolean {
  return anonPersonalisationAllowed() && !isPartnerKind(properties);
}

export function canRemind(properties: ITeaserProperties): boolean {
  if (!properties?.availability || !properties?.availability?.start) return false;
  const isUpcoming = new Date(properties?.availability?.start).getTime() > Date.now();

  return isUpcoming && hasUserToken() && !isPartnerKind(properties);
}

export const getBookmarkIcon = (canBookmark: boolean, isReminderAllowed: boolean): string => {
  const labels: Record<string, Record<string, string>> = {
    true: {
      true: t('reminder-remove'),
      false: t('favourites-remove'),
    },
    false: {
      true: t('reminder-add'),
      false: t('favourites-add'),
    },
  };

  return labels[String(canBookmark)][String(isReminderAllowed)];
};

export const getBookmarkLabel = (canBookmark: boolean, isReminderAllowed: boolean): string => {
  const labels: Record<string, Record<string, string>> = {
    true: {
      true: t('notifications__reminder_remove'),
      false: t('favorites__delete'),
    },
    false: {
      true: t('notifications__reminder_add'),
      false: t('favorites__add'),
    },
  };

  return labels[String(canBookmark)][String(isReminderAllowed)];
};

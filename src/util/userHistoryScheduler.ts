import { MeData, MeDataLastViewed } from '@apptypes/SSOResponse';
import { CustomError } from '@errors/CustomError';
import { userHistory } from '@features/usercontent/userContentData';
import { getUserViewHistory, setUserViewHistory } from '@util/cookies';
import { fromEpoch } from '@util/fromEpoch';
import { toEpoch } from '@util/toEpoch';

import { EVENTS } from '../constants';
import EventBus from './EventBus';

export const userHistoryScheduler = (() => {
  let schedulerInterval: string | number | NodeJS.Timeout | null | undefined = null;
  let firstStart = true;

  const addToQueue = (programId: string, position: number, duration: number) => {
    let currentData = JSON.parse(getUserViewHistory());

    if (!currentData) {
      currentData = [];
    }

    const existingIndex = currentData.findIndex((item) => item.programId === programId);

    if (existingIndex !== -1) {
      currentData[existingIndex].position = position;
      currentData[existingIndex].duration = duration;

      const updatedItem = currentData.splice(existingIndex, 1)[0];
      currentData.unshift(updatedItem);
    } else {
      const progressPercentage = (position / duration) * 100;
      if (progressPercentage < 5) return;

      currentData.unshift({
        programId,
        position,
        duration,
        timestamp: Date.now(),
      });
    }

    setUserViewHistory(JSON.stringify(currentData));

    !schedulerInterval && startQueueInterval();
  };

  const startQueueInterval = (immediatelySend: boolean = false, lastViewedItems: MeDataLastViewed[] | null = null) => {
    stopQueueInterval();

    schedulerInterval = setInterval(() => {
      sendItem(null);
    }, 10000);

    immediatelySend && firstStart && sendItem(lastViewedItems);
    firstStart = false;
  };

  const sendItem = (lastViewedItems: MeDataLastViewed[] | null) => {
    let currentData = JSON.parse(getUserViewHistory());
    if (!currentData || currentData.length === 0) {
      stopQueueInterval();
      return;
    }

    if (lastViewedItems) {
      const itemsToRemoveFromLocal = [];
      currentData.forEach((currentDataItem) => {
        const lastViewed = lastViewedItems.find(
          (lastViewedItem) => lastViewedItem.programId === currentDataItem.programId,
        );

        if (lastViewed && toEpoch(lastViewed.updatedAt) > currentDataItem.timestamp) {
          itemsToRemoveFromLocal.push(currentDataItem);
        }
      });
      currentData = currentData.filter((currentDataItem) => !itemsToRemoveFromLocal.includes(currentDataItem));
      setUserViewHistory(JSON.stringify(currentData));
    }

    const firstItem = currentData.shift();
    if (!firstItem) {
      stopQueueInterval();
      return;
    }

    const failback = (e: CustomError) => {
      EventBus.emit(EVENTS.ERROR, { [EVENTS.ERROR]: e });
    };

    userHistory.setUserHistory(firstItem.programId, firstItem.position, firstItem.duration, failback);
  };

  const queueItemRemoved = (programId: string) => {
    const currentData = JSON.parse(getUserViewHistory());
    const existingIndex = currentData.findIndex((item) => item.programId === programId);
    if (existingIndex !== -1) {
      currentData.splice(existingIndex, 1);
    }
    setUserViewHistory(JSON.stringify(currentData));
  };

  const stopQueueInterval = () => {
    clearInterval(schedulerInterval);
    schedulerInterval = null;
  };

  /**
   * Will get lastviewds from me data and if the local queue has items that are not in the lastviewed list,
   * it will ingest them into lastviewed
   */
  const feedIntoLastViewed = (meData: MeData) => {
    const lastViewedItems = meData?.lastvieweds;
    if (!lastViewedItems) return null;

    const currentData = JSON.parse(getUserViewHistory());
    if (!currentData) return null;

    const itemsToAddFromLocal = [];
    currentData.forEach((currentDataItem) => {
      const lastViewed = lastViewedItems.find(
        (lastViewedItem) => lastViewedItem.programId === currentDataItem.programId,
      );
      if (!lastViewed) {
        currentDataItem.updatedAt = fromEpoch(currentDataItem.timestamp);
        currentDataItem.timecode = currentDataItem.position;
        currentDataItem.progress = currentDataItem.position / currentDataItem.duration;
        itemsToAddFromLocal.push(currentDataItem);
      }
    });

    meData.lastvieweds = [...itemsToAddFromLocal, ...lastViewedItems];

    return meData;
  };

  const reset = () => {
    stopQueueInterval();
    setUserViewHistory(JSON.stringify([]));
  };

  return {
    startQueueInterval,
    addToQueue,
    queueItemRemoved,
    feedIntoLastViewed,
    reset,
  };
})();

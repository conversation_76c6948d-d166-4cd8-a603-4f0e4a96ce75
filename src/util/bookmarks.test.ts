import { describe } from 'vitest';

import { Bookmark } from '../types';
import { compareBookmarks } from './bookmarks';

describe('compareBookmarks', () => {
  describe('collections', () => {
    /**
     * NOTE we are comparing the same objects here. This might seem like a pointless test but it
     * serves a valid use case in helping to explain how bookmarking works in the first instance.
     *
     * When a bookmark is first created we save a copy of the teaser directly to bookmarks context.
     * This allows us to instantly update the UI with saved bookmarks, for example, display the
     * bookmarked rail on home page.
     */
    it('should return true for matching program_id from bookmarked teaser when comparing to current teaser', () => {
      const bookmarkedTeaser = {
        type: 'teaser',
        item_id: 'd88368ca-da6e-449f-a038-0ab0f08affcc',
        title: 'Two Mothers, One Child',
        kind: {
          code: 'TOPIC',
          isCollection: true,
          label: 'Collection',
        },
        isCollection: true,
        program_id: 'RC-025402',
      };

      const currentTeaser = {
        type: 'teaser',
        item_id: 'd88368ca-da6e-449f-a038-0ab0f08affcc',
        title: 'Two Mothers, One Child',
        kind: {
          code: 'TOPIC',
          isCollection: true,
          label: 'Collection',
        },
        isCollection: true,
        program_id: 'RC-025402',
      };

      const actual = compareBookmarks(bookmarkedTeaser as Bookmark, currentTeaser as Bookmark);
      const expected = true;
      expect(actual).toEqual(expected);
    });

    /**
     * When pages are reloaded we fetch favorite data from endpoint and initialise bookmarks context
     * with the latest bookmark data that we have - this means the UI has the most recent bookmark
     * state possible. This means overwriting any saved bookmarks with the 'teaser shaped' object
     * and replacing them with the 'sso response shaped objects' - which are DIFFERENT!
     *
     * Note that the SSO response is modified in `src/authcontent.js` to add some missing data that
     * we need in order to be compatible with data shape of responses from middleware.
     */
    it('should return true for matching program_id when comparing bookmarked COLLECTION from SSO to current teaser', () => {
      const bookmarkedCollectionTeaserSSO: Partial<Bookmark> = {
        id: 'RC-025402_en',
        kind: {
          isCollection: true,
        },
        isCollection: true,
        // @ts-expect-error SSO response contains `programId` whose value is copied to `program_id` to be compatible with middleware responses
        programId: 'RC-025402',
        title: 'Two Mothers, One Child',
        program_id: 'RC-025402',
      };

      const currentTeaser: Partial<Bookmark> = {
        item_id: 'd88368ca-da6e-449f-a038-0ab0f08affcc',
        title: 'Two Mothers, One Child',
        kind: {
          code: 'TOPIC',
          isCollection: true,
          label: 'Collection',
        },
        program_id: 'RC-025402',
      };

      const actual = compareBookmarks(bookmarkedCollectionTeaserSSO as Bookmark, currentTeaser as Bookmark);
      const expected = true;
      expect(actual).toEqual(expected);
    });

    it('should return false when comparing a bookmarked COLLECTION to a SHOW', () => {
      const bookmarkedCollectionTeaser: Partial<Bookmark> = {
        item_id: 'RC-025402',
        title: 'Two Mothers, One Child',
        kind: {
          code: 'TOPIC',
          isCollection: true,
          label: null,
        },
        program_id: '111699-001-A',
      };

      const showTeaser: Partial<Bookmark> = {
        type: 'teaser',
        item_id: '111699-001-A_en',
        title: 'Two Mothers, One Child (1/30)',
        kind: {
          code: 'SHOW',
          isCollection: false,
          label: 'Show',
        },
        program_id: '111699-001-A',
      };

      const actual = compareBookmarks(bookmarkedCollectionTeaser as Bookmark, showTeaser as Bookmark);
      const expected = false;
      expect(actual).toEqual(expected);
    });
  });

  describe('shows', () => {
    /**
     * NOTE we are comparing the same objects here. This might seem like a pointless test but it
     * serves a valid use case in helping to explain how bookmarking works in the first instance.
     *
     * When a bookmark is first created we save a copy of the teaser directly to bookmarks context.
     * This allows us to instantly update the UI with saved bookmarks, for example, display the
     * bookmarked rail on home page.
     */
    it('should return true for matching program_id when comparing bookmarked SHOW to current SHOW', () => {
      const bookmarkedShow: Partial<Bookmark> = {
        item_id: '70c29b98-3b18-4ab9-b590-34e03f86fa69',
        title: 'ARTE Europe Weekly',
        kind: {
          code: 'SHOW',
          isCollection: false,
          label: 'Show',
        },
        program_id: '112907-108-A',
      };

      const currentTeaser = {
        item_id: '70c29b98-3b18-4ab9-b590-34e03f86fa69',
        title: 'ARTE Europe Weekly',
        kind: {
          code: 'SHOW',
          isCollection: false,
          label: 'Show',
        },
        program_id: '112907-108-A',
      };

      const actual = compareBookmarks(bookmarkedShow as Bookmark, currentTeaser as Bookmark);
      const expected = true;
      expect(actual).toEqual(expected);
    });

    /**
     * When pages are reloaded we fetch favorite data from endpoint and initialise bookmarks context
     * with the latest bookmark data that we have - this means the UI has the most recent bookmark
     * state possible. This means overwriting any saved bookmarks with the 'teaser shaped' object
     * and replacing them with the 'sso response shaped objects' - which are DIFFERENT!
     *
     * Note that the SSO response is modified in `src/authcontent.js` to add some missing data that
     * we need in order to be compatible with data shape of responses from middleware.
     */
    it('should return true for matching program_id when comparing bookmarked SHOW from SSO to current SHOW', () => {
      const bookmarkedShowSSO: Partial<Bookmark> = {
        id: '112907-108-A_en',
        kind: {
          isCollection: false,
        },
        // @ts-expect-error SSO response contains `programId` whose value is copied to `program_id` to be compatible with middleware responses
        programId: '112907-108-A',
        title: 'ARTE Europe Weekly',
        subtitle: "What Germany’s Election Result Means for Europe's Economy",
        program_id: '112907-108-A',
      };

      const currentTeaser: Partial<Bookmark> = {
        item_id: '70c29b98-3b18-4ab9-b590-34e03f86fa69',
        title: 'ARTE Europe Weekly',
        kind: {
          code: 'SHOW',
          isCollection: false,
          label: 'Show',
        },
        program_id: '112907-108-A',
      };

      const actual = compareBookmarks(bookmarkedShowSSO as Bookmark, currentTeaser as Bookmark);
      const expected = true;
      expect(actual).toEqual(expected);
    });
  });

  describe('bookmark COLLECTION from SER route then confirm bookmarked via COLLECTION teaser', () => {
    /**
     * This test case occurs when adding a bookmark to a COLLECTION from the
     * `single-collectionContent` template, i.e. a COLLECTION top teaser. Additionally, note that
     * in the first instance the bookmarked object is the teaser response
     *
     * - navigate to series route: /page/SER
     * - select an item to view which will load the collection route e.g. collection/RC-023740
     * - bookmark the collection via the top teaser (`single-collectionContent` template)
     * - press BACK
     * - press green on the item you just bookmarked to open the modal
     * - assert item is bookmarked
     */
    it('should return true when saved teaser `item_id` equals current teaser `program_id`', () => {
      /**
       * NOTE this object is the collection we display in the UI, the `single-collectionContent`
       * template, thus it has an `item_id` that refers to the COLLECTION but the `program_id`
       * refers to the first SHOW in the COLLECTION
       */
      const bookmarkedTeaserCollection: Partial<Bookmark> = {
        item_id: 'RC-023740',
        title: 'Terrain Sensible',
        kind: {
          code: 'TV_SERIES',
          isCollection: true,
          label: null,
        },
        program_id: '110354-001-A',
        template: 'single-collectionContent',
      };

      /**
       * NOTE this object has a template type of `horizontal-landscapeBig` and the object represents
       * a COLLECTION whose `program_id` refers to the COLLECTION
       */
      const currentCollectionTeaser: Partial<Bookmark> = {
        item_id: 'f6a35e96-9d8d-46ff-bef3-24fa6488c7ea',
        title: 'Terrain sensible',
        kind: {
          code: 'TV_SERIES',
          isCollection: true,
          label: 'Série',
        },
        program_id: 'RC-023740',
        template: 'horizontal-landscapeBig',
      };

      const actual = compareBookmarks(bookmarkedTeaserCollection as Bookmark, currentCollectionTeaser as Bookmark);
      const expected = true;
      expect(actual).toEqual(expected);
    });

    /**
     * This test case confirms that the COLLECTION is still bookmarked after bookmarks have been
     * updated via the favorite SSO response.
     *
     * Assuming the above steps in the previous test have occurred, then...
     * - reload application starting on home page
     * - navigate to series route: /page/SER
     * - press green on the item you just bookmarked to open the modal
     * - assert item is bookmarked
     */
    it('should return true when comparing bookmarked COLLECTION from SSO with current COLLECTION', () => {
      const bookmarkedSSOCollection: Partial<Bookmark> = {
        id: 'RC-023740_fr',
        kind: {
          isCollection: true,
        },
        // @ts-expect-error SSO response contains `programId` whose value is copied to `program_id` to be compatible with middleware responses
        programId: 'RC-023740',
        title: 'Terrain Sensible',
        program_id: 'RC-023740',
        template: 'horizontal-landscape',
      };

      const currentCollectionTeaser: Partial<Bookmark> = {
        item_id: 'f6a35e96-9d8d-46ff-bef3-24fa6488c7ea',
        title: 'Terrain sensible',
        kind: {
          code: 'TV_SERIES',
          isCollection: true,
          label: 'Série',
        },
        program_id: 'RC-023740',
        template: 'horizontal-landscapeBig',
      };

      const actual = compareBookmarks(bookmarkedSSOCollection as Bookmark, currentCollectionTeaser as Bookmark);
      const expected = true;
      expect(actual).toEqual(expected);
    });
  });
});

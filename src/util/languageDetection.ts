import { getLanguage } from '@data/source';
import { logError } from '@errors/errorLogging';
import { getLanguage as storedLanguage, setLanguage } from '@util/cookies';
import { getSearchParams } from 'target';

import { FALLBACK_LANGUAGE, QUERY_STRING_LOOKUP_LANGUAGE } from '../constants';

async function detectLanguageViaMiddleware(): Promise<string> {
  try {
    const response = await getLanguage();

    if (response?.language) {
      console.log('[language detection]: using lang from middleware', response.language);
      return response.language;
    }

    console.log('[language detection]: using fallback lang', FALLBACK_LANGUAGE);
    return FALLBACK_LANGUAGE;
  } catch (error) {
    logError(error, 'WARNING');
    console.log('[language detection]: using fallback lang', FALLBACK_LANGUAGE);
    return FALLBACK_LANGUAGE;
  }
}

export async function setUserLanguage() {
  const userPrefLang = storedLanguage();
  const langViaQueryString = getSearchParams().get(QUERY_STRING_LOOKUP_LANGUAGE);
  const langFromBrowser = navigator?.language;

  // to aid QA debugging
  console.log('[language detection]: stored lang', userPrefLang);
  console.log('[language detection]: query string', langViaQueryString);
  console.log('[language detection]: browser', langFromBrowser);

  // if there is a stored language, use it
  if (userPrefLang) {
    console.log('[language detection]: using stored lang', userPrefLang);
    return true;
  }

  // if there is a language in the query string use it
  // note we don't have to check for a supported lang here, i18n will handle that
  if (langViaQueryString) {
    setLanguage(langViaQueryString);
    console.log('[language detection]: using lang from query string', langViaQueryString);
    return true;
  }

  // get language from browser, if it is not `en`, use it
  if (langFromBrowser && !langFromBrowser.startsWith('en')) {
    console.log('[language detection]: using lang from browser', langFromBrowser);
    setLanguage(langFromBrowser);
    return true;
  }

  // get language from middleware
  const lang = await detectLanguageViaMiddleware();
  setLanguage(lang);
  return true;
}

import { fetchWithRetry } from '@routes/utils';

import { PageResponseBody } from '../types';

const CACHE_ITEM_LIMIT = 10; // number of json responses to keep in memory
const CACHE_TIME_LIMIT = 0.5; // minutes, new data will be fetched after this time if requested

type CacheHistoryItem = {
  url: string;
  response: PageResponseBody | null;
  checksum: string;
  timestamp: number;
};

const cacheHistory: CacheHistoryItem[] = [];

const storeCachedResponse = (url: string, response: PageResponseBody | null) => {
  const itemFoundAt = cacheHistory.findIndex((item) => item.url === url);
  if (itemFoundAt !== -1) {
    const item = cacheHistory[itemFoundAt];
    item.response = response;
    item.checksum = getChecksum(response);
    item.timestamp = Date.now();

    moveItemToEndOfArray(item, itemFoundAt);
  } else {
    cacheHistory.length >= CACHE_ITEM_LIMIT && cacheHistory.shift();

    cacheHistory.push(<CacheHistoryItem>{
      url,
      response,
      checksum: getChecksum(response),
      timestamp: Date.now(),
    });
  }
};

const getCachedResponse = (url: string) => {
  if (!url) return null;

  const itemFoundAt = cacheHistory.findIndex((item) => item.url === url);
  if (itemFoundAt > -1) return cacheHistory[itemFoundAt];

  return null;
};

const timedAllowedToFetch = (cachedApiHistory: CacheHistoryItem) => {
  const diffInMinutes = (Date.now() - cachedApiHistory.timestamp) / (1000 * 60);

  return diffInMinutes <= CACHE_TIME_LIMIT;
};

const getChecksum = (data: PageResponseBody | null) => data?.crc || 'no crc';

const updateTimestamp = (cachedHistory: CacheHistoryItem) => {
  const itemFoundAt = cacheHistory.findIndex((item) => item.url === cachedHistory.url);
  if (itemFoundAt !== -1) {
    cacheHistory[itemFoundAt].timestamp = Date.now();
  }
  moveItemToEndOfArray(cachedHistory, itemFoundAt);
};

const fetchCachedData = async (cachedHistory: CacheHistoryItem) => {
  if (timedAllowedToFetch(cachedHistory)) {
    moveItemToEndOfArray(cachedHistory);
    return cachedHistory.response;
  } else {
    const data = await fetchUrl(cachedHistory.url + '&crc=1');
    const currentChecksum = getChecksum(data);
    if (cachedHistory.checksum === currentChecksum) {
      updateTimestamp(cachedHistory);
      return cachedHistory.response;
    }
  }

  return null;
};

const fetchUrl = async (url: string, requestInit?: RequestInit) => {
  const response = await fetchWithRetry(url, false, requestInit);
  return await response.json();
};

/**
 * Moving item to the end of the array everytime we fetch its data, or we update its timestamp
 * Like this, the item is considered to be often used and will be kept in memory longer
 */
const moveItemToEndOfArray = (cachedItem: CacheHistoryItem, itemFoundAt: number | null = null) => {
  let itemPosition = itemFoundAt;

  if (!itemFoundAt) {
    itemPosition = cacheHistory.findIndex((item) => item.url === cachedItem.url);
  }

  if (!itemPosition || itemPosition === -1) return;

  cacheHistory.splice(itemPosition, 1);
  cacheHistory.push(cachedItem);
};

const resetCacheHistory = () => {
  cacheHistory.length = 0;
};

export { fetchCachedData, fetchUrl, storeCachedResponse, getCachedResponse, resetCacheHistory };

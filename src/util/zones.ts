import { Zone } from '../types';

function getFavouritesZone(zones: Zone[]) {
  return zones.filter((zone) => zone.authenticatedContent === 'sso-favorites');
}

export function hasFavouritesZone(zones: Zone[]) {
  return !!getFavouritesZone(zones).length;
}

export function getTeasersFromFavouritesZone(zones: Zone[]) {
  if (!hasFavouritesZone(zones)) return;
  const favouritesZone = getFavouritesZone(zones);
  return favouritesZone[0]?.teaserList;
}

export function zoneHasPagination(zone: Zone): boolean {
  if (!zone) return false;
  const { pages, teaser_count } = zone;
  return !!pages && teaser_count > 0;
}

import { Bookmark } from '@apptypes/bookmarks';
import { MeData, MeDataLastViewed } from '@apptypes/SSOResponse';

export function getBookmarks(meData: MeData): Bookmark[] {
  return meData?.favorites.map((id) => {
    return { program_id: id as string } as Bookmark;
  });
}

export function getLastViewedData(programId: string, lastvieweds?: MeDataLastViewed[]) {
  return lastvieweds?.find((item) => item.programId === programId);
}

import cssExports from '../styles/exports.module.scss';
import { isFHD } from './resolution';

const getBaseFontSize = (): number => {
  const rootPx = Number(cssExports.rootPx);
  const baseFontSize = isFHD() ? rootPx : (1280 / 1920) * rootPx;
  return baseFontSize;
};

const pxToRem = (px: number) => px / getBaseFontSize();

const remToPx = (rem: number) => rem * getBaseFontSize();

export { remToPx, pxToRem };

import { AvailabilityRights } from '../types';

export const isCurrentTimeWithinRange = (availability: AvailabilityRights | undefined) => {
  if (!availability) return true;

  const now = new Date();
  const startTime = new Date(availability.begin);
  const endTime = new Date(availability.end);

  return now >= startTime && now <= endTime;
};

const formatTimeUnit = (value: number) => {
  return value.toString().padStart(2, '0');
};

export const formatDate = (availability: AvailabilityRights) => {
  const date = new Date(availability.begin);
  return `${formatTimeUnit(date.getDate())}/${formatTimeUnit(
    date.getMonth() + 1,
  )}/${date.getFullYear()} - ${formatTimeUnit(date.getHours())}:${formatTimeUnit(date.getMinutes())}`;
};

const defaultTimeLeft = {
  days: 0,
  hours: '00',
  minutes: '00',
  seconds: '00',
  isAvailable: true,
};

export const getTimeLeftUntilAvailability = (availability: AvailabilityRights) => {
  if (!availability || !availability?.begin) return defaultTimeLeft;

  const now = new Date();
  const startTime = new Date(availability.begin);
  const timeDiff = startTime - now;

  if (timeDiff <= 0) {
    return defaultTimeLeft;
  }

  const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
  const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);

  return {
    days: days,
    hours: formatTimeUnit(hours),
    minutes: formatTimeUnit(minutes),
    seconds: formatTimeUnit(seconds),
    isAvailable: false,
  };
};

import { PAGE_IDS } from '@constants';
import { Params } from 'react-router-dom';

import { MY_ARTE_PAGE_ZONE_IDS } from '../routes/myArtePageLoader';
import { Zone } from '../types';

const buildPageId = (routeParams: Readonly<Params<string>>, zones: Zone[] = []): string => {
  const pageId = routeParams?.pageId?.toLowerCase() || routeParams?.collectionId || routeParams?.programId;
  if (pageId) return pageId;

  if (zones.find((zone) => MY_ARTE_PAGE_ZONE_IDS.includes(zone.id))) {
    return PAGE_IDS.MYARTE;
  }

  return PAGE_IDS.HOME;
};

export { buildPageId };

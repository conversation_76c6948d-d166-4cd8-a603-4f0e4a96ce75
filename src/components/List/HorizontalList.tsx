import {
  getChildImageHeight,
  getChildOffset,
  getChildrenWidth,
  getChildWidth,
  getVisibleWidth,
} from '@components/List/listHelper';
import { HorizontalNavigation } from '@components/MouseNavigation/HorizontalNavigation';
import { MouseContext } from '@providers/MouseProvider';
import classNames from 'classnames';
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react';

import { focusClassNames, withFocusableContainer } from '../../focus';
import styles from './horizontal-list.module.scss';
import { IHorizontalListProperties } from './types';

/**
 * Note that this code has been taken from the previous List component in order to separate out the horizontal
 * scrolling logic from the vertical. This will hopefully make things easier to reason about if we need to
 * modify behaviour here.
 *
 * We should consider a refactor of the logic here as it is not clear what is supposed to happen.
 */

export const HorizontalList = withFocusableContainer(
  React.forwardRef<HTMLDivElement, IHorizontalListProperties>(
    (
      {
        children,
        currentItem = 0,
        peekSize = 0,
        scrollStartIndex = 0,
        scrollToEnd,
        className,
        contentsClassName,
        totalItems = 0,
        searchMenuPresent,
        ...properties
      },
      reference,
    ) => {
      const isPeeking = peekSize > 0 && currentItem > 0;
      const currentItemState = useRef(0);

      const { isMouseActive } = useContext(MouseContext);
      const showMouseNavigation = isMouseActive;
      const [mouseScrollState, setMouseScrollState] = useState({ page: 0, direction: 'right' });
      const [imageHeight, setImageHeight] = useState(0);
      const [showLeftArrow, setShowLeftArrow] = useState(false);
      const [showRightArrow, setShowRightArrow] = useState(false);
      const [horizontalPageCount, setHorizontalPageCount] = useState(0);
      const [horizontalVisibleSize, setHorizontalVisibleSize] = useState(0);

      useEffect(() => {
        setShowLeftArrow(isMouseActive && mouseScrollState.page > 0);
      }, [isMouseActive, mouseScrollState]);

      useEffect(() => {
        setShowRightArrow(isMouseActive && mouseScrollState.page < horizontalPageCount - 1);
      }, [isMouseActive, mouseScrollState, horizontalPageCount]);

      const updateOffset = useCallback((node: HTMLDivElement, value: number) => {
        node.style.left = `${value}px`;
      }, []);

      function mouseScrollLeft() {
        if (mouseScrollState.page === 0) return;
        setMouseScrollState(({ page }) => {
          return { page: page - 1, direction: 'left' };
        });
      }

      function mouseScrollRight() {
        setMouseScrollState(({ page }) => {
          return { page: page + 1, direction: 'right' };
        });
      }

      /**
       * We have separate behaviour when the mouse is active. Navigational arrow are displayed in the UI, when
       * clicked we page the items (as opposed to scrolling one item at a time using keys).
       */
      const navigateHorizontallyViaMouse = useCallback(
        (node: HTMLDivElement) => {
          const childWidth = getChildWidth(node, 0);
          const visibleSize = getVisibleWidth(node, searchMenuPresent);
          const noOfItemsFitOnScreen = Math.floor(visibleSize / childWidth);
          const offset = noOfItemsFitOnScreen * childWidth;
          const calculatedOffset = -1 * offset * mouseScrollState.page;
          const totalPages = noOfItemsFitOnScreen > 0 ? Math.ceil(totalItems / noOfItemsFitOnScreen) : 0;
          setHorizontalPageCount(totalPages);
          setImageHeight(getChildImageHeight(node, 0));
          setHorizontalVisibleSize(visibleSize);

          if (mouseScrollState.direction === 'right') {
            mouseScrollState.page > 0 && updateOffset(node, calculatedOffset);
          }

          if (mouseScrollState.direction === 'left') {
            updateOffset(node, mouseScrollState.page > 0 ? calculatedOffset : 0);
          }
        },
        [searchMenuPresent, mouseScrollState, totalItems, updateOffset],
      );

      const navigateViaKeys = useCallback(
        (node: HTMLDivElement) => {
          let scrollStartIndexModified = scrollStartIndex;
          const visibleSize = getVisibleWidth(node, searchMenuPresent);
          const childWidth = getChildWidth(node, 0);
          const offsetProperty = 'offsetLeft';

          scrollStartIndexModified = Math.round(Math.floor(visibleSize / childWidth) - 1);

          const childOffset = getChildOffset(node, currentItem - scrollStartIndexModified, offsetProperty);
          // node.scrollWidth returns the correct width for lists wider than the screen
          // Otherwise, it retuns the screen size because divs take up 100% of space by default
          // In that case we need to compute the width in js.
          const totalSize = node.scrollWidth > window.screen.width ? node.scrollWidth : getChildrenWidth(node);
          const hiddenSize = totalSize - visibleSize;
          const contentOffset = currentItem > scrollStartIndexModified ? childOffset ?? 0 : 0;
          const offsetAndPeek = contentOffset - (isPeeking ? peekSize : 0);

          let direction;
          currentItem < currentItemState.current ? (direction = 'left') : (direction = 'right');

          let totalOffset: number | undefined = 0;
          let shouldUpdateOffset = false;
          const ratioVal = Math.round(childWidth / 2);

          if (totalItems && totalItems * childWidth < visibleSize) {
            totalOffset = 0;
          } else if (scrollToEnd) {
            totalOffset = offsetAndPeek;
            shouldUpdateOffset = true;
          } else if (currentItem === totalItems - 1 && totalItems > 1) {
            totalOffset = hiddenSize;
            shouldUpdateOffset = true;
          } else {
            const noOfItemsFitOnScreen = Math.floor((visibleSize - ratioVal) / childWidth);

            if (direction === 'right') {
              totalOffset = Math.max(
                0,
                Math.floor(currentItem * childWidth - noOfItemsFitOnScreen * childWidth + ratioVal),
              );
            } else if (direction === 'left') {
              totalOffset = Math.max(0, Math.floor(currentItem * childWidth - ratioVal));
              const trueLength = React.Children.count(children) - 1 ?? 0;
              if (currentItemState.current === trueLength) {
                // We're going left from the last item
                if (trueLength > noOfItemsFitOnScreen) {
                  // compute offset if not all items fit on screen
                  totalOffset -= (noOfItemsFitOnScreen - 1) * childWidth;
                } else {
                  // all items fit on screen
                  totalOffset = 0;
                }
                shouldUpdateOffset = true;
              }
            }

            const itemRelativePos = Math.round((currentItem + 1) * childWidth + parseInt(node.style.left));
            const isItemInView = itemRelativePos > childWidth && itemRelativePos < visibleSize;
            if (!isItemInView && noOfItemsFitOnScreen !== 0) {
              shouldUpdateOffset = true;
            }
          }

          currentItemState.current = currentItem;

          if (shouldUpdateOffset) {
            if (!isNaN(totalOffset)) {
              totalOffset = totalOffset < 0 ? 0 : totalOffset;
            }
            updateOffset(node, -1 * totalOffset);
          }
        },
        [
          currentItem,
          isPeeking,
          peekSize,
          scrollStartIndex,
          scrollToEnd,
          totalItems,
          searchMenuPresent,
          children,
          updateOffset,
        ],
      );

      const contentReference = useCallback(
        (node: HTMLDivElement) => {
          if (!node) {
            return;
          }

          if (isMouseActive) {
            navigateHorizontallyViaMouse(node);
            return;
          }

          navigateViaKeys(node);
        },
        [navigateViaKeys, navigateHorizontallyViaMouse, isMouseActive],
      );

      return (
        <div ref={reference} className={focusClassNames(styles, properties, styles.list, className)}>
          {showMouseNavigation && (
            <HorizontalNavigation
              horizontalVisibleSize={horizontalVisibleSize}
              imageHeight={imageHeight}
              showLeftArrow={showLeftArrow}
              showRightArrow={showRightArrow}
              moveLeft={mouseScrollLeft}
              moveRight={mouseScrollRight}
            />
          )}
          <div className={classNames(styles.contents, contentsClassName)} ref={contentReference}>
            {children}
          </div>
        </div>
      );
    },
  ),
);

HorizontalList.displayName = 'HorizontalList';

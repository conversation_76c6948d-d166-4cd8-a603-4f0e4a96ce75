import { getChildHeight, getChildOffset, getChildWidth, getVisibleHeight } from '@components/List/listHelper';
import { setNavHistoryVerticalOffset } from '@util/NavHistory';
import classNames from 'classnames';
import React, { useCallback, useRef } from 'react';

import { focusClassNames, withFocusableContainer } from '../../focus';
import { IVerticalListProperties } from './types';
import styles from './vertical-list.module.scss';

/**
 * Note that this code has been taken from the previous List component in order to separate out the vertical
 * scrolling logic from the horizontal. This will hopefully make things easier to reason about if we need to
 * modify behaviour here.
 */

export const VerticalList = withFocusableContainer(
  React.forwardRef<HTMLDivElement, IVerticalListProperties>(
    (
      {
        children,
        currentItem = 0,
        peekSize = 0,
        scrollStartIndex = 0,
        scrollToEnd,
        className,
        contentsClassName,
        totalItems = 0,
        initialOffset,
        pageId,
        interacted,
        moveChildOffsetToCenter,
        bypassVisibleSize = false,
        bypassMultiplier,
        bottomPeekSize = 0,
        centerOffsetCorrectionDivider = 3,
        testId,
        ...properties
      },
      reference,
    ) => {
      const elementId = `vertical-list-${Date.now()}`;
      const isPeeking = peekSize > 0 && currentItem > 0;
      const currentItemState = useRef(0);

      const updateOffset = useCallback((node: HTMLDivElement, value: number) => {
        node.style.top = `${Math.ceil(value)}px`;
      }, []);

      const contentReference = useCallback(
        (node: HTMLDivElement) => {
          if (!node) {
            return;
          }

          let visibleSize = getVisibleHeight(elementId);
          if (bypassVisibleSize && bypassMultiplier) {
            visibleSize = bypassMultiplier * getChildHeight(node, 0);
          }
          const childWidth = getChildWidth(node, 0);
          const offsetProperty = 'offsetTop';
          let childOffset = getChildOffset(node, currentItem - scrollStartIndex, offsetProperty);
          if (moveChildOffsetToCenter) {
            childOffset -= Math.ceil(getChildHeight(node, 0) / centerOffsetCorrectionDivider);
          }
          const totalSize = node.scrollHeight;
          const hiddenSize = totalSize - visibleSize;
          const contentOffset = currentItem > scrollStartIndex ? childOffset ?? 0 : 0;
          const offsetAndPeek = contentOffset - (isPeeking ? peekSize : 0);
          let totalOffset: number | undefined = 0;
          if (totalItems && totalItems * childWidth < visibleSize) {
            totalOffset = 0;
          } else if (totalSize < visibleSize) {
            // the content fits in the visible area
            totalOffset = 0;
          } else if (scrollToEnd) {
            totalOffset = offsetAndPeek;
          } else if (currentItem === totalItems - 1 && totalItems > 1) {
            if (visibleSize < totalSize) {
              totalOffset = hiddenSize + peekSize;
            }
          } else {
            totalOffset = offsetAndPeek;
          }

          currentItemState.current = currentItem;
          if (
            totalOffset + visibleSize > totalSize &&
            currentItem !== 0 &&
            (bottomPeekSize !== 0 || bypassVisibleSize === true)
          ) {
            totalOffset = Math.ceil(totalSize - visibleSize + bottomPeekSize);
          }

          if (!interacted && initialOffset !== undefined) {
            // The list has not been yet interacted with and there's an offset remembered from the last interaction
            updateOffset(node, initialOffset);
          } else {
            // Update the scroll position based on a calculated value and store it for future reference
            const newOffset = -1 * (totalOffset ?? 0);
            updateOffset(node, newOffset);
            setNavHistoryVerticalOffset(pageId, newOffset);
          }
        },
        [
          currentItem,
          isPeeking,
          peekSize,
          scrollStartIndex,
          scrollToEnd,
          totalItems,
          updateOffset,
          interacted,
          pageId,
          initialOffset,
          elementId,
          moveChildOffsetToCenter,
          bypassMultiplier,
          bypassVisibleSize,
          bottomPeekSize,
          centerOffsetCorrectionDivider,
        ],
      );

      return (
        <div
          id={elementId}
          data-testid={testId}
          ref={reference}
          className={focusClassNames(styles, properties, styles.list, className)}
        >
          <div className={classNames(styles.contents, contentsClassName)} ref={contentReference}>
            {children}
          </div>
        </div>
      );
    },
  ),
);

VerticalList.displayName = 'VerticalList';

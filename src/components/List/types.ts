import React from 'react';

import { IFocusState } from '../../focus';

export interface IListProperties extends IFocusState {
  children: React.ReactNode;
  /**
   * Padding in px to show before any element except the first.
   * Default: 0
   */
  peekSize?: number;
  /**
   * 0-based index of the current item
   */
  currentItem?: number;
  /**
   * 0-based index of the element where scrolling should start.
   * Default: 0.
   */
  scrollStartIndex?: number;
  /**
   * Whether the list should keep scrolling until its last element or stop scrolling
   * once the last element is visible.
   * Default: true
   */
  scrollToEnd?: boolean;
  className?: string;
  /**
   * A classname of the parent element wrapping the items
   */
  contentsClassName?: string;
  /**
   * the total no of items in the list
   */
  totalItems?: number;
}

export interface IHorizontalListProperties extends IListProperties {
  /**
   * boolean value stating the presence of the search menu
   */
  searchMenuPresent?: boolean;
}

export interface IVerticalListProperties extends IListProperties {
  /**
   * Vertical offset remembered from last time
   */
  initialOffset?: number;
  /**
   * Page id needed to store vertical offset
   */
  pageId?: string;
  /**
   * Whether the list has been scrolled or not
   */
  interacted?: boolean;
  /**
   * This will slightly move the to the center of the visible screen
   */
  moveChildOffsetToCenter?: boolean;
  /**
   * These two values should be used to bypass the total visible size of the list
   * This can be useful when the list is not fully visible and doesn't take the full height of the screen
   * and the focusable items are not fully visible
   */
  bypassVisibleSize?: boolean;
  /**
   * The visible size will be equal to item height multiplied by this value
   */
  bypassMultiplier?: number;

  /**
   * The bottom peek size of the list
   */
  bottomPeekSize?: number;

  /**
   * The center offset correction divider
   * This will divide the height of the visible focused item by this value and will move the list to the center and keep it there
   */
  centerOffsetCorrectionDivider?: number;

  /**
   * test id for automation testing
   */
  testId?: string;
}

import { getScreenHeight, MAIN_MENU_CORRECTION, SEARCH_MENU_CORRECTION } from '@util/resolution';

export function getChildOffset(
  node: HTMLDivElement,
  childIndex: number,
  offsetDirection: 'offsetLeft' | 'offsetTop',
): number {
  return (node.children[childIndex] as HTMLElement)?.[offsetDirection];
}

export function getChildWidth(node: HTMLDivElement, childIndex: number): number {
  const element = node.children[childIndex] as HTMLElement;
  let margin = 0;
  if (element) {
    margin = parseInt(window.getComputedStyle(element).marginRight);
  }
  return Math.floor(element?.offsetWidth + margin);
}

export function getChildrenWidth(node: HTMLDivElement): number {
  let totalWidth = 0;
  for (let i = 0; i < node.children.length; ++i) {
    totalWidth += getChildWidth(node, i);
  }
  return totalWidth;
}

export function getChildHeight(node: HTMLDivElement, childIndex: number): number {
  const element = node.children[childIndex] as HTMLElement;
  let margin = 0;
  if (element) {
    margin = parseInt(window.getComputedStyle(element).marginBottom);
  }
  return Math.floor(element?.offsetHeight + margin);
}

export function getVisibleWidth(node: HTMLDivElement, searchMenuPresent?: boolean): number {
  const resolution = node.offsetWidth < 1600 ? 'low' : 'high';
  const correction = MAIN_MENU_CORRECTION[resolution] + (searchMenuPresent ? SEARCH_MENU_CORRECTION[resolution] : 0);
  return node.offsetWidth - correction;
}

export function getVisibleHeight(elementId: string) {
  const top = document.getElementById(elementId)?.getBoundingClientRect().top;
  return getScreenHeight() - (top || 0);
}

export function getChildImageHeight(node: HTMLDivElement, childIndex: number): number {
  const element = node.children[childIndex] as HTMLElement;
  if (!element) return 0;
  const imageElement = element.querySelector('img');
  if (!imageElement) return 0;
  return imageElement.offsetHeight;
}

import { ITeaserProperties } from '@apptypes/ITeaserProperties';
import { formatTString } from '@util/formatTString';
import { TFunction } from 'i18next';
import { useTranslation } from 'react-i18next';

import styles from './availability.module.scss';

type AvailabilityDate = {
  day: string;
  date: string;
  hour: string;
};

const dayOfWeekToTranslationKey = (dayOfWeek: number): string => {
  const days = new Map<number, string>([
    [0, 'sunday'],
    [1, 'monday'],
    [2, 'tuesday'],
    [3, 'wednesday'],
    [4, 'thursday'],
    [5, 'friday'],
    [6, 'saturday'],
  ]);
  return days.get(dayOfWeek) as string;
};

const parseNextDate = (t: TFunction, broadcastDates: string[] | null): AvailabilityDate | undefined => {
  if (!broadcastDates) return undefined;

  const currentDate = new Date();
  const futureDates = broadcastDates.map((dateStr) => new Date(dateStr)).filter((date) => date > currentDate);

  if (futureDates.length === 0) {
    return undefined;
  }

  const closestDate = new Date(Math.min(...futureDates.map((date) => date.getTime())));

  const day = String(closestDate.getDate()).padStart(2, '0');
  const month = String(closestDate.getMonth() + 1).padStart(2, '0');
  const year = closestDate.getFullYear();

  const hours = closestDate.getHours();
  const minutes = String(closestDate.getMinutes()).padStart(2, '0');

  const dayOfWeek = closestDate.getDay();

  // Returns date in a format matching 'nextBroadcast' translation field
  return {
    day: t(dayOfWeekToTranslationKey(dayOfWeek)),
    date: `${day}/${month}/${year}`,
    hour: `${hours}:${minutes}`,
  };
};

export const Availability = (properties: ITeaserProperties) => {
  const { broadcastDates, availability } = properties;
  const { t } = useTranslation();

  const nextDate = parseNextDate(t, broadcastDates);

  return (
    <div className={styles.availability}>
      {availability?.label && <span>{availability?.label}</span>}
      {nextDate && <span>{formatTString(t('nextBroadcast'), nextDate)}</span>}
    </div>
  );
};

import React, { ButtonHTMLAttributes, Children } from 'react';

import { focusClassNames, IFocusState, withFocusable } from '../../focus';
import styles from './underline-button.module.scss';

interface IUnderlineButton extends ButtonHTMLAttributes<HTMLButtonElement>, IFocusState {
  label: string;
  onEnterPress: () => void;
  testIdFocused?: boolean;
}

export const UnderlineButton = withFocusable(
  React.forwardRef<HTMLButtonElement, IUnderlineButton>(
    ({ label, children, testIdFocused, ...properties }, reference) => {
      return (
        <button
          ref={reference}
          data-testid-focused={testIdFocused ? 'true' : undefined}
          onClick={properties.onEnterPress}
          className={focusClassNames(styles, properties, styles.button)}
        >
          <span className={styles.label}>{label}</span>
          {Children.map(children, (child) => (
            <span>{child}</span>
          ))}
        </button>
      );
    },
  ),
);

import { PaginatedTeaserList } from '@components/TeaserList/PaginatedTeaserList';
import { SINGLE_TEASER_TEMPLATES, THEMES } from '@constants';
import { FocusHandler } from '@noriginmedia/norigin-spatial-navigation/dist/useFocusable';
import { PageContext } from '@providers/PageContext';
import { zoneHasPagination } from '@util/zones';
import { useContext } from 'react';

import { Zone } from '../../types';
import { BookmarksTeaserList } from '../TeaserList/BookmarksTeaserList';
import { TeaserList, TeaserListProperties } from '../TeaserList/TeaserList';

const getTeasers = (zone: Zone) =>
  SINGLE_TEASER_TEMPLATES.includes(zone.template) ? zone.teaserList.slice(0, 1) : zone.teaserList;

const getTitle = (zone: Zone) => {
  if (SINGLE_TEASER_TEMPLATES.includes(zone.template)) return '';

  return zone.showZoneTitle ? zone.title : '';
};

const getTheme = (zone: Zone) => {
  const zoneTheme = zone?.theme;

  return (zoneTheme && THEMES[zoneTheme.toUpperCase()]) || undefined;
};

const getTeaserListComponent = (zone: Zone): ((props: TeaserListProperties) => JSX.Element) => {
  switch (zone.authenticatedContent) {
    case 'sso-favorites':
      return BookmarksTeaserList;
    case 'sso-personalzone':
    case 'sso-history':
      return TeaserList;
    default:
      return zoneHasPagination(zone) ? PaginatedTeaserList : TeaserList;
  }
};

export interface IZoneFactory {
  zone: Zone;
  onFocus: FocusHandler;
  index: number;
  last: boolean;
  pageId: string;
  focusOnMount: boolean;
  searchMenuPresent?: boolean;
}

export const ZoneFactory = ({ onFocus, zone, index, last, pageId, focusOnMount, searchMenuPresent }: IZoneFactory) => {
  const { listFocusedOnMount, minListLengthForMoreLink } = useContext(PageContext);

  const TeaserListComponent = getTeaserListComponent(zone);
  const link = minListLengthForMoreLink && zone.teaserList.length >= minListLengthForMoreLink ? zone?.link : undefined;

  return (
    <TeaserListComponent
      title={getTitle(zone)}
      link={link}
      teasers={getTeasers(zone)}
      theme={getTheme(zone)}
      onFocus={onFocus}
      template={zone.template}
      focusOnMount={listFocusedOnMount && focusOnMount}
      zoneIndex={index}
      lastZone={last}
      pageId={pageId}
      zoneId={zone.id}
      authenticatedContent={zone.authenticatedContent}
      searchMenuPresent={searchMenuPresent}
      zone={zone}
    />
  );
};

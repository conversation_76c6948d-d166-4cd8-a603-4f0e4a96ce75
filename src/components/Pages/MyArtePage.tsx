import { PageResponseBody } from '@apptypes/PageResponseBody';
import { useLogout } from '@features/usercontent/hooks/useLogout';
import { useCustomNavigate } from '@hooks/useCustomNavigate';
import { useNormalizedParams } from '@hooks/useNormalizedParams';
import { useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import { buildPageId } from '@util/buildPageId';
import { getHorizontalNavIndex, getVerticalNavIndex } from '@util/NavHistory';
import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useRemoteController } from 'react-remote-controller';
import { useLoaderData } from 'react-router-dom';
import { getKeyMap } from 'target';

import { ROUTES } from '../../constants';
import { FOCUS_KEY_PRIMARY, teaserFocusKey } from '../../focus';
import { Button } from '../Button/Button';
import { ContentPage } from './ContentPage';
import styles from './my-arte-page.module.scss';

const MyArtePage = () => {
  const { zones, title } = useLoaderData() as PageResponseBody;
  const routeParams = useNormalizedParams();
  const { setFocus } = useFocusable();
  const { t } = useTranslation();
  const navigate = useCustomNavigate();
  const pageId = buildPageId(routeParams, zones);
  const { logout } = useLogout();
  const [localVerticalIndex, setLocalVerticalIndex] = useState<number>(1);
  const doFocus = useCallback(() => {
    const verticalNavIndex = getVerticalNavIndex(pageId);
    const zone = zones[verticalNavIndex];
    const horizontalNavIndex = getHorizontalNavIndex(pageId, zone.id);
    setFocus(teaserFocusKey(verticalNavIndex, horizontalNavIndex));
  }, [pageId, setFocus, zones]);

  useEffect(() => {
    const timeoutId = setTimeout(doFocus, 50);

    return () => clearTimeout(timeoutId);
  }, [pageId, doFocus]);

  const onFocusCallback = useCallback(() => {
    if (zones?.length > 0 && localVerticalIndex !== 0) {
      doFocus();
    }
  }, [doFocus, localVerticalIndex, zones]);

  const upController = useCallback(() => {
    setLocalVerticalIndex((prev) => prev - 1);
  }, [setLocalVerticalIndex]);

  const downController = useCallback(() => {
    setLocalVerticalIndex(Math.min(localVerticalIndex + 1, zones.length));
  }, [zones, setLocalVerticalIndex, localVerticalIndex]);

  useRemoteController<ReturnType<typeof getKeyMap>>({
    listenTo: {
      up: upController,
      down: downController,
    },
  });

  useEffect(() => {
    if (zones?.length > 0) {
      if (localVerticalIndex === 0) {
        setFocus(FOCUS_KEY_PRIMARY);
        return;
      }
    }
  }, [localVerticalIndex, setFocus, zones]);

  return (
    <div className={styles['my-arte-page']}>
      <div>
        <h1 className={styles.title}>{title}</h1>
        <Button
          label={t('logout')}
          className={styles.logout}
          focusKey={FOCUS_KEY_PRIMARY}
          focusOnMount={zones?.length === 0}
          onFocus={onFocusCallback}
          onEnterPress={() => {
            logout();

            navigate(ROUTES.MYARTE.LOGIN, { replace: true });
          }}
          onArrowPress={(direction) => {
            switch (direction) {
              case 'left':
                return true;
              case 'down':
                if (zones?.length > 0) {
                  doFocus();
                }
                return false;

              default:
                return false;
            }
          }}
        />
      </div>
      {zones && <ContentPage focusKey="focus-key-my-arte-list" className={styles.zones} />}
    </div>
  );
};

export { MyArtePage };

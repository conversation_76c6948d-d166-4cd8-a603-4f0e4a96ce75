import { PageResponseBody } from '@apptypes/PageResponseBody';
import { Button } from '@components/Button/Button';
import { TextModal } from '@components/Modal/TextModal/TextModal';
import { PAGE_IDS } from '@constants';
import { clearHistory } from '@features/usercontent/userContentData';
import { useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import { useModalContext } from '@providers/ModalContext';
import { deletePage, setGridFocusedItem } from '@util/NavHistory';
import classNames from 'classnames';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLoaderData } from 'react-router-dom';

import { FOCUS_KEY_PURGE_HISTORY_BUTTON, myHistoryPageFocusGuard, teaserFocusKey } from '../../../focus';
import { GridPage } from '../GridPage';
import styles from './my-history-page.module.scss';

const ROW_LENGTH = 4;

export const MyHistoryPage = () => {
  const pageId = PAGE_IDS.MY_HISTORY;

  const { t } = useTranslation();
  const { zones } = useLoaderData() as PageResponseBody;
  const { hideModal, showModal } = useModalContext();
  const { setFocus } = useFocusable();

  const [index, setIndex] = useState<number | undefined>();
  const [teaserList, setTeaserList] = useState(zones?.[0]?.teaserList || []);

  const hasHistory = teaserList.length > 0;

  useEffect(() => {
    setGridFocusedItem(pageId, index || 0);
  }, [index, pageId]);

  return (
    <>
      {hasHistory && (
        <Button
          label={t('myHistory__purgehistory_cta')}
          focusKey={FOCUS_KEY_PURGE_HISTORY_BUTTON}
          className={classNames(styles['purge-button'], { [styles['hidden']]: index && index >= ROW_LENGTH })}
          onArrowPress={(direction) => {
            switch (direction) {
              case 'down':
                return true;
              default:
                return false;
            }
          }}
          onEnterPress={() =>
            showModal({
              content: (
                <TextModal
                  headline={t('myHistory__confirmation_popup_title')}
                  buttons={
                    <>
                      <Button
                        label={t('yes')}
                        onEnterPress={() => {
                          deletePage(PAGE_IDS.MY_VIDEOS);
                          clearHistory();
                          setTeaserList([]);
                          hideModal();
                          setFocus(teaserFocusKey(0, 0));
                        }}
                        focusOnMount
                      />
                      <Button
                        label={t('cancel')}
                        onEnterPress={() => {
                          hideModal();
                          setFocus(FOCUS_KEY_PURGE_HISTORY_BUTTON);
                        }}
                      />
                    </>
                  }
                  returnFocusKey={FOCUS_KEY_PURGE_HISTORY_BUTTON}
                />
              ),
            })
          }
        />
      )}
      <GridPage
        list={teaserList}
        title={t('my_videos')}
        setIndex={setIndex}
        focusGuard={myHistoryPageFocusGuard}
        pageId={pageId}
      />
    </>
  );
};

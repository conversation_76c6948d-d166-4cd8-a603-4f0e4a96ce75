import { ITeaserResponse } from '@apptypes/ITeaserResponse';
import { PageResponseBody } from '@apptypes/PageResponseBody';
import { DefaultInitialResults, IInitialResults, IPageResults } from '@apptypes/pagination';
import { GridView } from '@components/GridView/GridView';
import { getNextZonePage } from '@data/source';
import { usePagination } from '@features/pagination/usePagination';
import { getUserContent } from '@features/usercontent/userContentData';
import { UseFocusableResult } from '@noriginmedia/norigin-spatial-navigation';
import { getStoredPaginationResult, processNewPageLoad } from '@util/NavHistory';
import { useEffect, useState } from 'react';
import { useLoaderData } from 'react-router-dom';

import { FOCUS_KEY_PRIMARY, gridPageFocusGuard } from '../../focus';
import styles from './grid-page.module.scss';
import { PageTitle } from './PageTitle/PageTitle';

interface IGridPageProperties {
  list?: ITeaserResponse[];
  resultsRowLength?: number;
  title?: string;
  pageId?: string;
  showDescription?: boolean;
  setIndex?: (index?: number) => void;
  focusGuard?: (
    teaser: ITeaserResponse,
    direction: string,
    index: number,
    rowLength: number,
    setFocus: UseFocusableResult['setFocus'],
  ) => boolean;
}

export const GridPage = ({
  list,
  resultsRowLength = 4,
  title,
  showDescription = false,
  setIndex,
  pageId,
  focusGuard = gridPageFocusGuard,
}: IGridPageProperties) => {
  const { description, zones, title: pageResponseTitle } = useLoaderData() as PageResponseBody;
  const [currentGridItemIndex, setCurrentGridItemIndex] = useState<number | undefined>();
  const { showZoneTitle, title: zoneTitle } = zones?.[0] || {};
  const pageTitle = title || pageResponseTitle;
  const showTitle = pageTitle && (pageTitle !== zoneTitle || !showZoneTitle);
  const fetchPage = zones?.[0]?.authenticatedContent ? getUserContent : getNextZonePage;

  const {
    initialResults,
    pageResults,
    fetchNextPage,
    setInitialResults,
    busyFetchingPage,
    setPageId,
    setCurrentPaginationPage,
    setPageResults,
  } = usePagination<IPageResults, IInitialResults>(fetchPage, DefaultInitialResults);

  // Get teaser list from a parent component or from a loader
  let teaserList = list || zones?.[0].teaserList;

  if (!teaserList?.length) {
    teaserList = [
      {
        authenticatedContent: zones?.[0].authenticatedContent,
        image: 'http://foo.bar', // any url
        template: zones?.[0].template,
        emptyState: true,
      },
    ];
  }

  useEffect(() => {
    const {
      template,
      showItemTitle = false,
      showZoneTitle,
      title,
      meta,
      authenticatedContent,
      pages,
    } = zones?.[0] || {};
    const id = authenticatedContent || zones?.[0].id;

    setInitialResults({
      results: teaserList,
      template,
      showItemTitle,
      title: showZoneTitle ? title : '',
      pages: pages || meta?.pages,
      id: id,
    });
  }, [setInitialResults, teaserList, zones]);

  useEffect(() => {
    if (pageId) {
      processNewPageLoad(pageId, teaserList);
    }
  }, [pageId, teaserList]);

  const TitleComponent = showDescription ? (
    <PageTitle title={pageTitle} description={description} />
  ) : (
    <h1 className={styles.title}>{pageTitle}</h1>
  );

  useEffect(() => {
    if (pageId) {
      setPageId(pageId);
      const currentPage = getStoredPaginationResult(pageId);
      currentPage?.paginationCurrentPage && setCurrentPaginationPage(currentPage.paginationCurrentPage);
      currentPage?.paginationData && setPageResults(currentPage?.paginationData);
    }
  }, [pageId, setCurrentPaginationPage, setPageId, setPageResults]);

  return (
    <div>
      {showTitle && TitleComponent}
      <GridView
        initialResults={initialResults}
        fetchNextPage={fetchNextPage}
        busyFetchingPage={busyFetchingPage}
        pageResults={pageResults}
        setCurrentGridItemIndex={setIndex || setCurrentGridItemIndex}
        currentGridItemIndex={currentGridItemIndex}
        resultsRowLength={resultsRowLength}
        focusKey={FOCUS_KEY_PRIMARY}
        focusOnMount={true}
        focusGuard={focusGuard}
      />
    </div>
  );
};

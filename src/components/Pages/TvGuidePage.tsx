import { useMouseWheel } from '@components/MouseNavigation/useMouseWheel';
import { setVerticalScrollIndex } from '@components/MouseNavigation/verticalScrollIndex';
import { useCustomNavigate } from '@hooks/useCustomNavigate';
import useTimeout from '@hooks/useTimeout';
import { useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import { MouseContext } from '@providers/MouseProvider';
import { getPage, storeGuidePageLoad } from '@util/NavHistory';
import { handleOnFocus } from '@util/navigation';
import { useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { useLoaderData } from 'react-router-dom';

import { ROUTES } from '../../constants';
import {
  FOCUS_KEY_PRIMARY,
  FOCUS_KEY_SIDE_MENU,
  FOCUS_TV_GUIDE_PROGRAM,
  teaserFocusKey,
  tvGuideMenuFocusKey,
} from '../../focus';
import { ITvGuideTeaserProperties, TvGuideResponseBody } from '../../types';
import { TvGuideListItem, TvGuideProgram } from '../TvGuide';
import { TvGuideMenu } from '../TvGuide/TvGuideMenu';
import styles from './tv-guide-page.module.scss';
interface ITvGuideLoaderData {
  tvGuidePage?: TvGuideResponseBody;
  tvGuideDayData?: ITvGuideTeaserProperties[];
  tvGuideDay?: ITvGuideTeaserProperties[];
  activeDay: number;
  primeTeaserIndex?: number;
  liveTeaserIndex?: number;
  error?: Error;
}

const TV_GUIDE_MENU_BYPASS_MULTIPLIER = 6;

const TvGuidePage = () => {
  const { tvGuidePage, tvGuideDayData, activeDay, primeTeaserIndex, liveTeaserIndex, error } =
    useLoaderData() as ITvGuideLoaderData;
  const [showComponent, setShowComponent] = useState(false);
  const items = tvGuidePage?.items;
  const [selectedItemIndex, setSelectedItemIndex] = useState(20);
  const { setFocus } = useFocusable();
  const navigate = useCustomNavigate();
  const { isMouseActive } = useContext(MouseContext);
  useMouseWheel();

  /**
   * This will show the guide program with a small delay to protect against flickering on slow device
   * Due to slow rendering the list would take a bit of time to render and the focus would move from initial 0 position quite noticeably
   * Ticket: https://artetv.atlassian.net/browse/TVAPPS-1172
   */
  useTimeout(() => {
    setShowComponent(true);
  }, 300);

  const getTeaserFocusKey = useCallback(() => {
    const currentPage = getPage('TV_GUIDE');
    if (currentPage && currentPage?.currentDay === activeDay) {
      return teaserFocusKey(activeDay, currentPage.verticalIndex);
    }

    const teaserIndex = liveTeaserIndex && liveTeaserIndex > 0 ? liveTeaserIndex : primeTeaserIndex;
    return teaserFocusKey(activeDay, teaserIndex || 0);
  }, [activeDay, liveTeaserIndex, primeTeaserIndex]);

  useEffect(() => {
    setFocus(getTeaserFocusKey());
  }, [setFocus, getTeaserFocusKey]);

  useEffect(
    function onTvGuideMenuIndexChanged() {
      setVerticalScrollIndex(selectedItemIndex);
    },
    [selectedItemIndex],
  );

  useEffect(() => {
    error && navigate(ROUTES.ROOT);
  }, [error, navigate]);

  useEffect(() => {
    const currentPage = getPage('TV_GUIDE');
    if (!currentPage) {
      storeGuidePageLoad(`TV_GUIDE`, 0, activeDay);
    } else {
      if (currentPage.currentDay !== activeDay) {
        storeGuidePageLoad(`TV_GUIDE`, 0, activeDay);
      }
    }
  }, [activeDay]);

  const days = useMemo(() => {
    return items?.map((item, index) => (
      <TvGuideListItem
        {...item}
        key={`${item.date}-${item.day}-${item.month}`}
        focusKey={tvGuideMenuFocusKey(index)}
        onFocus={(layout, props, focusDetails) => handleOnFocus(focusDetails, index, setSelectedItemIndex)}
        focusOnMount={index === activeDay}
        onEnterPress={() => {
          navigate(`${ROUTES.TV_GUIDE}/${selectedItemIndex}`);
        }}
        onClick={(event) => {
          event.preventDefault();
          if (index !== selectedItemIndex) navigate(`${ROUTES.TV_GUIDE}/${index}`);
        }}
        isActive={index === activeDay}
        onArrowPress={(direction) => {
          switch (direction) {
            case 'right':
              setFocus(getTeaserFocusKey());
              return false;
            case 'down':
              return true;
            case 'left':
              // before focus is sent to side menu, ensure we reset the focus of this
              // list back to the active day, without this, when navigating back to this
              // list, we can have a state where a non-active item has focus
              setFocus(tvGuideMenuFocusKey(activeDay));
              // now focus the menu
              setFocus(FOCUS_KEY_SIDE_MENU);
              return false;
            case 'up':
              switch (index) {
                case 0:
                  return false;
                default:
                  return true;
              }
            default:
              return false;
          }
        }}
      />
    ));
  }, [activeDay, items, navigate, selectedItemIndex, setFocus, getTeaserFocusKey]);

  /**
   * the active day in the menu can be scrolled out of view and focus can then be lost
   * to another component e.g. main menu or tv guide teaser list
   * when receiving focus again, ensure the list item for the currently active day is in view
   */
  function onTvGuideMenuFocus() {
    if (activeDay !== selectedItemIndex) setSelectedItemIndex(activeDay);
  }

  return (
    !error && (
      <div className={styles['tv-guide-page-wrapper']}>
        <div className={styles['tv-guide-menu']}>
          <div
            onMouseEnter={() => {
              isMouseActive && setFocus(FOCUS_KEY_PRIMARY);
            }}
          >
            <TvGuideMenu
              days={days ?? []}
              selectedItemIndex={selectedItemIndex}
              upArrowVisibility={selectedItemIndex > TV_GUIDE_MENU_BYPASS_MULTIPLIER / 2 - 1}
              downArrowVisibility={!!(items && selectedItemIndex !== items.length - 1)}
              onFocus={onTvGuideMenuFocus}
              bypassMultiplier={TV_GUIDE_MENU_BYPASS_MULTIPLIER}
            />
          </div>
          <div
            className={styles['tv-guide-program']}
            onMouseEnter={() => {
              isMouseActive && setFocus(FOCUS_TV_GUIDE_PROGRAM);
            }}
          >
            {tvGuideDayData && (
              <TvGuideProgram index={activeDay} tvGuideDayData={tvGuideDayData} showComponent={showComponent} />
            )}
          </div>
        </div>
      </div>
    )
  );
};

export { TvGuidePage };

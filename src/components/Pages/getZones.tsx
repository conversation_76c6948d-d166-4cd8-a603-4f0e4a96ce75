import { Zone } from '@apptypes/PageResponseBody';
import { VALID_ZONE_TEMPLATES } from '@constants';
import { Tracking } from '@tracking/Tracking';
import { buildPageId } from '@util/buildPageId';
import { handleOnFocus } from '@util/navigation';
import { Dispatch, SetStateAction } from 'react';
import { Params } from 'react-router-dom';

import { IZoneFactory, ZoneFactory } from './ZoneFactory';

interface IGetZonesParams extends Pick<IZoneFactory, 'pageId' | 'searchMenuPresent'> {
  routeParams: Readonly<Params<string>>;
  setSelectedItemIndex: Dispatch<SetStateAction<number>>;
  indexToFocusOnMount: number;
  zones: Zone[];
}

const getZones = ({
  pageId,
  routeParams,
  setSelectedItemIndex,
  zones,
  indexToFocusOnMount,
  searchMenuPresent,
}: IGetZonesParams) => {
  let delayedPageId = pageId;
  if (!delayedPageId) delayedPageId = buildPageId(routeParams, zones);

  const filteredZones = zones.filter((zone) => VALID_ZONE_TEMPLATES.includes(zone.template));

  Tracking.setZonesInUsage(filteredZones);

  return filteredZones.map((zone, index) => (
    <ZoneFactory
      key={zone.id}
      zone={zone}
      pageId={delayedPageId}
      last={index === filteredZones.length - 1}
      onFocus={(layout, props, focusDetails) => {
        handleOnFocus(focusDetails, index, setSelectedItemIndex);
      }}
      index={index}
      focusOnMount={indexToFocusOnMount === index}
      searchMenuPresent={searchMenuPresent}
    />
  ));
};

export { getZones };

import TaDa from '@assets/img/icon/tada.png';
import NoUserIcon from '@assets/img/no-user-icon.png';
import { SampleEventTeaser } from '@components/Teaser/SampleEventTeaser/SampleEventTeaser';
import { ROUTES, TEST_ID } from '@constants';
import { useLogout } from '@features/usercontent/hooks/useLogout';
import { useCustomNavigate } from '@hooks/useCustomNavigate';
import { GlobalContext } from '@providers/GlobalContextProvider';
import { useContext } from 'react';
import { useTranslation } from 'react-i18next';

import { FOCUS_KEY_PRIMARY } from '../../../focus';
import { Button } from '../../Button/Button';
import styles from './myaccount.module.scss';

const MyAccount = () => {
  const { userData } = useContext(GlobalContext);
  const userContent = userData?.data[0];

  const avatarImages = userContent?.avatar?.images;
  const { t } = useTranslation();
  const navigate = useCustomNavigate();
  const { logout } = useLogout();
  const avatar = avatarImages?.find((item) => item?.w === '200');

  return (
    <div className={styles['my-account-page']}>
      <div className={styles['top-wrapper']}>
        <div className={styles.avatarMask}>
          <img src={avatar?.url || NoUserIcon} className={styles.avatar} alt={'avatar'} />
        </div>
        <div className={styles.userDetails}>
          <h2 className={styles.title}>{userContent?.email}</h2>
          {t('my_account__loggedin_confirmation') && (
            <p>
              {t('my_account__loggedin_confirmation')} <img src={TaDa} className={styles.tada} alt={'tada'} />
            </p>
          )}
          <Button
            label={t('logout')}
            testId={TEST_ID.AUTH.LOGOUT}
            className={styles.logout}
            focusKey={FOCUS_KEY_PRIMARY}
            focusOnMount={true}
            onEnterPress={() => {
              logout();

              navigate(ROUTES.MYARTE.LOGIN, { replace: true });
            }}
            onArrowPress={(direction) => {
              switch (direction) {
                case 'left':
                  return true;
                case 'down':
                  return true;

                default:
                  return false;
              }
            }}
          />
        </div>
      </div>
      {/*hardcoding an event teaser*/}
      <SampleEventTeaser />
    </div>
  );
};

export { MyAccount };

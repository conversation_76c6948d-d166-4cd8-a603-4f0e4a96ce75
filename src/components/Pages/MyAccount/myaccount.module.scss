@use '~styles/globals' as *;

.my-account-page {
  padding-top: px-to-rem(80);
  margin-left: px-to-rem(50);
}

.top-wrapper {
  overflow: hidden;
}

.title {
  display: block;
  font-size: px-to-rem(55);
  line-height: px-to-rem(55);
  font-family: $font-family-bold;
}

.my-account-page p {
  font-size: px-to-rem(32);
  margin-top: px-to-rem(20);
}

.logout {
  margin-top: px-to-rem(60);
}

.zones {
  margin-top: px-to-rem(50);
  overflow: hidden;
}

.avatarMask {
  width: px-to-rem(210);
  height: px-to-rem(210);
  border-radius: 50%;
  overflow: hidden;
  float: left;
}

.avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.userDetails {
  float: left;
  margin-left: px-to-rem(50);
}

.tada {
  margin-left: px-to-rem(5);
  width: px-to-rem(32);
  height: px-to-rem(32);
}

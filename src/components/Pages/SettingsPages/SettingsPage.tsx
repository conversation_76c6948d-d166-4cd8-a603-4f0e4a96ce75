import { useMouseWheel } from '@components/MouseNavigation/useMouseWheel';
import { setVerticalScrollIndex } from '@components/MouseNavigation/verticalScrollIndex';
import { useCustomNavigate } from '@hooks/useCustomNavigate';
import { useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import { useEffect, useState } from 'react';
import { Outlet, useLoaderData, useLocation } from 'react-router-dom';

import { ROUTES } from '../../../constants';
import { FOCUS_KEY_PRIMARY, FOCUS_KEY_SIDE_MENU, settingsMenuFocusKey } from '../../../focus/';
import { SettingsResponseBody } from '../../../types';
import { SettingsMenu } from '../../Settings/SettingsMenu';
import { SettingsMenuListItem } from '../../Settings/SettingsMenuListItem';
import { NavButton } from './NavButton';
import styles from './settings-page.module.scss';

function getSettingsRoute(key: string) {
  return ROUTES.SETTINGS[key.toUpperCase() as keyof typeof ROUTES.SETTINGS];
}

function SettingsPage() {
  const { settingsMenuItems, routes } = useLoaderData() as SettingsResponseBody;
  const location = useLocation();
  const route = location.pathname.split('/').pop() || '';
  const initialActiveItemIndex = Math.max(routes.indexOf(route), 0);
  const [activeItemIndex, setActiveItemIndex] = useState(initialActiveItemIndex);
  const navigate = useCustomNavigate();
  const { setFocus } = useFocusable();
  useMouseWheel();
  setVerticalScrollIndex(activeItemIndex);

  /**
   * update activeItemIndex on route change, this allows
   * the menu state to be accurate during back journeys
   */
  useEffect(() => {
    setActiveItemIndex(location.state?.activeItemIndex || 0);
  }, [location, activeItemIndex]);

  const menuItems = settingsMenuItems.map((menuItem, index) => (
    <SettingsMenuListItem
      testId={`settings-nav-${routes[index]}`}
      menuItem={menuItem}
      key={menuItem}
      focusKey={settingsMenuFocusKey(index)}
      onEnterPress={() => {
        navigate(getSettingsRoute(routes[index]), {
          state: { activeItemIndex: index },
        });
        setFocus(FOCUS_KEY_PRIMARY);
      }}
      isActive={index === activeItemIndex}
      onArrowPress={(direction) => {
        switch (direction) {
          case 'right':
            setFocus(FOCUS_KEY_PRIMARY);
            return false;
          case 'down':
            switch (index) {
              case routes.length - 1:
                return false;
              default:
                return true;
            }
          case 'left':
            setFocus(FOCUS_KEY_SIDE_MENU);
            return false;
          case 'up':
            switch (index) {
              case 0:
                return false;
              default:
                return true;
            }
          default:
            return false;
        }
      }}
    />
  ));

  return (
    <div className={styles['settings-page-wrapper']}>
      <SettingsMenu menuItems={menuItems} activeItemIndex={activeItemIndex} />
      <div className={styles['content-wrapper']}>
        <div className={styles['settings-arrow-up']}>
          <NavButton
            direction="up"
            label={activeItemIndex > 0 ? settingsMenuItems[activeItemIndex - 1] : ''}
            onFocus={() => {
              if (activeItemIndex === 0) {
                // NOTE navigating up here when activeItemIndex === 0 means we loose focus (not sure why?)
                // to prevent this set focus back to the child page
                setFocus(FOCUS_KEY_PRIMARY);
                return;
              }
              const idx = activeItemIndex - 1;
              navigate(getSettingsRoute(routes[idx]), {
                state: { activeItemIndex: idx },
              });
            }}
          ></NavButton>
        </div>
        <div className={styles['settings-pages']}>
          <Outlet />
        </div>
        <div className={styles['settings-arrow-down']}>
          {activeItemIndex < routes.length - 1 && (
            <NavButton
              direction="down"
              label={activeItemIndex + 1 > routes.length ? '' : settingsMenuItems[activeItemIndex + 1]}
              onFocus={() => {
                const idx = activeItemIndex + 1;
                navigate(getSettingsRoute(routes[idx]), {
                  state: { activeItemIndex: idx },
                });
              }}
            ></NavButton>
          )}
        </div>
      </div>
    </div>
  );
}

export { SettingsPage };

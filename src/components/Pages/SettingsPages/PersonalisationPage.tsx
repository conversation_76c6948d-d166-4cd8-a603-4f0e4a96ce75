import { underlineWithToggleFocusGuard } from '@components/UnderlineButton/underlineWithToggleFocusGuard';
import { usePersonalisation } from '@features/usercontent/hooks/usePersonalisation';
import { t } from 'i18next';
import React, { useState } from 'react';

import { FOCUS_KEY_PRIMARY, withFocusable } from '../../../focus';
import { acceptPersonalisationCookies, hasPersonalisationCookieConsent } from '../../../util/cookies';
import { Toggle } from '../../Toggle/Toggle';
import { UnderlineButton } from '../../UnderlineButton/UnderlineButton';
import styles from './personalisation-page.module.scss';

const PersonalisationPage = withFocusable(
  React.forwardRef<HTMLDivElement>(({}, reference) => {
    const { disablePersonalisation } = usePersonalisation();
    const [isCookieActive, setIsCookieActive] = useState<boolean>(hasPersonalisationCookieConsent());

    function togglePersonalisationCookie() {
      if (isCookieActive) {
        disablePersonalisation();
      } else {
        acceptPersonalisationCookies();
      }

      setIsCookieActive(!isCookieActive);
    }

    return (
      <div className={styles.wrapper} ref={reference}>
        <UnderlineButton
          label={t('cookies__personalisation')}
          focusOnMount
          focusKey={FOCUS_KEY_PRIMARY}
          onEnterPress={togglePersonalisationCookie}
          onArrowPress={underlineWithToggleFocusGuard}
        >
          <Toggle on={isCookieActive} />
        </UnderlineButton>
        <p>{t('cookies__personalisation_desc')}</p>
      </div>
    );
  }),
);

export { PersonalisationPage };

import { underlineWithToggleFocusGuard } from '@components/UnderlineButton/underlineWithToggleFocusGuard';
import {
  acceptCookies,
  acceptTechnicalCookies,
  hasCookieConsent,
  hasTechnicalCookieConsent,
  rejectCookies,
  rejectTechnicalCookies,
} from '@util/cookies';
import React, { useState } from 'react';

import { FOCUS_KEY_PRIMARY, withFocusableContainer } from '../../../../focus';
import i18n from '../../../../i18n';
import { Toggle } from '../../../Toggle/Toggle';
import { UnderlineButton } from '../../../UnderlineButton/UnderlineButton';
import styles from './privacy.module.scss';

export const PrivacyPage = withFocusableContainer(
  React.forwardRef<HTMLDivElement>(({}, reference) => {
    const [isAudienceCookieActive, setIsAudienceCookieActive] = useState<boolean>(hasCookieConsent());
    const [isTechnicalCookieActive, setIsTechnicalCookieActive] = useState<boolean>(hasTechnicalCookieConsent());

    function toggleAudienceCookie() {
      isAudienceCookieActive ? rejectCookies() : acceptCookies();
      setIsAudienceCookieActive(!isAudienceCookieActive);
    }

    function toggleTechnicalCookie() {
      isTechnicalCookieActive ? rejectTechnicalCookies() : acceptTechnicalCookies();
      setIsTechnicalCookieActive(!isTechnicalCookieActive);
    }

    return (
      <div className={styles.wrapper} ref={reference}>
        <UnderlineButton
          label={i18n.t('cookies__audience')}
          focusOnMount
          focusKey={FOCUS_KEY_PRIMARY}
          onEnterPress={toggleAudienceCookie}
          onArrowPress={underlineWithToggleFocusGuard}
        >
          <Toggle on={isAudienceCookieActive} />
        </UnderlineButton>

        <p>{i18n.t('cookies__audience_desc')}</p>
        <UnderlineButton
          label={i18n.t('cookies__technical')}
          onEnterPress={toggleTechnicalCookie}
          onArrowPress={underlineWithToggleFocusGuard}
        >
          <Toggle on={isTechnicalCookieActive} />
        </UnderlineButton>
        <p>{i18n.t('cookies__technical_desc')}</p>
      </div>
    );
  }),
);

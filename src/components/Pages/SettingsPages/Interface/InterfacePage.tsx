import { GridLayout } from '@components/GridLayout/GridLayout';
import { defaultRelaunchQueryParams } from '@features/queryParams/relaunchQueryParams';
import { setLanguage } from '@util/cookies';
import i18n from 'i18next';
import React, { useState } from 'react';
import { reloadApp, showMessageOnReload } from 'target';

import { defaultFocusGuard, FOCUS_KEY_PRIMARY, withFocusableContainer } from '../../../../focus';
import { SUPPORTED_LANGUAGES } from '../../../../i18n';
import { TickButton } from '../../../TickButton/TickButton';
import styles from './interface.module.scss';

const RELOAD_TIMEOUT = 300;

export const InterfacePage = withFocusableContainer(
  React.forwardRef<HTMLDivElement>(({}, reference) => {
    const [showMessage, setShowMessage] = useState<boolean>(false);

    /**
     * for some older devices we have seen issues when changing the
     * language and reloading the app, there can be long delays between
     * invoking the language change and the app starting to reload.
     *
     * in this case we render a message to indicate the app is reloading,
     * it is necessary to add a timeout, without it the message may not render
     *
     * note that a spinner was tried but it will not animate as the teardown
     * during reload appears to affect any further rendering
     */
    function handleReloadWithMessage() {
      setShowMessage(true);
      // no need to cleanup timeoutId here as app will reload
      setTimeout(handleReload, RELOAD_TIMEOUT);
    }

    function handleReload() {
      reloadApp(defaultRelaunchQueryParams().toString());
    }

    function handleLanguageChange(languageCode: string) {
      if (languageCode === i18n.language) return;
      setLanguage(languageCode);
      showMessageOnReload() ? handleReloadWithMessage() : handleReload();
    }

    return (
      <div className={styles.wrapper} ref={reference}>
        <h2>{i18n.t('languages')}</h2>
        <GridLayout rowLength={4}>
          {Array.from(SUPPORTED_LANGUAGES).map(([code, translation], index) => (
            <TickButton
              onArrowPress={defaultFocusGuard}
              onEnterPress={() => handleLanguageChange(code)}
              focusOnMount={index === 0}
              focusKey={index === 0 ? FOCUS_KEY_PRIMARY : ''}
              label={i18n.t(translation.translationKey)}
              key={code}
              isActive={code === i18n.language}
            />
          ))}
        </GridLayout>
        {showMessage && <p>{i18n.t('alert__message_interface_loading')}...</p>}
      </div>
    );
  }),
);

@use '~styles/globals' as *;

$content-width: px-to-rem(1920) - $menu-width-collapsed - $settings-menu-width;

.settings-page-wrapper {
  height: 100%;
}

.content-wrapper {
  position: absolute;
  left: $settings-menu-width;
  top: 0;
  width: $content-width;
  height: 100%;
}

.settings-pages {
  position: absolute;
  padding: 0 px-to-rem(48);
  top: px-to-rem(150);
  bottom: px-to-rem(150);
  width: 100%;
}

.settings-arrow-up,
.settings-arrow-down {
  position: absolute;
  left: 0;
  right: 0;
  margin: 0 auto;
  width: $settings-nav-button-width;
}

.settings-arrow-up {
  top: px-to-rem(30);
}

.settings-arrow-down {
  bottom: px-to-rem(30);
}

import { Icon, IconType } from '@components/Icon/Icon';
import classNames from 'classnames';
import React from 'react';

import { withFocusable } from '../../../focus';
import styles from './nav-button.module.scss';

type Direction = 'up' | 'down';

interface INavButton {
  label: string;
  direction: Direction;
  onFocus: () => void;
}

function renderIcon(type: IconType) {
  return <Icon type={type} size="S" className={styles.icon} />;
}

export const NavButton = withFocusable(
  React.forwardRef<HTMLButtonElement, INavButton>(({ direction, label, onFocus }, reference) => {
    return (
      <button
        ref={reference}
        className={classNames(!label ? styles.hide : '', styles.button)}
        onClick={() => onFocus()}
      >
        {direction === 'up' && renderIcon('arrow-up')}
        {label}
        {direction === 'down' && renderIcon('arrow-down')}
      </button>
    );
  }),
);

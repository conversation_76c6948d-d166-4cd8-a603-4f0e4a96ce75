@use '~styles/globals' as *;

.image-wrapper {
  padding-top: px-to-rem(60);
}

.logo {
  height: px-to-rem(150);
  width: px-to-rem(610);
  margin-left: auto;
  margin-right: auto;
}

.text-wrapper {
  text-align: center;
  padding-top: px-to-rem(120);
  padding-bottom: px-to-rem(120);
  font-family: $font-family-regular;
  font-size: px-to-rem(32);
  line-height: px-to-rem(42);
}

.grid-wrapper {
  text-align: center;
}

.row-layout {
  padding-bottom: px-to-rem(25);
}

.button {
  margin-left: px-to-rem(25);
}

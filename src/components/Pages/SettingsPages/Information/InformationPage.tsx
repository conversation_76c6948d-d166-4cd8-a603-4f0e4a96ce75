import { InformationSettingsPage } from '@apptypes/SettingsResponseBody';
import { InformationTitle } from '@apptypes/SettingsResponseBody';
import logo from '@assets/img/arte-logo.png';
import { Button } from '@components/Button/Button';
import { DebugModal } from '@components/Debug/DebugModal';
import { GridLayout } from '@components/GridLayout/GridLayout';
import { Image } from '@components/Image/Image';
import { ScrollableModal } from '@components/Modal/ScrollableModal/ScrollableModal';
import { SettingsHelpModal } from '@components/Modal/SettingsHelpModal/SettingsHelpModal';
import { CenteredSpinner } from '@components/Spinner/CenteredSpinner';
import { useKeySequence } from '@hooks/useKeySequence';
import { useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import { useModalContext } from '@providers/ModalContext';
import React, { useEffect, useState } from 'react';
import { useLoaderData } from 'react-router-dom';

import { FOCUS_KEY_PRIMARY, withFocusable } from '../../../../focus';
import styles from './information-page.module.scss';

interface IInformationPageProperties {
  isFocused: boolean;
}

const InformationPage = withFocusable(
  React.forwardRef<HTMLDivElement, IInformationPageProperties>(() => {
    const [loading, setLoading] = useState(true);
    const { showModal } = useModalContext();
    const { version, application, titles } = useLoaderData() as InformationSettingsPage;
    const { setFocus } = useFocusable();
    const rowLength = 2;
    const { unlocked } = useKeySequence();

    useEffect(() => {
      if (loading && titles) {
        setLoading(false);
      }
    }, [loading, titles]);

    useEffect(() => {
      if (!unlocked) return;
      showModal({
        content: <DebugModal returnFocusKey={FOCUS_KEY_PRIMARY} />,
      });
    }, [showModal, unlocked]);

    const buttons = (titles: InformationTitle[]) => {
      return titles?.map((titleObject, index) => {
        const buttonFocusKey = index === 0 ? FOCUS_KEY_PRIMARY : `${FOCUS_KEY_PRIMARY}_${index}`;
        return (
          <Button
            className={styles['button']}
            label={titleObject.txt}
            key={titleObject.url}
            focusKey={buttonFocusKey}
            focusOnMount={index === 0}
            onEnterPress={() => {
              titleObject.url === 'help'
                ? showModal({ content: <SettingsHelpModal returnFocusKey={buttonFocusKey} version={version} /> })
                : showModal({
                    content: <ScrollableModal returnFocusKey={buttonFocusKey} uri={titleObject.url} />,
                  });
            }}
            onArrowPress={(direction) => {
              switch (direction) {
                case 'up':
                  return true;
                case 'down':
                  return true;
                case 'right':
                  if (index === titles.length - 1) return false;
                  if (index % rowLength === rowLength - 1) {
                    setFocus(`FOCUS_KEY_PRIMARY_${index + 1}`);
                    return false;
                  }
                  return true;
                case 'left':
                  if (index === 0) return true;
                  if (index % rowLength === 0) {
                    setFocus(`FOCUS_KEY_PRIMARY_${index - 1}`);
                    return false;
                  }
                  return true;
                default:
                  return false;
              }
            }}
          />
        );
      });
    };

    if (loading) return <CenteredSpinner />;

    return (
      <>
        <div className={styles['image-wrapper']}>
          <Image src={logo} className={styles['logo']} loadingStrategy="lazy" />
        </div>
        <div className={styles['text-wrapper']}>
          <p className={styles['version']}>Version: {version}</p>
          <p className={styles['application']}>Application: {application}</p>
        </div>
        <div className={styles['grid-wrapper']}>
          <GridLayout rowLength={rowLength} rowClassName={styles['row-layout']}>
            {buttons(titles)}
          </GridLayout>
        </div>
      </>
    );
  }),
);

export { InformationPage };

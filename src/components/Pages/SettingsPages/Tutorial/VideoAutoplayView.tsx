import { underlineWithToggleFocusGuard } from '@components/UnderlineButton/underlineWithToggleFocusGuard';
import { autoplayEnabled, setAutoplayEnabledCookie } from '@util/cookies';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { FOCUS_KEY_PRIMARY } from '../../../../focus';
import { Toggle } from '../../../Toggle/Toggle';
import { UnderlineButton } from '../../../UnderlineButton/UnderlineButton';
import { SustainabilityIcon } from './SustainabilityIcon';

export function VideoAutoplayView() {
  const { t } = useTranslation();
  const [isAutoplayEnabled, setIsAutoplayEnabled] = useState<boolean>(autoplayEnabled());
  function toggleAutoplay() {
    setAutoplayEnabledCookie(!isAutoplayEnabled);
    setIsAutoplayEnabled(!isAutoplayEnabled);
  }

  return (
    <>
      <UnderlineButton
        label={t('settings__autoplay_title')}
        focusOnMount
        focusKey={FOCUS_KEY_PRIMARY}
        onEnterPress={toggleAutoplay}
        onArrowPress={underlineWithToggleFocusGuard}
      >
        <SustainabilityIcon />
        <Toggle on={isAutoplayEnabled} />
      </UnderlineButton>
      <p>{t('settings__autoplay_desc')}</p>{' '}
    </>
  );
}

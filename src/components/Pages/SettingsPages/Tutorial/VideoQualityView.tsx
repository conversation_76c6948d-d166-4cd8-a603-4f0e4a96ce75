import { underlineWithToggleFocusGuard } from '@components/UnderlineButton/underlineWithToggleFocusGuard';
import { getVideoQualityCookie, setVideoQualityCookie } from '@util/cookies';
import { VideoQuality } from '@util/videoQuality';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { defaultFocusGuard } from '../../../../focus';
import { TickButton } from '../../../TickButton/TickButton';
import { SustainabilityIcon } from './SustainabilityIcon';

export function VideoQualityView() {
  const { t } = useTranslation();
  const initialVideoQualityState = getVideoQualityCookie();
  const [videoQuality, setVideoQuality] = useState(initialVideoQualityState);

  function handleVideoQuality(quality: VideoQuality) {
    if (quality === videoQuality) return;
    setVideoQuality(quality);
    setVideoQualityCookie(quality);
  }

  return (
    <>
      <h2>
        {t('settings__quality_title')} <SustainabilityIcon />
      </h2>
      <p>{t('settings__quality_desc')}</p>
      <TickButton
        isActive={videoQuality === VideoQuality.MAX}
        label={t('settings__max_quality')}
        onEnterPress={() => handleVideoQuality(VideoQuality.MAX)}
        onArrowPress={defaultFocusGuard}
      />
      <TickButton
        isActive={videoQuality === VideoQuality.GOOD}
        label={t('settings__good_qaulity')} // note typo here is deliberate as it is contained in translation files
        onEnterPress={() => handleVideoQuality(VideoQuality.GOOD)}
        onArrowPress={defaultFocusGuard}
      />
      <TickButton
        isActive={videoQuality === VideoQuality.LOW}
        label={t('settings__low_quality')}
        onEnterPress={() => handleVideoQuality(VideoQuality.LOW)}
        onArrowPress={underlineWithToggleFocusGuard}
      />
    </>
  );
}

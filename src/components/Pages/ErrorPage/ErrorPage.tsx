import { PAGE_IDS, ROUTES, TEST_ID } from '@constants';
import { ErrorModalType } from '@errors/ErrorModalType';
import { Manifest404Error } from '@errors/Mainfest404Error';
import { SsoRequestUnauthorizedError } from '@errors/SsoRequestUnauthorizedError';
import { TvGuide404Error } from '@errors/TvGuide404Error';
import { TypeCheckError } from '@errors/TypeCheckError';
import { useBackJourney } from '@features/backjourney/useBackJourney';
import { useLogout } from '@features/usercontent/hooks/useLogout';
import { usePersonalisation } from '@features/usercontent/hooks/usePersonalisation';
import { useCustomNavigate } from '@hooks/useCustomNavigate';
import { GlobalContext } from '@providers/GlobalContextProvider';
import { acceptPersonalisationCookies, isLoggedIn } from '@util/cookies';
import { useCallback, useContext, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useRemoteController } from 'react-remote-controller';
import { useLocation, useRouteError } from 'react-router-dom';
import { getKeyMap } from 'target';

import {
  ApiRequestFailError,
  InvalidPageError,
  LoadVideoError,
  MissingProgramIdError,
  MyArtePageMissingContentError,
  NoStreamsFoundError,
} from '../../../errors';
import { FOCUS_KEY_PRIMARY } from '../../../focus';
import { Button } from '../../Button/Button';
import { CenteredLayout } from '../../CenteredLayout/CenteredLayout';
import { CountdownModal } from '../../Modal/CountdownModal/CountdownModal';
import { TextModal } from '../../Modal/TextModal/TextModal';
import styles from './error-page.module.scss';

export const ErrorPage = () => {
  const navigate = useCustomNavigate();
  const { t } = useTranslation();
  const error = useRouteError() as Error;
  const { handleBack } = useBackJourney();
  const { setShowSplashScreen } = useContext(GlobalContext);
  const { logout } = useLogout();
  const { disablePersonalisation } = usePersonalisation();
  const location = useLocation();
  const localErrorType = location?.state?.errorType;
  const localErrorParams = location?.state?.errorParams;
  const goHome = useCallback(() => navigate(`${ROUTES.PAGE}/${PAGE_IDS.HOME}`), [navigate]);

  const goHomeWithNewAnonymousToken = useCallback(() => {
    disablePersonalisation();
    acceptPersonalisationCookies();
    goHome();
  }, [disablePersonalisation, goHome]);

  const goBack = useCallback(() => {
    localErrorType ? navigate(-2) : handleBack();
  }, [localErrorType, navigate, handleBack]);

  const goToLogin = useCallback(() => {
    logout();
    navigate(ROUTES.MYARTE.LOGIN, { replace: true });
  }, [logout, navigate]);

  const defaultHeadline = t('error__generic');

  const defaultMessage = useCallback(
    (useStack = false) => {
      if (localErrorType) {
        return `${t('error__generic_2')}`;
      }
      return `${t('error__generic_2')}\n${(useStack && error.stack) || error}`;
    },
    [localErrorType, t, error],
  );

  const ButtonBack = useMemo(
    () => <Button label={t('back')} onEnterPress={goBack} focusOnMount={true} focusKey={FOCUS_KEY_PRIMARY} />,
    [t, goBack],
  );

  const ButtonReEnablePersonalisation = useMemo(
    () => <Button label={t('home')} onEnterPress={goHomeWithNewAnonymousToken} focusOnMount />,
    [t, goHomeWithNewAnonymousToken],
  );

  const ButtonHome = useMemo(() => <Button label={t('home')} onEnterPress={goHome} focusOnMount />, [t, goHome]);

  const ButtonLogout = useMemo(
    () => <Button label={t('back')} onEnterPress={goToLogin} focusOnMount />,
    [t, goToLogin],
  );

  useEffect(() => {
    setShowSplashScreen(false);
  }, [setShowSplashScreen]);

  useRemoteController<ReturnType<typeof getKeyMap>>({
    listenTo: {
      back: () => {
        switch (true) {
          case error instanceof SsoRequestUnauthorizedError:
            if (isLoggedIn()) {
              goToLogin();
            } else {
              goHomeWithNewAnonymousToken();
            }
            break;
          default:
            goBack();
        }
      },
    },
  });

  const errorModalProps = useMemo(() => {
    if (localErrorType) {
      if (localErrorType === 'LiveStreamUnavailable') {
        return {
          message: defaultMessage(),
          buttons: ButtonBack,
          returnFocusKey: FOCUS_KEY_PRIMARY,
          params: localErrorParams,
          type: ErrorModalType.COUNTDOWN,
        };
      }

      return {
        headline: defaultHeadline,
        message: defaultMessage(true),
        buttons: ButtonHome,
        returnFocusKey: FOCUS_KEY_PRIMARY,
        type: ErrorModalType.TEXT,
      };
    }

    switch (true) {
      case error instanceof TvGuide404Error:
        return {
          headline: t('error__try_again'),
          message: `(404)`,
          secondaryMessage: error.getParams(),
          buttons: ButtonHome,
          returnFocusKey: FOCUS_KEY_PRIMARY,
          type: ErrorModalType.TEXT,
        };

      case error instanceof Manifest404Error:
        return {
          headline: t('error__try_again'),
          message: `(404) Manifest not found.`,
          secondaryMessage: error.getParams(),
          buttons: ButtonBack,
          returnFocusKey: FOCUS_KEY_PRIMARY,
          type: ErrorModalType.TEXT,
        };

      case error instanceof NoStreamsFoundError:
        return {
          headline: t('error__no-stream'),
          message: '',
          buttons: ButtonBack,
          returnFocusKey: FOCUS_KEY_PRIMARY,
          type: ErrorModalType.TEXT,
        };

      case error instanceof ApiRequestFailError:
        return {
          headline: defaultHeadline,
          message: defaultMessage(),
          buttons: ButtonBack,
          returnFocusKey: FOCUS_KEY_PRIMARY,
          type: ErrorModalType.TEXT,
        };

      case error instanceof InvalidPageError:
        return {
          headline: defaultHeadline,
          message: defaultMessage(),
          buttons: ButtonBack,
          returnFocusKey: FOCUS_KEY_PRIMARY,
          type: ErrorModalType.TEXT,
        };

      case error instanceof LoadVideoError:
        return {
          headline: defaultHeadline,
          message: defaultMessage(),
          buttons: ButtonBack,
          returnFocusKey: FOCUS_KEY_PRIMARY,
          type: ErrorModalType.TEXT,
        };

      case error instanceof MissingProgramIdError:
        return {
          headline: defaultHeadline,
          message: defaultMessage(),
          buttons: ButtonBack,
          returnFocusKey: FOCUS_KEY_PRIMARY,
          type: ErrorModalType.TEXT,
        };

      case error instanceof MyArtePageMissingContentError:
        return {
          headline: defaultHeadline,
          message: defaultMessage(),
          buttons: ButtonBack,
          returnFocusKey: FOCUS_KEY_PRIMARY,
          type: ErrorModalType.TEXT,
        };

      case error instanceof SsoRequestUnauthorizedError:
        return {
          headline: defaultHeadline,
          message: defaultMessage(),
          buttons: isLoggedIn() ? ButtonLogout : ButtonReEnablePersonalisation,
          returnFocusKey: FOCUS_KEY_PRIMARY,
          type: ErrorModalType.TEXT,
        };

      case error instanceof TypeCheckError:
        return {
          headline: defaultHeadline,
          message: error.getParams(),
          buttons: ButtonBack,
          returnFocusKey: FOCUS_KEY_PRIMARY,
          type: ErrorModalType.TEXT,
        };

      default:
        return {
          headline: defaultHeadline,
          message: defaultMessage(true),
          buttons: ButtonHome,
          returnFocusKey: FOCUS_KEY_PRIMARY,
          type: ErrorModalType.TEXT,
        };
    }
  }, [
    localErrorType,
    error,
    localErrorParams,
    t,
    defaultHeadline,
    defaultMessage,
    ButtonBack,
    ButtonHome,
    ButtonLogout,
    ButtonReEnablePersonalisation,
  ]);

  return (
    <CenteredLayout testId={TEST_ID.PAGES.ERROR} className={styles['error-page']}>
      {errorModalProps.type === ErrorModalType.TEXT ? (
        <TextModal {...errorModalProps} />
      ) : (
        <CountdownModal {...errorModalProps} />
      )}
    </CenteredLayout>
  );
};

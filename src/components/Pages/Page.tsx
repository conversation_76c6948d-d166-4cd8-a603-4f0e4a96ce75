import { CustomNavigate } from '@components/CustomNavigate/CustomNavigate';
import { PAGE_IDS, ZONE_TEMPLATES } from '@constants';
import { Tracking } from '@tracking/Tracking';
import { shouldNotShowMyVideosPage } from '@util/shouldNotShowMyVideosPage';
import { useTranslation } from 'react-i18next';
import { useLoaderData } from 'react-router-dom';

import PageContextProvider from '../../providers/PageContext';
import SearchContextProvider from '../../providers/SearchContext';
import { PageResponseBody } from '../../types';
import { SearchPage } from '../Search/SearchPage';
import { ContentPage } from './ContentPage';
import { GridPage } from './GridPage';
import { MyHistoryPage } from './MyHistoryPage/MyHistoryPage';
import { MyVideosPage } from './MyVideosPage';

function Page() {
  const { id, zones } = useLoaderData() as PageResponseBody;
  const { t } = useTranslation();

  const getPage = () => {
    switch (id) {
      case PAGE_IDS.SEARCH_HOME:
        return (
          <SearchContextProvider>
            <SearchPage pageId={id} />
          </SearchContextProvider>
        );
      case PAGE_IDS.MY_FAVORITES:
      case PAGE_IDS.MY_RESUME:
        if (shouldNotShowMyVideosPage()) return <CustomNavigate path="/" replace />;
        return <GridPage title={t('my_videos')} pageId={id} list={zones?.[0]?.teaserList || []} />;
      case PAGE_IDS.MY_HISTORY:
        if (shouldNotShowMyVideosPage()) return <CustomNavigate path="/" replace />;
        return <MyHistoryPage />;
      case PAGE_IDS.MY_VIDEOS:
        if (shouldNotShowMyVideosPage()) return <CustomNavigate path="/" replace />;
        Tracking.setZonesInUsage(zones);
        return <MyVideosPage />;

      default:
        const showGrid = zones.length === 1 && zones[0].template === ZONE_TEMPLATES.VERTICAL_LANDSCAPE;
        Tracking.setZonesInUsage(zones);
        return showGrid ? <GridPage pageId={id} list={zones?.[0]?.teaserList || []} /> : <ContentPage />;
    }
  };

  const children = getPage();

  return (
    <PageContextProvider pageId={id} key={id}>
      {children}
    </PageContextProvider>
  );
}

export { Page };

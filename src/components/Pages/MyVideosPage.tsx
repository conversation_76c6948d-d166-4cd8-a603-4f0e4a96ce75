import { Bookmark } from '@apptypes/bookmarks';
import { PageResponseBody, Zone } from '@apptypes/PageResponseBody';
import { useNormalizedParams } from '@hooks/useNormalizedParams';
import { useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import { BookmarksContext } from '@providers/BookmarksContext';
import { getPage } from '@util/NavHistory';
import { remToPx } from '@util/pxToRem';
import { useCallback, useContext, useEffect } from 'react';
import { useLoaderData } from 'react-router-dom';

import { teaserFocusKey } from '../../focus';
import { ContentPage } from './ContentPage';
import styles from './my-videos-page.module.scss';

/**
 * This will make the vertical list "peek" when the last item is focused
 */
const MY_VIDEOS_VERTICAL_BOTTOM_PEEK_SIZE = remToPx(5);

const getFirstFocusableKey = (zones: Zone[], bookmarks?: Bookmark[]): string => {
  // Find first index of a zone that has at least one item
  const zoneIndex = Math.max(
    0,
    zones.findIndex((zone) =>
      // When checking zones:
      // 1. Don't check the backend response if it's a bookmarks zone. Check context bookmarks instead.
      // 2. Check backend response if it's any other zone.
      zone.authenticatedContent === 'sso-favorites' ? bookmarks?.length : zone.teaserList.length,
    ),
  );
  return teaserFocusKey(zoneIndex, 0);
};

export function MyVideosPage() {
  const { zones } = useLoaderData() as PageResponseBody;
  const routeParams = useNormalizedParams();
  const { setFocus } = useFocusable();
  const { bookmarks } = useContext(BookmarksContext);

  const doFocus = useCallback(() => {
    setFocus(getFirstFocusableKey(zones, bookmarks));
  }, [bookmarks, setFocus, zones]);

  useEffect(() => {
    if (getPage(routeParams.pageId as string)) {
      return;
    }
    // Apply special focus logic if the page is not present in nav history

    const timeoutId = setTimeout(doFocus, 50);

    return () => clearTimeout(timeoutId);
  }, [doFocus, routeParams.pageId]);

  return <ContentPage titleClassName={styles.title} bottomPeekSize={MY_VIDEOS_VERTICAL_BOTTOM_PEEK_SIZE} />;
}

import classNames from 'classnames';

import styles from './page-title.module.scss';

interface IPageTitleProperties {
  title?: string;
  description?: string;
  className?: string;
  show?: boolean;
}

const PageTitle = ({ title, description, className, show = true }: IPageTitleProperties) => {
  return (
    <div className={classNames(styles['page-title'], show ? styles.shown : styles.hidden, className)}>
      <h1>
        {title} <span>{description}</span>
      </h1>
    </div>
  );
};

export { PageTitle };

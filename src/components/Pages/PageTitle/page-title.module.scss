@use '~styles/globals' as *;
@use '~styles/functions';

.page-title {
  width: 85%;
  padding: px-to-rem(30) px-to-rem(30) px-to-rem(30) 0;

  h1 {
    font-size: px-to-rem(40);
    font-family: $font-family-bold;
    @include truncate();
  }

  span {
    margin-left: px-to-rem(20);
    font-size: px-to-rem(25px);
    font-family: $font-family-regular;
  }
}

.hidden {
  opacity: 0;
}

.shown {
  opacity: 1;
}

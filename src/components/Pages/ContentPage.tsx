import { ContentPageData } from '@apptypes/ContentPageData';
import { VerticalList } from '@components/List/VerticalList';
import { useMouseWheel } from '@components/MouseNavigation/useMouseWheel';
import { setVerticalScrollIndex } from '@components/MouseNavigation/verticalScrollIndex';
import { useNormalizedParams } from '@hooks/useNormalizedParams';
import { useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import { BookmarksContext } from '@providers/BookmarksContext';
import { getBookmarks } from '@util/meData';
import {
  getNavHistoryVerticalOffset,
  getNavHistoryZoneId,
  getVerticalNavIndex,
  processNewPageLoad,
  setNavHistoryVerticalIndex,
  setNavHistoryZoneId,
} from '@util/NavHistory';
import { remToPx } from '@util/pxToRem';
import { toEpoch } from '@util/toEpoch';
import { getTeasersFromFavouritesZone } from '@util/zones';
import { useContext, useEffect, useRef, useState } from 'react';
import { useLoaderData, useLocation } from 'react-router-dom';

import { THEMES } from '../../constants';
import { FOCUS_KEY_PRIMARY } from '../../focus';
import { Zone } from '../../types';
import styles from './content-page.module.scss';
import { getZones } from './getZones';
import { PageTitle } from './PageTitle/PageTitle';

const ZONES_AHEAD = 3;

/**
 * Computes the initial vertical index based on the last remembered zone id and the current zones data.
 * Remembering vertical index is not enough, because the number of zones can be different upon returning to the same page.
 * @param pageId Page id to check in nav history
 * @param zones Current zones data to be presented on a page
 * @returns Vertical index of a zone that gets the initial focus
 */
const getInitialZoneIndex = (pageId: string, zones: Zone[]) =>
  Math.max(
    0,
    zones.findIndex((zone) => zone.id === getNavHistoryZoneId(pageId as string)),
  );

/**
 * Computes the initial vertical position.
 * It takes the last known position from nav history and corrects it in case some zones were removed or added
 * @param pageId Page id to check in nav history
 * @param zones Current zones data to be presented on a page
 * @returns The initial vertical scroll position in px.
 */
const getInitialOffset = (pageId: string, zones: Zone[]): number | undefined => {
  const expectedVerticalIndex = getVerticalNavIndex(pageId);
  const actualZoneIndex = getInitialZoneIndex(pageId, zones);
  const indexDifference = actualZoneIndex - expectedVerticalIndex;

  let navHistoryOffset = getNavHistoryVerticalOffset(pageId);
  if (navHistoryOffset) {
    // Correct the last known offset by an arbitrary number roughly representing the height of a personal zone.
    navHistoryOffset -= indexDifference * remToPx(27);
  }

  return navHistoryOffset;
};

interface IContentPageProperties {
  focusKey?: string; // set it if you don't want the list to receive focus when arriving from the side menu
  className?: string;
  titleClassName?: string;
  bottomPeekSize?: number;
  searchMenuPresent?: boolean;
}

function ContentPage(properties: IContentPageProperties) {
  const { showTitle, title, description, zones: loaderZones, meData, id: pageId } = useLoaderData() as ContentPageData;
  const routeParams = useNormalizedParams();
  const location = useLocation();

  // zones have a state because for bookmarking we want to manipulate them without needing a page reload
  const [zones, setZones] = useState<Zone[]>([]);
  const [filteredZones, setFilteredZones] = useState(loaderZones);
  const [selectedItemIndex, setSelectedItemIndex] = useState(getInitialZoneIndex(pageId, loaderZones));
  const interacted = useRef(false);
  const initialZoneId = useRef(getNavHistoryZoneId(pageId));
  const initialOffset = useRef(getInitialOffset(pageId, loaderZones));
  const { bookmarks, bookmarksTimestamp, initBookmarks, updateExistingBookmarks } = useContext(BookmarksContext);
  const { focusKey, className, searchMenuPresent, titleClassName } = properties;
  const favourites = getTeasersFromFavouritesZone(loaderZones);
  const meDataFavourites = getBookmarks(meData);
  const [bookmarksInitialisedFromMeData, setBookmarksInitialisedFromMeData] = useState(false);
  const { resume } = useFocusable();

  useMouseWheel();

  useEffect(() => {
    resume();
  }, [location, resume]);

  useEffect(() => {
    setVerticalScrollIndex(selectedItemIndex);
  }, [selectedItemIndex]);

  /**
   * Bookmarks context is initialised for every page load if favourites are present
   * in the response. This allows us to keep the bookmarks as up to date as possible in the UI.
   */
  useEffect(
    function initialiseBookmarksFromFavourites() {
      // Where are the latest bookmarks? SSO or local?
      // Thanks to this function the bookmarks from SSO will be initialised only if they're most recent.
      const areLatestFavourites = () => toEpoch(meData?.meta?.updatedAt) > bookmarksTimestamp;

      if (favourites && favourites.length) {
        if (areLatestFavourites()) {
          initBookmarks(favourites);
        } else {
          updateExistingBookmarks(favourites);
        }
      }
    },
    [bookmarksTimestamp, favourites, initBookmarks, updateExistingBookmarks, meData?.meta?.updatedAt],
  );

  useEffect(() => {
    if (bookmarks && bookmarks.length === 0) {
      // All bookmarks have been removed. Filter out the bookmarks zone if it cannot show an empty state.
      const bookmarksZone = loaderZones.find((zone) => zone.authenticatedContent === 'sso-favorites');
      const bookmarksZoneCanShowEmptyState = bookmarksZone?.theme === THEMES.SHOWEMPTYZONE;
      if (bookmarksZone && !bookmarksZoneCanShowEmptyState) {
        setFilteredZones(loaderZones.filter((zone) => zone.authenticatedContent !== 'sso-favorites'));
      }
    }
  }, [bookmarks, loaderZones]);

  useEffect(
    function initialiseBookmarksOnDeeplink() {
      const bookmarksEqual = JSON.stringify(meDataFavourites) === JSON.stringify(bookmarks);
      const isHomepage = location.pathname.includes('HOME');
      if (!isHomepage && !bookmarksInitialisedFromMeData && location?.state?.isDeeplinkRedirect && !bookmarksEqual) {
        initBookmarks(meDataFavourites);
        setBookmarksInitialisedFromMeData(true);
      }
    },
    [location?.state, bookmarks, initBookmarks, meDataFavourites, bookmarksInitialisedFromMeData, location],
  );

  useEffect(() => {
    loaderZones.length > 0 && pageId && processNewPageLoad(pageId, loaderZones);
    const zoneIndexToFocus = getInitialZoneIndex(pageId, loaderZones);
    initialZoneId.current = loaderZones[zoneIndexToFocus].id;
    interacted.current = false;
    setZones(loaderZones.slice(0, zoneIndexToFocus + ZONES_AHEAD));
    setFilteredZones(loaderZones);
  }, [loaderZones, pageId]);

  useEffect(() => {
    setZones(filteredZones.slice(0, selectedItemIndex + ZONES_AHEAD));
  }, [filteredZones, selectedItemIndex]);

  useEffect(() => {
    if (pageId && routeParams && filteredZones.length > 0) {
      const zoneId = filteredZones[selectedItemIndex]?.id;
      // remember last zone id
      setNavHistoryZoneId(pageId, zoneId);
      // remember last vertical index
      setNavHistoryVerticalIndex(selectedItemIndex, pageId);
      // by saving both, we can detect if zones were added or removed when returning to the same page
    }
  }, [selectedItemIndex, pageId, routeParams, filteredZones]);

  if (initialZoneId.current !== filteredZones[selectedItemIndex]?.id) {
    interacted.current = true;
  }

  if (!loaderZones.length) return null;

  return (
    <div>
      {showTitle && selectedItemIndex === 0 && (!initialOffset.current || interacted.current) && (
        <PageTitle
          title={title}
          description={description}
          className={titleClassName}
          show={showTitle && selectedItemIndex === 0}
        />
      )}
      <VerticalList
        contentsClassName={styles['content']}
        currentItem={selectedItemIndex}
        focusKey={focusKey || FOCUS_KEY_PRIMARY}
        className={className}
        peekSize={remToPx(2.5)}
        scrollToEnd={false}
        totalItems={zones.length}
        initialOffset={initialOffset.current}
        pageId={pageId}
        interacted={interacted.current}
        bottomPeekSize={properties?.bottomPeekSize}
      >
        {zones.length > 0 &&
          getZones({
            pageId,
            routeParams,
            setSelectedItemIndex,
            zones,
            indexToFocusOnMount: selectedItemIndex,
            searchMenuPresent,
          })}
      </VerticalList>
    </div>
  );
}

export { ContentPage, getInitialZoneIndex };

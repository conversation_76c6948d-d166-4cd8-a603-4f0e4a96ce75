import { setVerticalScrollIndex } from '@components/MouseNavigation/verticalScrollIndex';
import { remToPx } from '@util/pxToRem';

import { FOCUS_TV_GUIDE_PROGRAM } from '../../focus';
import { ITvGuideTeaserProperties } from '../../types';
import { TeaserList } from '../TeaserList/TeaserList';

interface ITvGuideProgramProperties {
  index: number;
  tvGuideDayData: ITvGuideTeaserProperties[];
  showComponent: boolean;
}

/**
 * This will make the vertical list "peek" when the last item is focused
 */
const TV_GUIDE_VERTICAL_BOTTOM_PEEK_SIZE = remToPx(10);

export const TvGuideProgram = (properties: ITvGuideProgramProperties) => {
  const { tvGuideDayData, index, showComponent } = properties;
  return (
    <div style={{ opacity: showComponent ? 1 : 0 }}>
      <TeaserList
        teasers={tvGuideDayData}
        zoneIndex={index}
        template={'tableview-guide'}
        horizontal={false}
        scrollStartIndex={1}
        scrollToEnd={true}
        pageId={`TV_GUIDE`}
        getCurrentIndex={setVerticalScrollIndex}
        listFocusKey={FOCUS_TV_GUIDE_PROGRAM}
        moveChildOffsetToCenter={true}
        bottomPeekSize={TV_GUIDE_VERTICAL_BOTTOM_PEEK_SIZE}
      />
    </div>
  );
};

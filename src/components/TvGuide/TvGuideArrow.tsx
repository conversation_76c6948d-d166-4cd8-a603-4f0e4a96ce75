import { Icon } from '@components/Icon/Icon';
import { useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import { navigateByDirectionDown, navigateByDirectionUp } from '@util/navigation';
import classNames from 'classnames';

import styles from './tv-guide-arrow.module.scss';

interface ITvGuideArrow {
  direction: string;
  isVisible: boolean;
}

const TvGuideArrow = ({ direction, isVisible }: ITvGuideArrow) => {
  const { navigateByDirection } = useFocusable();
  const buttonClassNames = classNames(styles['tv-guide-arrow-indicator'], styles['tv-guide-arrow'], {
    [styles['is-visible']]: isVisible,
  });

  function handleClick() {
    if (direction === 'up') {
      navigateByDirectionUp(navigateByDirection);
    }
    if (direction === 'down') {
      navigateByDirectionDown(navigateByDirection);
    }
  }

  return (
    <button
      onClick={() => {
        handleClick();
      }}
      className={buttonClassNames}
    >
      <Icon type={direction === 'up' ? 'arrow-up' : 'arrow-down'} size="S" />
    </button>
  );
};

export { TvGuideArrow };

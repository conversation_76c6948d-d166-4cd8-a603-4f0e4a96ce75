import { TvGuide404Error } from '@errors/TvGuide404Error';
import { loadMeData } from '@routes/pageHydration';
import { hydrateTvGuideDay } from '@routes/zoneHydration';

import { getTvGuideDay } from '../../data';
import { logError } from '../../errors';
import { TvGuideDay } from '../../types';

const getTvGuideDayData = async (item: TvGuideDay) => {
  const getDayData = getTvGuideDay(item.date);
  const dynamicTvGuideData = await getDayData;

  if (dynamicTvGuideData?.type === 'error' && dynamicTvGuideData?.code === '404') {
    throw new TvGuide404Error(dynamicTvGuideData?.error);
  }
  return await loadMeData()
    .then(async (response) => await hydrateTvGuideDay(dynamicTvGuideData, response))
    .catch((error) => logError(error, 'WARNING'));
};

export { getTvGuideDayData };

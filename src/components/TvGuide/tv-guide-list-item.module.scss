@use '~styles/globals' as *;

.tv-guide-list-item {
  display: table;
  padding: 0 px-to-rem(48);

  &.is-focused {
    background-color: $dark-grey;
  }

  &.is-active {
    font-family: $font-family-bold;
    color: $white;
  }
}

.tv-guide-date-container {
  display: table-cell;
  vertical-align: middle;
  height: $tv-guide-menu-item-height;
}

.tv-guide-dow-container {
  display: table-cell;
  vertical-align: middle;
  padding-left: px-to-rem(70);
}

.tv-guide-day {
  color: $grey;
  font-family: $font-family-regular;
  font-size: px-to-rem(40);

  &.is-focused {
    color: $white;
  }

  &.is-active {
    font-family: $font-family-bold;
    color: $white;
  }
}

.tv-guide-month {
  color: $grey;
  font-family: $font-family-regular;
  font-size: px-to-rem(25);

  &.is-focused {
    color: $white;
  }

  &.is-active {
    font-family: $font-family-bold;
    color: $white;
  }
}

.tv-guide-dow {
  color: $grey;
  font-family: $font-family-regular;
  text-align: left;
  font-size: px-to-rem(40);

  &.is-focused {
    color: $white;
  }

  &.is-active {
    font-family: $font-family-bold;
    color: $white;
  }
}

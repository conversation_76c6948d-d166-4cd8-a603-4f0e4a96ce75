import { VerticalList } from '@components/List/VerticalList';
import React from 'react';

import { FOCUS_KEY_PRIMARY, withFocusableContainer } from '../../focus';
import i18n from '../../i18n';
import styles from './tv-guide-menu.module.scss';
import { TvGuideArrow } from './TvGuideArrow';

interface ITvGuideMenu {
  days: JSX.Element[];
  selectedItemIndex: number;
  upArrowVisibility: boolean;
  downArrowVisibility: boolean;
  bypassMultiplier: number;
}

export const TvGuideMenu = withFocusableContainer(
  React.forwardRef<HTMLDivElement, ITvGuideMenu>(
    (
      { days, selectedItemIndex, upArrowVisibility, downArrowVisibility, bypassMultiplier }: ITvGuideMenu,
      reference,
    ) => {
      return (
        <div className={styles.wrapper} ref={reference}>
          <h2>{i18n.t('tvGuide')}</h2>
          <TvGuideArrow direction={'up'} isVisible={upArrowVisibility} />
          <div className={styles.list}>
            <VerticalList
              currentItem={selectedItemIndex}
              scrollStartIndex={2}
              focusKey={FOCUS_KEY_PRIMARY}
              bypassVisibleSize={true}
              bypassMultiplier={bypassMultiplier}
            >
              {days}
            </VerticalList>
          </div>
          {selectedItemIndex <= days?.length - 2 - bypassMultiplier / 2 && (
            <TvGuideArrow direction={'down'} isVisible={downArrowVisibility} />
          )}
        </div>
      );
    },
  ),
);

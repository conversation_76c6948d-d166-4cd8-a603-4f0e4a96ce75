import React from 'react';

import { focusClassNames, withFocusable } from '../../focus';
import { TvGuideDay } from '../../types';
import styles from './tv-guide-list-item.module.scss';

export const TvGuideListItem = withFocusable(
  React.forwardRef<HTMLAnchorElement>(({ ...properties }, reference) => {
    const { day, month, dow, onClick } = properties as TvGuideDay;
    return (
      <a
        href="#"
        onClick={onClick}
        ref={reference}
        className={focusClassNames(styles, properties, styles['tv-guide-list-item'])}
      >
        <div className={styles['tv-guide-date-container']}>
          <p className={focusClassNames(styles, properties, styles['tv-guide-month'])}>{month.toUpperCase()}</p>
          <p className={focusClassNames(styles, properties, styles['tv-guide-day'])}>{day}</p>
        </div>
        <div className={styles['tv-guide-dow-container']}>
          <p className={focusClassNames(styles, properties, styles['tv-guide-dow'])}>{dow}</p>
        </div>
      </a>
    );
  }),
);

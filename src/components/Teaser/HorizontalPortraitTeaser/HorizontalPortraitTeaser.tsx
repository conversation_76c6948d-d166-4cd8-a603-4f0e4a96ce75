import { LinkPlaceholder } from '@components/LinkPlaceholder/LinkPlaceholder';
import classNames from 'classnames';
import i18n from 'i18next';
import React from 'react';
import { config } from 'target';

import { THEMES } from '../../../constants';
import { focusClassNames, withFocusable } from '../../../focus';
import { ITeaserProperties } from '../../../types';
import { ColorButton } from '../../ColorButton/ColorButton';
import { Image } from '../../Image/Image';
import { ImagePlaceholder } from '../../Image/ImagePlaceholder';
import { ProgressTeaser } from '../../ProgressTeaser/ProgressTeaser';
import { getLabel } from '../getLabel';
import { Label } from '../Label/Label';
import { useInfoLayer } from '../useInfoLayer';
import styles from './horizontal-portrait-teaser.module.scss';

export const HorizontalPortraitTeaser = withFocusable(
  React.forwardRef<HTMLDivElement, ITeaserProperties>(({ className, ...properties }, reference) => {
    useInfoLayer(properties);

    const label = getLabel(properties);
    const theme = properties?.theme;
    const link = properties?.link;

    function getColorButton() {
      return (
        <div className={classNames(styles['button-wrapper'], { [styles['is-focused']]: properties.isFocused })}>
          <ColorButton color="green" label={i18n.t('moreInfos')} />
        </div>
      );
    }

    return (
      <div
        ref={reference}
        className={classNames(styles.teaser, className, theme && styles[THEMES[theme.toUpperCase()]])}
        data-template={properties.template}
      >
        {link ? (
          <a
            href="#"
            data-testid-focused={properties.testIdFocused ? 'true' : undefined}
            className={classNames(styles['image-area'], { [styles['is-focused']]: properties.isFocused })}
            onClick={properties.onMouseClick}
          >
            <LinkPlaceholder width={properties.imageWidth} height={properties.imageHeight} title={link.title} />
          </a>
        ) : (
          <>
            <a
              href="#"
              data-testid-focused={properties.testIdFocused ? 'true' : undefined}
              data-kind={properties?.dataKind}
              className={classNames(
                styles['image-area'],
                { [styles['is-focused']]: properties.isFocused },
                { [styles['livestream-web-template']]: properties?.isLivestreamWebTemplate },
                { [styles['live-web-template']]: properties?.isLiveWebTemplate },
              )}
              onClick={properties.onMouseClick}
            >
              <ProgressTeaser viewedProgress={properties.viewedProgress} />
              <ImagePlaceholder>
                <Image
                  className={focusClassNames(styles, properties)}
                  width={properties.imageWidth}
                  height={properties.imageHeight}
                  src={properties.image}
                  loadingStrategy="lazy"
                />
              </ImagePlaceholder>
            </a>
            {label && (
              <div className={styles.label}>
                <Label label={label} properties={properties} />
              </div>
            )}
            {config.hasColorButtonSupport && getColorButton()}
          </>
        )}
      </div>
    );
  }),
);

@use '~styles/globals' as *;

.teaser {
  position: relative;
  margin-right: $rail-item-gap;
  width: $teaser-horizontal-portrait-width;

  @media (min-width: 1920px) {
    width: $teaser-horizontal-portrait-width-fhd;
  }
}

.teaser a {
  display: block;
  border-style: solid;
  border-color: transparent;
  border-width: $focused-border-width;
  position: relative;

  @media (min-width: 1920px) {
    border-width: $focused-border-width-fhd;
  }

  &.is-focused {
    background-color: $focused-border-color;
  }

  &.livestream-web-template {
    background-color: $border-live-concert-color;
  }

  &.live-web-template {
    background-color: $arte-highlight;
  }
}

.teaser.theme-white,
.teaser.theme-info {
  & a:not(.livestream-web-template):not(.live-web-template).is-focused {
    background-color: $theme-white-border-color;
  }
}

.button-wrapper {
  padding: px-to-rem(12) px-to-rem(23);
  visibility: hidden;
  background-color: $dark-grey;

  &.is-focused {
    visibility: visible;
  }
}

.label {
  position: absolute;
  top: px-to-rem(20);
  left: px-to-rem(24);
  width: 85%;
}

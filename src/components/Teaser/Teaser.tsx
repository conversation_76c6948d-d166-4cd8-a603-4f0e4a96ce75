import { getLivestreamWebTemplate, getLiveWebTemplate } from '@components/Teaser/getLivestreamWebTemplate';
import { ZONE_TEMPLATES } from '@constants';

import { ITeaserProperties, ITvGuideTeaserProperties } from '../../types';
import { addImageSize } from '.';
import { EmptyStateTeaser } from './EmptyStateTeaser/EmptyStateTeaser';
import { EventTeaser } from './EventTeaser/EventTeaser';
import { getLive, getPrime } from './getStickerInformation';
import { HorizontalLandscapeBigWithSubtitleTeaser } from './HorizontalLandscapeBigWithSubtitleTeaser/HorizontalLandscapeBigWithSubtitleTeaser';
import { HorizontalLandscapeTeaser } from './HorizontalLandscapeTeaser/HorizontalLandscapeTeaser';
import { HorizontalPortraitTeaser } from './HorizontalPortraitTeaser/HorizontalPortraitTeaser';
import { HorizontalSquareTeaser } from './HorizontalSquareTeaser/HorizontalSquareTeaser';
import { HorizontalLandscapeHighlightTeaser } from './TopTeasers/HorizontalLandscapeHighlightTeaser';
import { PageHeaderTeaser } from './TopTeasers/PageHeaderTeaser';
import { SingleContentTeaser } from './TopTeasers/SingleContentTeaser';
import { TvGuideTeaser } from './TvGuideTeaser/TvGuideTeaser';

export const Teaser = (properties: ITeaserProperties | ITvGuideTeaserProperties) => {
  const { emptyState, template } = properties;

  if (emptyState) {
    const emptyTeaserProps = { ...addImageSize(properties), ...properties };
    return <EmptyStateTeaser {...emptyTeaserProps} />;
  }

  const liveLabelTemplate = {
    isLiveWebTemplate: getLiveWebTemplate(properties),
    isLivestreamWebTemplate: getLivestreamWebTemplate(properties),
  };

  const propsWithImageAndLiveTemplate = { ...addImageSize(properties), ...liveLabelTemplate };
  switch (template) {
    case ZONE_TEMPLATES.HORIZONTAL_PORTRAIT:
      return <HorizontalPortraitTeaser {...propsWithImageAndLiveTemplate} />;
    case ZONE_TEMPLATES.HORIZONTAL_SQUARE:
      return <HorizontalSquareTeaser {...propsWithImageAndLiveTemplate} />;
    case ZONE_TEMPLATES.HORIZONTAL_LANDSCAPE:
    case ZONE_TEMPLATES.VERTICAL_LANDSCAPE:
      return <HorizontalLandscapeTeaser {...propsWithImageAndLiveTemplate} />;
    case ZONE_TEMPLATES.HORIZONTAL_HIGHLIGHTED:
      return <HorizontalLandscapeHighlightTeaser {...propsWithImageAndLiveTemplate} />;
    case ZONE_TEMPLATES.HORIZONTAL_LANDSCAPE_BIG:
    case ZONE_TEMPLATES.HORIZONTAL_LANDSCAPE_BIG_SUBTITLE:
      return <HorizontalLandscapeBigWithSubtitleTeaser {...propsWithImageAndLiveTemplate} />;
    case ZONE_TEMPLATES.EVENT_RIGHT:
    case ZONE_TEMPLATES.EVENT_LEFT:
      return <EventTeaser {...propsWithImageAndLiveTemplate} />;
    case ZONE_TEMPLATES.SINGLE_COLLECTION:
    case ZONE_TEMPLATES.SINGLE_PROGRAM:
      return <SingleContentTeaser {...propsWithImageAndLiveTemplate} />;
    case ZONE_TEMPLATES.SINGLE_PAGE_HEADER:
      return <PageHeaderTeaser {...propsWithImageAndLiveTemplate} />;
    case ZONE_TEMPLATES.TABLEVIEW_GUIDE:
      const live = getLive(properties);
      const prime = getPrime(properties);
      return (
        <TvGuideTeaser {...(propsWithImageAndLiveTemplate as ITvGuideTeaserProperties)} live={live} prime={prime} />
      );
    default:
      return <h3>Unknown teaser type {template}</h3>;
  }
};

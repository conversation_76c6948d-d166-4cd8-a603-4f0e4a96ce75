import { ITeaserProperties } from '@apptypes/ITeaserProperties';
import { ITvGuideDayData } from '@apptypes/ITvGuideTeaserProperties';
import { Genre } from '@components/Genre/Genre';
import { getGenreName } from '@util/genre';
import classNames from 'classnames';
import React from 'react';
import { config } from 'target';

import { withFocusableContainer } from '../../../focus';
import i18n from '../../../i18n';
import { Button } from '../../Button/Button';
import { ColorButton } from '../../ColorButton/ColorButton';
import { Image } from '../../Image/Image';
import { ImagePlaceholder } from '../../Image/ImagePlaceholder';
import { ProgressTeaser } from '../../ProgressTeaser/ProgressTeaser';
import { useInfoLayer } from '../useInfoLayer';
import styles from './tv-guide-teaser.module.scss';

interface ITvGuideTeaserProps extends ITvGuideDayData, ITeaserProperties {}

export const TvGuideTeaser = withFocusableContainer(
  React.forwardRef<HTMLAnchorElement, ITvGuideTeaserProps>(
    ({ genre, start, live, prime, className, testIdFocused, ...properties }, reference) => {
      useInfoLayer(properties);
      return (
        <a
          href="#"
          onClick={properties.onMouseClick}
          ref={reference}
          className={classNames(styles.wrapper, className, { [styles['is-focused']]: properties.isFocused })}
          data-testid-focused={testIdFocused ? 'true' : undefined}
        >
          <div className={styles.time}>
            <div className={classNames(styles.start)}>{start}</div>
          </div>
          <div>
            <div className={styles['image-wrapper']}>
              {!live && <ProgressTeaser viewedProgress={properties.viewedProgress} />}
              <ImagePlaceholder>
                <Image
                  width={properties.imageWidth}
                  height={properties.imageHeight}
                  src={properties.image}
                  className={classNames(styles.image, { [styles['is-prime']]: prime }, { [styles['is-live']]: live })}
                  loadingStrategy="lazy"
                />
                {live && (
                  <p className={styles.live}>
                    <span></span>
                    {live.label?.toUpperCase()}
                  </p>
                )}
                <Genre label={getGenreName(genre)} className={styles.genre} />
              </ImagePlaceholder>
            </div>
          </div>
          <div>
            <div
              className={classNames(
                styles['metadata-container'],
                { [styles['is-focused']]: properties.isFocused },
                { [styles['is-prime']]: prime },
              )}
            >
              {properties.title && <p className={styles['title']}>{properties.title}</p>}
              {properties.subtitle && <p className={styles['subtitle']}>{properties.subtitle}</p>}

              {properties.description && <p className={styles['description']}>{properties.description}</p>}
            </div>
          </div>
          {properties.isFocused && (
            <div className={classNames(styles['green-button-watch-container'], { [styles['is-prime']]: prime })}>
              {properties.playable && (
                <Button
                  icon="play"
                  label={properties.watchOrResumeLabel}
                  className={styles['watch-button']}
                  isFocused={properties.isFocused}
                />
              )}
              {config.hasColorButtonSupport && <ColorButton color="green" label={i18n.t('moreInfos')} />}
            </div>
          )}
        </a>
      );
    },
  ),
);

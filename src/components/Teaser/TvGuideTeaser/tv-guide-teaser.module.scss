@use '~styles/globals' as *;

$teaser-width: px-to-rem(1390);
$live-dot-size: px-to-rem(10);
$sticker-margin: px-to-rem(12);
$width: 265px;
$width-fhd: 374px;
$width-prime: 325px;
$width-prime-fhd: 480px;

.wrapper {
  padding: px-to-rem(33) 0;
  display: table;

  > div {
    display: table-cell;
    vertical-align: middle;
  }

  &.is-focused {
    background-color: $dark-grey;
  }
}

.time {
  display: inline-block;
  width: px-to-rem(170);
}

.start {
  vertical-align: middle;
  text-align: center;
  font-family: $font-family-bold;
  font-size: px-to-rem(32);
}

.image-wrapper {
  position: relative;
}

.image {
  &.is-live {
    width: $width;
    outline: $border-width-live solid $arte-highlight;

    @media (min-width: 1920px) {
      width: $width-fhd;
    }

    &.is-prime {
      width: $width-prime;

      @media (min-width: 1920px) {
        width: $width-prime-fhd;
      }
    }
  }
}

.live {
  position: absolute;
  top: $sticker-margin;
  left: $sticker-margin;
  padding: px-to-rem(4) px-to-rem(16);
  background-color: $white;
  font-family: $font-family-bold;
  font-size: px-to-rem(25);
  color: $black;

  span {
    display: inline-block;
    margin-bottom: px-to-rem(3);
    margin-right: px-to-rem(6);
    width: $live-dot-size;
    height: $live-dot-size;
    border-radius: $live-dot-size;
    background-color: $arte-highlight;
  }
}

.genre {
  position: absolute;
  bottom: $sticker-margin;
  right: $sticker-margin;
  padding: px-to-rem(6);
  font-family: $font-family-regular;
  font-size: px-to-rem(25);
  background: $black;
}

.metadata-container {
  min-height: px-to-rem(230);
  padding: 0 px-to-rem(32);
  overflow: hidden;
}

.title,
.subtitle,
.description {
  font-size: px-to-rem(32);
}

.description,
.subtitle {
  font-size: px-to-rem(26);
  margin-top: px-to-rem(8);
  color: $white;
}

.subtitle {
  @include line-clamp(1);
}

.title {
  font-family: $font-family-bold;
  @include line-clamp(2);
}

.description {
  @include line-clamp(3);
}

.green-button-watch-container {
  width: px-to-rem(340);

  p {
    font-size: px-to-rem(24);
  }
}

.watch-button {
  margin-bottom: px-to-rem(40);
}

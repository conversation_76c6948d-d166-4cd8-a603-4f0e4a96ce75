import { ITeaserProperties } from '@apptypes/ITeaserProperties';
import { ColorButton } from '@components/ColorButton/ColorButton';
import { Genre } from '@components/Genre/Genre';
import { Image } from '@components/Image/Image';
import { ImagePlaceholder } from '@components/Image/ImagePlaceholder';
import { LinkPlaceholder } from '@components/LinkPlaceholder/LinkPlaceholder';
import { ProgressTeaser } from '@components/ProgressTeaser/ProgressTeaser';
import { getLabel } from '@components/Teaser/getLabel';
import { Label } from '@components/Teaser/Label/Label';
import { truncateClassNames } from '@features/truncate/truncate';
import { getGenreLabel, shouldDisplayGenreLabel } from '@util/genre';
import { getNumberOfLines, LandscapeBigTeaserTexts } from '@util/linesInText';
import classNames from 'classnames';
import i18n from 'i18next';
import React from 'react';
import { config } from 'target';

import { focusClassNames, withFocusable } from '../../../focus';
import { useInfoLayer } from '../useInfoLayer';
import styles from './horizontal-landscape-big-with-subtitle-teaser.module.scss';

export const HorizontalLandscapeBigWithSubtitleTeaser = withFocusable(
  React.forwardRef<HTMLDivElement, ITeaserProperties>(({ className, ...properties }, reference) => {
    useInfoLayer(properties);

    const maxLines = 4;
    const theme = properties?.theme;
    const link = properties?.link;
    const titleLines = Math.min(maxLines, getNumberOfLines(properties.title, LandscapeBigTeaserTexts.TITLE));
    const subtitleLines = Math.min(
      maxLines - titleLines,
      getNumberOfLines(properties.subtitle, LandscapeBigTeaserTexts.SUBTITLE),
    );

    const label = getLabel(properties);

    function getColorButton() {
      return (
        <div className={styles['button-wrapper']}>
          <ColorButton color="green" label={i18n.t('moreInfos')} />
        </div>
      );
    }

    return (
      <div
        ref={reference}
        className={classNames(styles.teaser, className, theme && styles[theme])}
        data-template={properties.template}
      >
        {link ? (
          <a
            href="#"
            onClick={properties.onMouseClick}
            className={classNames(styles['image-area'], { [styles['is-focused']]: properties.isFocused })}
          >
            <LinkPlaceholder width={properties.imageWidth} height={properties.imageHeight} title={link.title} />
          </a>
        ) : (
          <>
            <a
              href="#"
              onClick={properties.onMouseClick}
              className={classNames(
                styles['image-area'],
                { [styles['is-focused']]: properties.isFocused },
                { [styles['livestream-web-template']]: properties?.isLivestreamWebTemplate },
                { [styles['live-web-template']]: properties?.isLiveWebTemplate },
              )}
              data-kind={properties?.dataKind}
            >
              <ProgressTeaser viewedProgress={properties.viewedProgress} />
              <ImagePlaceholder>
                <Image
                  className={focusClassNames(styles, properties)}
                  width={properties.imageWidth}
                  height={properties.imageHeight}
                  src={properties.image}
                  loadingStrategy="lazy"
                />
              </ImagePlaceholder>
              {shouldDisplayGenreLabel(properties.theme) && (
                <Genre label={getGenreLabel(properties)} className={styles.genre} />
              )}
            </a>

            {label && (
              <div className={styles.label}>
                <Label label={label} properties={properties} />
              </div>
            )}

            {(properties.showItemTitle || config.hasColorButtonSupport) && (
              <div className={classNames(styles.metadata, { [styles['is-focused']]: properties.isFocused })}>
                {properties.showItemTitle && (
                  <>
                    <h3 className={truncateClassNames(titleLines, styles.title)}>{properties.title}</h3>
                    <p className={truncateClassNames(subtitleLines, styles.subtitle)}>{properties.subtitle}</p>
                  </>
                )}
                {config.hasColorButtonSupport && getColorButton()}
              </div>
            )}
          </>
        )}
      </div>
    );
  }),
);

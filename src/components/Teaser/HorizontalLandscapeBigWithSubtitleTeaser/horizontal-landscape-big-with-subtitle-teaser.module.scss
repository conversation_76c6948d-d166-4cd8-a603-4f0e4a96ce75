@use '~styles/globals' as *;

.teaser {
  position: relative;
  margin-right: $rail-item-gap;
  width: $teaser-horizontal-landscape-big-width;

  @media (min-width: 1920px) {
    width: $teaser-horizontal-landscape-big-width-fhd;
  }
}

.teaser a {
  display: block;
  position: relative;
  border-style: solid;
  border-color: transparent;
  border-width: $focused-border-width;

  @media (min-width: 1920px) {
    border-width: $focused-border-width-fhd;
  }

  &.is-focused {
    background-color: $focused-border-color;
  }

  &.livestream-web-template {
    background-color: $border-live-concert-color;
  }

  &.live-web-template {
    background-color: $arte-highlight;
  }
}

.teaser.theme-white,
.teaser.theme-info {
  & a:not(.livestream-web-template):not(.live-web-template).is-focused {
    background-color: $theme-white-border-color;
  }
}

.genre {
  position: absolute;
  right: px-to-rem(20);
  bottom: px-to-rem(20);
  color: $white;
}

.button-wrapper {
  visibility: hidden;
}

.metadata {
  padding: px-to-rem(12) px-to-rem(23) px-to-rem(12) px-to-rem(23);
  margin-bottom: px-to-rem(12);
  white-space: pre-line;

  &.is-focused {
    background: $dark-grey;

    .button-wrapper {
      visibility: visible;
    }
  }
}

.teaser.theme-white .metadata,
.teaser.theme-info .metadata {
  &.is-focused {
    color: $white;
  }

  color: $black;
}

.title {
  margin-bottom: px-to-rem(14);
}

.subtitle {
  margin-bottom: px-to-rem(24);
}

.label {
  position: absolute;
  top: px-to-rem(20);
  left: px-to-rem(24);
  width: 85%;
}

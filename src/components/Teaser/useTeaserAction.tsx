import { ZoneLink } from '@apptypes/PageResponseBody';
import { getRouteForContentType } from '@components/Teaser/teaserAction.helper';
import { ROUTES } from '@constants';
import { useCustomNavigate } from '@hooks/useCustomNavigate';
import { getRoute, ROUTE_PREFIX_PAGE } from '@routes/route';
import { Tracking } from '@tracking/Tracking';
import { useCallback, useState } from 'react';
import { useLocation } from 'react-router-dom';

import { MissingProgramIdError, useErrors } from '../../errors';
import { ITeaserProperties, ITeaserResponse } from '../../types';

function isDeeplink(teaser: ITeaserResponse): boolean {
  const { deeplink, program_id, template } = teaser;
  return program_id === null && !!deeplink && template !== 'single-pageHeader';
}

function isMissingProgramIdError(teaser: ITeaserResponse) {
  const { clip, program_id, trailer } = teaser;
  return !program_id && !clip && !trailer;
}

/**
 * priority rules for actioning teasers
 * 1. deeplink if it exists
 * 2. program_id
 * 3. clip
 * 4. trailer
 */
export function useTeaserAction() {
  const navigate = useCustomNavigate();
  const { setError } = useErrors();
  const [blockExtraKeys, setBlockExtraKeys] = useState(false);
  const location = useLocation();

  const handleTeaserPress = useCallback(
    (teaser: ITeaserProperties, isTrailer = false, link: ZoneLink | undefined) => {
      const { clip, trailer, deeplink, program_id, playable, template, emptyState, code } = teaser;
      const isLive = code === 'program_content';

      if (!playable && template === 'tableview-guide') return;
      if (emptyState) return;

      setBlockExtraKeys(true);

      Tracking.trackTeaserClick(teaser);

      if (link && link?.deeplink) {
        const linkValue = link.deeplink.startsWith('arte://') ? `${ROUTE_PREFIX_PAGE}${link.page}` : link.deeplink;
        navigate(getRoute(linkValue));
        return;
      }

      if (isDeeplink(teaser)) {
        // deeplink here is e.g. `PG-DOR` which is a route that gets parsed
        if (deeplink) navigate(getRoute(deeplink));
        return;
      }

      if (isTrailer) {
        const trailerId = trailer.split('/').pop();
        navigate(`${ROUTES.VERIFICATION}/${trailerId}`, { state: { isTrailer } });
        return;
      }

      if (program_id) {
        navigate(getRouteForContentType(teaser, location), { state: { isLive } });
        return;
      }

      if (isMissingProgramIdError(teaser)) {
        setError(new MissingProgramIdError());
        return;
      }

      if (clip) {
        const clipId = clip.split('/').pop();
        navigate(`${ROUTES.VERIFICATION}/${clipId}`);
      }
    },
    [navigate, location, setError],
  );

  return { handleTeaserPress, blockExtraKeys };
}

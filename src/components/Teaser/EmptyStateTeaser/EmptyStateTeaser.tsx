import { ITeaserProperties } from '@apptypes/ITeaserProperties';
import { UserContentType } from '@apptypes/UserContentType';
import { CenteredLayout } from '@components/CenteredLayout/CenteredLayout';
import React from 'react';
import { useTranslation } from 'react-i18next';

import { focusClassNames, withFocusable } from '../../../focus';
import styles from './empty-state-teaser.module.scss';

const getTranslationKey = (authenticatedContent?: UserContentType) => {
  switch (authenticatedContent) {
    case 'sso-favorites':
      return 'myVideos__empty_favorites';
    case 'sso-history':
      return 'myVideos__empty_history';
    case 'sso-personalzone':
      return 'myVideos__empty_personalzone';
    default:
      return '';
  }
};

export const EmptyStateTeaser = withFocusable(
  React.forwardRef<HTMLDivElement, ITeaserProperties>(
    ({ authenticatedContent, imageWidth, imageHeight, testIdFocused, ...properties }, reference) => {
      const { t } = useTranslation();
      return (
        <div
          data-testid-focused={testIdFocused ? 'true' : undefined}
          ref={reference}
          className={focusClassNames(styles, properties, styles['empty-state-teaser'])}
        >
          <CenteredLayout
            className={styles['text-wrapper']}
            style={{
              width: `${imageWidth}px`,
              height: `${imageHeight}px`,
            }}
          >
            {t(getTranslationKey(authenticatedContent))}
          </CenteredLayout>
        </div>
      );
    },
  ),
);

import { ITeaserProperties } from '../../types';
import { getLive, getPrime } from './getStickerInformation';

describe('getLive', () => {
  test('it should return undefined when stickers is null', () => {
    const properties: Partial<ITeaserProperties> = {
      // @ts-expect-error null test
      stickers: null,
    };

    const actual = getLive(properties as ITeaserProperties);
    const expected = undefined;

    expect(actual).toEqual(expected);
  });

  test('it should return undefined when stickers is not present', () => {
    const properties: Partial<ITeaserProperties> = {};

    const actual = getLive(properties as ITeaserProperties);
    const expected = undefined;

    expect(actual).toEqual(expected);
  });

  test('it should return undefined when stickers is an empty array', () => {
    const properties: Partial<ITeaserProperties> = {
      stickers: [],
    };

    const actual = getLive(properties as ITeaserProperties);
    const expected = undefined;

    expect(actual).toEqual(expected);
  });

  test('it should return undefined when no live stickers are found', () => {
    const properties: Partial<ITeaserProperties> = {
      stickers: [
        {
          code: 'UNKNOWN',
          label: 'Live',
        },
        {
          code: 'NOT_KNOWN',
          label: 'Live',
        },
      ],
    };

    const actual = getLive(properties as ITeaserProperties);
    const expected = undefined;

    expect(actual).toEqual(expected);
  });

  test('it should return ISticker object when sticker.code is LIVE', () => {
    const properties: Partial<ITeaserProperties> = {
      stickers: [
        {
          code: 'LIVE',
          label: 'Live',
        },
      ],
    };

    const actual = getLive(properties as ITeaserProperties);
    const expected = {
      code: 'LIVE',
      label: 'Live',
    };

    expect(actual).toEqual(expected);
  });

  test('it should return ISticker object when sticker.code is TV_LIVE', () => {
    const properties: Partial<ITeaserProperties> = {
      stickers: [
        {
          code: 'TV_LIVE',
          label: 'Live',
        },
      ],
    };

    const actual = getLive(properties as ITeaserProperties);
    const expected = {
      code: 'TV_LIVE',
      label: 'Live',
    };

    expect(actual).toEqual(expected);
  });
});

describe('getPrime', () => {
  test('it should return false when stickers is null', () => {
    const properties: Partial<ITeaserProperties> = {
      // @ts-expect-error null test
      stickers: null,
    };

    const actual = getPrime(properties as ITeaserProperties);
    const expected = false;

    expect(actual).toEqual(expected);
  });

  test('it should return false when stickers is not present', () => {
    const properties: Partial<ITeaserProperties> = {};

    const actual = getPrime(properties as ITeaserProperties);
    const expected = false;

    expect(actual).toEqual(expected);
  });

  test('it should return false when stickers is an empty array', () => {
    const properties: Partial<ITeaserProperties> = {
      stickers: [],
    };

    const actual = getPrime(properties as ITeaserProperties);
    const expected = false;

    expect(actual).toEqual(expected);
  });

  test('it should return true when sticker.code is PRIME_TIME', () => {
    const properties: Partial<ITeaserProperties> = {
      stickers: [
        {
          code: 'PRIME_TIME',
        },
      ],
    };

    const actual = getPrime(properties as ITeaserProperties);
    const expected = true;

    expect(actual).toEqual(expected);
  });
});

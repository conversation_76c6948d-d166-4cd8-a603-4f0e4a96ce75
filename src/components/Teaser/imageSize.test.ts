import { describe } from 'vitest';

import { getImageUrlWithTypeQueryParam, hasQueryParams, hasTypeQueryParam } from './imageSize';

describe('getImageQueryParams', () => {
  it('should return true if url has any query params', () => {
    const imageUrl = 'https://api-cdn.arte.tv/img/v2/image/haeM4krLzfVydGinC5V2J6/__SIZE__?foo=bar';
    const actual = hasQueryParams(imageUrl);
    const expected = true;
    expect(actual).toEqual(expected);
  });

  it('should return false if url has NO query params', () => {
    const imageUrl = 'https://api-cdn.arte.tv/img/v2/image/haeM4krLzfVydGinC5V2J6/__SIZE__';
    const actual = hasQueryParams(imageUrl);
    const expected = false;
    expect(actual).toEqual(expected);
  });
});

describe('hasTypeQueryParam', () => {
  it('should return true if image url has a `type` query param', () => {
    const imageUrl = 'https://api-cdn.arte.tv/img/v2/image/haeM4krLzfVydGinC5V2J6/__SIZE__?type=TEXT';
    const actual = hasTypeQueryParam(imageUrl);
    const expected = true;
    expect(actual).toEqual(expected);
  });

  it('should return false if image url does NOT have a `type` query param', () => {
    const imageUrl = 'https://api-cdn.arte.tv/img/v2/image/haeM4krLzfVydGinC5V2J6/__SIZE__?foo=bar';
    const actual = hasTypeQueryParam(imageUrl);
    const expected = false;
    expect(actual).toEqual(expected);
  });

  it('should return false if imageUrl is an empty string', () => {
    const imageUrl = '';
    const actual = hasTypeQueryParam(imageUrl);
    const expected = false;
    expect(actual).toEqual(expected);
  });

  it('should return false if imageUrl is a string with only whitespace', () => {
    const imageUrl = '   ';
    const actual = hasTypeQueryParam(imageUrl);
    const expected = false;
    expect(actual).toEqual(expected);
  });

  it('should return false if imageUrl is null', () => {
    const imageUrl = null;
    const actual = hasTypeQueryParam(imageUrl);
    const expected = false;
    expect(actual).toEqual(expected);
  });

  it('should return false if imageUrl is undefined', () => {
    const imageUrl = undefined;
    const actual = hasTypeQueryParam(imageUrl);
    const expected = false;
    expect(actual).toEqual(expected);
  });
});

describe('getImageUrlWithTypeQueryParam', () => {
  it('should add `?type=PHOTO` if there are not existing query params', () => {
    const imageUrl = 'https://api-cdn.arte.tv/img/v2/image/haeM4krLzfVydGinC5V2J6/__SIZE__';
    const actual = getImageUrlWithTypeQueryParam(imageUrl);
    const expected = 'https://api-cdn.arte.tv/img/v2/image/haeM4krLzfVydGinC5V2J6/__SIZE__?type=PHOTO';
    expect(actual).toEqual(expected);
  });

  it('should append `type=PHOTO` when there are existing query params', () => {
    const imageUrl = 'https://api-cdn.arte.tv/img/v2/image/haeM4krLzfVydGinC5V2J6/__SIZE__?watermark=true';
    const actual = getImageUrlWithTypeQueryParam(imageUrl);
    const expected = 'https://api-cdn.arte.tv/img/v2/image/haeM4krLzfVydGinC5V2J6/__SIZE__?watermark=true&type=PHOTO';
    expect(actual).toEqual(expected);
  });
});

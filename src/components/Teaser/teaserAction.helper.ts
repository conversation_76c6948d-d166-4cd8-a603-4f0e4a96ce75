import { ITeaserProperties } from '@apptypes/ITeaserProperties';
import { ROUTES } from '@constants';
import { getRoute, isVideoRoute } from '@routes/route';
import { isCollectionAndItemIdIsCollection } from '@util/isCollection';
import { Location } from 'react-router-dom';
import { config } from 'target';

function getRouteForProgramId(programId: string) {
  // when we have colour button support programs can be played directly via a teaser click
  if (isVideoRoute(programId) && config.hasColorButtonSupport) {
    return `${ROUTES.VERIFICATION}/${programId}`;
  }

  // when there is no support for colour buttons we redirect to the program page
  // so that users are presented with the program metadata before playing content
  return getRoute(programId);
}

/**
 * item_id with a language suffix
 * e.g. item_id: 'RC-019870_fr'
 *
 * note just checking here for an underscore followed by 2 letters here, I do not
 * think it is necessary to check for a valid supported language at this point
 */
function itemIdHasLanguageSuffix(itemId: string) {
  return /^.*_[a-z]{2}$/i.test(itemId);
}

export function getRouteForContentType(teaser: ITeaserProperties, location: Location) {
  const { program_id, item_id } = teaser;

  // in some cases we get an item_id with a language suffix, this can happen for zones such as
  // last chance, in this case we can safely use program_id
  if (itemIdHasLanguageSuffix(item_id)) return getRouteForProgramId(program_id);

  if (isCollectionAndItemIdIsCollection(teaser)) {
    // already on collection page so get route using program_id
    if (location.pathname.includes(ROUTES.COLLECTION)) return getRouteForProgramId(program_id);

    // this happens when we have a initial teaser as a bookmark and NOT the sso bookmark response, so we use `item_id`
    // to get the route for the collection page
    return getRoute(item_id);
  }

  return getRouteForProgramId(program_id);
}

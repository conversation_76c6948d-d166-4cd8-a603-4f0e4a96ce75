import { ITeaserProperties } from '@apptypes/ITeaserProperties';

enum AvailabilityCodes {
  LIVESTREAM_TV = 'LIVESTREAM_TV',
  LIVESTREAM_WEB = 'LIVESTREAM_WEB',
  VOD = 'VOD',
}

const hasAvailability = (properties: ITeaserProperties): boolean =>
  properties.availability !== undefined &&
  properties.availability !== null &&
  JSON.stringify(properties.availability) !== '{}';

const beginsToday = (properties: ITeaserProperties): boolean => {
  const { availability } = properties;
  if (!availability) return false;

  const today = new Date();
  const start = new Date(availability.start);

  return today.toDateString() === start.toDateString();
};

const formatTime = (date: string): string =>
  new Date(date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', hour12: false });
const formatDate = (date: string): string =>
  new Date(date).toLocaleDateString('en-GB', { day: '2-digit', month: '2-digit' });

const beginsLaterToday = (properties: ITeaserProperties): boolean => {
  const { availability } = properties;
  if (!availability) return false;

  const now = new Date();
  const start = new Date(availability.start);

  return now.toDateString() === start.toDateString() && now < start;
};

const beginsAfterToday = (properties: ITeaserProperties): boolean => {
  const { availability } = properties;
  if (!availability) return false;

  const now = new Date();
  const start = new Date(availability.start);

  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const startDate = new Date(start.getFullYear(), start.getMonth(), start.getDate());

  return startDate > today;
};

const isVOD = (properties: ITeaserProperties): boolean => properties.availability?.type === AvailabilityCodes.VOD;

const isLivestream = (properties: ITeaserProperties): boolean => isTvLive(properties) || isLivestreamWeb(properties);

const isTvLive = (properties: ITeaserProperties): boolean =>
  properties.availability?.type === AvailabilityCodes.LIVESTREAM_TV;

const isLivestreamWeb = (properties: ITeaserProperties): boolean =>
  properties.availability?.type === AvailabilityCodes.LIVESTREAM_WEB;

const isLiveRightNow = (properties: ITeaserProperties): boolean => {
  const availability = properties.availability;
  if (!availability) return false;

  const now = new Date();
  const start = new Date(availability.start);
  const end = new Date(availability.end);
  return now >= start && now <= end;
};

export {
  hasAvailability,
  beginsToday,
  beginsLaterToday,
  isVOD,
  isLivestream,
  beginsAfterToday,
  formatDate,
  formatTime,
  isLivestreamWeb,
  isLiveRightNow,
  isTvLive,
};

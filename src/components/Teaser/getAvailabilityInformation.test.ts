import { ITeaserProperties } from '@apptypes/ITeaserProperties';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import {
  beginsAfterToday,
  beginsLaterToday,
  beginsToday,
  hasAvailability,
  isLiveRightNow,
  isLivestream,
  isTvLive,
  isVOD,
} from './getAvailabilityInformation';

describe('Availability Utilities', () => {
  describe('hasAvailability', () => {
    it('should return true if the availability object is not null and not empty', () => {
      const properties: ITeaserProperties = { availability: { start: '2024-04-19T11:43:00Z' } };
      expect(hasAvailability(properties)).toBe(true);
    });

    it('should return false if there is no availability object', () => {
      const properties: ITeaserProperties = {};
      expect(hasAvailability(properties)).toBe(false);
    });

    it('should return false if the availability object is empty', () => {
      const properties: ITeaserProperties = { availability: {} };
      expect(hasAvailability(properties)).toBe(false);
    });
  });

  describe('beginsToday', () => {
    beforeEach(() => {
      vi.useFakeTimers().setSystemTime(new Date('2024-04-19'));
    });

    afterEach(() => {
      vi.useRealTimers();
    });

    it('should return false when availability is null', () => {
      const properties: Partial<ITeaserProperties> = { availability: null };
      const actual = beginsToday(properties as ITeaserProperties);
      const expected = false;
      expect(actual).toEqual(expected);
    });

    it('should return true if the event starts today', () => {
      const properties: ITeaserProperties = { availability: { start: '2024-04-19T12:00:00Z' } };
      expect(beginsToday(properties)).toBe(true);
    });

    it('should return false if the event starts on another day', () => {
      const properties: ITeaserProperties = { availability: { start: '2024-04-20T12:00:00Z' } };
      expect(beginsToday(properties)).toBe(false);
    });
  });

  describe('beginsLaterToday', () => {
    beforeEach(() => {
      vi.useFakeTimers().setSystemTime(new Date('2024-04-19T10:00:00Z'));
    });

    afterEach(() => {
      vi.useRealTimers();
    });

    it('should return false when availability is null', () => {
      const properties: Partial<ITeaserProperties> = { availability: null };
      const actual = beginsLaterToday(properties as ITeaserProperties);
      const expected = false;
      expect(actual).toEqual(expected);
    });

    it('should return true if the event starts later today', () => {
      const properties: ITeaserProperties = { availability: { start: '2024-04-19T11:43:00Z' } };
      expect(beginsLaterToday(properties)).toBe(true);
    });

    it('should return false if the event started earlier today', () => {
      const properties: ITeaserProperties = { availability: { start: '2024-04-19T09:00:00Z' } };
      expect(beginsLaterToday(properties)).toBe(false);
    });
  });

  describe('beginsAfterToday', () => {
    beforeEach(() => {
      vi.useFakeTimers().setSystemTime(new Date('2024-04-19'));
    });

    afterEach(() => {
      vi.useRealTimers();
    });

    it('should return false when availability is null', () => {
      const properties: Partial<ITeaserProperties> = { availability: null };
      const actual = beginsAfterToday(properties as ITeaserProperties);
      const expected = false;
      expect(actual).toEqual(expected);
    });

    it('should return true if the event begins after today', () => {
      const properties: ITeaserProperties = { availability: { start: '2024-04-20T00:00:00Z' } };
      expect(beginsAfterToday(properties)).toBe(true);
    });

    it('should return false if the event begins today or earlier', () => {
      const properties: ITeaserProperties = { availability: { start: '2024-04-19T00:00:00Z' } };
      expect(beginsAfterToday(properties)).toBe(false);
    });
  });

  describe('isVOD', () => {
    it('should return false when availability is null', () => {
      const properties: Partial<ITeaserProperties> = { availability: null };
      const actual = isVOD(properties as ITeaserProperties);
      const expected = false;
      expect(actual).toEqual(expected);
    });

    it('should return true if the type is VOD', () => {
      const properties: ITeaserProperties = { availability: { type: 'VOD' } };
      expect(isVOD(properties)).toBe(true);
    });

    it('should return false if the type is not VOD', () => {
      const properties: ITeaserProperties = { availability: { type: 'LIVESTREAM_TV' } };
      expect(isVOD(properties)).toBe(false);
    });
  });

  describe('isLivestream', () => {
    it('should return false when availability is null', () => {
      const properties: Partial<ITeaserProperties> = { availability: null };
      const actual = isLivestream(properties as ITeaserProperties);
      const expected = false;
      expect(actual).toEqual(expected);
    });

    it('should return true if the type is LIVESTREAM_TV', () => {
      const properties: ITeaserProperties = { availability: { type: 'LIVESTREAM_TV' } };
      expect(isLivestream(properties)).toBe(true);
    });

    it('should return true if the type is LIVESTREAM_WEB', () => {
      const properties: ITeaserProperties = { availability: { type: 'LIVESTREAM_WEB' } };
      expect(isLivestream(properties)).toBe(true);
    });

    it('should return false if the type is not a livestream', () => {
      const properties: ITeaserProperties = { availability: { type: 'VOD' } };
      expect(isLivestream(properties)).toBe(false);
    });
  });

  describe('isTvLive', () => {
    it('should return false when availability is null', () => {
      const properties: Partial<ITeaserProperties> = { availability: null };
      const actual = isTvLive(properties as ITeaserProperties);
      const expected = false;
      expect(actual).toEqual(expected);
    });
  });

  describe('isLiveRightNow', () => {
    it('should return false when availability is null', () => {
      const properties: Partial<ITeaserProperties> = { availability: null };
      const actual = isLiveRightNow(properties as ITeaserProperties);
      const expected = false;
      expect(actual).toEqual(expected);
    });
  });
});

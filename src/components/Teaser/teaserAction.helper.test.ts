import { Location } from 'react-router-dom';
import { config } from 'target';
import { describe, vi } from 'vitest';

import { ROUTES } from '../../constants';
import { Bookmark, ITeaserProperties } from '../../types';
import { getRouteForContentType } from './teaserAction.helper';

const bookmarkedCollectionTeaser: Partial<Bookmark> = {
  item_id: 'RC-022404',
  title: 'Brigade Mobile',
  kind: {
    code: 'TV_SERIES',
    isCollection: true,
    label: null,
  },
  program_id: '100739-001-A',
};

const bookmarkedCollectionTeaserSSO: Partial<Bookmark> = {
  id: 'RC-022404_fr',
  kind: {
    isCollection: true,
  },
  // @ts-expect-error SSO response contains `programId` whose value is copied to `program_id` to be compatible with middleware responses
  programId: 'RC-022404',
  title: 'Brigade Mobile',
  program_id: 'RC-022404',
};

const collectionTeaser: Partial<ITeaserProperties> = {
  item_id: 'RC-019324',
  title: 'Amours solitaires',
  kind: {
    code: 'TV_SERIES',
    isCollection: true,
    label: null,
  },
  program_id: '116112-001-A',
  template: 'single-collectionContent',
};

const showTeaser: Partial<ITeaserProperties> = {
  item_id: '122669-000-A_fr',
  title: 'Double destinée',
  kind: {
    code: 'SHOW',
    isCollection: false,
    label: 'Programme',
  },
  program_id: '122669-000-A',
};

const collectionTeaserWithSuffixItemId: Partial<ITeaserProperties> = {
  item_id: 'RC-019870_fr',
  title: 'Fluide',
  kind: {
    code: 'TV_SERIES',
    isCollection: true,
    label: 'Série',
  },
  program_id: 'RC-019870',
};

const locationCollection: Location = {
  pathname: ROUTES.COLLECTION,
  search: '',
  hash: '',
  state: null,
  key: '',
};

const locationHome: Location = {
  pathname: ROUTES.ROOT,
  search: '',
  hash: '',
  state: null,
  key: '',
};

describe('getRouteForContentType', () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  describe('hasColorButtonSupport true', () => {
    vi.mock('target', () => {
      return {
        config: {
          hasColorButtonSupport: true,
        },
      };
    });

    beforeEach(() => {
      vi.mocked(config).hasColorButtonSupport = true;
    });

    afterEach(() => {
      vi.restoreAllMocks();
    });

    it('should return a collection route for a bookmarked COLLECTION teaser', () => {
      expect(config.hasColorButtonSupport).toBe(true);
      const actual = getRouteForContentType(bookmarkedCollectionTeaser as Bookmark, locationHome);
      const expected = `${ROUTES.COLLECTION}/${bookmarkedCollectionTeaser.item_id}`;
      expect(actual).toEqual(expected);
    });

    it('should return a collection route for a bookmarked COLLECTION teaser from SSO', () => {
      expect(config.hasColorButtonSupport).toBe(true);
      const actual = getRouteForContentType(bookmarkedCollectionTeaserSSO as Bookmark, locationHome);
      const expected = `${ROUTES.COLLECTION}/${bookmarkedCollectionTeaserSSO.program_id}`;
      expect(actual).toEqual(expected);
    });

    /**
     * this case can happen for generated/automatic teasers such as last chance where the item_id
     * is suffixed with language code
     */
    it('should return a collection route for COLLECTION teaser with suffixed item_id', () => {
      expect(config.hasColorButtonSupport).toBe(true);
      const actual = getRouteForContentType(collectionTeaserWithSuffixItemId as ITeaserProperties, locationHome);
      const expected = `${ROUTES.COLLECTION}/${collectionTeaserWithSuffixItemId.program_id}`;
      expect(actual).toEqual(expected);
    });

    it('should return a /verification route for a COLLECTION and we are on the /collection route', () => {
      expect(config.hasColorButtonSupport).toBe(true);
      // mock location.pathname so we can test against `/collection` route
      const actual = getRouteForContentType(collectionTeaser as ITeaserProperties, locationCollection);
      const expected = `${ROUTES.VERIFICATION}/${collectionTeaser.program_id}`;
      expect(actual).toEqual(expected);
    });

    it('should return a /verification route for a SHOW', () => {
      expect(config.hasColorButtonSupport).toBe(true);
      const actual = getRouteForContentType(showTeaser as ITeaserProperties, locationHome);
      const expected = `${ROUTES.VERIFICATION}/${showTeaser.program_id}`;
      expect(actual).toEqual(expected);
    });
  });

  describe('hasColorButtonSupport false', () => {
    vi.mock('target', () => {
      return {
        config: {
          hasColorButtonSupport: false,
        },
      };
    });

    beforeEach(() => {
      vi.mocked(config).hasColorButtonSupport = false;
    });

    afterEach(() => {
      vi.restoreAllMocks();
    });

    it('should return a collection route for a bookmarked COLLECTION teaser', () => {
      expect(config.hasColorButtonSupport).toBe(false);
      const actual = getRouteForContentType(bookmarkedCollectionTeaser as Bookmark, locationHome);
      const expected = `${ROUTES.COLLECTION}/${bookmarkedCollectionTeaser.item_id}`;
      expect(actual).toEqual(expected);
    });

    it('should return a collection route for a bookmarked COLLECTION teaser from SSO', () => {
      expect(config.hasColorButtonSupport).toBe(false);
      const actual = getRouteForContentType(bookmarkedCollectionTeaserSSO as Bookmark, locationHome);
      const expected = `${ROUTES.COLLECTION}/${bookmarkedCollectionTeaserSSO.program_id}`;
      expect(actual).toEqual(expected);
    });

    /**
     * this case can happen for generated/automatic teasers such as last chance where the item_id
     * is suffixed with language code
     */
    it('should return a collection route for COLLECTION teaser with suffixed item_id', () => {
      expect(config.hasColorButtonSupport).toBe(false);
      const actual = getRouteForContentType(collectionTeaserWithSuffixItemId as ITeaserProperties, locationHome);
      const expected = `${ROUTES.COLLECTION}/${collectionTeaserWithSuffixItemId.program_id}`;
      expect(actual).toEqual(expected);
    });

    it('should return a /program route for a COLLECTION and we are on the /collection route', () => {
      expect(config.hasColorButtonSupport).toBe(false);
      // mock location.pathname so we can test against `/collection` route
      const actual = getRouteForContentType(collectionTeaser as ITeaserProperties, locationCollection);
      const expected = `${ROUTES.PROGRAM}/${collectionTeaser.program_id}`;
      expect(actual).toEqual(expected);
    });

    it('should return a /program route for a SHOW', () => {
      expect(config.hasColorButtonSupport).toBe(false);
      const actual = getRouteForContentType(showTeaser as ITeaserProperties, locationHome);
      const expected = `${ROUTES.PROGRAM}/${showTeaser.program_id}`;
      expect(actual).toEqual(expected);
    });
  });
});

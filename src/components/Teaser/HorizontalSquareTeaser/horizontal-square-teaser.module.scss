@use '~styles/globals' as *;

.teaser {
  position: relative;
  margin-right: $rail-item-gap;
  width: $teaser-horizontal-square-width;

  @media (min-width: 1920px) {
    width: $teaser-horizontal-square-width-fhd;
  }
}

.teaser a {
  display: block;
  border-style: solid;
  border-color: transparent;
  border-width: $focused-border-width;
  position: relative;

  @media (min-width: 1920px) {
    border-width: $focused-border-width-fhd;
  }

  &.is-focused {
    background-color: $focused-border-color;
  }
}

.teaser.theme-white a,
.teaser.theme-info a {
  &.is-focused {
    background-color: $theme-white-border-color;
  }
}

.title {
  font-family: $font-family-bold;
  font-size: px-to-rem(32);
  word-wrap: break-word;
}

.subtitle {
  font-size: px-to-rem(32);
  word-wrap: break-word;
}

.metadata {
  padding: px-to-rem(16);
  white-space: pre-line;

  &.is-focused {
    background: $dark-grey;
  }
}

.genre {
  position: absolute;
  right: px-to-rem(20);
  top: px-to-rem(145);
}

.button-wrapper {
  padding-top: px-to-rem(4);
  visibility: hidden;
  background-color: $dark-grey;

  &.is-focused {
    visibility: visible;
  }

  p {
    font-size: px-to-rem(20);
    width: 70%;
  }
}

import { LinkPlaceholder } from '@components/LinkPlaceholder/LinkPlaceholder';
import { getGenreLabel, shouldDisplayGenreLabel } from '@util/genre';
import classNames from 'classnames';
import i18n from 'i18next';
import React from 'react';
import { config } from 'target';

import { THEMES } from '../../../constants';
import { withFocusable } from '../../../focus';
import { ITeaserProperties } from '../../../types';
import { ColorButton } from '../../ColorButton/ColorButton';
import { Genre } from '../../Genre/Genre';
import { Image } from '../../Image/Image';
import { ImagePlaceholder } from '../../Image/ImagePlaceholder';
import { ProgressTeaser } from '../../ProgressTeaser/ProgressTeaser';
import { useInfoLayer } from '../useInfoLayer';
import styles from './horizontal-square-teaser.module.scss';

export const HorizontalSquareTeaser = withFocusable(
  React.forwardRef<HTMLDivElement, ITeaserProperties>(({ className, ...properties }, reference) => {
    useInfoLayer(properties);
    const theme = properties?.theme;
    const link = properties?.link;

    function getColorButton() {
      return (
        <div className={classNames(styles['button-wrapper'], { [styles['is-focused']]: properties.isFocused })}>
          <ColorButton color="green" label={i18n.t('more_information')} small={true} />
        </div>
      );
    }

    return (
      <div
        ref={reference}
        className={classNames(styles.teaser, className, theme && styles[THEMES[theme.toUpperCase()]])}
        data-template={properties.template}
      >
        {link ? (
          <a
            href="#"
            className={classNames(styles['image-area'], { [styles['is-focused']]: properties.isFocused })}
            onClick={properties.onMouseClick}
          >
            <LinkPlaceholder width={properties.imageWidth} height={properties.imageHeight} title={link.title} />
          </a>
        ) : (
          <>
            <a
              href="#"
              className={classNames(styles['image-area'], { [styles['is-focused']]: properties.isFocused })}
              onClick={properties.onMouseClick}
            >
              <ProgressTeaser viewedProgress={properties.viewedProgress} />
              <ImagePlaceholder>
                <Image
                  width={properties.imageWidth}
                  height={properties.imageHeight}
                  src={properties.image}
                  loadingStrategy="lazy"
                />
              </ImagePlaceholder>
            </a>
            {shouldDisplayGenreLabel(properties.theme) && (
              <Genre label={getGenreLabel(properties)} className={styles.genre} />
            )}
            {properties.showItemTitle && (
              <div className={classNames(styles.metadata, { [styles['is-focused']]: properties.isFocused })}>
                <h3 className={styles.title}>{properties.title}</h3>
                <p className={styles.subtitle}>{properties.subtitle}</p>
                {config.hasColorButtonSupport && getColorButton()}
              </div>
            )}
          </>
        )}
      </div>
    );
  }),
);

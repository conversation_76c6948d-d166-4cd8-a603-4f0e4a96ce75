import { ITeaserProperties } from '@apptypes/ITeaserProperties';
import { Button } from '@components/Button/Button';
import { useTeaserAction } from '@components/Teaser';
import { getLive } from '@components/Teaser/getStickerInformation';
import { TEST_ID } from '@constants';
import { t } from 'i18next';

import { FOCUS_KEY_SIDE_MENU } from '../../../focus';

interface IPlayButtonProperties extends ITeaserProperties {
  nextDownToFocus?: string;
}

export function PlayButton(properties: IPlayButtonProperties) {
  const { handleTeaserPress } = useTeaserAction();
  const { callToAction, clip, nextDownToFocus, setFocus, template, watchOrResumeLabel } = properties;

  let label = watchOrResumeLabel;
  if (template === 'single-pageHeader') {
    label = clip ? callToAction : watchOrResumeLabel;
  }

  if (properties?.isLivestreamWebTemplate && getLive(properties)) {
    label = t('call_to_action__live');
  }

  if (properties?.isLivestreamWebTemplate && !getLive(properties)) {
    label = t('call_to_action__livestream');
  }

  return (
    <Button
      testId={TEST_ID.TOP_TEASER_PLAY}
      icon="play"
      label={label}
      onArrowPress={(direction) => {
        switch (direction) {
          case 'left':
            setFocus(FOCUS_KEY_SIDE_MENU);
            return false;
          case 'down':
            if (nextDownToFocus) {
              setFocus(nextDownToFocus);
              return false;
            }
            return true;
          case 'right':
            return true;
          default:
            return false;
        }
      }}
      onEnterPress={() => handleTeaserPress(properties, false, undefined)}
    />
  );
}

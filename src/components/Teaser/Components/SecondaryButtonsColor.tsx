import { ITeaserProperties } from '@apptypes/ITeaserProperties';
import { ColorButton } from '@components/ColorButton/ColorButton';
import { ScrollableModal } from '@components/Modal/ScrollableModal/ScrollableModal';
import { useTeaserAction } from '@components/Teaser';
import { useModalContext } from '@providers/ModalContext';
import { ClickType } from '@tracking/clickType';
import { Tracking } from '@tracking/Tracking';
import { ControlGroupName, ControlGroupType } from '@tracking/types';
import i18n from 'i18next';
import { useRemoteController } from 'react-remote-controller';
import { getKeyMap } from 'target';

import { teaserFocusKey } from '../../../focus';
import styles from './secondary-buttons-color.module.scss';
import { getModalTypeForTemplate } from './SecondaryButtons.helper';

export function SecondaryButtonsColor(properties: ITeaserProperties) {
  const { handleTeaserPress } = useTeaserAction();
  const { showModal } = useModalContext();
  const showYellowButton = properties.trailer;

  useRemoteController<ReturnType<typeof getKeyMap>>({
    listenTo: {
      yellow: () => {
        if (!showYellowButton || !properties.isFocused) return;

        handleTeaserPress(properties, true, undefined);
        Tracking.trackTeaserClick(properties, ControlGroupType.TEASER_ACTIONS, ControlGroupName.WATCH_TRAILER);

        Tracking.trackClick(ClickType.YELLOW_BUTTON, properties);
      },
      green: () => {
        if (!properties.isFocused || !properties.description) return;

        Tracking.trackTeaserClick(properties, ControlGroupType.TEASER_ACTIONS, ControlGroupName.MORE_INFO);

        Tracking.trackClick(ClickType.GREEN_BUTTON, properties);

        const Modal = getModalTypeForTemplate(properties.template);
        showModal({
          content: (
            <ScrollableModal returnFocusKey={teaserFocusKey(properties.zoneIndex, properties.position)} closeOnGreen>
              <Modal {...properties} />
            </ScrollableModal>
          ),
        });
      },
    },
  });

  return (
    <div className={styles.wrapper}>
      {showYellowButton && <ColorButton color="yellow" label={i18n.t('trailer__watch')} />}
      {properties.description && <ColorButton color="green" label={i18n.t('moreInfos')} />}
    </div>
  );
}

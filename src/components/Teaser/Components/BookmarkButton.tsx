import { ITeaserProperties } from '@apptypes/ITeaserProperties';
import { Button, IButtonProperties } from '@components/Button/Button';
import { IconType } from '@components/Icon/Icon';
import { toastAddReminder } from '@components/ToastNotification/toasts';
import { FocusHandler } from '@noriginmedia/norigin-spatial-navigation';
import { BookmarksContext } from '@providers/BookmarksContext';
import { canRemind, getBookmarkIcon, getBookmarkLabel } from '@util/canBookmark';
import i18n from 'i18next';
import { useContext } from 'react';

import { teaserFocusGuard } from '../../../focus';

interface IBookmarkButtonProperties extends ITeaserProperties, Pick<IButtonProperties, 'small'> {
  // a teaser has its own focusKey and onFocus handler
  // with buttonFocusKey and onButtonFocus we're targeting only the button
  buttonFocusKey: string;
  onButtonFocus: FocusHandler;
}

export function BookmarkButton(properties: IBookmarkButtonProperties) {
  const { buttonFocusKey, small, onButtonFocus } = properties;
  const { addBookmark, removeBookmark, isBookmarked } = useContext(BookmarksContext);
  const isReminderAllowed = canRemind(properties);

  const bookmarkProperties = { ...properties };
  if (properties.template === 'single-collectionContent') {
    if (bookmarkProperties.item_id.includes('RC-')) {
      bookmarkProperties.image += '?type=TEXT';
      bookmarkProperties.landscapeImage += '?type=TEXT';
      if (!bookmarkProperties.duration) {
        bookmarkProperties.duration = i18n.t('series');
      }
    }
  }
  return (
    <Button
      small={small}
      icon={getBookmarkIcon(isBookmarked(properties), isReminderAllowed) as IconType}
      label={getBookmarkLabel(isBookmarked(properties), isReminderAllowed)}
      onArrowPress={(direction) => {
        // small button is rendered in a different position so does not require the focus guard
        if (small) return true;
        return teaserFocusGuard(direction, properties.zoneIndex, 1, properties.setFocus);
      }}
      onEnterPress={() => {
        if (isBookmarked(properties)) {
          removeBookmark(bookmarkProperties);
        } else {
          addBookmark(bookmarkProperties);
          canRemind(properties) && toastAddReminder();
        }
      }}
      onFocus={onButtonFocus}
      focusKey={buttonFocusKey}
    />
  );
}

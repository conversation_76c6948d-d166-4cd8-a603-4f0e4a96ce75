import { ITeaserProperties } from '@apptypes/ITeaserProperties';
import { canBookmark } from '@util/canBookmark';
import { useState } from 'react';
import { config } from 'target';

import styles from './action-buttons.module.scss';
import { BookmarkButton } from './BookmarkButton';
import { PlayButton } from './PlayButton';
import { SecondaryButtons } from './SecondaryButtons';
import { SecondaryButtonsColor } from './SecondaryButtonsColor';

export function ActionButtons(properties: ITeaserProperties) {
  // focus key of the last selected secondary button
  // we need it to know what to focus next when going down from the play button
  const [focusedSecondaryButton, setFocusedSecondaryButton] = useState('');

  return (
    <div className={styles.wrapper}>
      <PlayButton nextDownToFocus={focusedSecondaryButton} {...properties} />
      {config.hasColorButtonSupport && (
        <>
          {canBookmark(properties) && <BookmarkButton {...properties} />}
          <SecondaryButtonsColor {...properties} />
        </>
      )}
      {!config.hasColorButtonSupport && (
        <SecondaryButtons setFocusedSecondaryButton={setFocusedSecondaryButton} {...properties} />
      )}
    </div>
  );
}

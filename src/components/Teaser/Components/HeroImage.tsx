import { ITeaserProperties } from '@apptypes/ITeaserProperties';
import { Image } from '@components/Image/Image';
import { ImagePlaceholder } from '@components/Image/ImagePlaceholder';
import React from 'react';

import styles from './hero-image.module.scss';

export function HeroImage(properties: ITeaserProperties) {
  return (
    <div className={styles.wrapper}>
      <ImagePlaceholder size="large">
        <Image
          width={properties.imageWidth}
          height={properties.imageHeight}
          className={styles.image}
          src={properties.image}
          loadingStrategy="lazy"
        />
      </ImagePlaceholder>
      <div className={styles.gradient}></div>
    </div>
  );
}

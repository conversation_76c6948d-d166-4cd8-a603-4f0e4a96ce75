@use '~styles/globals' as *;

.wrapper {
  position: relative;
  width: $hero-image-width;
  height: $hero-image-height;

  @media (min-width: 1920px) {
    width: $hero-image-width-fhd;
    height: $hero-image-height-fhd;
  }
}

.gradient {
  position: absolute;
  top: 0;
  width: 100%;
  height: $hero-image-height;
  background-repeat: no-repeat;
  background-image: url('@assets/img/landscape-hl-teaser-gradient-720p.png');

  @media (min-width: 1920px) {
    height: $hero-image-height-fhd;
    background-image: url('@assets/img/landscape-hl-teaser-gradient-1080p.png');
  }
}

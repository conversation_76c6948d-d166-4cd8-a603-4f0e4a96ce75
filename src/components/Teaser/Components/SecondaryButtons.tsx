import { ITeaserProperties } from '@apptypes/ITeaserProperties';
import { Button } from '@components/Button/Button';
import { ScrollableModal } from '@components/Modal/ScrollableModal/ScrollableModal';
import { useModalContext } from '@providers/ModalContext';
import { canBookmark } from '@util/canBookmark';
import i18n from 'i18next';
import { Dispatch, SetStateAction } from 'react';

import {
  FOCUS_KEY_SIDE_MENU,
  FOCUS_KEY_TOP_TEASER_BOOKMARK,
  FOCUS_KEY_TOP_TEASER_MORE_INFO,
  FOCUS_KEY_TOP_TEASER_TRAILER,
} from '../../../focus';
import { useTeaserAction } from '../useTeaserAction';
import { BookmarkButton } from './BookmarkButton';
import styles from './secondary-buttons.module.scss';
import { getModalTypeForTemplate } from './SecondaryButtons.helper';

interface ISecondaryButtonProperties extends ITeaserProperties {
  setFocusedSecondaryButton: Dispatch<SetStateAction<string>>; // tells the parent component which button receives focus
}

export function SecondaryButtons(properties: ISecondaryButtonProperties) {
  const { handleTeaserPress } = useTeaserAction();
  const { showModal } = useModalContext();
  const { setFocusedSecondaryButton } = properties;

  function getTrailerButton() {
    return (
      <Button
        small
        label={i18n.t('trailer__watch')}
        focusKey={FOCUS_KEY_TOP_TEASER_TRAILER}
        onFocus={() => setFocusedSecondaryButton(FOCUS_KEY_TOP_TEASER_TRAILER)}
        onEnterPress={() => handleTeaserPress(properties, true, undefined)}
      ></Button>
    );
  }

  return (
    <div className={styles.wrapper}>
      <Button
        small
        className={styles['button']}
        label={i18n.t('moreInfos')}
        focusKey={FOCUS_KEY_TOP_TEASER_MORE_INFO}
        onFocus={() => setFocusedSecondaryButton(FOCUS_KEY_TOP_TEASER_MORE_INFO)}
        onArrowPress={(direction) => {
          switch (direction) {
            case 'left':
              properties.setFocus(FOCUS_KEY_SIDE_MENU);
              return false;
            default:
              return true;
          }
        }}
        onEnterPress={() => {
          const Modal = getModalTypeForTemplate(properties.template);
          showModal({
            content: (
              <ScrollableModal returnFocusKey={FOCUS_KEY_TOP_TEASER_MORE_INFO} closeOnEnter>
                <Modal {...properties} />
              </ScrollableModal>
            ),
          });
        }}
      ></Button>

      {canBookmark(properties) && (
        <BookmarkButton
          small={true}
          buttonFocusKey={FOCUS_KEY_TOP_TEASER_BOOKMARK}
          onButtonFocus={() => setFocusedSecondaryButton(FOCUS_KEY_TOP_TEASER_BOOKMARK)}
          {...properties}
        />
      )}

      {properties.trailer && getTrailerButton()}
    </div>
  );
}

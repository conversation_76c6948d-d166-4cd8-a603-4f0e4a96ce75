@use '~styles/globals' as *;

.label {
  position: relative;
  display: inline-block;
  padding: px-to-rem(8) px-to-rem(16);
  background-color: $white;
  color: $black;
  font-size: px-to-rem(25);
  font-family: $font-family-bold;
}

.label.livestream-web-template,
.label.live-web-template {
  &::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    margin: auto 0;
    width: px-to-rem(12);
    height: px-to-rem(12);
    border-radius: 50%;
  }

  span {
    margin-left: px-to-rem(18);
  }
}

.label.livestream-web-template::before {
  background-color: $border-live-concert-color;
}

.label.live-web-template::before {
  background-color: $border-live-color;
}

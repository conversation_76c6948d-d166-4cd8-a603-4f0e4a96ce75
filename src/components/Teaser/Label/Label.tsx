import { ITeaserProperties } from '@apptypes/ITeaserProperties';
import { ITvGuideTeaserProperties } from '@apptypes/ITvGuideTeaserProperties';
import { Attributes } from '@apptypes/VideoResponseBody';
import classNames from 'classnames';
import React, { FC } from 'react';

import styles from './label.module.scss';

interface LabelProps {
  label: string;
  properties: ITeaserProperties | ITvGuideTeaserProperties | Attributes;
}

export const Label: FC<LabelProps> = ({ label, properties }) => {
  return (
    <p
      className={classNames(
        styles.label,
        {
          [styles['livestream-web-template']]: properties?.isLivestreamWebTemplate,
        },
        { [styles['live-web-template']]: properties?.isLiveWebTemplate },
      )}
    >
      <span>{label}</span>
    </p>
  );
};

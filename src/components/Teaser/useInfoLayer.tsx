import { getLivestreamWebTemplate, getLiveWebTemplate } from '@components/Teaser/getLivestreamWebTemplate';
import { ClickType } from '@tracking/clickType';
import { ControlGroupName, ControlGroupType } from '@tracking/types';
import { useRemoteController } from 'react-remote-controller';
import { useLoaderData } from 'react-router-dom';
import { getKeyMap } from 'target';

import { teaserFocusKey } from '../../focus';
import { useModalContext } from '../../providers/ModalContext';
import { Tracking } from '../../tracking/Tracking';
import { ITeaserProperties, PageResponseBody } from '../../types';
import { TeaserMoreOptionsModal } from '../Modal/TeaserMoreOptionsModal/TeaserMoreOptionsModal';

export function useInfoLayer(properties: ITeaserProperties) {
  const { showModal, modalOpen } = useModalContext();
  const { zones } = useLoaderData() as PageResponseBody;
  const returnFocusKey = teaserFocusKey(properties.zoneIndex, properties.position);
  const { shouldBlockExtraKeys } = properties;
  const liveLabelTemplate = {
    isLiveWebTemplate: getLiveWebTemplate(properties),
    isLivestreamWebTemplate: getLivestreamWebTemplate(properties),
  };
  useRemoteController<ReturnType<typeof getKeyMap>>({
    listenTo: {
      green: () => {
        if (modalOpen) return;

        if (properties.link) return;
        if (properties.placeholderMode) return;
        if (shouldBlockExtraKeys || !properties.isFocused || properties.program_id === null) return;

        Tracking.trackClick(ClickType.GREEN_BUTTON, properties);
        Tracking.trackTeaserClick(properties, ControlGroupType.TEASER_ACTIONS, ControlGroupName.MORE_INFO);

        showModal({
          content: (
            <TeaserMoreOptionsModal
              returnFocusKey={returnFocusKey}
              zones={zones}
              {...{ ...properties, ...liveLabelTemplate }}
            />
          ),
        });
      },
    },
  });
}

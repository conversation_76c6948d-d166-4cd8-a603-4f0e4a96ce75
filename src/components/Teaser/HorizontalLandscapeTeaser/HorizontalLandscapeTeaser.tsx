import { LinkPlaceholder } from '@components/LinkPlaceholder/LinkPlaceholder';
import { truncateClassNames } from '@features/truncate/truncate';
import { getGenreLabel, shouldDisplayGenreLabel } from '@util/genre';
import { getNumberOfLines, LandscapeTeaserTexts } from '@util/linesInText';
import classNames from 'classnames';
import i18n from 'i18next';
import React from 'react';
import { config } from 'target';

import { focusClassNames, withFocusable } from '../../../focus';
import { ITeaserProperties } from '../../../types';
import { ColorButton } from '../../ColorButton/ColorButton';
import { Genre } from '../../Genre/Genre';
import { Image } from '../../Image/Image';
import { ImagePlaceholder } from '../../Image/ImagePlaceholder';
import { ProgressTeaser } from '../../ProgressTeaser/ProgressTeaser';
import { getLabel } from '../getLabel';
import { Label } from '../Label/Label';
import { useInfoLayer } from '../useInfoLayer';
import styles from './horizontal-landscape-teaser.module.scss';

export const HorizontalLandscapeTeaser = withFocusable(
  React.forwardRef<HTMLDivElement, ITeaserProperties>(({ className, ...properties }, reference) => {
    useInfoLayer(properties);
    const label = getLabel(properties);
    const theme = properties?.theme;
    const link = properties?.link;
    const fixedHeight = properties?.fixedHeight;
    const maxLines = 4;
    const titleLines = Math.min(maxLines, getNumberOfLines(properties.title, LandscapeTeaserTexts.TITLE));
    const subtitleLines = Math.min(
      maxLines - titleLines,
      getNumberOfLines(properties.subtitle, LandscapeTeaserTexts.SUBTITLE),
    );

    function getColorButton() {
      return (
        <div className={classNames(styles['button-wrapper'], { [styles['is-focused']]: properties.isFocused })}>
          <ColorButton color="green" label={i18n.t('moreInfos')} />
        </div>
      );
    }

    return (
      <div
        ref={reference}
        className={classNames(styles.teaser, className, theme && styles[theme], fixedHeight && styles['fixed-height'])}
        data-template={properties.template}
      >
        {link ? (
          <a
            href="#"
            onClick={properties.onMouseClick}
            className={classNames(
              { [styles['is-focused']]: properties.isFocused },
              { [styles['livestream-web-template']]: properties?.isLivestreamWebTemplate },
              { [styles['live-web-template']]: properties?.isLiveWebTemplate },
            )}
            data-testid-focused={properties.testIdFocused ? 'true' : undefined}
          >
            <LinkPlaceholder width={properties.imageWidth} height={properties.imageHeight} title={link.title} />
          </a>
        ) : (
          <>
            <a
              href="#"
              onClick={properties.onMouseClick}
              className={classNames(
                { [styles['is-focused']]: properties.isFocused },
                { [styles['livestream-web-template']]: properties?.isLivestreamWebTemplate },
                { [styles['live-web-template']]: properties?.isLiveWebTemplate },
              )}
              data-testid-focused={properties.testIdFocused ? 'true' : undefined}
              data-kind={properties?.dataKind}
            >
              <ProgressTeaser viewedProgress={properties.viewedProgress} />
              <ImagePlaceholder>
                <Image
                  className={focusClassNames(styles, properties)}
                  width={properties.imageWidth}
                  height={properties.imageHeight}
                  src={properties.image}
                  loadingStrategy="lazy"
                />
              </ImagePlaceholder>

              {shouldDisplayGenreLabel(properties.theme) && (
                <Genre label={getGenreLabel(properties)} className={styles.genre} />
              )}
            </a>

            {properties.showItemTitle && (
              <div className={classNames(styles.metadata, { [styles['is-focused']]: properties.isFocused })}>
                <h3 className={truncateClassNames(titleLines, styles.title)}>{properties.title}</h3>
                <p className={truncateClassNames(subtitleLines)}>{properties.subtitle}</p>
                {label && (
                  <div className={styles.label}>
                    <Label label={label} properties={properties} />
                  </div>
                )}
                {config.hasColorButtonSupport && getColorButton()}
              </div>
            )}
          </>
        )}
      </div>
    );
  }),
);

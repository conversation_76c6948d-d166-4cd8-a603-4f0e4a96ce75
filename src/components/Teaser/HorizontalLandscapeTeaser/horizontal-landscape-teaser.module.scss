@use '~styles/globals' as *;

.teaser {
  position: relative;
  margin-right: $rail-item-gap;
  width: $teaser-horizontal-landscape-width;

  @media (min-width: 1920px) {
    width: $teaser-horizontal-landscape-width-fhd;
  }
}

.teaser.fixed-height {
  height: $teaser-horizontal-landscape-fixed-height;

  @media (min-width: 1920px) {
    height: $teaser-horizontal-landscape-fixed-height-fhd;
  }
}

.teaser a {
  display: block;
  position: relative;
  border-style: solid;
  border-color: transparent;
  border-width: $focused-border-width;

  @media (min-width: 1920px) {
    border-width: $focused-border-width-fhd;
  }

  &.is-focused {
    background-color: $focused-border-color;
  }

  &.livestream-web-template {
    background-color: $border-live-concert-color;
  }

  &.live-web-template {
    background-color: $arte-highlight;
  }
}

.teaser.theme-white,
.teaser.theme-info {
  & a:not(.livestream-web-template):not(.live-web-template).is-focused {
    background-color: $theme-white-border-color;
  }
}

.genre {
  position: absolute;
  right: px-to-rem(20);
  bottom: px-to-rem(20);
  color: $white;
}

.metadata {
  padding: px-to-rem(16);
  white-space: pre-line;

  &.is-focused {
    background: #333;
  }
}

.teaser.theme-white .metadata,
.teaser.theme-info .metadata {
  &.is-focused {
    color: $white;
  }

  color: $black;
}

.title {
  margin-bottom: px-to-rem(14);
}

.button-wrapper {
  margin-top: px-to-rem(24);
  visibility: hidden;

  &.is-focused {
    visibility: visible;
  }
}

.label {
  position: absolute;
  top: px-to-rem(20);
  left: px-to-rem(24);
  width: 85%;
}

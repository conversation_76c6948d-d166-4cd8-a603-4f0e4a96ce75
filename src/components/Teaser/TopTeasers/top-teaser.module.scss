@use '~styles/globals' as *;

.wrapper {
  position: relative;
  width: calc(100% - $menu-width-collapsed);
  min-height: $hero-image-height;
  max-height: px-to-rem(720);
  overflow: hidden;

  @media (min-width: 1920px) {
    min-height: $hero-image-height-fhd;
  }

  h2,
  h3 {
    line-height: 1.2;
  }

  h2 {
    font-size: px-to-rem(70);
  }

  h3 {
    margin-top: px-to-rem(10);
    font-size: px-to-rem(50);
    font-family: $font-family-regular;
  }

  .label {
    margin-top: px-to-rem(10);
    margin-bottom: px-to-rem(8);
  }
}

.metadata {
  width: px-to-rem(800);
  padding-top: px-to-rem(54);
}

.duration {
  color: $grey;
  font-size: px-to-rem(25);
}

.image {
  position: absolute;
  top: 0;
  right: 0;
}

.availability,
.description,
.duration {
  margin-top: px-to-rem(15);
}

.buttons {
  position: relative;
  margin-top: px-to-rem(45);
}

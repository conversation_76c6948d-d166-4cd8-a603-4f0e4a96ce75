import { ITeaserProperties } from '@apptypes/ITeaserProperties';
import { Availability } from '@components/Availability/Availability';
import { ActionButtons } from '@components/Teaser/Components/ActionButtons';
import { HeroImage } from '@components/Teaser/Components/HeroImage';
import { getLabel } from '@components/Teaser/getLabel';
import { Label } from '@components/Teaser/Label/Label';
import { truncateClassNames } from '@features/truncate/truncate';
import { TopTeaserTexts } from '@util/linesInText';
import classNames from 'classnames';
import React from 'react';

import { withFocusableContainer } from '../../../focus';
import styles from './top-teaser.module.scss';
import { getLineCount } from './utils';

export const SingleContentTeaser = withFocusableContainer(
  React.forwardRef<HTMLDivElement, ITeaserProperties>((properties, reference) => {
    const { broadcastDates, description, duration, subtitle, template, title } = properties;
    const maxLines = 6;
    const titleLines = getLineCount(2, title, TopTeaserTexts.TITLE);
    const maxDescriptionLines = Math.min(4, maxLines - titleLines);
    const descriptionLines = getLineCount(maxDescriptionLines, description, TopTeaserTexts.DESCRIPTION);
    const maxSubtitleLines = Math.min(2, maxLines - titleLines - descriptionLines);
    const subtitleLines = getLineCount(maxSubtitleLines, subtitle, TopTeaserTexts.SUBTITLE);
    const label = getLabel(properties);

    return (
      <div ref={reference} className={classNames(styles.wrapper)} data-template={template}>
        <div className={styles.image}>
          <HeroImage {...properties} />
        </div>
        <div className={styles.metadata}>
          {titleLines > 0 && <h2 className={truncateClassNames(titleLines)}>{title}</h2>}
          {subtitleLines > 0 && <h3 className={truncateClassNames(subtitleLines)}>{subtitle}</h3>}
          {label && (properties?.isLiveWebTemplate || properties?.isLivestreamWebTemplate) && (
            <div className={styles.label}>
              <Label label={label} properties={properties} />
            </div>
          )}
          {duration && <span className={styles.duration}>{duration}</span>}
          {broadcastDates && (
            <div className={styles.availability}>
              <Availability {...properties} />
            </div>
          )}
          {descriptionLines > 0 && (
            <p className={truncateClassNames(descriptionLines, styles.description)}>{description}</p>
          )}
        </div>
        <div className={styles.buttons}>
          <ActionButtons {...properties} />
        </div>
      </div>
    );
  }),
);

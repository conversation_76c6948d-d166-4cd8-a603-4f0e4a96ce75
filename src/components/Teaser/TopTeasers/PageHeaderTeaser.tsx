import { ITeaserProperties } from '@apptypes/ITeaserProperties';
import { HeroImage } from '@components/Teaser/Components/HeroImage';
import { PlayButton } from '@components/Teaser/Components/PlayButton';
import { truncateClassNames } from '@features/truncate/truncate';
import { TopTeaserTexts } from '@util/linesInText';
import React from 'react';

import { withFocusableContainer } from '../../../focus';
import styles from './top-teaser.module.scss';
import { getLineCount } from './utils';

export const PageHeaderTeaser = withFocusableContainer(
  React.forwardRef<HTMLDivElement, ITeaserProperties>((properties, reference) => {
    const { description, subtitle, template, title } = properties;

    const maxLines = 7;
    const titleLines = getLineCount(3, title, TopTeaserTexts.TITLE);
    const maxDescriptionLines = Math.min(3, maxLines - titleLines);
    const descriptionLines = getLineCount(maxDescriptionLines, description, TopTeaserTexts.DESCRIPTION);
    const maxSubtitleLines = Math.min(2, maxLines - titleLines - descriptionLines);
    const subtitleLines = getLineCount(maxSubtitleLines, subtitle, TopTeaserTexts.SUBTITLE);

    return (
      <div ref={reference} className={styles.wrapper} data-template={template}>
        <div className={styles.image}>
          <HeroImage {...properties} />
        </div>
        <div className={styles.metadata}>
          {titleLines > 0 && <h2 className={truncateClassNames(titleLines, styles.title)}>{title}</h2>}
          {subtitleLines > 0 && <h3 className={truncateClassNames(subtitleLines, styles.subtitle)}>{subtitle}</h3>}
          {descriptionLines > 0 && (
            <p className={truncateClassNames(descriptionLines, styles.description)}>{description}</p>
          )}
          <div className={styles.buttons}>
            <PlayButton {...properties} />
          </div>
        </div>
      </div>
    );
  }),
);

import { ITeaserProperties } from '@apptypes/ITeaserProperties';
import { ActionButtons } from '@components/Teaser/Components/ActionButtons';
import { HeroImage } from '@components/Teaser/Components/HeroImage';
import { getLabel } from '@components/Teaser/getLabel';
import { Label } from '@components/Teaser/Label/Label';
import { getLineCount } from '@components/Teaser/TopTeasers/utils';
import { truncateClassNames } from '@features/truncate/truncate';
import { getNumberOfLines, TopTeaserTexts } from '@util/linesInText';
import React from 'react';

import { withFocusableContainer } from '../../../focus';
import styles from './top-teaser.module.scss';

export const HorizontalLandscapeHighlightTeaser = withFocusableContainer(
  React.forwardRef<HTMLDivElement, ITeaserProperties>((properties, reference) => {
    const { title, subtitle, description, duration, template } = properties;
    const maxLines = 6;
    const titleLines = Math.min(3, getNumberOfLines(title, TopTeaserTexts.TITLE));
    const maxDescriptionLines = Math.min(3, maxLines - titleLines);
    const descriptionLines = getLineCount(maxDescriptionLines, description, TopTeaserTexts.DESCRIPTION);
    const maxSubtitleLines = Math.min(2, maxLines - titleLines - descriptionLines);
    const subtitleLines = getLineCount(maxSubtitleLines, subtitle, TopTeaserTexts.SUBTITLE);
    const label = getLabel(properties);

    return (
      <div ref={reference} className={styles.wrapper} data-template={template}>
        <div className={styles.image}>
          <HeroImage {...properties} />
        </div>
        <div className={styles.metadata}>
          {titleLines > 0 && <h2 className={truncateClassNames(titleLines, styles.title)}>{title}</h2>}
          {subtitleLines > 0 && <h3 className={truncateClassNames(subtitleLines, styles.subtitle)}>{subtitle}</h3>}
          {label && (properties?.isLiveWebTemplate || properties?.isLivestreamWebTemplate) && (
            <div className={styles.label}>
              <Label label={label} properties={properties} />
            </div>
          )}
          {duration && <p className={styles.duration}>{duration}</p>}
          {descriptionLines > 0 && (
            <p className={truncateClassNames(descriptionLines, styles.description)}>{description}</p>
          )}
        </div>
        <div className={styles.buttons}>
          <ActionButtons {...properties} />
        </div>
      </div>
    );
  }),
);

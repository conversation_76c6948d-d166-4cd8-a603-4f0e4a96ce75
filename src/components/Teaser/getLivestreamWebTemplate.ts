import { ITeaserProperties } from '@apptypes/ITeaserProperties';
import { isLiveRightNow, isLivestreamWeb, isTvLive } from '@components/Teaser/getAvailabilityInformation';
import { getLive } from '@components/Teaser/getStickerInformation';

const getLivestreamWebTemplate = (properties: ITeaserProperties): boolean => {
  if (getLive(properties) && isLivestreamWeb(properties)) {
    return isLiveRightNow(properties);
  }
  return false;
};

const getLiveWebTemplate = (properties: ITeaserProperties): boolean => {
  if (getLive(properties) && isTvLive(properties)) {
    return isLiveRightNow(properties);
  }
  return false;
};
export { getLivestreamWebTemplate, getLiveWebTemplate };

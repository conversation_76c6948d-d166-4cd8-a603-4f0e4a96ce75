@use '~styles/globals' as *;

.sample-event-teaser {
  position: relative;
  margin-top: px-to-rem(115);
  width: calc(100% - $menu-width-collapsed);
  margin-right: px-to-rem(50);
}

.sample-image-wrapper {
  display: table-cell;
  width: px-to-rem(826);
  vertical-align: top;
}

.sample-image {
  display: block;
  width: 100%;
  height: auto;
}

.description {
  display: table-cell;
  background-color: #333;
  color: #fff;
  padding: px-to-rem(40) px-to-rem(58);
  box-sizing: border-box;
  vertical-align: top;
}

.description h2 {
  margin: 0;
  font-size: px-to-rem(70);
}

.description p {
  margin-top: px-to-rem(21);
  font-size: px-to-rem(32);
}

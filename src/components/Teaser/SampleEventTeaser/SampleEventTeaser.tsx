import sampleImage from '@assets/img/arte-sample-rectangle.png';
import React from 'react';
import { useTranslation } from 'react-i18next';

import styles from './sample-event-teaser.module.scss';

/*
temporary hardcoded teaser component
 */
export const SampleEventTeaser = () => {
  const { t } = useTranslation();

  return (
    <div className={styles['sample-event-teaser']}>
      <div className={styles['sample-image-wrapper']}>
        <img src={sampleImage} alt="Sample Image" className={styles['sample-image']} />
      </div>
      <div className={styles['description']}>
        <h2>{t('my_videos__empty_state_title')}</h2>
        <p>{t('my_videos__empty_state_description')}</p>
      </div>
    </div>
  );
};

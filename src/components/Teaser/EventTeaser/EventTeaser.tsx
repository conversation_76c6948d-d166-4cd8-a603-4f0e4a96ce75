import classNames from 'classnames';
import i18n from 'i18next';
import React from 'react';

import { truncateClassNames } from '../../../features/truncate/truncate';
import { teaserFocusGuard, withFocusableContainer } from '../../../focus';
import { ITeaserProperties } from '../../../types';
import { EventTeaserTexts, getNumberOfLines } from '../../../util/linesInText';
import { Button } from '../../Button/Button';
import { Image } from '../../Image/Image';
import { ImagePlaceholder } from '../../Image/ImagePlaceholder';
import { useTeaserAction } from '../useTeaserAction';
import styles from './event-teaser.module.scss';

export const EventTeaser = withFocusableContainer(
  React.forwardRef<HTMLDivElement, ITeaserProperties>((teaser, reference) => {
    const {
      callToAction,
      className,
      description,
      image,
      imageWidth,
      imageHeight,
      isFocused,
      onMouseClick,
      setFocus,
      subtitle,
      template,
      title,
      zoneIndex,
    } = teaser;

    const buttonLabel = callToAction || i18n.t('watch');

    const { handleTeaserPress } = useTeaserAction();
    const renderImage = () => (
      <a href="#" onClick={onMouseClick} className={classNames({ [styles['is-focused']]: isFocused })}>
        <ImagePlaceholder size="large">
          <Image width={imageWidth} height={imageHeight} src={image} loadingStrategy="lazy" />
        </ImagePlaceholder>
      </a>
    );

    const titleLines = Math.min(3, getNumberOfLines(title, EventTeaserTexts.TITLE));
    let subtitleLines = 0;
    let descriptionLines = 0;

    switch (titleLines) {
      case 3:
        subtitleLines = 1;
        descriptionLines = 0;
        break;
      case 2:
        subtitleLines = Math.min(2, getNumberOfLines(subtitle, EventTeaserTexts.SUBTITLE));
        descriptionLines = subtitleLines === 2 ? 0 : subtitleLines === 1 ? 1 : 0;
        break;
      case 1:
        subtitleLines = Math.min(2, getNumberOfLines(subtitle, EventTeaserTexts.SUBTITLE));
        descriptionLines = subtitleLines === 2 ? 1 : 2;
        break;
    }

    return (
      <div ref={reference} className={classNames(styles.teaser, className)} data-template={template}>
        {template === 'event-textOnRightSide' && renderImage()}
        <div className={styles.metadata}>
          <div className={styles['text-container']}>
            <h3 className={truncateClassNames(titleLines, styles.title)}>{title}</h3>
            {subtitleLines > 0 && <p className={truncateClassNames(subtitleLines, styles.subtitle)}>{subtitle}</p>}
            {descriptionLines > 0 && (
              <p className={truncateClassNames(descriptionLines, styles.description)}>{description}</p>
            )}
          </div>

          <div className={styles.buttons}>
            <Button
              icon="play"
              label={buttonLabel}
              onArrowPress={(direction) => teaserFocusGuard(direction, zoneIndex, 0, setFocus)}
              onEnterPress={() => handleTeaserPress(teaser, false, undefined)}
            />
          </div>
        </div>
        {template === 'event-textOnLeftSide' && renderImage()}
      </div>
    );
  }),
);

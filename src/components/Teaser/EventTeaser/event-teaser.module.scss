@use '~styles/globals' as *;

.teaser {
  position: relative;
  width: 1120px;
  height: 350px;
  line-height: 1;

  @media (min-width: 1920px) {
    width: 1710px;
    height: 473px;
  }
}

.teaser a {
  display: inline-block;
  width: 620px;

  @media (min-width: 1920px) {
    width: 840px;
  }
}

.metadata {
  display: inline-block;
  vertical-align: top;
  padding: px-to-rem(40) px-to-rem(58);
  background: $dark-grey;
  width: 500px;
  height: 350px;

  @media (min-width: 1920px) {
    width: 870px;
    height: 473px;
  }
}

.text-container {
  max-height: px-to-rem(390);
  overflow: hidden;
}

.title,
.subtitle,
.description {
  line-height: 1.2;
}

.title {
  font-family: $font-family-bold;
  margin-bottom: px-to-rem(16);
  font-size: px-to-rem(70);
}

.subtitle {
  font-family: $font-family-regular;
  margin-bottom: px-to-rem(16);
  font-size: px-to-rem(50);
}

.description {
  font-family: $font-family-regular;
  margin-bottom: px-to-rem(16);
  font-size: px-to-rem(32);
}

.buttons {
  padding: px-to-rem(18) px-to-rem(21) px-to-rem(18) px-to-rem(0);
}

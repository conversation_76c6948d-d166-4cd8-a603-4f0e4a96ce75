import { ZONE_TEMPLATES } from '@constants';
import { getImageDimensionsFromPath } from '@util/getImageDimensionsFromPath';
import { isEmpty } from '@util/isEmpty';
import { protocol } from '@util/protocol';
import { isFHD } from '@util/resolution';

import { ITeaserProperties, ITvGuideTeaserProperties, ZoneTemplate } from '../../types';
import { getPrime } from './getStickerInformation';

export function hasQueryParams(url: string) {
  const searchParams = new URL(url).searchParams;
  return searchParams.toString() !== '';
}

export function hasTypeQueryParam(url: string) {
  if (isEmpty(url)) return false;

  const params = new URL(url).searchParams;
  return params.has('type');
}

export function getImageUrlWithTypeQueryParam(url: string) {
  if (!hasQueryParams(url)) return `${url}?type=PHOTO`;
  return `${url}&type=PHOTO`;
}

function isTopTeaser(template: ZoneTemplate) {
  return (
    template === ZONE_TEMPLATES.HORIZONTAL_HIGHLIGHTED ||
    template === ZONE_TEMPLATES.SINGLE_COLLECTION ||
    template === ZONE_TEMPLATES.SINGLE_PAGE_HEADER ||
    template === ZONE_TEMPLATES.SINGLE_PROGRAM
  );
}

const withProtocol = (imageUrl: string) => (imageUrl ? imageUrl.replace(/^(http|https):\/\//, protocol) : imageUrl);

export const addImageSize = (
  props: ITeaserProperties | ITvGuideTeaserProperties,
): ITeaserProperties | ITvGuideTeaserProperties => {
  let size = undefined;
  switch (props.template) {
    case ZONE_TEMPLATES.EVENT_RIGHT:
    case ZONE_TEMPLATES.EVENT_LEFT:
      size = isFHD() ? '840x473' : '620x350';
      break;
    case ZONE_TEMPLATES.SINGLE_COLLECTION:
    case ZONE_TEMPLATES.SINGLE_PAGE_HEADER:
    case ZONE_TEMPLATES.SINGLE_PROGRAM:
    case ZONE_TEMPLATES.HORIZONTAL_HIGHLIGHTED:
      size = isFHD() ? '940x530' : '640x360';
      break;
    case ZONE_TEMPLATES.HORIZONTAL_LANDSCAPE:
    case ZONE_TEMPLATES.VERTICAL_LANDSCAPE:
      size = isFHD() ? '384x216' : '265x149';
      break;
    case ZONE_TEMPLATES.TABLEVIEW_GUIDE:
      size = getPrime(props) ? (isFHD() ? '480x270' : '325x183') : isFHD() ? '374x210' : '265x149';
      break;
    case ZONE_TEMPLATES.HORIZONTAL_LANDSCAPE_BIG:
    case ZONE_TEMPLATES.HORIZONTAL_LANDSCAPE_BIG_SUBTITLE:
      size = isFHD() ? '540x304' : '384x216';
      break;
    case ZONE_TEMPLATES.HORIZONTAL_PORTRAIT:
      size = isFHD() ? '300x450' : '235x352';
      break;
    case ZONE_TEMPLATES.HORIZONTAL_SQUARE:
      size = isFHD() ? '200x200' : '125x125';
      break;
  }

  const landscapeImageSize = isFHD() ? '540x304' : '384x216';
  const imagePath = withProtocol(props.image);
  let modifiedImagePath = imagePath;
  if (!hasTypeQueryParam(imagePath) && isTopTeaser(props.template)) {
    modifiedImagePath = getImageUrlWithTypeQueryParam(modifiedImagePath);
  }

  return {
    ...props,
    image: `${modifiedImagePath.replace('__SIZE__', size)}`,
    imageWidth: getImageDimensionsFromPath(size)?.width,
    imageHeight: getImageDimensionsFromPath(size)?.height,
    landscapeImage: imagePath.replace('__SIZE__', landscapeImageSize),
    landscapeImageWidth: getImageDimensionsFromPath(landscapeImageSize)?.width,
    landscapeImageHeight: getImageDimensionsFromPath(landscapeImageSize)?.height,
  };
};

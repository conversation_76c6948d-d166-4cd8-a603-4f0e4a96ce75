import {
  beginsAfterToday,
  beginsLaterToday,
  beginsToday,
  formatDate,
  formatTime,
  hasAvailability,
  isLivestream,
  isLivestreamWeb,
  isTvLive,
  isVOD,
} from '@components/Teaser/getAvailabilityInformation';
import { formatTString } from '@util/formatTString';
import { t } from 'i18next';

import { ITeaserProperties } from '../../types';
import { getCustomSticker, getLive, getRemainingDays, getSoon } from './getStickerInformation';

const getLabel = (properties: ITeaserProperties) => {
  const soon = getSoon(properties);
  const live = getLive(properties);
  const custom = getCustomSticker(properties);

  if (live && live?.label) {
    return live?.label?.toUpperCase();
  }

  if (custom && custom?.label) {
    return custom?.label;
  }

  if (hasAvailability(properties)) {
    if (getRemainingDays(properties)) {
      const remainingDays = getRemainingDays(properties);
      if (remainingDays) {
        const translation = JSON.parse(t('availability__remaining_days'));
        if (remainingDays === 1) {
          return translation.one;
        } else if (remainingDays > 1 && remainingDays <= 5) {
          return translation.other.replace('{{DD}}', remainingDays.toString());
        }
      }
    }
  }

  if (soon && soon?.label) {
    return soon?.label;
  }

  if (!getLive(properties) && hasAvailability(properties)) {
    return handleAvailability(properties);
  }

  return null;
};

const handleAvailability = (properties: ITeaserProperties): string | null => {
  if (!properties.availability?.start) {
    return null;
  }

  if (beginsToday(properties) && beginsLaterToday(properties)) {
    return handleSpecificAvailability(properties, true);
  }

  if (beginsAfterToday(properties)) {
    return handleSpecificAvailability(properties, false);
  }

  return null;
};

const handleSpecificAvailability = (properties: ITeaserProperties, isToday: boolean): string | null => {
  const { availability } = properties;

  if (availability) {
    if (isToday) {
      if (isVOD(properties)) {
        return formatTString(t('availability__vod_hour'), {
          hour: formatTime(availability.start),
        });
      }

      if (isLivestream(properties)) {
        if (isTvLive(properties)) {
          return formatTString(t('broadcast__availability_same_day'), {
            hour: formatTime(availability.start),
          });
        }
        if (isLivestreamWeb(properties)) {
          return formatTString(t('availability__livestream_hours'), {
            hour: formatTime(availability.start),
          });
        }
      }
    } else {
      if (isVOD(properties)) {
        return formatTString(t('availability__vod_date'), {
          date: formatDate(availability.start),
        });
      }

      if (isLivestream(properties)) {
        if (isTvLive(properties)) {
          return formatTString(t('broadcast__availability_day'), {
            date: formatDate(availability.start),
            hour: formatTime(availability.start),
          });
        }
        if (isLivestreamWeb(properties)) {
          return formatTString(t('availability__livestream_day'), {
            date: formatDate(availability.start),
            hour: formatTime(availability.start),
          });
        }
      }
    }
  }

  return null;
};

export { getLabel };

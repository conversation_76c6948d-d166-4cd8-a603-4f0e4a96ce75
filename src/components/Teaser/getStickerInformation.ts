import { ITeaserProperties } from '@apptypes/ITeaserProperties';
import { Attributes } from '@apptypes/VideoResponseBody';

enum StickerCodes {
  CLIP = 'CLIP',
  LIVE = 'LIVE',
  TV_LIVE = 'TV_LIVE',
  PRIME_TIME = 'PRIME_TIME',
  LAST_DAY = 'LAST_DAY',
  SOON = 'SOON',
  TRAILER = 'TRAILER',
  ARTE_CONCERT = 'ARTE_CONCERT',
  EDITO = 'EDITO',
}

function hasStickers(properties: ITeaserProperties | Attributes): boolean {
  return properties.stickers && properties.stickers.length !== 0;
}

/**
 * Returns custom sticker set by editors, this can overwrite the availability sticker.
 * @param properties
 */
const getCustomSticker = (properties: ITeaserProperties) => {
  if (!hasStickers(properties)) return;
  return properties.stickers.find((sticker) => sticker.code === StickerCodes.EDITO);
};

const getLive = (properties: ITeaserProperties) => {
  if (!hasStickers(properties)) return;
  return properties.stickers.find(
    (sticker) => sticker.code === StickerCodes.LIVE || sticker.code === StickerCodes.TV_LIVE,
  );
};

const getPrime = (properties: ITeaserProperties) => {
  if (!hasStickers(properties)) return false;
  return !!properties.stickers.find((sticker) => sticker.code === StickerCodes.PRIME_TIME);
};

const getRemainingDays = (properties: ITeaserProperties) => {
  const { availability } = properties;
  if (availability && availability.remainingDays) {
    return availability.remainingDays;
  }
};

const getSoon = (properties: ITeaserProperties) => {
  if (!hasStickers(properties)) return;
  return properties.stickers.find((sticker) => sticker.code === StickerCodes.SOON);
};

const getTrailer = (properties: Attributes) => {
  if (!hasStickers(properties)) return;
  return properties.stickers.find((sticker) => sticker.code === StickerCodes.TRAILER);
};

const getClip = (properties: Attributes) => {
  if (!hasStickers(properties)) return;
  return properties.stickers.find((sticker) => sticker.code === StickerCodes.CLIP);
};

export { getLive, getPrime, getSoon, getTrailer, getClip, getRemainingDays, getCustomSticker };

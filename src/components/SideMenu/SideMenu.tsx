import { MenuItem } from '@apptypes/MenuResponseBody';
import Logo from '@assets/img/arte-logo_vertical.png';
import { computeKeyboardFocusKey } from '@components/Keyboard/keyboardConfig';
import { VerticalList } from '@components/List/VerticalList';
import { PAGE_IDS, PROD_HOST, ROUTES, TEST_ID } from '@constants';
import { useCustomNavigate } from '@hooks/useCustomNavigate';
import { UseFocusableResult } from '@noriginmedia/norigin-spatial-navigation';
import { GlobalContext } from '@providers/GlobalContextProvider';
import { useModalContext } from '@providers/ModalContext';
import { MouseContext } from '@providers/MouseProvider';
import { Tracking } from '@tracking/Tracking';
import { ControlGroupType } from '@tracking/types';
import { COOKIE_PERSONALISATION, COOKIE_USER_TOKEN } from '@util/cookies';
import EventBus from '@util/EventBus';
import { getRootPage, getSearchKeyboardNavExitKeyLeft, resetNavHistory } from '@util/NavHistory';
import { isHosted } from '@util/platform';
import { shouldNotShowMyVideosPage } from '@util/shouldNotShowMyVideosPage';
import classnames from 'classnames';
import React, { useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { Location, useLocation } from 'react-router-dom';
import { isExitEnabled, thirdPartyAuth } from 'target';

import { FOCUS_KEY_MENU_QUIT, FOCUS_KEY_PRIMARY, menuFocusKey, withFocusableContainer } from '../../focus';
import i18n from '../../i18n';
import { BuildMetadata } from '../Metadata/BuildMetadata';
import { getExitModal } from '../Modal/modal';
import { NavigationItem } from './NavigationItem/NavigationItem';
import styles from './side-menu.module.scss';

const getCurrentMenuItem = (location: Location, items?: MenuItem[]) => {
  // For pathname check to work, we assume the root page is same as home page
  const pathname = location.pathname === '/' ? `/page/${PAGE_IDS.HOME}` : location.pathname;

  const currentItem = items?.find((item) => pathname.includes(item.page));
  const rootPageId = getRootPage()?.routeId;
  return currentItem || (rootPageId && items?.find((item) => item.page === rootPageId));
};

interface ISideMenuProperties {
  expanded: boolean;
  setFocus: UseFocusableResult['setFocus'];
  classname?: string;
  menuItems: MenuItem[];
  onFocus: () => void;
  onBlur: () => void;
}

function getStaticMenuItems() {
  const exitEnabled = isExitEnabled();
  const menuItems: MenuItem[] = [
    { label: i18n.t('myArte'), page: PAGE_IDS.MYARTE },
    { label: i18n.t('settings'), page: PAGE_IDS.SETTINGS },
  ];
  if (exitEnabled) menuItems.push({ label: i18n.t('quit'), page: PAGE_IDS.QUIT });

  return menuItems;
}

const allowBuildMetadata = isHosted() && window.location.host !== PROD_HOST;

const filterMenuItems = (menuItems: MenuItem[], staticMenuItems: MenuItem[]): MenuItem[] => {
  return [...menuItems, ...staticMenuItems].filter(
    (item) =>
      !(shouldNotShowMyVideosPage() && item.page === PAGE_IDS.MY_VIDEOS) &&
      !(thirdPartyAuth.enabled && item.page === PAGE_IDS.MYARTE),
  );
};

export const SideMenu = withFocusableContainer(
  React.forwardRef<HTMLDivElement, ISideMenuProperties>(
    ({ menuItems = [], expanded, setFocus, classname, onFocus, onBlur }, reference) => {
      const navigate = useCustomNavigate();
      const location = useLocation();
      const { showModal, hideModal } = useModalContext();
      const staticMenuItems = useMemo(() => getStaticMenuItems(), []);
      const { userData } = useContext(GlobalContext);
      const [allMenuItems, setAllMenuItems] = useState<MenuItem[]>(filterMenuItems(menuItems, staticMenuItems));
      const currentItem = getCurrentMenuItem(location, allMenuItems);
      const { isMouseActive } = useContext(MouseContext);
      const [showBuildMetadata, setShowBuildMetadata] = useState<boolean>(false);

      const updateMenuItems = useCallback(() => {
        const updatedMenuItems = filterMenuItems(menuItems, staticMenuItems);
        setAllMenuItems(updatedMenuItems);
      }, [menuItems, staticMenuItems]);

      useEffect(() => {
        updateMenuItems();
      }, [menuItems, userData, updateMenuItems]);

      useEffect(() => {
        EventBus.on(COOKIE_PERSONALISATION, updateMenuItems);
        EventBus.on(COOKIE_USER_TOKEN, updateMenuItems);

        return () => {
          EventBus.off(COOKIE_PERSONALISATION, updateMenuItems);
          EventBus.off(COOKIE_USER_TOKEN, updateMenuItems);
        };
      }, [menuItems, staticMenuItems, updateMenuItems]);

      const navItems = allMenuItems.map((item, index) => (
        <NavigationItem
          {...item}
          isActive={currentItem === item}
          expanded={expanded}
          key={item.label}
          focusKey={menuFocusKey(item.page)}
          onEnterPress={() => {
            Tracking.trackControlClick(ControlGroupType.SLIDER_MENU, item.page);
            switch (item.page) {
              case PAGE_IDS.QUIT:
                showModal({ content: getExitModal(hideModal, setFocus, FOCUS_KEY_MENU_QUIT) });
                break;
              case 'myarte':
                resetNavHistory();
                navigate(ROUTES.MYARTE.ROOT);
                break;
              default:
                resetNavHistory();
                navigate(`${ROUTES.PAGE}/${item.page}`);
            }
          }}
          onArrowPress={(direction) => {
            switch (direction) {
              case 'right':
                if (location.pathname.includes(PAGE_IDS.SEARCH_HOME)) {
                  setFocus(computeKeyboardFocusKey(getSearchKeyboardNavExitKeyLeft()) || FOCUS_KEY_PRIMARY);
                } else {
                  setFocus(FOCUS_KEY_PRIMARY);
                }
                if (allowBuildMetadata) setShowBuildMetadata(false);
                return false;
              case 'down':
                return true;
              case 'left':
                // for all envs excluding prod, pressing left when focus
                // is on the first menu item will render build metadata
                // for QA and debugging purposes
                if (allowBuildMetadata && index === 0) {
                  setShowBuildMetadata(true);
                }
                return false;
              case 'up':
                switch (index) {
                  case 0:
                    return false;
                  default:
                    return true;
                }
              default:
                return false;
            }
          }}
        />
      ));

      useEffect(() => {
        if (allowBuildMetadata) setShowBuildMetadata(false);
      }, [location, isMouseActive]);

      return (
        <>
          {showBuildMetadata && (
            <div className={styles.metadata}>
              <BuildMetadata />
            </div>
          )}
          <div
            ref={reference}
            onMouseEnter={() => isMouseActive && onFocus()}
            onMouseLeave={() => isMouseActive && onBlur()}
            className={classnames(
              styles['side-menu'],
              {
                [styles.expanded]: expanded,
              },
              classname,
            )}
            data-testid={TEST_ID.MAIN_NAVIGATION}
            data-menu-expanded={expanded ? 'true' : 'false'}
          >
            <img src={Logo} className={styles.logo} />
            <VerticalList
              preferredChildFocusKey={menuFocusKey(currentItem ? currentItem.page : 'home')}
              saveLastFocusedChild={false}
            >
              {navItems}
            </VerticalList>
          </div>
        </>
      );
    },
  ),
);

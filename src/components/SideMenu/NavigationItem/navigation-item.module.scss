@use '~styles/globals' as *;

$height: px-to-rem(107);

.navigation-item {
  color: $grey;
  height: $height;

  h3 {
    font-family: $font-family-regular;
  }
}

.navigation-item.is-focused {
  color: $white;
  background-color: #484848;
}

.navigation-item.is-active {
  color: $white;
}

.icon {
  margin-left: px-to-rem(48);
  margin-top: px-to-rem(29);
}

.label {
  position: absolute;
  display: inline-block;
  line-height: $height;
  margin-left: px-to-rem(15);
}

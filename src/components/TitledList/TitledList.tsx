import { HorizontalList } from '@components/List/HorizontalList';
import { IListProperties } from '@components/List/types';
import { VerticalList } from '@components/List/VerticalList';
import { Title } from '@components/Title/Title';
import { UseFocusableConfig } from '@noriginmedia/norigin-spatial-navigation';

import { THEMES } from '../../constants';
import { Focusable, focusClassNames, IFocusState } from '../../focus';
import styles from './titled-list.module.scss';

export interface ITitledListProperties
  extends IListProperties,
    IFocusState,
    Pick<UseFocusableConfig, 'onFocus' | 'onBlur'>,
    Pick<Focusable<unknown>, 'focusOnMount'> {
  title?: string;
  className?: string;
  theme?: string;
  listFocusKey?: string;
  horizontal?: boolean;
  searchMenuPresent?: boolean;
  moveChildOffsetToCenter?: boolean;
  bottomPeekSize?: number;
}

export const TitledList = ({
  horizontal,
  listFocusKey,
  title,
  theme,
  className,
  ...properties
}: ITitledListProperties) => {
  function renderList() {
    if (horizontal) return <HorizontalList {...properties} focusKey={listFocusKey} />;
    return <VerticalList {...properties} focusKey={listFocusKey} />;
  }

  return (
    <div
      className={focusClassNames(
        styles,
        properties,
        className,
        styles['titled-list'],
        theme && styles[THEMES[theme.toUpperCase()]],
      )}
    >
      <Title title={title} theme={theme} {...properties} />
      {renderList()}
    </div>
  );
};

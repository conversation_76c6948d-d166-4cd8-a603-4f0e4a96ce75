import { FocusContext, useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import React from 'react';
import { useRemoteController } from 'react-remote-controller';
import { getKeyMap } from 'target';

import { focusClassNames, IFocusState } from '../../../focus';
import { useCloseModal } from '../useCloseModal';
import styles from './text-modal.module.scss';

export interface ITextModalProperties extends IFocusState {
  headline: string;
  message?: string;
  secondaryMessage?: string;
  buttons: React.ReactNode;
  returnFocusKey: string;
  testId?: string;
}

export const TextModal = ({
  headline,
  message,
  secondaryMessage,
  buttons,
  returnFocusKey,
  ...properties
}: ITextModalProperties) => {
  const { focusKey, setFocus } = useFocusable({ isFocusBoundary: true });
  const { close } = useCloseModal(setFocus, returnFocusKey);

  useRemoteController<ReturnType<typeof getKeyMap>>({
    listenTo: {
      back: () => close(),
    },
  });

  return (
    <FocusContext.Provider value={focusKey}>
      <div data-testid={properties.testId} className={focusClassNames(styles, properties, styles['text-modal'])}>
        <h1 className={styles.headline}>{headline}</h1>

        {message && <p>{message}</p>}
        {secondaryMessage && <p>{secondaryMessage}</p>}

        <div className={styles['button-wrapper']}>{buttons}</div>
      </div>
    </FocusContext.Provider>
  );
};

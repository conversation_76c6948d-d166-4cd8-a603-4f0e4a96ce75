@use '~styles/globals' as *;

.text-modal {
  display: table;
  background-color: $primary-background;
  padding: px-to-rem(150) px-to-rem(185);

  p {
    text-align: center;
    font-size: px-to-rem(32);
    margin-top: px-to-rem(30);
    word-wrap: break-word;
    white-space: pre-wrap;
  }
}

.headline {
  font-size: px-to-rem(55);
  font-family: $font-family-bold;
  text-align: center;
}

.button-wrapper {
  display: table;
  margin: px-to-rem(60) auto 0;

  > :not(:first-child) {
    margin-left: px-to-rem(33);
  }
}

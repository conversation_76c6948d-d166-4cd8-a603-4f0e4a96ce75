import { forwardRef } from 'react';

import { ITeaserProperties } from '../../../types';
import { Availability } from '../../Availability/Availability';
import { Icon } from '../../Icon/Icon';
import styles from './program-modal.module.scss';

export const ProgramModal = forwardRef<HTMLDivElement, ITeaserProperties>(
  (properties: ITeaserProperties, reference) => {
    const {
      title,
      subtitle,
      audioVersions,
      duration,
      description: shortDescription, // EMAC's shortDescription maps to description in ASM
      fullDescription,
    } = properties;

    const audioIcons = audioVersions?.map((version) => <Icon type={version.code} size="ML" key={version.code} />);

    return (
      <div ref={reference} className={styles['program-modal']}>
        <h1 className={styles.title}>{title}</h1>

        {subtitle && <h2 className={styles.subtitle}>{subtitle}</h2>}

        {audioIcons && <div className={styles['audio-icons']}>{audioIcons}</div>}

        <div className={styles.time}>
          <span>{duration}</span>
          <Availability {...properties} />
        </div>

        <p className={styles['short-description']}>{shortDescription}</p>

        <p className={styles['full-description']}>{fullDescription}</p>
      </div>
    );
  },
);

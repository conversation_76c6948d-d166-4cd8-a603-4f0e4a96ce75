@use '~styles/globals' as *;

$verticalGap: px-to-rem(30);

.program-modal {
  p {
    font-size: 22px;

    @media (min-width: 1920px) {
      font-size: 35px;
    }
  }

  span {
    font-size: 18px;

    @media (min-width: 1920px) {
      font-size: 30px;
    }
  }
}

.title {
  font-family: $font-family-bold;
  font-size: px-to-rem(55);
}

.subtitle {
  font-family: $font-family-regular;
  font-size: px-to-rem(50);
}

.audio-icons {
  margin-top: $verticalGap;

  > * {
    margin-right: px-to-rem(12);
  }
}

.time {
  margin-top: $verticalGap;
}

.short-description {
  margin-top: $verticalGap;
  font-family: $font-family-bold;
}

.full-description {
  margin-top: $verticalGap;
}

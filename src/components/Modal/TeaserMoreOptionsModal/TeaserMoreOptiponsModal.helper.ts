import { ITeaserProperties } from '@apptypes/ITeaserProperties';
import { getRoute, ROUTE_PREFIX_PROGRAM } from '@routes/route';
import { isCollection, isCollectionAndItemIdIsCollection } from '@util/isCollection';

function getRouteId(isCollection: boolean, programId: string) {
  return isCollection ? programId : `${ROUTE_PREFIX_PROGRAM}${programId}`;
}

export function getRouteForContentType(teaser: ITeaserProperties) {
  const { item_id, program_id } = teaser;

  if (isCollectionAndItemIdIsCollection(teaser)) {
    // this happens when we have a initial teaser as a bookmark and NOT the sso response version
    return getRoute(item_id);
  }

  const routeId = getRouteId(isCollection(teaser), program_id);
  return getRoute(routeId);
}

@use '~styles/globals' as *;

.text-modal {
  max-width: px-to-rem(1368);
  background-color: $primary-background;
  padding: px-to-rem(90);
}

.title {
  font-size: px-to-rem(55);
  font-family: $font-family-regular;
  line-height: 1;
}

.description {
  font-size: px-to-rem(32);
  margin-top: px-to-rem(8);
}

.image-container {
  position: relative;
  display: inline-block;
  margin-top: px-to-rem(44);
  border-style: solid;
  border-color: transparent;
  border-width: $focused-border-width;

  img {
    display: block;
  }

  &.livestream-web-template {
    border-color: $border-live-concert-color;
  }

  &.live-web-template {
    border-color: $arte-highlight;
  }
}

.genre {
  position: absolute;
  right: px-to-rem(20);
  bottom: px-to-rem(20);
}

.button-wrapper {
  margin-left: px-to-rem(44);
  margin-top: px-to-rem(44);
  vertical-align: top;
  display: inline-block;

  > :not(:first-child) {
    margin-top: px-to-rem(16);
  }

  > button {
    display: block;
    font-family: $font-family-bold;
  }
}

.label {
  position: absolute;
  top: px-to-rem(20);
  left: px-to-rem(24);
  width: 85%;
}

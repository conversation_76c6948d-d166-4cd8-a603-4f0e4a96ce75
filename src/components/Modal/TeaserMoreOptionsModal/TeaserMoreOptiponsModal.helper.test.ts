import { describe } from 'vitest';

import { ROUTES } from '../../../constants';
import { Bookmark, ITeaserProperties } from '../../../types';
import { getRouteForContentType } from './TeaserMoreOptiponsModal.helper';

const bookmarkedTeaserCollection: Partial<Bookmark> = {
  item_id: 'RC-022404',
  title: 'Brigade Mobile',
  kind: {
    code: 'TV_SERIES',
    isCollection: true,
    label: null,
  },
  program_id: '100739-001-A',
};

const bookmarkedTeaserCollectionSSO: Partial<Bookmark> = {
  id: 'RC-022404_fr',
  kind: {
    isCollection: true,
  },
  // @ts-expect-error SSO response contains `programId` whose value is copied to `program_id` to be compatible with middleware responses
  programId: 'RC-022404',
  title: 'Brigade Mobile',
  program_id: 'RC-022404',
};

const showTeaser: Partial<ITeaserProperties> = {
  item_id: '104778-002-A_fr',
  title: 'Grand Canyon - Un voyage au centre de la Terre',
  kind: {
    code: 'SHOW',
    isCollection: false,
    label: 'Programme',
  },
  program_id: '104778-002-A',
};

describe('more options modal: getRouteForContentType', () => {
  it('should return a collection route for bookmarked teaser COLLECTION', () => {
    const actual = getRouteForContentType(bookmarkedTeaserCollection as Bookmark);
    const expected = `${ROUTES.COLLECTION}/${bookmarkedTeaserCollection.item_id}`;
    expect(actual).toEqual(expected);
  });

  it('should return a collection route for bookmarked teaser COLLECTION SSO', () => {
    const actual = getRouteForContentType(bookmarkedTeaserCollectionSSO as Bookmark);
    const expected = `${ROUTES.COLLECTION}/${bookmarkedTeaserCollectionSSO.program_id}`;
    expect(actual).toEqual(expected);
  });

  it('should return a /program route for SHOW', () => {
    const actual = getRouteForContentType(showTeaser as ITeaserProperties);
    const expected = `/program/${showTeaser.program_id}`;
    expect(actual).toEqual(expected);
  });
});

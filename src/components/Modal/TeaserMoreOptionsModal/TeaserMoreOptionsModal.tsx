import { ITeaserProperties } from '@apptypes/ITeaserProperties';
import { Zone } from '@apptypes/PageResponseBody';
import { getRouteForContentType } from '@components/Modal/TeaserMoreOptionsModal/TeaserMoreOptiponsModal.helper';
import { getLabel } from '@components/Teaser/getLabel';
import { Label } from '@components/Teaser/Label/Label';
import { toastAddReminder } from '@components/ToastNotification/toasts';
import { MissingProgramIdError } from '@errors/MissingProgramIdError';
import { useErrors } from '@errors/useErrors';
import { useCustomNavigate } from '@hooks/useCustomNavigate';
import { useZones } from '@hooks/useZones';
import { FocusContext, useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import { BookmarksContext } from '@providers/BookmarksContext';
import { useModalContext } from '@providers/ModalContext';
import { ClickType } from '@tracking/clickType';
import { Tracking } from '@tracking/Tracking';
import { ControlGroupName, ControlGroupType } from '@tracking/types';
import { compareBookmarks } from '@util/bookmarks';
import { canBookmark, canRemind, getBookmarkIcon, getBookmarkLabel } from '@util/canBookmark';
import { getGenreLabel } from '@util/genre';
import classNames from 'classnames';
import i18n from 'i18next';
import React, { useContext, useState } from 'react';
import { useRemoteController } from 'react-remote-controller';
import { getKeyMap } from 'target';

import { FOCUS_KEY_SIDE_MENU, focusClassNames, IFocusState, teaserFocusKey } from '../../../focus';
import { Button } from '../../Button/Button';
import { Genre } from '../../Genre/Genre';
import styles from './teaser-more-options-modal.module.scss';

const showGenre = (properties: ITeaserProperties, zones: Zone[]) => {
  const zoneIndex = properties.zoneIndex;
  if (zoneIndex && zones && zones[zoneIndex]) {
    return zones[zoneIndex]?.theme === 'genre';
  }
  return false;
};

export interface ITeaserMoreOptionsModalProperties extends IFocusState, ITeaserProperties {
  returnFocusKey: string;
  zones: Zone[];
  isLiveWebTemplate?: boolean;
  isLivestreamWebTemplate?: boolean;
}

// We cannot completely desctructure the properties because when adding a bookmark we need to have them all
export const TeaserMoreOptionsModal = ({ returnFocusKey, zones, ...properties }: ITeaserMoreOptionsModalProperties) => {
  const { focusKey, setFocus } = useFocusable({ isFocusBoundary: true });
  const { hideModal } = useModalContext();
  const navigate = useCustomNavigate();
  const { addBookmark, removeBookmark, isBookmarked, bookmarks, bookmarksZoneIndex } = useContext(BookmarksContext);
  const { zonesExist } = useZones(zones);
  const { setError } = useErrors();
  const [initialBookmarks] = useState(bookmarks ? [...bookmarks] : []);
  const [bookmarked, setBookmarked] = useState<boolean>(isBookmarked(properties));
  const label = getLabel(properties);
  const isBookmarkAllowed = canBookmark(properties);
  const isReminderAllowed = canRemind(properties);
  const lastZone = properties.lastZone;
  const comingFromBookmarks = properties.authenticatedContent === 'sso-favorites';
  const comingFromZoneBelowBookmarks = properties.zoneIndex > bookmarksZoneIndex && bookmarksZoneIndex >= 0;
  const wasInitiallyBookmarked = initialBookmarks.some((bookmark) => compareBookmarks(bookmark, properties));
  const removedFromBookmarks = !bookmarked && wasInitiallyBookmarked;
  const removedLastBookmark = wasInitiallyBookmarked && initialBookmarks.length === 1 && !bookmarked;
  const rightMostBookmark =
    comingFromBookmarks && initialBookmarks[initialBookmarks.length - 1].program_id === properties.program_id;
  const { pause } = useFocusable();
  const close = () => {
    hideModal();

    if (bookmarked !== wasInitiallyBookmarked) {
      if (bookmarked) {
        addBookmark(properties);
      } else {
        removeBookmark(properties);
      }
    }

    if (removedLastBookmark && comingFromBookmarks && lastZone) {
      // Bookmarks is the last zone and we just removed the last item from it.
      // Ideally we should check if there are other zones present and set focus on them.
      // But because it's an edge case, setting focus on the menu will do for now.
      setFocus(FOCUS_KEY_SIDE_MENU);
    } else if (removedLastBookmark && !zonesExist()) {
      // Bookmarks was the only zone left and we just removed the last item from it.
      // There are no other zones on the page.
      setFocus(FOCUS_KEY_SIDE_MENU);
    } else if (rightMostBookmark && removedFromBookmarks) {
      // The teaser we would normally return to has been removed
      // and there's no other teaser in its place (because it was the rightmost teaser).
      // So we focus the first teaser from that zone instead.
      setFocus(teaserFocusKey(properties.zoneIndex, 0));
    } else if (comingFromZoneBelowBookmarks && removedLastBookmark) {
      // We removed the last bookmark and as a result the entire bookmarks zone disappeared.
      // To accommodate this change we need to subtract 1 from the zone index.
      const newFocusKey = teaserFocusKey(properties.zoneIndex - 1, properties.position);
      setFocus(newFocusKey);
    } else {
      // return focus to the selected key
      setFocus(returnFocusKey);
    }
  };

  useRemoteController<ReturnType<typeof getKeyMap>>({
    listenTo: {
      back: () => {
        close();
      },
      green: () => {
        Tracking.trackClick(ClickType.GREEN_BUTTON, properties);
        Tracking.trackTeaserClick(properties, ControlGroupType.TEASER_ACTIONS, ControlGroupName.MORE_INFO);
        close();
      },
    },
  });

  return (
    <FocusContext.Provider value={focusKey}>
      <div className={focusClassNames(styles, properties, styles['text-modal'])}>
        <h1 className={styles.title}>{properties.title}</h1>

        <p className={styles.description}>{properties.description}</p>

        <div
          className={classNames(styles['image-container'], {
            [styles['livestream-web-template']]: properties.isLivestreamWebTemplate,
            [styles['live-web-template']]: properties.isLiveWebTemplate,
          })}
        >
          <img
            width={properties.landscapeImageWidth}
            height={properties.landscapeImageHeight}
            src={properties.landscapeImage}
            className={styles.image}
          />
          {label && !properties.playable && (
            <div className={styles.label}>
              <Label label={label} properties={properties} />
            </div>
          )}
          {showGenre(properties, zones) && <Genre label={getGenreLabel(properties)} className={styles.genre} />}
        </div>

        <div className={styles['button-wrapper']}>
          {(isBookmarkAllowed || isReminderAllowed) && (
            <Button
              icon={getBookmarkIcon(bookmarked, isReminderAllowed)}
              label={getBookmarkLabel(bookmarked, isReminderAllowed)}
              onEnterPress={() => {
                if (bookmarked) {
                  setBookmarked(false);
                } else {
                  canRemind(properties) && toastAddReminder();
                  setBookmarked(true);
                }
              }}
              focusOnMount
            />
          )}

          <Button
            label={i18n.t('moreInfos')}
            icon="info"
            onEnterPress={() => {
              if (!properties.program_id) {
                setError(new MissingProgramIdError());
                return;
              }
              close();
              pause();
              navigate(getRouteForContentType(properties));
            }}
            focusOnMount={!isBookmarkAllowed && !isReminderAllowed}
          />
          <Button label={i18n.t('close')} icon="close" onEnterPress={() => close()} />
        </div>
      </div>
    </FocusContext.Provider>
  );
};

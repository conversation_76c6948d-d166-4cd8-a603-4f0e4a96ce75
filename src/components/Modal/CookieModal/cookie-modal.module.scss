@use '~styles/globals' as *;

$button-grid-width: px-to-rem(600);
$button-gap: px-to-rem(16);

.wrapper {
  padding: px-to-rem(110px) px-to-rem(80px);
  width: px-to-rem(1518);
  height: px-to-rem(848);
  background-color: $primary-background;
  background-image: url('~@assets/img/announce.png');
  background-repeat: no-repeat;
  background-position: 110% center;
  background-size: px-to-rem(742) px-to-rem(397);

  h2,
  p {
    width: px-to-rem(700);
  }

  h2 {
    font-size: px-to-rem(55);
  }

  p {
    margin-top: px-to-rem(32);
    font-size: px-to-rem(32);
    font-family: $font-family-bold;
    line-height: normal;
  }
}

.buttons {
  width: $button-grid-width;
  margin-top: px-to-rem(32);

  button {
    margin-right: px-to-rem(16);
    margin-bottom: px-to-rem(16);
    font-family: $font-family-bold;
  }

  div:last-child {
    button {
      width: 100%;
    }
  }

  // NOTE order is important here
  // :first-child comes after :last-child
  // this is because the buttons are laid out by the grid component and
  // we can have either 1 or 2 rows. When there is only one row we want
  // the :first-child styles to take precedence
  // this a brittle solution should more buttons be added here but
  // it works and it is simple
  div:first-child {
    button {
      width: calc(($button-grid-width / 2) - ($button-gap / 2));
    }
  }
}

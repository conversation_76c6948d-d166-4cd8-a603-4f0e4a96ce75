import { ROUTES, TEST_ID } from '@constants';
import { useCustomNavigate } from '@hooks/useCustomNavigate';
import { FocusContext, useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import { useTranslation } from 'react-i18next';
import { useRemoteController } from 'react-remote-controller';
import { config, getKeyMap } from 'target';

import { IFocusState } from '../../../focus';
import {
  acceptCookies,
  acceptTechnicalCookies,
  rejectCookies,
  rejectTechnicalCookies,
  setCookiesNotified,
} from '../../../util/cookies';
import { Button } from '../../Button/Button';
import { GridLayout } from '../../GridLayout/GridLayout';
import { useCloseModal } from '../useCloseModal';
import styles from './cookie-modal.module.scss';

export interface ICookieProperties extends IFocusState {
  returnFocusKey: string;
  withCookieModification: boolean;
}

function reject() {
  rejectCookies();
  rejectTechnicalCookies();
  setCookiesNotified();
}

function accept() {
  acceptCookies();
  acceptTechnicalCookies();
  setCookiesNotified();
}

export const CookieModal = ({ returnFocusKey, withCookieModification }: ICookieProperties) => {
  const { focusKey, setFocus } = useFocusable({ isFocusBoundary: true });
  const { close } = useCloseModal(setFocus, returnFocusKey);
  const navigate = useCustomNavigate();
  const { t } = useTranslation();

  useRemoteController<ReturnType<typeof getKeyMap>>({
    listenTo: {
      back: () => {
        if (config.cookieModal?.acceptCookiesOnPressingBack) {
          handleAcceptCookies();
        }
      },
    },
  });

  function handleAcceptCookies() {
    accept();
    close();
  }
  function handleRejectCookies() {
    reject();
    close();
  }

  function handleModifyCookies() {
    accept();
    close();
    navigate(`${ROUTES.SETTINGS.PRIVACY}`);
  }

  return (
    <FocusContext.Provider value={focusKey}>
      <div className={styles.wrapper} data-testid={TEST_ID.COOKIE.VIEW}>
        <h2>{t('cookies__pop-up_title')}</h2>
        <p>{t('cookies__pop-up_desc')}</p>
        <GridLayout rowLength={2} className={styles.buttons}>
          <Button
            testId={TEST_ID.COOKIE.ACCEPT}
            focusOnMount
            label={t('cookies__pop-up_accept')}
            onEnterPress={handleAcceptCookies}
          />
          <Button testId={TEST_ID.COOKIE.REJECT} label={t('refuse')} onEnterPress={handleRejectCookies} />
          {withCookieModification && (
            <Button
              testId={TEST_ID.COOKIE.MODIFY}
              label={t('cookies__pop-up_cookies-choice')}
              onEnterPress={handleModifyCookies}
            />
          )}
        </GridLayout>
      </div>
    </FocusContext.Provider>
  );
};

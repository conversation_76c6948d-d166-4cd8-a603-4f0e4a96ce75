import { AvailabilityCore } from '@apptypes/ITeaserProperties';
import { ErrorModalType } from '@errors/ErrorModalType';
import { useCustomNavigate } from '@hooks/useCustomNavigate';
import { useInterval } from '@hooks/useInterval';
import { FocusContext, useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import { formatDate, getTimeLeftUntilAvailability } from '@util/isCurrentTimeWithinRange';
import i18n from 'i18next';
import React, { useEffect, useState } from 'react';

import { ROUTES } from '../../../constants';
import { focusClassNames, IFocusState } from '../../../focus';
import styles from './countdown-modal.module.scss';

export interface ICountdownModalProperties extends IFocusState {
  buttons: React.ReactNode;
  returnFocusKey: string;
  type: typeof ErrorModalType;
  params: AvailabilityCore;
}

export const CountdownModal = ({ buttons, params, ...properties }: ICountdownModalProperties) => {
  const { focusKey } = useFocusable({ isFocusBoundary: true });
  const availability = params?.availabilityRights;
  const videoId = params?.videoId;
  const [intervalTime, setIntervalTime] = useState<number | null>(1000);
  const [availableCount, setAvailableCount] = useState(getTimeLeftUntilAvailability(availability));
  const navigate = useCustomNavigate();

  useInterval(() => {
    const currentAvailability = getTimeLeftUntilAvailability(availability);
    setAvailableCount(currentAvailability);
    if (currentAvailability.isAvailable) {
      setIntervalTime(null);
      navigate(`${ROUTES.VERIFICATION}/${videoId}`, { state: { stepsBack: 3 } });
    }
  }, intervalTime);

  useEffect(() => {
    return () => {
      setIntervalTime(null);
    };
  }, [setIntervalTime]);

  return (
    <FocusContext.Provider value={focusKey}>
      <div className={focusClassNames(styles, properties, styles['text-modal'])}>
        {!availableCount.isAvailable && (
          <>
            <h1 className={styles.headline}>
              {availableCount.days > 0
                ? i18n.t('player__countdown_vod_laterDay')
                : i18n.t('player__countdown_vod_sameDay')}
            </h1>
            {availableCount.days > 0 ? (
              <p className={styles.message}>{formatDate(availability)}</p>
            ) : (
              <div className={styles.counterBlock}>
                <div className={styles.countInner}>
                  <p className={styles.count}>{availableCount.hours}</p>

                  <p className={styles.legend}>{i18n.t('hours')}</p>
                </div>
                <div className={styles.separator}>:</div>
                <div className={styles.countInner}>
                  <p className={styles.count}>{availableCount.minutes}</p>

                  <p className={styles.legend}>{i18n.t('minutes_short')}</p>
                </div>
                <div className={styles.separator}>:</div>
                <div className={styles.countInner}>
                  <p className={styles.count}>{availableCount.seconds}</p>
                  <p className={styles.legend}>{i18n.t('seconds__short')}</p>
                </div>
              </div>
            )}
          </>
        )}

        <div className={styles['button-wrapper']}>{buttons}</div>
      </div>
    </FocusContext.Provider>
  );
};

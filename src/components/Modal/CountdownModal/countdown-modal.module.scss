@use '~styles/globals' as *;

.text-modal {
  display: table;
  background-color: $primary-background;
  padding: px-to-rem(150) px-to-rem(185);
}

.headline {
  font-size: px-to-rem(50);
  font-family: $font-family-regular;
  text-align: center;
}

.message {
  text-align: center;
  font-family: $font-family-bold;
  font-size: px-to-rem(70);
  margin-top: px-to-rem(40);
}

.button-wrapper {
  display: table;
  margin: px-to-rem(60) auto 0;

  > :not(:first-child) {
    margin-left: px-to-rem(33);
  }
}

.counterBlock {
  text-align: center;
}

.countInner {
  margin-left: px-to-rem(50);
  margin-right: px-to-rem(50);
  display: inline-block;
  margin-top: px-to-rem(40);
  text-align: center;
  width: px-to-rem(80);
}

.count {
  font-size: px-to-rem(70);
  font-family: $font-family-bold;
  margin-bottom: px-to-rem(20);
  display: inline-block;
}

.legend {
  font-size: px-to-rem(32);
  font-family: $font-family-regular;
}

.separator {
  font-size: px-to-rem(32);
  position: absolute;
  margin-top: px-to-rem(65);
  display: inline-block;
}

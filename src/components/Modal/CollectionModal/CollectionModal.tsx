import { TEST_ID } from '@constants';
import React, { forwardRef } from 'react';

import { ITeaserProperties } from '../../../types';
import styles from './collection-modal.module.scss';

export interface CollectionModalProperties extends ITeaserProperties {
  testId?: string;
}

export const CollectionModal = forwardRef<HTMLDivElement, CollectionModalProperties>(
  (properties: CollectionModalProperties, reference) => {
    const { title, subtitle, description, testId } = properties;
    return (
      <div data-testid={testId} ref={reference}>
        <h1 data-testid={TEST_ID.MODAL.TITLE} className={styles.title}>
          {title}
        </h1>
        {subtitle && <h2 className={styles.subtitle}>{subtitle}</h2>}
        <p data-testid={TEST_ID.MODAL.DESRIPTION} className={styles.description}>
          {description}
        </p>
      </div>
    );
  },
);

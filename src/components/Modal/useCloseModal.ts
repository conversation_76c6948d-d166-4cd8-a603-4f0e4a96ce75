import { UseFocusableResult } from '@noriginmedia/norigin-spatial-navigation';

import { useModalContext } from '../../providers/ModalContext';

export function useCloseModal(setFocus: UseFocusableResult['setFocus'], returnFocusKey: string) {
  const { hideModal } = useModalContext();

  const close = () => {
    hideModal();
    // return focus to the selected key
    setFocus(returnFocusKey);
  };

  return { close };
}

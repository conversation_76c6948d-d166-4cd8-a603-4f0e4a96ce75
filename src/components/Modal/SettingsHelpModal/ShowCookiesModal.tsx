import { forwardRef } from 'react';

import { getAllAppData } from '../../../features/appdata/appdata';
import i18n from '../../../i18n';
import styles from './show-cookies-modal.module.scss';

export const ShowCookiesModal = forwardRef<HTMLDivElement>(({}, reference) => {
  return (
    <div ref={reference} className={styles.wrapper}>
      <h2 className={styles['title']}>{i18n.t('help__button-cookies')}</h2>
      <div className={styles['content']}>
        {Array.from(getAllAppData()).map((item) => (
          <p key={item[0]}>
            <span className={styles['key']}>{item[0]}</span>
            <span className={styles['value']}> {decodeURIComponent(item[1])}</span>
          </p>
        ))}
      </div>
    </div>
  );
});

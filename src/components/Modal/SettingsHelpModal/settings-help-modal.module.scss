@use '~styles/globals' as *;

.wrapper {
  display: table;
  width: px-to-rem(1623);
  height: px-to-rem(848);
  padding: px-to-rem(100);
  background-color: $primary-background;

  button {
    margin-top: px-to-rem(30);
    margin-right: px-to-rem(30);
    font-family: $font-family-bold;
  }
}

.heading {
  h2 {
    margin-bottom: px-to-rem(20);
  }
}

.col-left {
  display: table-cell;
  width: px-to-rem(632);
  height: px-to-rem(585);

  p {
    font-size: px-to-rem(25);
    line-height: px-to-rem(32);
  }

  p.subtitle {
    font-size: px-to-rem(32);
    line-height: px-to-rem(42);
    margin-bottom: px-to-rem(17);
  }

  p.label {
    font-family: $font-family-bold;
  }

  span.value {
    font-family: $font-family-regular;
  }

  p.top-row {
    margin-top: px-to-rem(40);
  }
}

.col-right {
  display: table-cell;
  width: px-to-rem(646);
  height: px-to-rem(585);
  padding-left: px-to-rem(100);
  padding-right: px-to-rem(100);
  font-family: $font-family-regular;
  font-size: px-to-rem(32);

  img {
    margin-top: px-to-rem(40);
  }

  p.url {
    width: 150px;
    font-size: px-to-rem(30);
    line-height: px-to-rem(39);
    text-align: center;

    @media (min-width: 1920px) {
      width: 250px;
    }
  }
}

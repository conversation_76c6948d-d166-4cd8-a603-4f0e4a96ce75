import { LanguageResponseBody } from '@apptypes/language';
import { eraseAllAppData } from '@features/appdata/appdata';
import { debugRelaunchQueryParams, defaultRelaunchQueryParams } from '@features/queryParams/relaunchQueryParams';
import { FocusContext, useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import { useModalContext } from '@providers/ModalContext';
import { isFHD } from '@util/resolution';
import { getTargetAndSubsetName } from '@util/target';
import classNames from 'classnames';
import { useRemoteController } from 'react-remote-controller';
import { getKeyMap, reloadApp } from 'target';

import { useDataSource } from '../../../data';
import { IFocusState } from '../../../focus';
import i18n, { getGeoLocation } from '../../../i18n';
import { But<PERSON> } from '../../Button/Button';
import { GridLayout } from '../../GridLayout/GridLayout';
import { ScrollableModal } from '../ScrollableModal/ScrollableModal';
import { useCloseModal } from '../useCloseModal';
import styles from './settings-help-modal.module.scss';
import { ShowCookiesModal } from './ShowCookiesModal';

const rowLength = 2;

export interface ISettingsHelpModal extends IFocusState {
  returnFocusKey: string;
  buttonFocusKey?: string;
  version: string;
}

interface IButtons {
  label: string;
  action: (arg0: string) => void;
}

const getHelpQrCodeImage = (resolution: string, language: string) => {
  try {
    return require(`@assets/img/qr-codes/help/${resolution}/qr-code-help-${language}.png`);
  } catch (e) {
    return require(`@assets/img/qr-codes/help/${resolution}/qr-code-fallback-en.png`);
  }
};

export const SettingsHelpModal = ({ returnFocusKey, buttonFocusKey, version }: ISettingsHelpModal) => {
  const { focusKey, setFocus } = useFocusable({ isFocusBoundary: true });
  const { close } = useCloseModal(setFocus, returnFocusKey);
  const { showModal } = useModalContext();
  const firstFocus = buttonFocusKey || 'FOCUS_KEY_MODAL_0';
  const geoLocationData = useDataSource<LanguageResponseBody>(getGeoLocation);

  const buttons: IButtons[] = [
    {
      label: i18n.t('help__button-cookies-delete'),
      action: () => {
        eraseAllAppData();
        reloadApp(defaultRelaunchQueryParams().toString());
      },
    },
    {
      label: i18n.t('help__button-cookies'),
      action: (buttonFocusKey) => {
        showModal({
          content: (
            <ScrollableModal returnFocusKey={returnFocusKey} buttonFocusKey={buttonFocusKey} version={version}>
              <ShowCookiesModal />
            </ScrollableModal>
          ),
        });
      },
    },
    {
      label: i18n.t('help__button-logs'),
      action: () => {
        reloadApp(debugRelaunchQueryParams().toString());
      },
    },
  ];

  useRemoteController<ReturnType<typeof getKeyMap>>({
    listenTo: {
      back: () => {
        close();
      },
    },
  });

  return (
    <FocusContext.Provider value={focusKey}>
      <div className={styles.wrapper}>
        <div className={styles.heading}>
          <h2>{i18n.t('settings__help')}</h2>
        </div>
        <div>
          <div className={styles['col-left']}>
            <p className={styles['subtitle']}>{i18n.t('help__show_data_text')}</p>
            <GridLayout rowLength={rowLength}>
              {buttons.map((button, index) => {
                const buttonFocusKey = `FOCUS_KEY_MODAL_${index}`;
                return (
                  <Button
                    // eslint-disable-next-line react/no-array-index-key
                    key={index}
                    focusKey={buttonFocusKey}
                    label={button.label}
                    onEnterPress={() => button.action(buttonFocusKey)}
                    focusOnMount={buttonFocusKey === firstFocus}
                    onArrowPress={(direction) => {
                      switch (direction) {
                        case 'up':
                          return true;
                        case 'down':
                          return true;
                        case 'right':
                          if (index === buttons.length - 1) return false;
                          if (index % rowLength === rowLength - 1) {
                            setFocus(`FOCUS_KEY_MODAL_${index + 1}`);
                            return false;
                          }
                          return true;
                        case 'left':
                          if (index === 0) return true;
                          if (index % rowLength === 0) {
                            setFocus(`FOCUS_KEY_MODAL_${index - 1}`);
                            return false;
                          }
                          return true;
                        default:
                          return false;
                      }
                    }}
                  />
                );
              })}
            </GridLayout>
            <p className={classNames(styles['label'], styles['top-row'])}>
              {i18n.t('help__version')}: <span className={styles['value']}>{version}</span>
            </p>
            <p className={classNames(styles['label'])}>
              {i18n.t('help__platform')}: <span className={styles['value']}>{getTargetAndSubsetName()}</span>
            </p>
            <p className={classNames(styles['label'])}>
              {'userAgent'}: <span className={styles['value']}>{navigator.userAgent}</span>
            </p>
            <p className={classNames(styles['label'])}>
              {geoLocationData && i18n.t('help__geoloc')}:{' '}
              <span className={styles['value']}>{geoLocationData?.country}</span>
            </p>
          </div>
          <div className={styles['col-right']}>
            <p className={styles['qr-code-instructions']}>{i18n.t('help__qr-code')}</p>
            <img
              src={getHelpQrCodeImage(isFHD() ? '1080p' : '720p', i18n.language)}
              className={styles['qr-code']}
              alt="qr-code"
            />
            <p className={styles['url']}>{i18n.t('help__url')}</p>
          </div>
        </div>
      </div>
    </FocusContext.Provider>
  );
};

@use '~styles/globals' as *;

.text-modal {
  position: relative;
  max-width: px-to-rem(1623);
  max-height: px-to-rem(848);
  background-color: $primary-background;
  padding: px-to-rem(90) px-to-rem(145);
  visibility: hidden;
}

.text-modal-visible {
  visibility: visible;
}

.title {
  font-size: px-to-rem(55);
  font-family: $font-family-regular;
  margin-bottom: px-to-rem(40);
}

.contentContainer {
  overflow: hidden;
  position: relative;
}

.htmlTextContent {
  position: absolute;
  top: 0;
  font-size: px-to-rem(28);
  font-family: $font-family-regular;
}

.modal-bottom-mask {
  background-color: $primary-background;
  width: 100%;
  height: px-to-rem(40);
  z-index: 2;
  position: absolute;
  bottom: px-to-rem(-8);
}

.scrollbar {
  top: px-to-rem(90);
  position: absolute;
  right: px-to-rem(50);
  z-index: 2;
}

.stick {
  width: px-to-rem(20);
  height: px-to-rem(675);
  background-color: #252525;
}

.bar {
  position: absolute;
  margin-top: px-to-rem(8);
  margin-bottom: px-to-rem(8);
  top: 0;
  left: px-to-rem(6);
  width: px-to-rem(8);
  height: px-to-rem(74);
  background-color: #606060;
}

.spinner {
  position: absolute;
  top: 0;
  bottom: 0;
  margin: auto;
  left: 0;
  right: 0;
  width: $spinner-size-small;
  height: $spinner-size-small;
}

import { LegalModalResponseBody } from '@apptypes/LegalModalResponseBody';
import { useCustomMouseWheel } from '@components/MouseNavigation/useCustomMouseWheel';
import { CenteredSpinner } from '@components/Spinner/CenteredSpinner';
import { useContentScrolling } from '@hooks/useContentScrolling';
import { FocusContext, useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import { useModalContext } from '@providers/ModalContext';
import { legalLoader } from '@routes/legalLoader';
import { isOrange } from '@util/platform';
import { pxToRem } from '@util/pxToRem';
import { cloneElement, ReactNode, useCallback, useEffect, useRef, useState } from 'react';
import { useRemoteController } from 'react-remote-controller';
import { getKeyMap } from 'target';

import { useErrors } from '../../../errors';
import { SettingsHelpModal } from '../SettingsHelpModal/SettingsHelpModal';
import styles from './scrollable-modal.module.scss';

const FOCUS_KEY_SCROLL_BAR = 'SCROLL_BAR';
const CONTAINER_WITH_TITLE_HEIGHT_LIMIT_REM = 35;
const CONTAINER_NO_TITLE_HEIGHT_LIMIT_REM = 39.5;

const parseHtmlTags = (pContent) => {
  let processed = pContent.replace(/\r\n/g, '<br/>');
  processed = processed.replace(/ +/g, ' ');

  const entities = {
    '&amp;': '&',
    '&lt;': '<',
    '&gt;': '>',
    '&quot;': '"',
    '&#39;': "'",
  };

  for (const [key, value] of Object.entries(entities)) {
    processed = processed.replace(new RegExp(key, 'g'), value);
  }

  return processed;
};

interface IScrollableModalProperties {
  returnFocusKey: string;
  closeOnGreen?: boolean;
  // uri and children are mutually exclusive
  uri?: string;
  children?: ReactNode;
  version?: string;
  buttonFocusKey?: string;
  closeOnEnter?: boolean;
}

export const ScrollableModal = (properties: IScrollableModalProperties) => {
  const { uri, returnFocusKey, children, closeOnGreen, closeOnEnter } = properties;
  const { focusKey, setFocus } = useFocusable({ isFocusBoundary: true });
  const { hideModal, showModal } = useModalContext();
  const { setError } = useErrors();
  const [rendered, setRendered] = useState(false);
  const [data, setData] = useState<null | LegalModalResponseBody>(null);
  const [loading, setLoading] = useState<boolean>(!!uri);
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const containerHeightLimitRem = children
    ? CONTAINER_NO_TITLE_HEIGHT_LIMIT_REM
    : CONTAINER_WITH_TITLE_HEIGHT_LIMIT_REM;
  const [containerHeight, setContainerHeight] = useState(containerHeightLimitRem);
  const stickRef = useRef(null);
  const barRef = useRef(null);
  const contentContainerRef = useRef(null);
  const contentRef = useRef(null);
  const childrenRef = useRef(null);
  // children need to be cloned to get a ref
  // TODO strongly type
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const clonedChildren = children && cloneElement(children as any, { ref: childrenRef });
  const selectedContent = content || children;
  const selectedContentRef = content ? contentRef : childrenRef;

  const { contentYOffset, scrollBarYOffset, scroll, shouldShowScrollbar } = useContentScrolling(
    contentContainerRef,
    contentRef,
    stickRef,
    barRef,
    content,
  );

  function onScrollUp() {
    scroll('up');
  }

  function onScrollDown() {
    scroll('down');
  }

  useCustomMouseWheel({ onMouseWheelScrollUp: onScrollUp, onMouseWheelScrollDown: onScrollDown });

  const close = useCallback(() => {
    hideModal();
    setFocus(returnFocusKey);
    if (properties.version)
      showModal({
        content: (
          <SettingsHelpModal
            returnFocusKey={returnFocusKey}
            buttonFocusKey={properties.buttonFocusKey || 'FOCUS_KEY_MODAL_0'}
            version={properties.version}
          />
        ),
      });
  }, [hideModal, properties.buttonFocusKey, properties.version, returnFocusKey, setFocus, showModal]);

  useEffect(() => {
    uri &&
      legalLoader(uri)
        .then((result) => {
          setFocus(FOCUS_KEY_SCROLL_BAR);
          setData(result);
        })
        .catch((error) => {
          setError(error);
        });
  }, [close, setError, setFocus, uri]);

  useEffect(() => {
    if (data) {
      setLoading(false);
      setTitle(data.title);
      setContent(parseHtmlTags(data.content));
    }
  }, [data]);

  useEffect(() => {
    children && setFocus(FOCUS_KEY_SCROLL_BAR);
  }, [children, setFocus]);

  useEffect(() => {
    if (!!selectedContent) {
      if (selectedContentRef.current) {
        const contentHeightRem = pxToRem(selectedContentRef.current.scrollHeight);
        if (contentHeightRem < containerHeightLimitRem) {
          setContainerHeight(contentHeightRem + 1);
        }
      }
    }
  }, [containerHeightLimitRem, selectedContent, selectedContentRef]);

  useEffect(() => {
    setRendered(true);
  }, [rendered]);

  useRemoteController<ReturnType<typeof getKeyMap>>({
    listenTo: {
      back: () => {
        close();
      },
      green: () => {
        closeOnGreen && close();
      },
      enter: () => {
        closeOnEnter && rendered && close();
      },
      up: () => {
        onScrollUp();
        return false;
      },
      down: () => {
        onScrollDown();
        return false;
      },
    },
  });

  return (
    <FocusContext.Provider value={focusKey}>
      <div className={`${styles['text-modal']} ${rendered ? styles['text-modal-visible'] : ''}`}>
        {loading && <CenteredSpinner />}

        {title && <h1 className={styles.title}>{title}</h1>}
        <div ref={contentContainerRef} className={styles.contentContainer} style={{ height: `${containerHeight}rem` }}>
          <div
            ref={contentRef}
            className={styles.htmlTextContent}
            dangerouslySetInnerHTML={content ? { __html: content } : undefined}
            style={{ top: contentYOffset }}
          >
            {clonedChildren}
          </div>
          {
            /**
             * On the OrangeTV box we get an imprint of the bottom text
             * Even if the text scrolls up or down we get that bottom imprint.
             * This will simply create a div, that will be placed at the bottom of the modal having the same color as the background
             * covering the text imprint.
             * Only for the OrangeTV box [ https://artetv.atlassian.net/browse/TVAPPS-1153 ]
             */
            isOrange() && shouldShowScrollbar && <div className={styles['modal-bottom-mask']} />
          }
        </div>

        {shouldShowScrollbar && (
          <div className={styles['scrollbar']}>
            <div ref={stickRef} className={styles['stick']} />
            <div
              focuskey={FOCUS_KEY_SCROLL_BAR}
              ref={barRef}
              className={styles['bar']}
              style={{ top: scrollBarYOffset }}
            />
          </div>
        )}
      </div>
    </FocusContext.Provider>
  );
};

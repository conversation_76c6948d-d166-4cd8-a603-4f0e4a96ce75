import { TEST_ID } from '@constants';
import { UseFocusableResult } from '@noriginmedia/norigin-spatial-navigation';
import i18n from 'i18next';
import { exitApp } from 'target';

import { Button } from '../Button/Button';
import { TextModal } from './TextModal/TextModal';

export const getExitModal = (
  hideModal: () => void,
  setFocus: UseFocusableResult['setFocus'],
  returnFocusKey: string,
) => (
  <TextModal
    testId={TEST_ID.EXIT.MODAL}
    headline={i18n.t('quit__confirm')}
    message=""
    buttons={
      <>
        <Button testId={TEST_ID.EXIT.ACCEPT} label={i18n.t('yes')} onEnterPress={() => exitApp()} focusOnMount />
        <Button
          testId={TEST_ID.EXIT.REJECT}
          label={i18n.t('no')}
          onEnterPress={() => {
            hideModal();
            setFocus(returnFocusKey);
          }}
        />
      </>
    }
    returnFocusKey={returnFocusKey}
  />
);

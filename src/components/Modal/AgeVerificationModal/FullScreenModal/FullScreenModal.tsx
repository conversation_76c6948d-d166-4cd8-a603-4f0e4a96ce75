import { FocusContext, useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import React from 'react';
import { useRemoteController } from 'react-remote-controller';
import { getKeyMap } from 'target';

import { useBackJourney } from '../../../../features/backjourney/useBackJourney';
import { IFocusState } from '../../../../focus';
import { useModalContext } from '../../../../providers/ModalContext';
import { CenteredLayout } from '../../../CenteredLayout/CenteredLayout';
import styles from './full-screen-modal.module.scss';

export interface IFullScreenModalProperties extends IFocusState {
  children?: React.ReactNode;
}

export const FullScreenModal = ({ children }: IFullScreenModalProperties) => {
  const { focusKey } = useFocusable({ isFocusBoundary: true });
  const { handleBack } = useBackJourney();
  const { hideModal } = useModalContext();

  useRemoteController<ReturnType<typeof getKeyMap>>({
    listenTo: {
      back: () => {
        hideModal();
        handleBack();
      },
    },
  });

  return (
    <FocusContext.Provider value={focusKey}>
      <div className={styles.wrapper}>
        <CenteredLayout>{children}</CenteredLayout>
      </div>
    </FocusContext.Provider>
  );
};

import { useBackJourney } from '../../../../features/backjourney/useBackJourney';
import { useModalContext } from '../../../../providers/ModalContext';
import { Alert } from '../components/Alert/Alert';
import { FullScreenModal } from '../FullScreenModal/FullScreenModal';

export interface IWarningModalProperties {
  message: string;
  title: string;
}

export function WarningModal({ message, title }: IWarningModalProperties) {
  const { handleBack } = useBackJourney();
  const { hideModal } = useModalContext();

  function handleEnterPress() {
    hideModal();
    handleBack();
  }

  return (
    <FullScreenModal>
      <Alert handleEnterPress={handleEnterPress} message={message} title={title} />
    </FullScreenModal>
  );
}

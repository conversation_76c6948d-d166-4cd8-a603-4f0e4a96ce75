import React from 'react';

import { AgeConfirmation } from '../components/AgeConfirmation/AgeConfirmation';
import { AgeVerificationWarning } from '../components/AgeVerificationWarning/AgeVerificationWarning';
import { FullScreenModal } from '../FullScreenModal/FullScreenModal';
import styles from './age-verification-modal-step2.module.scss';

export interface IAgeVerificationStep2 {
  age: string;
  message: string;
  revalidate: () => void;
  title: string;
}

export function AgeVerificationModalStep2({ age, message, revalidate, title }: IAgeVerificationStep2) {
  return (
    <FullScreenModal>
      <AgeVerificationWarning title={title} message={message} age={age} />
      <div className={styles.wrapper}>
        <AgeConfirmation revalidate={revalidate} age={age} />
      </div>
    </FullScreenModal>
  );
}

import { PlayActions } from '../components/actions/PlayActions';
import { AgeVerificationWarning } from '../components/AgeVerificationWarning/AgeVerificationWarning';
import { ButtonLayout } from '../components/ButtonLayout/ButtonLayout';
import { FullScreenModal } from '../FullScreenModal/FullScreenModal';

export interface IAgeVerificationWarningModalProperties {
  age: string;
  message: string;
  play: () => void;
  title: string;
}

export function AgeVerificationWarningModal({ age, message, play, title }: IAgeVerificationWarningModalProperties) {
  return (
    <FullScreenModal>
      <AgeVerificationWarning age={age} message={message} title={title} />
      <ButtonLayout>
        <PlayActions play={play} />
      </ButtonLayout>
    </FullScreenModal>
  );
}

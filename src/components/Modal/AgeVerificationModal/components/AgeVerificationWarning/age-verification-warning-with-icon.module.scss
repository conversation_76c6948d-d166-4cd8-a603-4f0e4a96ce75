@use '~styles/globals' as *;

$image-dimension: 82px;
$image-dimension-1080: 134px;

.wrapper {
  display: table;
  min-height: $image-dimension;
  background-repeat: no-repeat;
  background-position: center left;

  @media (min-width: 1920px) {
    min-height: $image-dimension-1080;
  }

  &.age-16 {
    background-image: url('@assets/img/age-verification-16-720p.png');
    @media (min-width: 1920px) {
      background-image: url('@assets/img/age-verification-16-1080p.png');
    }
  }

  &.age-18 {
    background-image: url('@assets/img/age-verification-18-720p.png');
    @media (min-width: 1920px) {
      background-image: url('@assets/img/age-verification-18-1080p.png');
    }
  }
}

.content-wrapper {
  display: table-cell;
  vertical-align: middle;
}

.content {
  margin-left: $image-dimension + 24;

  @media (min-width: 1920px) {
    margin-left: $image-dimension-1080 + 40;
  }
}

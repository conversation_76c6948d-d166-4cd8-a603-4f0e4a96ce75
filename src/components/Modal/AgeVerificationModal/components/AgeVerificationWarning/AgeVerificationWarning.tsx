import styles from './age-verification-warning.module.scss';
import { AgeVerificationWarningTextOnly } from './AgeVerificationWarningTextOnly';
import { AgeVerificationWarningWithIcon } from './AgeVerificationWarningWithIcon';
import { AgeVerificationWarningProps } from './types';

export function AgeVerificationWarning({ age, message, title }: AgeVerificationWarningProps) {
  const component = age ? (
    <AgeVerificationWarningWithIcon age={age} message={message} title={title} />
  ) : (
    <AgeVerificationWarningTextOnly message={message} title={title} />
  );

  return <div className={styles.wrapper}>{component}</div>;
}

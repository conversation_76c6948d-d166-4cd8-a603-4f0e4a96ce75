import { TEST_ID } from '@constants';
import classNames from 'classnames';

import styles from './age-verification-warning-with-icon.module.scss';
import { AgeVerificationText } from './AgeVerificationText';
import { AgeVerificationWarningProps } from './types';

export function AgeVerificationWarningWithIcon({ age, message, title }: AgeVerificationWarningProps) {
  return (
    <div className={classNames(styles.wrapper, styles[`age-${age}`])} data-testid={TEST_ID.VERIFICATION.AGE_WARNING_16}>
      <div className={styles['content-wrapper']}>
        <div className={styles['content']}>
          <AgeVerificationText title={title} message={message} />
        </div>
      </div>
    </div>
  );
}

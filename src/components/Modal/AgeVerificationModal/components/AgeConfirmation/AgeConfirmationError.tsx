import i18n from 'i18next';

import { FullScreenOverlay } from '../../../../FullScreenOverlay/FullScreenOverlay';
import { Alert } from '../Alert/Alert';

interface IAgeConfirmationErrorProperties {
  handleEnterPress: () => void;
}

export function AgeConfirmationError({ handleEnterPress }: IAgeConfirmationErrorProperties) {
  return (
    <FullScreenOverlay>
      <Alert
        handleEnterPress={handleEnterPress}
        message={i18n.t('error__try_again')}
        title={i18n.t('fsk__confirm_age_error')}
      />
    </FullScreenOverlay>
  );
}

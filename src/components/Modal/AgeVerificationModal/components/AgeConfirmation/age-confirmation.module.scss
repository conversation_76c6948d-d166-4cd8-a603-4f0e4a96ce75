@use '~styles/globals' as *;

$imageWidth: 200px;
$imageWidth1080: 300px;
$column-padding: px-to-rem(60);

.wrapper {
  display: table;
  max-width: px-to-rem(1528);
  margin: 0 auto;

  p {
    font-size: px-to-rem(40);
  }
}

.wrapper > div {
  display: table-cell;
  width: 50%;
}

.wrapper > div:first-child {
  padding-right: $column-padding;
  border-right: 1px solid $middle-grey;
}

.wrapper > div:last-child {
  padding-left: $column-padding;
  text-align: right;
}

.step1-wrapper {
  position: relative;
}

.step1-wrapper p {
  margin-left: $imageWidth + 30px;
  min-height: px-to-rem(200);

  @media (min-width: 1920px) {
    margin-left: $imageWidth1080 + 50px;
  }
}

.step1-wrapper img {
  position: absolute;
  margin: auto 0;
  top: 0;
  bottom: 0;
}

.step2-wrapper {
  text-align: center;
}

.step2-wrapper p {
  margin: 0 auto px-to-rem(50);
  max-width: px-to-rem(490);
  text-align: left;
}

.step2-wrapper button {
  margin-right: px-to-rem(25);
}

import { Button } from '@components/Button/Button';
import { useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import { useModalContext } from '@providers/ModalContext';
import { isFHD } from '@util/resolution';
import i18n from 'i18next';
import React, { useState } from 'react';

import { getAgeVerificationToken } from '../../../../../data';
import { FOCUS_KEY_AGE_CONFIRMATION, FOCUS_KEY_AGE_CONFIRMATION_ERROR } from '../../../../../focus';
import { BackButton } from '../buttons/BackButton';
import styles from './age-confirmation.module.scss';
import { isAgeVerificationError } from './AgeConfirmation.helper';
import { AgeConfirmationError } from './AgeConfirmationError';

const imageSize = isFHD() ? 300 : 200;

async function getImageSrc() {
  const res = isFHD() ? '1080' : '720';
  return import(`@assets/img/age-verification-qr-code-${res}p.png`);
}

let qrCodeSrc: string;

getImageSrc().then((module) => {
  qrCodeSrc = module.default;
});

interface IAgeConfirmationProperties {
  age: string;
  revalidate: () => void;
}

export function AgeConfirmation({ age, revalidate }: IAgeConfirmationProperties) {
  const [showVerificationError, setShowVerificationError] = useState(false);
  const { setFocus } = useFocusable();
  const { hideModal } = useModalContext();

  async function verify() {
    const ageVerificationToken = await getAgeVerificationToken();
    if (isAgeVerificationError(ageVerificationToken, age)) {
      setShowVerificationError(true);
      setFocus(FOCUS_KEY_AGE_CONFIRMATION_ERROR);
      return;
    }

    hideModal();
    revalidate();
  }

  function handleErrorConfirmation() {
    setShowVerificationError(false);
    setFocus(FOCUS_KEY_AGE_CONFIRMATION);
  }

  return (
    <>
      <div className={styles.wrapper}>
        <div>
          <div className={styles['step1-wrapper']}>
            <img src={qrCodeSrc} alt="" width={imageSize} height={imageSize} />
            <p>{i18n.t('fsk__confirm_age_step1')}</p>
          </div>
        </div>
        <div>
          <div className={styles['step2-wrapper']}>
            <p>{i18n.t('fsk__confirm_age_step2')}</p>
            <Button
              focusKey={FOCUS_KEY_AGE_CONFIRMATION}
              focusOnMount
              label={i18n.t('fsk__confirm_age_button_step2')}
              onArrowPress={(direction) => {
                switch (direction) {
                  case 'right':
                    return true;
                  default:
                    return false;
                }
              }}
              onEnterPress={() => verify()}
            />
            <BackButton />
          </div>
        </div>
      </div>
      {showVerificationError && <AgeConfirmationError handleEnterPress={handleErrorConfirmation} />}
    </>
  );
}

import i18n from 'i18next';

import { Button } from '../../../../Button/Button';
import { ButtonLayout } from '../ButtonLayout/ButtonLayout';
import styles from './alert.module.scss';

export interface IWarningProperties {
  handleEnterPress: () => void;
  message: string;
  title: string;
}

export function Alert({ handleEnterPress, message, title }: IWarningProperties) {
  return (
    <div className={styles.wrapper}>
      {title && <h2>{title}</h2>}
      {message && <p>{message}</p>}
      <ButtonLayout>
        <Button label={i18n.t('ok')} onArrowPress={() => false} onEnterPress={() => handleEnterPress()} focusOnMount />
      </ButtonLayout>
    </div>
  );
}

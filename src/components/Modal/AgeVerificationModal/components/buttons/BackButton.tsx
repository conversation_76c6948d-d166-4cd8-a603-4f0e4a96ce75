import { Button } from '@components/Button/Button';
import { useBackJourney } from '@features/backjourney/useBackJourney';
import { useModalContext } from '@providers/ModalContext';
import i18n from 'i18next';

export const BackButton = () => {
  const { handleBack } = useBackJourney();
  const { hideModal } = useModalContext();

  return (
    <Button
      label={i18n.t('back')}
      onArrowPress={(direction) => {
        switch (direction) {
          case 'left':
            return true;
          default:
            return false;
        }
      }}
      onEnterPress={() => {
        hideModal();
        handleBack();
      }}
    />
  );
};

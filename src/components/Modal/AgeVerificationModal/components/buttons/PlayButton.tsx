import { Button } from '@components/Button/Button';
import i18n from 'i18next';

interface IPlayButtonProperties {
  play: () => void;
}

export const PlayButton = ({ play }: IPlayButtonProperties) => (
  <Button
    label={i18n.t('watch')}
    onArrowPress={(direction) => {
      switch (direction) {
        case 'right':
          return true;
        default:
          return false;
      }
    }}
    onEnterPress={() => {
      play();
    }}
    focusOnMount
  />
);

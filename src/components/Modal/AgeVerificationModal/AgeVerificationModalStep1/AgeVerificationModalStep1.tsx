import { PlayActions } from '../components/actions/PlayActions';
import { AgeVerificationWarning } from '../components/AgeVerificationWarning/AgeVerificationWarning';
import { ButtonLayout } from '../components/ButtonLayout/ButtonLayout';
import { FullScreenModal } from '../FullScreenModal/FullScreenModal';
import { LoginActions } from './LoginActions';

export interface IAgeVerificationStep1 {
  age: string;
  message: string;
  play: () => void;
  title: string;
}

export function AgeVerificationModalStep1({ age, message, play, title }: IAgeVerificationStep1) {
  const Actions = age ? <LoginActions /> : <PlayActions play={play} />;

  return (
    <FullScreenModal>
      <AgeVerificationWarning title={title} message={message} age={age} />
      <ButtonLayout>{Actions}</ButtonLayout>
    </FullScreenModal>
  );
}

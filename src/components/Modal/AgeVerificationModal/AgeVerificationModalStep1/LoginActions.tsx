import { useCustomNavigate } from '@hooks/useCustomNavigate';
import i18n from 'i18next';

import { ROUTES } from '../../../../constants';
import { useModalContext } from '../../../../providers/ModalContext';
import { Button } from '../../../Button/Button';
import { BackButton } from '../components/buttons/BackButton';

export function LoginActions() {
  const { hideModal } = useModalContext();
  const navigate = useCustomNavigate();

  return (
    <>
      <Button
        label={i18n.t('login')}
        onArrowPress={(direction) => {
          switch (direction) {
            case 'right':
              return true;
            default:
              return false;
          }
        }}
        onEnterPress={() => {
          hideModal();
          navigate(ROUTES.MYARTE.ROOT);
        }}
        focusOnMount
      />
      <BackButton />
    </>
  );
}

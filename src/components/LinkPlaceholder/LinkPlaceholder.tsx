import styles from './link-placeholder.module.scss';

interface LinkPlaceholderProps {
  width: string | undefined;
  height: string | undefined;
  title: string;
}

export function LinkPlaceholder({ width, height, title }: LinkPlaceholderProps) {
  return (
    <div
      className={styles.wrapper}
      style={{
        width: `${width}px`,
        height: `${height}px`,
      }}
    >
      {title}
    </div>
  );
}

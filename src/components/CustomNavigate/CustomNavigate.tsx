import { getPersistedQueryString } from '@features/queryParams/persistedQueryParams';
import { Navigate } from 'react-router-dom';

interface CustomNavigateProps {
  path: string;
  replace?: boolean;
}

/**
 * wrapper around <Navigate/> to ensure we always use persisted query string
 */
export function CustomNavigate({ path, replace = false }: CustomNavigateProps) {
  return <Navigate to={`${path}${getPersistedQueryString()}`} replace={replace} />;
}

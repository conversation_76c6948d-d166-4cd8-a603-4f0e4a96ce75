import { useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import { isSpecificTVModel } from '@util/isSpecificTVModel';
import { useCallback } from 'react';

import { FOCUS_KEY_PRIMARY } from '../../focus';
import { useSeek, useVideoContext } from '.';

interface IVideoControls {
  cancelInterrupt: () => unknown;
  interrupt: (canPauseVideo: boolean) => unknown;
  isInterrupted: boolean;
  pause: () => unknown;
  play: () => unknown;
  togglePlayPause: () => unknown;
}

/**
 * interrupt
 * - an interrupt allows a component to pause playback and render UI e.g. a modal
 * - all video controls are disabled until the interrupt is cancelled
 * - when the interrupt is cancelled previous play state is restored
 */
let wasPlayingOnInterrupt = false;
let isInterrupted = false;

export function useVideoControls(): IVideoControls {
  const { isPlaying, videoObject } = useVideoContext();
  const { stallProtectJumpBackwards } = useSeek();
  const { setFocus } = useFocusable();

  const play = useCallback(() => {
    /**
     * On some TV's resuming video after pause is not working properly
     * so we need to apply hack to prevent this issue
     * VideoStream can enter into a stalled state and this bypasses it
     */
    if (isSpecificTVModel('Samsung', 2020) || isSpecificTVModel('Samsung', 2019)) {
      stallProtectJumpBackwards();
    } else {
      if (isInterrupted) return;
      !isPlaying && videoObject?.resume();
      setFocus(FOCUS_KEY_PRIMARY);
    }
  }, [isPlaying, setFocus, stallProtectJumpBackwards, videoObject]);

  const pause = useCallback(() => {
    if (isInterrupted) return;
    isPlaying && videoObject?.pause();
    setFocus(FOCUS_KEY_PRIMARY);
  }, [isPlaying, setFocus, videoObject]);

  const togglePlayPause = useCallback(() => {
    if (isInterrupted) return;
    if (isPlaying) {
      pause();
    } else {
      play();
    }
  }, [isPlaying, pause, play]);

  const interrupt = (canPauseVideo: boolean) => {
    if (canPauseVideo) {
      pause();
      wasPlayingOnInterrupt = isPlaying;
    }
    isInterrupted = true;
  };

  const cancelInterrupt = () => {
    isInterrupted = false;
    if (wasPlayingOnInterrupt) play();
    wasPlayingOnInterrupt = false;
  };

  return { pause, play, togglePlayPause, interrupt, cancelInterrupt, isInterrupted };
}

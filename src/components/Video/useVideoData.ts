import { VideoData } from '@apptypes/VideoResponseBody';
import { getPlaybackConfig } from '@data/source';
import { VideoLoaderResponse } from '@routes/videoLoader';
import { useLoaderData, useLocation } from 'react-router-dom';

export function useVideoData() {
  const { videoId } = useLoaderData() as VideoLoaderResponse;
  const location = useLocation();

  const getVideoData = (): Promise<VideoData> => {
    return new Promise<VideoData>((resolve) => {
      if (location.state?.videoData) {
        resolve(location.state.videoData);
      } else {
        const { isTrailer } = location?.state || false;
        const { isLive } = location?.state || false;
        getPlaybackConfig(videoId, isTrailer, isLive).then((videoResponseBody) => resolve(videoResponseBody.data));
      }
    });
  };

  return { getVideoData };
}

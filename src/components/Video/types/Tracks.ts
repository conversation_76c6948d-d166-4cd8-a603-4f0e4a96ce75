type Labels = { lang: string | undefined; text: string | undefined }[];

type AudioChannelConfiguration = {
  schemeIdUri: string;
  value: string;
};

export const ROLES = {
  CAPTION: 'caption',
  FORCED_SUBTITLE: 'forced-subtitle',
  SUBTITLE: 'subtitle',
} as const;

export type RoleValue = (typeof ROLES)[keyof typeof ROLES];

export type Roles = {
  schemeIdUri: string;
  value: RoleValue;
};

type Track = {
  labels: Labels[];
  lang: string;
  roles?: Roles;
  audioChannelConfiguration?: AudioChannelConfiguration[];
};

export type Tracks = {
  audio?: Track;
  text?: Track;
};

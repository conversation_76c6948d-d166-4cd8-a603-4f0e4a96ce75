import { useOverlayTypeDetect } from '@components/Player/useOverlayTypeDetect';
import { getStartTime } from '@features/streams/streams';
import { VideoLoaderResponse } from '@routes/videoLoader';
import { VideoObjectEvent } from '@videoplayer/video-object/types';
import React, {
  FunctionComponent,
  ReactNode,
  SetStateAction,
  useCallback,
  useContext,
  useEffect,
  useState,
} from 'react';
import { useLoaderData, useLocation } from 'react-router-dom';

import { VideoData, VideoMenuType } from '../../types';
import { VideoObject } from '../../videoplayer';
import { useVideoData } from './useVideoData';

export const SKIP_SEEKING_TARGET_DEFAULT = -1;

interface IVideoContext {
  videoData?: VideoData;
  videoObject?: VideoObject;
  isPlaying: boolean;
  startTime: number;
  controlsActive: boolean;
  setControlsActive: React.Dispatch<SetStateAction<boolean>>;
  setLoadedStream: React.Dispatch<SetStateAction<object>>;
  loadedStream: object;
  fakeSeeking: boolean;
  setFakeSeeking: React.Dispatch<SetStateAction<boolean>>;
  skipSeekingTarget: number;
  setSkipSeekingTarget: React.Dispatch<SetStateAction<number>>;
  videoMenu?: VideoMenuType;
  setVideoMenu: React.Dispatch<SetStateAction<VideoMenuType | undefined>>;
  currentOverlayType: string | null;
  setCurrentOverlayType: React.Dispatch<SetStateAction<string | null>>;
}

export const VideoContext = React.createContext<IVideoContext>({
  videoData: undefined,
  videoObject: undefined,
  isPlaying: false,
  startTime: 0,
  controlsActive: true,
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  setControlsActive: () => {},
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  setLoadedStream: () => {},
  loadedStream: {},
  fakeSeeking: false,
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  setFakeSeeking: () => {},
  skipSeekingTarget: SKIP_SEEKING_TARGET_DEFAULT,
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  setSkipSeekingTarget: () => {},
  videoMenu: undefined,
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  setVideoMenu: () => {},
  currentOverlayType: null,
  setCurrentOverlayType: () => {},
});

export const VideoContextProvider: FunctionComponent<{ children: ReactNode; videoObject: VideoObject }> = ({
  children,
  videoObject,
}) => {
  const location = useLocation();

  const [videoMenu, setVideoMenu] = useState<VideoMenuType | undefined>();

  const [controlsActive, setControlsActive] = useState(true);

  const [fakeSeeking, setFakeSeeking] = useState(false);

  const [skipSeekingTarget, setSkipSeekingTarget] = useState(SKIP_SEEKING_TARGET_DEFAULT);

  const [loadedStream, setLoadedStream] = useState({});

  const videoLoaderData = useLoaderData() as VideoLoaderResponse;

  const { getVideoData } = useVideoData();

  const [videoData, setVideoData] = useState<VideoData>();

  const [isPlaying, setIsPlaying] = useState(false);

  const { currentOverlayType, setCurrentOverlayType } = useOverlayTypeDetect(videoObject, videoLoaderData);

  const [startTime, setStartTime] = useState(0);

  useEffect(() => {
    if (!videoData) {
      getVideoData().then((value) => {
        setVideoData(value);
      });
    }
  }, [getVideoData, videoData, setFakeSeeking]);

  useEffect(() => {
    const newStartTime = getStartTime(videoLoaderData.videoId, location.state?.lastViewedItems);
    setStartTime(newStartTime);
  }, [videoLoaderData, location]);

  const playEventListener = useCallback(() => {
    setIsPlaying(true);
  }, []);

  const pauseEventListener = useCallback(() => {
    setIsPlaying(false);
  }, []);

  useEffect(() => {
    videoObject.on(VideoObjectEvent.PLAYING, playEventListener);
    videoObject.on(VideoObjectEvent.PAUSE, pauseEventListener);
    return () => {
      videoObject.off(VideoObjectEvent.PLAYING, playEventListener);
      videoObject.off(VideoObjectEvent.PAUSE, pauseEventListener);
    };
  }, [pauseEventListener, playEventListener, videoObject]);

  return (
    <VideoContext.Provider
      value={{
        videoData,
        videoObject,
        isPlaying,
        startTime,
        controlsActive,
        setControlsActive,
        setLoadedStream,
        loadedStream,
        fakeSeeking,
        setFakeSeeking,
        skipSeekingTarget,
        setSkipSeekingTarget,
        setVideoMenu,
        videoMenu,
        currentOverlayType,
        setCurrentOverlayType,
      }}
    >
      {children}
    </VideoContext.Provider>
  );
};

export const useVideoContext = (): IVideoContext => useContext(VideoContext);

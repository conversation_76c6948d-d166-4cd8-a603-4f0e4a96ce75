import { useEffect, useState } from 'react';

import { useInterval } from '../../hooks/useInterval';
import { VideoObjectEvent } from '../../videoplayer/video-object/types';
import { useVideoContext } from '.';

const SEEK_DELAY = 1500;

export function useSeek(): {
  scrubTo: (delta: number) => void;
  immediateScrubTo: (time: number) => void;
  immediateScrubToPercentage: (time: number) => void;
  seekTime: number;
  seeking: boolean;
  seek: (time: number) => void;
  stallProtectJumpBackwards: () => void;
} {
  const { videoObject, isPlaying, videoData } = useVideoContext();

  const [seeking, setSeeking] = useState<boolean>(false);
  const [seekTime, setSeekTime] = useState<number>(0);
  const [lastScrubTimestamp, setLastScrubTimestamp] = useState(0);
  const [seekAfterInterval, setSeekAfterInterval] = useState<number | null>(null);

  const stallProtectJumpBackwards = () => {
    videoObject && scrubTo(videoObject.getTime() - 1);
  };

  const scrubTo = (pTargetTime: number) => {
    isPlaying && videoObject?.pause();
    setSeeking(true);

    const constrainedTime = Math.max(0, Math.min(pTargetTime, videoObject?.getDuration() || 0));
    setSeekTime(constrainedTime);

    setLastScrubTimestamp(Date.now());
    setSeekAfterInterval(SEEK_DELAY);
  };

  const immediateScrubToPercentage = (pTargetPercentage: number) => {
    const constrainedTime = Math.max(0, (Math.min(videoObject?.getDuration() || 0) * pTargetPercentage) / 100);
    immediateScrubTo(constrainedTime);
  };

  const immediateScrubTo = (pTargetTime: number) => {
    scrubTo(pTargetTime);
    seek(pTargetTime);
  };

  const seek = (time: number) => {
    if (videoObject?.isIdle()) return;
    if (!videoData) return;

    videoObject?.setTime(time);

    videoObject?.resume();
  };

  useInterval(() => {
    if (!seekAfterInterval) return;

    if (Date.now() - lastScrubTimestamp >= SEEK_DELAY) {
      setSeekAfterInterval(null);
      seek(seekTime);
    }
  }, seekAfterInterval);

  useEffect(() => {
    const onSeeked = () => {
      setSeeking(false);
    };

    videoObject?.on(VideoObjectEvent.ENDED, onSeeked);
    videoObject?.on(VideoObjectEvent.SEEKED, onSeeked);
    videoObject?.on(VideoObjectEvent.ERROR, onSeeked);

    return () => {
      videoObject?.off(VideoObjectEvent.ENDED, onSeeked);
      videoObject?.off(VideoObjectEvent.SEEKED, onSeeked);
      videoObject?.off(VideoObjectEvent.ERROR, onSeeked);
    };
  }, [videoObject]);

  return { seeking, seekTime, seek, scrubTo, immediateScrubTo, immediateScrubToPercentage, stallProtectJumpBackwards };
}

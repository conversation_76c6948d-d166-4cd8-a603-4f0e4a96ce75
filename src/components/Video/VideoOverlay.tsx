import { ControlsOverlay } from '@components/Player/ControlsOverlay/ControlsOverlay';
import { FinishedAssetOverlay } from '@components/Player/FinishedAssetOverlay/FinishedAssetOverlay';
import { PlayerPreview } from '@components/Player/PlayerPreview/PlayerPreview';
import { overlayType } from '@components/Player/useOverlayTypeDetect';
import { useVideoContext } from '@components/Video/VideoContext';
import { FunctionComponent } from 'react';

interface IVideoOverlay {
  isOffline: boolean;
}

export const VideoOverlay: FunctionComponent<IVideoOverlay> = ({ isOffline }) => {
  const { currentOverlayType } = useVideoContext();

  return (
    <>
      {currentOverlayType === overlayType.CONTROLS && <ControlsOverlay isOffline={isOffline} />}
      {currentOverlayType === overlayType.FINISHED_ASSET && <FinishedAssetOverlay />}
      {currentOverlayType === overlayType.PLAYER_PREVIEW && <PlayerPreview />}
    </>
  );
};

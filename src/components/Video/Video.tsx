import { useVideoData } from '@components/Video/useVideoData';
import { Manifest404Error } from '@errors/Mainfest404Error';
import { useBackJourney } from '@features/backjourney/useBackJourney';
import { getTestStream, isChannel77 } from '@features/queryParams/queryParamsLookup';
import { useCustomNavigate } from '@hooks/useCustomNavigate';
import { useInterval } from '@hooks/useInterval';
import { useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import { GlobalContext } from '@providers/GlobalContextProvider';
import { VideoLoaderResponse } from '@routes/videoLoader';
import { Tracking } from '@tracking/Tracking';
import EventBus from '@util/EventBus';
import { isCurrentTimeWithinRange } from '@util/isCurrentTimeWithinRange';
import { loadScript } from '@util/loadScript';
import { emitOfflineEvent, isOnlineBackupCheck } from '@util/offlineEvent';
import { isMagentaTv } from '@util/platform';
import { hideFullScreenSpinner, showFullScreenSpinner } from '@util/showFullScreenSpinnerEvent';
import { SEEK_SECONDS_LIMIT_BEFORE_END } from '@util/videoSeekProtect';
import { VideoObjectEvent, VideoObjectStateEvent } from '@videoplayer/video-object/types';
import { FunctionComponent, useContext, useEffect, useState } from 'react';
import { useLoaderData } from 'react-router-dom';
import { config } from 'target';

import { ROUTES } from '../../constants';
import { LoadVideoError, NoStreamsFoundError, useErrors } from '../../errors';
import { Stream, StreamProtocolEnum, SubtitlesTrack } from '../../types';
import {
  label,
  parseSavedSubtitle,
  setSubtitleForCookies,
} from '../Player/ControlsOverlay/VideoMenu/data/audiosubtitles/util';
import { useVideoContext, useVideoControls } from '.';
import { useSave } from './save/useSave';

const LIVE_CHECK_INTERVAL = 60;

function modifyStreamViaQueryString(stream: Stream) {
  const testStream = getTestStream();
  if (!testStream) return;
  stream.url = testStream;
}

interface IVideoOptions {
  isOffline: boolean;
}

const Video: FunctionComponent<IVideoOptions> = ({ isOffline }) => {
  const { videoId } = useLoaderData() as VideoLoaderResponse;
  const { startTime, videoObject, setLoadedStream, videoData, setCurrentOverlayType } = useVideoContext();
  const attributes = videoData?.attributes;
  const { setShowSplashScreen } = useContext(GlobalContext);
  const [playableStateAchieved, setPlayableStateAchieved] = useState(false);
  const [manifest404Error, setManifest404Error] = useState<Manifest404Error>();
  const [liveCheckInterval, setLiveCheckInterval] = useState<number | null>(null);
  const { getVideoData } = useVideoData();
  const { handleBack } = useBackJourney();
  const { setError } = useErrors();
  const navigate = useCustomNavigate();
  const { resume } = useFocusable();
  const [offlineOccured, setOfflineOccured] = useState(false); // true if connection was lost at some point
  const { pause } = useVideoControls();

  useEffect(() => {
    setShowSplashScreen(false);
  }, [setShowSplashScreen]);

  useEffect(() => {
    if (isOffline) {
      videoObject?.pause();
    } else {
      try {
        playableStateAchieved && videoObject?.resume();
      } catch (e) {}
    }
  }, [isOffline, playableStateAchieved, videoObject]);

  useEffect(() => {}, [isOffline]);

  useEffect(() => {
    const loadVttLibrary = async () => {
      const vttLibraryUrl = `${process.env.REACT_ROUTER_BASE_PATH}libraries/vtt.min.js`;
      const elScript = document.querySelector(`script[src="${vttLibraryUrl}"]`);
      if (!elScript) {
        await loadScript(vttLibraryUrl, true);
      }
    };

    const loadStream = (stream: Stream) => {
      if (!videoObject) {
        throw new Error('Video object not found');
      }

      showFullScreenSpinner();

      // check if video is idle (aka video can be loaded) and if not wait for statechange
      if (videoObject.isIdle()) {
        setLoadedStream(stream);
        videoObject.load({ startTime, ...stream }, videoData);
      } else {
        videoObject.once(VideoObjectStateEvent.ENTER_IDLE, () => {
          setLoadedStream(stream);
          videoObject.load({ startTime, ...stream }, videoData);
        });
        videoObject.reset();
      }
    };

    if (!attributes) {
      // video data not loaded yet
      return;
    }

    // Get dash stream
    const targetProtocols = Object.values(StreamProtocolEnum);
    const stream = attributes.streams?.find((stream) => targetProtocols.includes(stream.protocol));

    if (stream) modifyStreamViaQueryString(stream);
    const availabilityRights = videoData?.attributes?.rights;
    if (availabilityRights && !isCurrentTimeWithinRange(availabilityRights)) {
      navigate(ROUTES.ERROR, {
        state: {
          errorType: 'LiveStreamUnavailable',
          errorParams: { availabilityRights: availabilityRights, videoId: videoId },
        },
      });
      return;
    }

    if (!stream) {
      throw new NoStreamsFoundError();
    }

    loadVttLibrary()
      .then(() => {
        loadStream(stream);
        const trackingVideoData = {
          data: videoData as VideoLoaderResponse,
          stream,
          options: {
            isChannel77: isChannel77(),
          },
        };
        Tracking.setVideoData(trackingVideoData);
      })
      .catch((error) => {
        console.error('Error loading VTT library:', error);
      });

    return () => {
      console.log('Video unmount');
      videoObject?.destroy();
      hideFullScreenSpinner();
    };
  }, [attributes, setLoadedStream, startTime, videoData, videoObject, videoId, setCurrentOverlayType, navigate]);

  useInterval(() => {
    getVideoData().then((value) => {
      const { attributes } = value;

      if (!attributes?.live) {
        handleBack();
      }
    });
  }, liveCheckInterval);

  useEffect(() => {
    hideFullScreenSpinner();

    if (playableStateAchieved) {
      const subtitlePref = parseSavedSubtitle();
      const availableSubtitles: SubtitlesTrack[] = videoObject?.getAvailableSubtitles();

      if (subtitlePref && subtitlePref.role === 'subtitle') {
        const subtitleIdx = availableSubtitles.findIndex(
          (subtitle) => subtitle.lang === subtitlePref.lang && label(subtitle) === subtitlePref.label,
        );

        if (subtitleIdx !== -1) {
          setSubtitleForCookies(availableSubtitles[subtitleIdx]);
          videoObject?.setCurrentSubtitles(subtitleIdx);
          return;
        }
      }

      const currentAudioTrack: window.dashjs.MediaInfo = videoObject?.getCurrentAudioTrack();
      const subtitleForcedIdx = availableSubtitles.findIndex(
        (subtitle) => subtitle.lang === currentAudioTrack.lang && subtitle.roles.includes('forced-subtitle'),
      );

      setSubtitleForCookies(availableSubtitles[subtitleForcedIdx]);
      videoObject?.setCurrentSubtitles(subtitleForcedIdx);

      if (attributes?.live) {
        setLiveCheckInterval(LIVE_CHECK_INTERVAL * 1000);
      }

      if (isMagentaTv()) {
        const videoHtmlObject = document.getElementById('video-object');
        if (videoHtmlObject) {
          try {
            if (startTime < videoData?.attributes?.metadata?.duration?.seconds - SEEK_SECONDS_LIMIT_BEFORE_END) {
              videoHtmlObject.currentTime = startTime;
            } else {
              videoHtmlObject.currentTime = 0;
            }

            videoHtmlObject.play();
          } catch (e) {}
        }
      }
    }
  }, [playableStateAchieved, videoObject, setLiveCheckInterval, attributes, startTime, videoData]);

  useEffect(() => {
    setOfflineOccured(true);
    const errorEventListener = async (event: Event) => {
      if (offlineOccured && config.offline?.goBackFromPlayback) {
        // Users will be returned to a previous page. Video errors can be ignored.
        return;
      }

      const customEvent = event as CustomEvent;

      const response = customEvent?.detail?.error?.data?.response;

      if (response?.status === 404) {
        setManifest404Error(new Manifest404Error(response?.responseURL));
        return;
      }
      resume();

      const isBackupOffline = await isOnlineBackupCheck();
      if (!isBackupOffline) {
        emitOfflineEvent(true);
        pause();
        hideFullScreenSpinner();
        return;
      }
      !isOffline && setError(new LoadVideoError());
    };

    const trackAudioSubtitleChange = () => {
      Tracking.trackAudioSubtitleChange({
        audio: videoObject?.getCurrentAudioTrack(),
        text: videoObject?.getCurrentSubtitles(),
      });
    };

    const playEventListener = () => {
      trackAudioSubtitleChange();
      Tracking.trackPlay(videoObject?.getTime());
      !playableStateAchieved && setPlayableStateAchieved(true);
    };

    const pauseEventListener = () => {
      trackAudioSubtitleChange();
      Tracking.trackPause();
    };

    const endedEventListener = () => Tracking.trackEnded();
    const timeUpdateEventListener = (event) => Tracking.trackTimeUpdate(event);

    videoObject?.on(VideoObjectEvent.ERROR, errorEventListener);
    videoObject?.on(VideoObjectEvent.PLAYING, playEventListener);
    videoObject?.on(VideoObjectEvent.PAUSE, pauseEventListener);
    videoObject?.on(VideoObjectEvent.ENDED, endedEventListener);
    videoObject?.on(VideoObjectEvent.TIMEUPDATE, timeUpdateEventListener);
    EventBus.on(VideoObjectEvent.PLAYBACKNOTALLOWED, hideFullScreenSpinner);

    return () => {
      videoObject?.trigger(VideoObjectEvent.TIMEUPDATE);
      videoObject?.off(VideoObjectEvent.ERROR, errorEventListener);
      videoObject?.off(VideoObjectEvent.PLAYING, playEventListener);
      videoObject?.off(VideoObjectEvent.PAUSE, pauseEventListener);
      videoObject?.off(VideoObjectEvent.ENDED, endedEventListener);
      videoObject?.off(VideoObjectEvent.TIMEUPDATE, timeUpdateEventListener);
      EventBus.off(VideoObjectEvent.PLAYBACKNOTALLOWED, hideFullScreenSpinner);
      resume();
    };
  }, [videoObject, playableStateAchieved, setError, resume, isOffline, offlineOccured, pause]);

  useEffect(() => {
    // this should only ever run once, no additional dependencies should be added here
    return () => {
      Tracking.trackStop();
    };
  }, []);

  useSave(videoId);

  if (manifest404Error) {
    hideFullScreenSpinner();
    throw manifest404Error;
  }

  return null;
};

export { Video };

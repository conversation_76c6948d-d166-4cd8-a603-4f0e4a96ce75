import { anonPersonalisationAllowed } from '@util/anonPersonalisationAllowed';
import { userHistoryScheduler } from '@util/userHistoryScheduler';
import { VideoObjectEvent } from '@videoplayer/video-object/types';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useLocation } from 'react-router-dom';

import { useVideoContext } from '../VideoContext';

const SAVE_POSITION_INTERVAL = 10;
const SAVE_AT_PERCENTAGE = 1;

const getProgressPercentage = (progress: number, duration: number): number => (progress / duration) * 100;

const doesNewValueReachAThreshold = (threshold: number, oldValue: number, newValue: number): boolean =>
  oldValue < threshold && newValue >= threshold;

const doesNewTimeReachSaveAtPercentageThreshold = (oldTime: number, newTime: number) =>
  doesNewValueReachAThreshold(SAVE_AT_PERCENTAGE, oldTime, newTime);

export const useSave = (videoId: string) => {
  const [updateCount, setUpdateCount] = useState<number>(0);
  const { videoObject } = useVideoContext();
  const currentTime = useRef(0);
  const duration = useRef(0);
  const location = useLocation();

  const savePosition = useCallback(
    (time?: number) => {
      if (location.state?.isTrailer) return;

      if (anonPersonalisationAllowed()) {
        const savePosition = time || currentTime.current;
        userHistoryScheduler.addToQueue(videoId, Math.floor(savePosition), Math.floor(duration.current));
      }
      // reset save interval
      setUpdateCount(0);
    },
    [location.state?.isTrailer, videoId],
  );

  useEffect(() => {
    const onPause = () => savePosition();

    const onTimeUpdate = (event: Event) => {
      const { time, duration: newDuration } = (event as CustomEvent).detail;

      // store duration
      if (!duration.current) {
        duration.current = newDuration;
      }

      // Because the time update event may fire as frequently as few times per second
      // we check if playhead moves by at least one sec to make it count
      if (Math.abs(currentTime.current - time) >= 1) {
        setUpdateCount(() => updateCount + 1);

        if (
          currentTime.current && // check currentTime - first time it's always 0 even when resuming a video
          doesNewTimeReachSaveAtPercentageThreshold(
            getProgressPercentage(currentTime.current, duration.current),
            getProgressPercentage(time, duration.current),
          )
        ) {
          savePosition(time);
        }

        currentTime.current = Math.floor(time);
        if (updateCount && updateCount % SAVE_POSITION_INTERVAL === 0) {
          savePosition();
        }
      }
    };

    videoObject?.on(VideoObjectEvent.PAUSE, onPause);
    videoObject?.on(VideoObjectEvent.TIMEUPDATE, onTimeUpdate);
    return () => {
      videoObject?.off(VideoObjectEvent.PAUSE, onPause);
      videoObject?.off(VideoObjectEvent.TIMEUPDATE, onTimeUpdate);
    };
  }, [savePosition, updateCount, videoObject]);

  useEffect(() => {
    return () => {
      savePosition();
    };
  }, [savePosition]);
};

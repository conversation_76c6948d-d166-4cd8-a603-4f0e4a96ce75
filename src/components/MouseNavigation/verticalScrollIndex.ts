/**
 * utility for setting the current vertical scroll index so that
 * MouseWheel can determine when to stop navigating up.
 *
 * Any component that has content that we want to scroll via mouse
 * actions should `setVerticalScrollIndex` when their index changes
 */

let verticalScrollIndex: number = 0;

export function getVerticalScrollIndex(): number {
  return verticalScrollIndex;
}

export function setVerticalScrollIndex(idx: number) {
  verticalScrollIndex = idx;
}

import { pxToRem } from '@util/pxToRem';

import styles from './horizontal-navigation.module.scss';

const BUTTON_SIZE = 64;
const BUTTON_SPACING = 48;

interface IHorizontalNavigation {
  moveLeft: () => void;
  moveRight: () => void;
  showLeftArrow: boolean;
  showRightArrow: boolean;
  imageHeight: number;
  horizontalVisibleSize: number;
}

export function HorizontalNavigation({
  moveLeft,
  moveRight,
  showLeftArrow,
  showRightArrow,
  imageHeight,
  horizontalVisibleSize,
}: IHorizontalNavigation) {
  const top = pxToRem(imageHeight / 2 - BUTTON_SIZE / 2);
  const left = pxToRem(BUTTON_SPACING);
  const right = pxToRem(horizontalVisibleSize - BUTTON_SPACING);
  const buttonSize = pxToRem(BUTTON_SIZE);

  const baseButtonStyle = { top: `${top}rem`, width: `${buttonSize}rem`, height: `${buttonSize}rem` };
  const leftButtonStyle = { ...baseButtonStyle, left: `${left}rem` };
  const rightButtonStyle = { ...baseButtonStyle, left: `${right}rem` };

  function handleRight() {
    moveRight();
  }
  function handleLeft() {
    moveLeft();
  }

  return (
    <div className={styles.wrapper}>
      {showLeftArrow && <button className={styles.left} style={leftButtonStyle} onClick={handleLeft}></button>}
      {showRightArrow && <button className={styles.right} style={rightButtonStyle} onClick={handleRight}></button>}
    </div>
  );
}

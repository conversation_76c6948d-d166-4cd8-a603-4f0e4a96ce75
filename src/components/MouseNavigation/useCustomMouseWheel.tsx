import { throttledWheelEvent } from '@components/MouseNavigation/throttledWheelEvent'; // Adjust the import path as necessary
import { MouseContext } from '@providers/MouseProvider'; // Adjust the import path as necessary
import { useContext, useEffect, useRef } from 'react';
import { config } from 'target';

interface ICustomMouseWheelProperties {
  onMouseWheelScrollUp: () => void;
  onMouseWheelScrollDown: () => void;
}

export function useCustomMouseWheel({ onMouseWheelScrollUp, onMouseWheelScrollDown }: ICustomMouseWheelProperties) {
  const { isMouseActive } = useContext(MouseContext);

  const scrollUpRef = useRef(onMouseWheelScrollUp);
  const scrollDownRef = useRef(onMouseWheelScrollDown);

  useEffect(() => {
    scrollUpRef.current = onMouseWheelScrollUp;
    scrollDownRef.current = onMouseWheelScrollDown;
  }, [onMouseWheelScrollUp, onMouseWheelScrollDown]);

  useEffect(() => {
    if (!config.hasMouseSupport) {
      return;
    }

    function handleWheelChange(event: WheelEvent) {
      if (!isMouseActive) {
        return;
      }

      if (event.deltaY > 0) {
        scrollDownRef.current();
      }

      if (event.deltaY < 0) {
        scrollUpRef.current();
      }
    }

    return throttledWheelEvent(isMouseActive, handleWheelChange);
  }, [isMouseActive]);
}

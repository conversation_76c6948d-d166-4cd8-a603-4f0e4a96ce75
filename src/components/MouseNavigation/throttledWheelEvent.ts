import throttle from 'lodash/throttle';

const WAIT = 200;

export function throttledWheelEvent(isMouseActive: boolean, handleWheelChange: (event: WheelEvent) => void) {
  const throttledWheelChange = throttle((event: WheelEvent) => {
    if (isMouseActive) {
      handleWheelChange(event);
    }
  }, WAIT);

  document.addEventListener('wheel', throttledWheelChange);
  return () => {
    throttledWheelChange.cancel();
    document.removeEventListener('wheel', throttledWheelChange);
  };
}

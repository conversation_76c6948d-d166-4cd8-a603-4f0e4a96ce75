@use '~styles/globals' as *;

.wrapper {
  button {
    z-index: map-get($zindex, mouse-horizontal-scroll-zones);
    position: absolute;
    border-radius: 100%;
    border: px-to-rem(2) solid $white;
    background-repeat: no-repeat;
    background-position: center;
    background-color: $middle-grey;
    opacity: 0.8;
  }

  button:hover {
    background-color: $primary-accent;
  }
}

.left {
  background-image: url('~@assets/img/icon/arrow-left-32x32.png');
}

.right {
  background-image: url('~@assets/img/icon/arrow-right-32x32.png');
}

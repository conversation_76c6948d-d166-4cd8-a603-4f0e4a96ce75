import { throttledWheelEvent } from '@components/MouseNavigation/throttledWheelEvent';
import { getVerticalScrollIndex } from '@components/MouseNavigation/verticalScrollIndex';
import { useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import { MouseContext } from '@providers/MouseProvider';
import { navigateByDirectionDown, navigateByDirectionUp } from '@util/navigation';
import { useContext, useEffect } from 'react';
import { config } from 'target';

export function useMouseWheel() {
  const { navigateByDirection } = useFocusable();
  const { isMouseActive } = useContext(MouseContext);

  useEffect(() => {
    if (!config.hasMouseSupport) {
      return;
    }

    function handleWheelChange(event: WheelEvent) {
      if (!isMouseActive) {
        return;
      }

      if (event.deltaY > 0) {
        navigateByDirectionDown(navigateByDirection);
      }

      if (event.deltaY < 0) {
        if (getVerticalScrollIndex() === 0) return;
        navigateByDirectionUp(navigateByDirection);
      }
    }

    return throttledWheelEvent(isMouseActive, handleWheelChange);
  }, [isMouseActive, navigateByDirection]);

  return null;
}

import { Button } from '@components/Button/Button';
import { ROUTES, TEST_ID } from '@constants';
import { useCustomNavigate } from '@hooks/useCustomNavigate';
import { useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import React, { useEffect } from 'react';
import { useLoaderData } from 'react-router-dom';

import { FOCUS_KEY_PRIMARY, teaserFocusGuard } from '../../focus';
import styles from './login-page.module.scss';
import { LoginQRCode } from './LoginQRCode';

interface ILoginPageData {
  title: string;
  subtitle: string;
  connect_message: string;
  connect_button: string;
  qrCodeTitle: string;
  qrCodeMessage: string;
}

const LoginPage = () => {
  const { title, subtitle, connect_message, connect_button, qrCodeTitle, qrCodeMessage } =
    useLoaderData() as ILoginPageData;
  const { setFocus } = useFocusable();
  const navigate = useCustomNavigate();

  useEffect(() => {
    setFocus(FOCUS_KEY_PRIMARY);
  }, [setFocus]);

  return (
    <>
      <div className={styles['heading']}>
        <p className={styles['title']}>{title}</p>
        <p className={styles['subtitle']}>{subtitle}</p>
      </div>
      <div className={styles['column-wrapper']}>
        <div className={styles['column-left']}>
          <p className={styles['connect-message']}>{connect_message}</p>
          <Button
            className={styles['button']}
            label={connect_button}
            testId={TEST_ID.AUTH.LOGIN}
            focusKey={FOCUS_KEY_PRIMARY}
            onEnterPress={() => navigate(ROUTES.MYARTE.AUTHENTICATE)}
            onArrowPress={(direction) => teaserFocusGuard(direction, 0, 0, setFocus)}
          />
        </div>
        <div className={styles['column-right']}>
          <p className={styles['qr-code-title']}>{qrCodeTitle}</p>
          <p className={styles['qr-code-message']}>{qrCodeMessage}</p>
          <LoginQRCode />
        </div>
      </div>
    </>
  );
};

export { LoginPage };

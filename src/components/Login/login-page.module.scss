@use '~styles/globals' as *;

.heading {
  height: px-to-rem(130);
  padding: px-to-rem(36) px-to-rem(25) 0 px-to-rem(25);

  .title {
    font-family: $font-family-bold;
    font-size: px-to-rem(55);
  }

  .subtitle {
    font-family: $font-family-regular;
    font-size: px-to-rem(40);
    margin-top: px-to-rem(18);
  }
}

.column-wrapper {
  position: relative;
  width: calc(100% - $menu-width-collapsed);
  padding: px-to-rem(150) 0;

  button {
    margin-top: px-to-rem(45);
  }
}

.column-left {
  display: inline-block;
  width: 50%;
  text-align: center;
  vertical-align: top;
  padding-left: px-to-rem(180);

  .connect-message {
    font-family: $font-family-bold;
    font-size: px-to-rem(40);
  }
}

.column-right {
  display: inline-block;
  width: 50%;
  padding-right: px-to-rem(260);
  text-align: center;

  .qr-code-title {
    font-family: $font-family-bold;
    font-size: px-to-rem(40);
  }

  img {
    margin-top: px-to-rem(85);
  }

  .qr-code-message {
    width: px-to-rem(600);
    margin-left: auto;
    margin-right: auto;
    font-family: $font-family-regular;
    font-size: px-to-rem(32);
  }
}

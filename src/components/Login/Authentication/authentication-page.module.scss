@use '~styles/globals' as *;

.search-menu {
  width: $keyboard-view-width;
  height: px-to-rem(100);
  background-color: $dark-grey;
  padding-top: px-to-rem(80);
  padding-left: px-to-rem(50);
}

.title {
  display: block;
  font-family: $font-family-bold;
  font-size: px-to-rem(55);
  line-height: px-to-rem(55);
}

.wrapper {
  position: absolute;
  top: px-to-rem(120);
  left: calc($keyboard-view-width);
  padding-left: px-to-rem(80);
  width: px-to-rem(1200);

  p {
    margin-bottom: px-to-rem(32);
    font-size: px-to-rem(32);
  }

  p.error {
    color: red;
    font-size: px-to-rem(28);
  }
}

.column {
  display: inline-block;
  vertical-align: top;
  width: 50%;
  text-align: center;
  padding: 0 px-to-rem(24);
}

.spinner {
  margin: 0 auto;
  width: $spinner-size-small;
}

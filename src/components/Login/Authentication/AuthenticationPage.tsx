import { AuthToken } from '@apptypes/SSOResponse';
import { LoginKeyboard } from '@components/Keyboard/LoginKeyboard';
import { NumbersField } from '@components/NumbersField/NumbersField';
import { Spinner } from '@components/Spinner/Spinner';
import { ROUTES } from '@constants';
import { logError } from '@errors/errorLogging';
import { eraseAppData, writeAppData } from '@features/appdata/appdata';
import { requestUserToken } from '@features/usercontent/userContentData';
import { useCustomNavigate } from '@hooks/useCustomNavigate';
import { BookmarksContext } from '@providers/BookmarksContext';
import { GlobalContext } from '@providers/GlobalContextProvider';
import { temporarySaveAnonymousData, transferAnonymousDataToAuthenticatedUser } from '@util/anonToAuthDataCopier';
import { COOKIE_ANON_TOKEN, COOKIE_USER_TOKEN } from '@util/cookies';
import { videoIdStore } from '@util/videoIdStore';
import { useContext, useEffect, useState } from 'react';
import { useRemoteController } from 'react-remote-controller';
import { useLoaderData } from 'react-router-dom';
import { getKeyMap, updateKeys } from 'target';

import { LoginQRCode } from '../LoginQRCode';
import styles from './authentication-page.module.scss';

interface IAuthenticationPage {
  title: string;
  qrCodeMessage: string;
  connectMessage: string;
  networkErrorMessage: string;
  unauthorizedErrorMessage: string;
  qrCodeImage: string;
}

const CODE_LENGTH = 8;

const AuthenticationPage = () => {
  const { title, qrCodeMessage, connectMessage, networkErrorMessage, unauthorizedErrorMessage } =
    useLoaderData() as IAuthenticationPage;

  const { resetBookmarksTimestamp } = useContext(BookmarksContext);
  const { fetchUserData } = useContext(GlobalContext);
  const navigate = useCustomNavigate();
  const [code, setCode] = useState('');
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [loading, setLoading] = useState(false);

  function appendCode(value: string) {
    if (code.length === CODE_LENGTH) return;
    setCode((prevState) => {
      return `${prevState}${value}`;
    });
  }

  function onBackspacePress() {
    setCode((prevState) => {
      return prevState.slice(0, -1);
    });
  }

  useRemoteController<ReturnType<typeof getKeyMap>>({
    listenTo: {
      0: () => appendCode('0'),
      1: () => appendCode('1'),
      2: () => appendCode('2'),
      3: () => appendCode('3'),
      4: () => appendCode('4'),
      5: () => appendCode('5'),
      6: () => appendCode('6'),
      7: () => appendCode('7'),
      8: () => appendCode('8'),
      9: () => appendCode('9'),
    },
  });

  useEffect(() => {
    // A hack for Samsung 2019 in the hbbtv context.
    // We need to set the key mask again, otherwise entering numbers will switch tv channels.
    updateKeys();
  }, []);

  useEffect(() => {
    async function getUserToken(code: string) {
      setLoading(true);
      try {
        const response = await requestUserToken(code);
        if (!response.ok) {
          if (response.status === 400 || response.status === 401) {
            setErrorMessage(unauthorizedErrorMessage);
          }
          setLoading(false);
          return;
        }

        const data: AuthToken = await response.json();
        const { access_token } = data;
        if (!access_token) {
          logError(new Error('[auth] access token undefined'), 'CRITICAL');
          return;
        }

        await temporarySaveAnonymousData();
        const expirationDays = 365 * 10; // 10 years
        eraseAppData(COOKIE_ANON_TOKEN);
        writeAppData(COOKIE_USER_TOKEN, access_token, expirationDays);
        resetBookmarksTimestamp();
        fetchUserData();
        await transferAnonymousDataToAuthenticatedUser();

        // a stored video id indicates authentication was required to play the
        // content, if we have one navigate directly to the video
        const videoId = videoIdStore.get();
        if (videoId) {
          navigate(`${ROUTES.VERIFICATION}/${videoId}`);
          videoIdStore.clear();
          return;
        }
        navigate(ROUTES.MYARTE.ROOT, { state: { stepsBack: 3 } });
      } catch (error) {
        setLoading(false);
        logError(new Error(`[auth]' ${error}`), 'CRITICAL');
        setErrorMessage(networkErrorMessage);
      }
    }

    code.length === CODE_LENGTH ? getUserToken(code) : setErrorMessage('');
  }, [code, navigate, networkErrorMessage, resetBookmarksTimestamp, unauthorizedErrorMessage, fetchUserData]);

  return (
    <>
      <LoginKeyboard
        title={title}
        onKeyboardPress={appendCode}
        onBackspacePress={onBackspacePress}
        focusBackspaceButton={!!errorMessage}
      ></LoginKeyboard>
      <div className={styles.wrapper}>
        <div className={styles.column}>
          <p>1. {qrCodeMessage}</p>
          <LoginQRCode />
        </div>
        <div className={styles.column}>
          <p>2. {connectMessage}</p>
          <NumbersField code={code} size={CODE_LENGTH} />
          {errorMessage && <p className={styles.error}>{errorMessage}</p>}
          {loading && (
            <div className={styles.spinner}>
              <Spinner small />
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export { AuthenticationPage };

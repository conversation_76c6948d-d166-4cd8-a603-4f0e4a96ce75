@use '~styles/globals' as *;

.wrapper {
  display: inline-block;

  span {
    display: inline-block;
    vertical-align: middle;
    width: px-to-rem(30);
    height: px-to-rem(16);
    border-radius: px-to-rem(4);
  }

  p {
    display: inline-block;
    margin-left: px-to-rem(12);
    font-family: $font-family-regular;
    vertical-align: middle;
    font-size: px-to-rem(28);
  }

  .small {
    width: px-to-rem(15);
    height: px-to-rem(8);
    border-radius: px-to-rem(2);
  }
}

.green {
  background-color: #3ec86f;
}

.yellow {
  background-color: #f8e11d;
}

.red {
  background-color: #ec1f1f;
}

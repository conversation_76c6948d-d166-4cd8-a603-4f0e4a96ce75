import classNames from 'classnames';

import styles from './color-button.module.scss';

type Color = 'green' | 'yellow' | 'red';
interface IColorButton {
  color: Color;
  label?: string;
  small?: boolean;
  className?: string;
}

export function ColorButton({ color, label, small, className }: IColorButton) {
  return (
    <div className={classNames(styles.wrapper, className)}>
      <span className={classNames(styles[color], { [styles.small]: small })}></span>
      {label && <p>{label}</p>}
    </div>
  );
}

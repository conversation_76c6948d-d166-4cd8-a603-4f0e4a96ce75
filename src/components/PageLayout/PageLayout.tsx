import { MenuResponseBody } from '@apptypes/MenuResponseBody';
import { CenteredLayout } from '@components/CenteredLayout/CenteredLayout';
import { useBackJourney } from '@features/backjourney/useBackJourney';
import { useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import { GlobalContext } from '@providers/GlobalContextProvider';
import { useModalContext } from '@providers/ModalContext';
import { trackAppLoadingEnds } from '@tracking/appstart/appstart';
import { cookiesNotified } from '@util/cookies';
import { useContext, useEffect, useState } from 'react';
import { useRemoteController } from 'react-remote-controller';
import { Outlet, useLoaderData, useLocation } from 'react-router-dom';
import { getKeyMap } from 'target';

import { FOCUS_KEY_PRIMARY, FOCUS_KEY_SIDE_MENU } from '../../focus';
import { CookieModal } from '../Modal/CookieModal/CookieModal';
import { SideMenu } from '../SideMenu/SideMenu';
import styles from './page-layout.module.scss';

export const PageLayout = () => {
  const { items: menuItems, featureFlags } = useLoaderData() as MenuResponseBody;
  const { setFocus } = useFocusable();
  const [menuExpanded, setMenuExpanded] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const { setShowSplashScreen, setFeatureFlags } = useContext(GlobalContext);
  const { showModal } = useModalContext();
  const { handleBack } = useBackJourney();
  const location = useLocation();

  useEffect(() => {
    setFeatureFlags(featureFlags);
  }, [featureFlags, setFeatureFlags]);

  useRemoteController<ReturnType<typeof getKeyMap>>({
    listenTo: {
      back: () => handleBack(),
    },
  });

  useEffect(() => {
    setIsMounted(true);
    trackAppLoadingEnds();
  }, []);

  useEffect(() => {
    if (cookiesNotified() || !isMounted) return;

    showModal({
      content: (
        <CenteredLayout className={styles['cookie-modal-wrapper']}>
          <CookieModal returnFocusKey={FOCUS_KEY_PRIMARY} withCookieModification={true} />
        </CenteredLayout>
      ),
    });
  }, [isMounted, location, showModal]);

  useEffect(() => {
    setShowSplashScreen(false);
  }, [location, setShowSplashScreen]);

  if (!cookiesNotified()) return;

  return (
    <>
      <SideMenu
        menuItems={menuItems}
        focusKey={FOCUS_KEY_SIDE_MENU}
        onFocus={() => setMenuExpanded(true)}
        onBlur={() => setMenuExpanded(false)}
        expanded={menuExpanded}
        setFocus={setFocus}
        classname={styles.menu}
      />
      <div className={styles['right-pane']}>
        <Outlet />
      </div>
    </>
  );
};

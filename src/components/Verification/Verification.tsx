import { useCustomNavigate } from '@hooks/useCustomNavigate';
import { BookmarksContext } from '@providers/BookmarksContext';
import { GlobalContext } from '@providers/GlobalContextProvider';
import { useModalContext } from '@providers/ModalContext';
import { trackAppLoadingEnds } from '@tracking/appstart/appstart';
import { COOKIE_USER_CONSENT, cookiesNotified, isLoggedIn } from '@util/cookies';
import { hideFullScreenSpinner, showFullScreenSpinner } from '@util/showFullScreenSpinnerEvent';
import { videoIdStore } from '@util/videoIdStore';
import { useCallback, useContext, useEffect, useState } from 'react';
import { useLoaderData, useLocation } from 'react-router-dom';
import { thirdPartyAuth } from 'target';

import { ROUTES } from '../../constants';
import { getPlaybackConfig } from '../../data';
import { LoadVideoError, useErrors } from '../../errors';
import { FOCUS_KEY_PRIMARY } from '../../focus';
import { Error, Warning } from '../../types';
import EventBus from '../../util/EventBus';
import { CenteredLayout } from '../CenteredLayout/CenteredLayout';
import { AgeVerificationModalStep1 } from '../Modal/AgeVerificationModal/AgeVerificationModalStep1/AgeVerificationModalStep1';
import { AgeVerificationModalStep2 } from '../Modal/AgeVerificationModal/AgeVerificationModalStep2/AgeVerificationModalStep2';
import { AgeVerificationWarningModal } from '../Modal/AgeVerificationModal/AgeVerificationWarningModal/AgeVerificationWarningModal';
import { WarningModal } from '../Modal/AgeVerificationModal/WarningModal/WarningModal';
import { CookieModal } from '../Modal/CookieModal/CookieModal';
import { getAge, isAgeRestricted } from '../Player/PlayerAgeVerification.helper';
import styles from './verification.module.scss';
import { VerificationLoaderResponse } from './verificationLoader';

/**
 * Prior to any video playback we need to perform age verification for restricted
 * content. Whenever a video is requested, either via a deeplink or invoking a
 * teaser in the UI, we navigate to this route in order to perform the verification
 * before attempting to load any content for playback.
 */

export function Verification() {
  const [showCookieModal, setShowCookieModal] = useState(!cookiesNotified());
  const { setError } = useErrors();
  const { hideModal, showModal } = useModalContext();
  const loaderData = useLoaderData() as VerificationLoaderResponse;
  const { bookmarks, videoId, lastViewedItems } = loaderData;
  const navigate = useCustomNavigate();
  const location = useLocation();
  const { initBookmarks } = useContext(BookmarksContext);
  const { setShowSplashScreen } = useContext(GlobalContext);
  const stepsBack = location?.state?.stepsBack;

  EventBus.on(COOKIE_USER_CONSENT, () => {
    setShowCookieModal(false);
  });

  useEffect(() => {
    setShowSplashScreen(false);
    showFullScreenSpinner();
  }, [setShowSplashScreen]);

  const runWorkflow = useCallback(async () => {
    const { isTrailer } = location?.state || false;
    const { isLive } = location?.state || false;

    async function getData(videoId: string) {
      const playbackConfigData = await getPlaybackConfig(videoId, isTrailer, isLive).catch(() => {
        setError(new LoadVideoError());
        return;
      });

      if (!playbackConfigData?.data) {
        setError(new LoadVideoError());
        return;
      }
      return playbackConfigData?.data;
    }

    function play() {
      hideModal();
      navigate(`${ROUTES.VIDEO}/${videoId}`, {
        state: { videoData, lastViewedItems, isTrailer, stepsBack },
        replace: true,
      });
    }

    function revalidate() {
      runWorkflow();
    }

    function getAgeVerificationModal(responseError: Error) {
      const { message, title } = responseError;
      const age = getAge(responseError);

      const props = {
        age,
        message,
        title,
      };

      if (isLoggedIn()) {
        return <AgeVerificationModalStep2 revalidate={revalidate} {...props} />;
      }

      return <AgeVerificationModalStep1 play={play} {...props} />;
    }

    function getAgeVerificationWarningModal(responseWarning: Warning) {
      const { message, title } = responseWarning;
      const age = getAge(responseWarning);
      return <AgeVerificationWarningModal age={age} message={message} play={play} title={title} />;
    }

    function getWarningModal(response: Error | Warning) {
      const { message, title } = response;
      return <WarningModal message={message} title={title} />;
    }

    const videoData = await getData(videoId);
    hideFullScreenSpinner();

    if (thirdPartyAuth.enabled) {
      play();
      return;
    }

    switch (true) {
      case !!videoData.attributes.error:
        const error = videoData.attributes.error;
        showModal({
          content: isAgeRestricted(error) ? getAgeVerificationModal(error) : getWarningModal(error),
        });
        break;
      case videoData.attributes.warnings?.length > 0:
        const warning = videoData.attributes.warnings[0];
        showModal({
          content: isAgeRestricted(warning) ? getAgeVerificationWarningModal(warning) : getWarningModal(warning),
        });
        break;
      default:
        play();
    }
  }, [hideModal, lastViewedItems, location, navigate, setError, showModal, videoId, stepsBack]);

  useEffect(() => {
    if (!showCookieModal) runWorkflow();
  }, [runWorkflow, showCookieModal]);

  useEffect(() => {
    return () => {
      hideModal();
    };
  }, [hideModal]);

  /**
   * store video id for cases where a user needs to login before playing content,
   * we can then use the videoId to navigate directly to the video after user
   * authentication
   */
  useEffect(
    function storeVideoId() {
      videoIdStore.set(videoId);
    },
    [videoId],
  );

  useEffect(
    function initialiseBookmarksOnDeeplinkToVideo() {
      if (!location?.state?.isDeeplinkRedirect) return;
      if (!bookmarks || !bookmarks.length) return;
      initBookmarks(bookmarks);
    },
    [location, bookmarks, initBookmarks],
  );

  if (showCookieModal) {
    trackAppLoadingEnds();
    return (
      <CenteredLayout className={styles['cookie-modal-wrapper']}>
        <CookieModal returnFocusKey={FOCUS_KEY_PRIMARY} withCookieModification={false} />
      </CenteredLayout>
    );
  }

  return null;
}

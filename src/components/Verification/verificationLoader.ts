import { userHistoryScheduler } from '@util/userHistoryScheduler';
import { LoaderFunctionArgs } from 'react-router-dom';
import { normalizeParams } from 'target';

import { logError } from '../../errors';
import { loadMeData } from '../../routes/pageHydration';
import { Bookmark } from '../../types';
import { MeData, MeDataLastViewed } from '../../types/SSOResponse';
import { anonPersonalisationAllowed } from '../../util/anonPersonalisationAllowed';
import { getBookmarks } from '../../util/meData';

export type VerificationLoaderResponse = {
  videoId: string;
  bookmarks: Bookmark[];
  lastViewedItems?: MeDataLastViewed[];
};

const verificationLoader = async ({ params }: LoaderFunctionArgs): Promise<VerificationLoaderResponse | Response> => {
  const normalizedParams = normalizeParams(params);
  const videoId = normalizedParams.videoId || '';
  let meData: MeData;
  let bookmarks: Bookmark[] = [];
  let lastViewedItems = undefined;

  if (anonPersonalisationAllowed()) {
    try {
      /**
       * when we deeplink to video, we do not a have a list of bookmarks in the
       * form of teasers, we can only get a flat array of favorites as program ids
       * from me/data endpoint. As a result we need to parse the data as partial
       * Bookmark types. This allows us to indicate if content has been bookmarked
       * when we deeplink to video.
       */
      meData = await loadMeData();
      bookmarks = getBookmarks(meData);
      lastViewedItems = meData.lastvieweds;
      userHistoryScheduler.startQueueInterval(true, lastViewedItems);
    } catch (e) {
      logError(new Error('cannot load user history'), 'WARNING');
    }
  }

  return {
    videoId,
    bookmarks,
    lastViewedItems,
  };
};

export { verificationLoader };

import { ISearchResults } from '../../types';
import { MIN_QUERY_CHARS } from './searchConfig';

export const validQuery = (searchResults: ISearchResults) => searchResults.query.length >= MIN_QUERY_CHARS;

export const validSearchResults = (searchResults: ISearchResults) => searchResults.results.length > 0;

export const noSearchResultsFound = (searchResults: ISearchResults) =>
  !validSearchResults(searchResults) && validQuery(searchResults);

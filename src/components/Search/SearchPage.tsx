import { IPageResults } from '@apptypes/pagination';
import { MeData } from '@apptypes/SSOResponse';
import { SearchKeyboard } from '@components/Keyboard/SearchKeyboard';
import { SearchContext } from '@providers/SearchContext';
import { loadMeData } from '@routes/pageHydration';
import { addTeaserProgress } from '@routes/zoneHydration';
import { getStoredPaginationResult, resetSearchResult, storePaginationFullResult } from '@util/NavHistory';
import i18next from 'i18next';
import { useCallback, useContext, useEffect, useState } from 'react';

import { search } from '../../data';
import { useLazyEffect } from '../../hooks';
import { DefaultGenre, DefaultSearchResults, IGenre, ISearchResponse } from '../../types';
import { MIN_QUERY_CHARS } from './searchConfig';
import { SearchView } from './SearchView/SearchView';

interface ISearchPageProperties {
  pageId?: string;
}

export const SearchPage = ({ pageId }: ISearchPageProperties) => {
  const [query, setQuery] = useState<string>('');
  const { setSearchResults, setSearchPageResults, setCurrentPaginationPage } = useContext(SearchContext);
  const [genre, setGenre] = useState<IGenre>(DefaultGenre);
  const keyboardTitle = i18next.t('search');
  const [loading, setLoading] = useState(false);
  const [shouldFocusGrid, setShouldFocusGrid] = useState(false);
  const [meData, setMeData] = useState<MeData | null>(null);
  useEffect(() => {
    async function getMeData() {
      const data = await loadMeData();
      setMeData(data);
    }
    getMeData();
  }, []);

  const processSearchResults = useCallback(
    (searchResponse: ISearchResponse, query: string, genre: IGenre) => {
      const { zones } = searchResponse || {};
      const { items = [], template, showItemTitle = false, showZoneTitle, title, pages, id } = zones?.[0] || {};
      const hydratedItems = meData ? addTeaserProgress(items, meData) : items;

      setSearchResults({
        results: hydratedItems,
        query,
        genre,
        template,
        showItemTitle,
        title: showZoneTitle ? title : '',
        pages,
        id,
      });
    },
    [setSearchResults, meData],
  );

  useLazyEffect(
    () => {
      const fetchResults = async (query: string, genre: IGenre) => {
        const searchResponse = await search(query, genre.value);
        setCurrentPaginationPage(1);
        setSearchPageResults({ data: [] });
        storePaginationFullResult(query, genre, searchResponse, pageId);
        setLoading(false);
        if (searchResponse) await processSearchResults(searchResponse, query, genre);
      };

      if (query.length >= MIN_QUERY_CHARS) {
        const storedSearchResult = getStoredPaginationResult(pageId);
        const storedSearchData = storedSearchResult?.search;
        if (storedSearchData) {
          setLoading(false);
          if (
            storedSearchData.query === query &&
            storedSearchData.genre.label === genre.label &&
            storedSearchData.genre.value === genre.value
          ) {
            return;
          }
        }
        fetchResults(query, genre);
      } else {
        setSearchResults(DefaultSearchResults);
      }
    },
    [query, genre],
    2000,
  );

  useEffect(() => {
    setLoading(query.length >= MIN_QUERY_CHARS);

    if (query.length >= 1 && query.length < MIN_QUERY_CHARS) {
      resetSearchResult();
    }
  }, [query]);

  const onKeyPress = useCallback(
    (key: string) => {
      setShouldFocusGrid(false);
      setQuery(query + key);
    },
    [query, setShouldFocusGrid],
  );

  const onBackspacePress = useCallback(() => setQuery(query.substring(0, query.length - 1)), [query]);
  const onGenrePress = useCallback(
    (genre: IGenre) => {
      setGenre(genre);
      if (!loading) {
        setLoading(true);
      }
    },
    [loading],
  );

  useEffect(() => {
    const storedSearchResult = pageId && getStoredPaginationResult(pageId);
    if (storedSearchResult && storedSearchResult.search) {
      setQuery(storedSearchResult.search.query);
      setGenre(storedSearchResult.search.genre);
      setShouldFocusGrid(true);
      setSearchPageResults(storedSearchResult.paginationData as IPageResults);
      setCurrentPaginationPage(storedSearchResult.paginationCurrentPage as number);

      processSearchResults(
        storedSearchResult.response as ISearchResponse,
        storedSearchResult.search.query,
        storedSearchResult.search.genre,
      );
    }
  }, [processSearchResults, setCurrentPaginationPage, setSearchPageResults, pageId]);

  return (
    <>
      <SearchKeyboard onKeyboardPress={onKeyPress} onBackspacePress={onBackspacePress} title={keyboardTitle} />
      <SearchView
        loading={loading}
        query={query}
        onGenrePress={onGenrePress}
        genre={genre}
        shouldFocusOnMount={shouldFocusGrid}
      />
    </>
  );
};

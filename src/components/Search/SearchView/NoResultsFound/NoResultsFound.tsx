import { TEST_ID } from '@constants';
import { SearchContext } from '@providers/SearchContext';
import { truncateString } from '@util/truncateString';
import { useContext } from 'react';
import { useTranslation } from 'react-i18next';

import styles from './no-results-found.module.scss';

export const NoResultsFound = () => {
  const { searchResults } = useContext(SearchContext);
  const { t } = useTranslation();
  const query = truncateString(searchResults.query, 24);
  const text = t('search__no-results', { 0: query });

  return (
    <span className={styles['no-results-found']} data-testid={TEST_ID.SEARCH.NO_RESULTS}>
      {text}
    </span>
  );
};

import { GridView } from '@components/GridView/GridView';
import { PAGE_IDS, TEST_ID } from '@constants';
import { SearchContext } from '@providers/SearchContext';
import { Tracking } from '@tracking/Tracking';
import i18n from 'i18next';
import React, { useContext, useEffect } from 'react';
import { useLoaderData } from 'react-router-dom';

import { FOCUS_KEY_GRID_VIEW, searchGridFocusGuard, withFocusableContainer } from '../../../focus';
import { IGenre, PageResponseBody, Zone } from '../../../types';
import { RESULTS_ROW_LENGTH } from '../searchConfig';
import { noSearchResultsFound, validQuery, validSearchResults } from '../searchUtil';
import { NoResultsFound } from './NoResultsFound/NoResultsFound';
import { Query } from './Query/Query';
import styles from './search-view.module.scss';
import { SearchEditorialContent } from './SearchEditorialContent/SearchEditorialContent';
import { SearchGenres } from './SearchGenres/SearchGenres';

interface ISearchViewProperties {
  loading: boolean;
  query: string;
  genre: IGenre;
  onGenrePress: (genre: IGenre) => void;
  shouldFocusOnMount?: boolean;
}

export const SearchView = withFocusableContainer(
  React.forwardRef<HTMLDivElement, ISearchViewProperties>(
    ({ query, genre, onGenrePress, loading, shouldFocusOnMount }, reference) => {
      const {
        searchResults,
        fetchNextPage,
        busyFetchingPage,
        searchPageResults,
        setCurrentGridItemIndex,
        currentGridItemIndex,
      } = useContext(SearchContext);
      const { zones: loaderZones } = useLoaderData() as PageResponseBody;

      useEffect(() => {
        /**
         * Using fullData to create a new array of zones which will be used to track the Search page and query on teaser click
         */
        const fullData: Zone[] = JSON.parse(JSON.stringify(loaderZones));
        if (
          fullData[0] &&
          fullData[0].teaserList &&
          (searchResults.results.length > 0 || searchPageResults.data.length > 0)
        ) {
          fullData[0].teaserList = [...searchResults.results, ...searchPageResults.data];
          fullData[0].title = `${PAGE_IDS.SEARCH_HOME} - ${query}`;
        } else {
          fullData[0].title = `${PAGE_IDS.SEARCH_HOME}`;
        }
        Tracking.setZonesInUsage(fullData);
      }, [searchPageResults, searchResults, loaderZones, query]);

      return (
        <div className={styles['search-view']} ref={reference}>
          <div className={styles['left-padding']}>
            <Query loading={loading} query={query} placeholder={i18n.t('search__placeholder')} />

            {validQuery(searchResults) && <SearchGenres onGenrePress={onGenrePress} selectedGenre={genre} />}

            {noSearchResultsFound(searchResults) && <NoResultsFound />}

            {validSearchResults(searchResults) && (
              <GridView
                initialResults={searchResults}
                fetchNextPage={fetchNextPage}
                busyFetchingPage={busyFetchingPage}
                pageResults={searchPageResults}
                setCurrentGridItemIndex={setCurrentGridItemIndex}
                currentGridItemIndex={currentGridItemIndex}
                resultsRowLength={RESULTS_ROW_LENGTH}
                focusKey={FOCUS_KEY_GRID_VIEW}
                focusGuard={searchGridFocusGuard}
                focusOnMount={shouldFocusOnMount}
                testid={TEST_ID.SEARCH.RESULTS}
              />
            )}
          </div>

          {!validSearchResults(searchResults) && <SearchEditorialContent />}
        </div>
      );
    },
  ),
);

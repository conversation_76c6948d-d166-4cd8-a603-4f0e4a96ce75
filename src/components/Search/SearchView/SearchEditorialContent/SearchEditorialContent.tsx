import { computeKeyboardFocusKey } from '@components/Keyboard/keyboardConfig';
import { useNormalizedParams } from '@hooks/useNormalizedParams';
import { useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import { Tracking } from '@tracking/Tracking';
import { buildPageId } from '@util/buildPageId';
import {
  getHorizontalNavIndex,
  getSearchKeyboardNavExitKeyLeft,
  getSearchKeyboardNavExitKeyRight,
  getVerticalNavIndex,
} from '@util/NavHistory';
import { useCallback, useEffect, useState } from 'react';
import { useRemoteController } from 'react-remote-controller';
import { useLoaderData } from 'react-router-dom';
import { getKeyMap } from 'target';

import {
  FOCUS_KEY_PRIMARY,
  FOCUS_KEY_SEARCH_EDITORIAL_CONTENT,
  teaserFocusKey,
  useReturnFocusOnUnmount,
} from '../../../../focus';
import { PageResponseBody } from '../../../../types';
import { ContentPage } from '../../../Pages/ContentPage';
import styles from './search-editorial-content.module.scss';

export const SearchEditorialContent = () => {
  useReturnFocusOnUnmount(FOCUS_KEY_PRIMARY, 'focus-key-zone-');
  const GRID_FIRST_LEFTSIDE_PATTERN = 'teaser-0';
  const routeParams = useNormalizedParams();
  const { zones: loaderZones } = useLoaderData() as PageResponseBody;

  const { setFocus, getCurrentFocusKey } = useFocusable();

  const [shouldImmediatelyGoLeft, setShouldImmediatelyGoLeft] = useState(false);

  const getPageZoneAndHorizontalIndex = useCallback(() => {
    const pageId = buildPageId(routeParams);
    if (!pageId) return {};
    const verticalIndex = getVerticalNavIndex(pageId);
    const zoneId = loaderZones[verticalIndex].id;
    const horizontalIndex = getHorizontalNavIndex(pageId, zoneId);
    return { pageId, zoneId, horizontalIndex, verticalIndex };
  }, [loaderZones, routeParams]);

  const handleDirectionalKeyPress = () => {
    setShouldImmediatelyGoLeft(getCurrentFocusKey().includes(GRID_FIRST_LEFTSIDE_PATTERN));
    return true;
  };

  useRemoteController<ReturnType<typeof getKeyMap>>({
    listenTo: {
      /** Both left and right listeners are used to handle the case when the user navigates away from the keyboard
       * Will make the focus correctly jump to the keyboard's last focused key
       * by leveraging the getSearchKeyboardNavExitKeyRight function from navHistory
       */
      left: () => {
        if (shouldImmediatelyGoLeft) {
          setShouldImmediatelyGoLeft(false);
          setFocus(computeKeyboardFocusKey(getSearchKeyboardNavExitKeyRight()) || FOCUS_KEY_PRIMARY);
          return false;
        }

        if (getCurrentFocusKey().includes(GRID_FIRST_LEFTSIDE_PATTERN)) {
          setShouldImmediatelyGoLeft(true);
        }

        return true;
      },
      right: handleDirectionalKeyPress,
      up: handleDirectionalKeyPress,
      down: handleDirectionalKeyPress,
    },
  });

  useEffect(() => {
    // Check if it's not the first visit on the search page
    if (getSearchKeyboardNavExitKeyLeft()) {
      const { horizontalIndex, verticalIndex } = getPageZoneAndHorizontalIndex();
      setFocus(teaserFocusKey(verticalIndex || 0, horizontalIndex || 0));
      setShouldImmediatelyGoLeft(horizontalIndex === 0);
    }
  }, [loaderZones, routeParams, setFocus, getPageZoneAndHorizontalIndex]);

  useEffect(() => {
    Tracking.setZonesInUsage(loaderZones);
  }, [loaderZones]);

  return (
    <div className={styles['search-editorial-content']}>
      <ContentPage focusKey={FOCUS_KEY_SEARCH_EDITORIAL_CONTENT} searchMenuPresent={true} />
    </div>
  );
};

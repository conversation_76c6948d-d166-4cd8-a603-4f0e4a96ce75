@use '~styles/globals' as *;

$horizontal-padding: px-to-rem(24);

.query {
  position: relative;
  width: $search-header-width;
  height: px-to-rem(90);
  padding: px-to-rem(28) $horizontal-padding;
  border-radius: px-to-rem(8);
  background: $dark-grey;
  font-family: $font-family-regular;
  font-size: px-to-rem(32);
  line-height: px-to-rem(33);
  margin-bottom: px-to-rem(40);

  @media (min-width: 1920px) {
    width: $search-header-width-fhd;
  }

  &.placeholder {
    color: $grey;
  }
}

.text {
  display: inline-block;
  width: 90%;
  overflow: hidden;
  white-space: nowrap;
}

.spinner {
  position: absolute;
  top: 0;
  right: px-to-rem(32);
  bottom: 0;
  margin: auto 0;
  height: $spinner-size-small;
}

import { Spinner } from '@components/Spinner/Spinner';
import { TEST_ID } from '@constants';
import classNames from 'classnames';
import React, { useRef } from 'react';

import styles from './query.module.scss';

interface ISearchQuery {
  loading: boolean;
  placeholder: string;
  query: string;
}

const formatQuery = (query: string) => query.charAt(0).toUpperCase() + query.substring(1).toLowerCase();

export const Query: React.FC<ISearchQuery> = ({ loading, query, placeholder }) => {
  const ref = useRef<HTMLDivElement>(null);

  // scroll content when it exceeds width
  if (ref.current) ref.current.scrollLeft = ref.current.scrollWidth;

  return (
    <div className={classNames(styles.query, { [styles.placeholder]: !query })}>
      <span ref={ref} className={styles.text} data-testid={TEST_ID.SEARCH.QUERY}>
        {query ? formatQuery(query) : placeholder}
      </span>
      {loading && (
        <div className={styles.spinner}>
          <Spinner small />
        </div>
      )}
    </div>
  );
};

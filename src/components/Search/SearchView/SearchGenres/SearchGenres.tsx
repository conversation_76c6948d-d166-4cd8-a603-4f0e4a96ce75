import { DefaultGenre, GenreType, IGenre } from '@apptypes/Search';
import { computeKeyboardFocusKey } from '@components/Keyboard/keyboardConfig';
import { useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import { SearchContext } from '@providers/SearchContext';
import { getSearchKeyboardNavExitKeyRight } from '@util/NavHistory';
import React, { useContext } from 'react';

import { FOCUS_KEY_PRIMARY, searchGenreFocusKey, teaserFocusKey, useReturnFocusOnUnmount } from '../../../../focus';
import { validSearchResults } from '../../searchUtil';
import { GenreButton } from './GenreButton/GenreButton';
import styles from './search-genres.module.scss';

const genres: IGenre[] = [
  { value: GenreType.Documentaries, translationKey: 'documentaries' },
  { value: GenreType.TvSeries, translationKey: 'series' },
  { value: GenreType.Movies, translationKey: 'films' },
  { value: GenreType.TvShows, translationKey: 'magazines' },
  { value: GenreType.Concerts, translationKey: 'concerts' },
  DefaultGenre,
];

interface ISearchGenresProperties {
  selectedGenre: IGenre;
  onGenrePress: (genre: IGenre) => void;
}

export const SearchGenres: React.FC<ISearchGenresProperties> = ({ selectedGenre, onGenrePress }) => {
  const { setFocus } = useFocusable();
  const { searchResults, currentGridItemIndex } = useContext(SearchContext);
  useReturnFocusOnUnmount(FOCUS_KEY_PRIMARY, 'focus-key-search-genre-');

  const buttons = genres.map((genre, index) => (
    <GenreButton
      focusKey={searchGenreFocusKey(index)}
      selected={selectedGenre.value === genre.value}
      onEnterPress={() => onGenrePress(genre)}
      onArrowPress={(direction) => {
        switch (direction) {
          case 'left':
            if (index) {
              return true;
            } else {
              setFocus(computeKeyboardFocusKey(getSearchKeyboardNavExitKeyRight()) || FOCUS_KEY_PRIMARY);
              return false;
            }
          case 'right':
            return true;
          case 'down':
            if (!validSearchResults(searchResults)) {
              setFocus(teaserFocusKey(0, currentGridItemIndex || 0));
              return false;
            }
            return true;
          case 'up':
            return false;
          default:
            return false;
        }
      }}
      key={genre.translationKey}
      {...genre}
    />
  ));

  return <div className={styles['search-genres']}>{buttons}</div>;
};

import i18n from 'i18next';
import React, { ButtonHTMLAttributes } from 'react';

import { focusClassNames, IFocusState, withFocusable } from '../../../../../focus';
import { Icon } from '../../../../Icon/Icon';
import styles from './genre-button.module.scss';

interface IGenreButtonProperties extends ButtonHTMLAttributes<HTMLButtonElement>, IFocusState {
  translationKey: string;
  selected: boolean;
  onEnterPress: () => void;
}

export const GenreButton = withFocusable(
  React.forwardRef<HTMLButtonElement, IGenreButtonProperties>(
    ({ translationKey, selected, className, style, ...properties }, reference) => (
      <button
        ref={reference}
        onClick={() => properties.onEnterPress()}
        style={style}
        className={focusClassNames(
          styles,
          properties,
          styles['genre-button'],
          {
            [styles['selected']]: selected,
          },
          className,
        )}
      >
        {selected && <Icon type="check" size="XS" isFocused={properties.isFocused} className={styles.icon} />}
        <span className={focusClassNames(styles, properties, styles.label)}>{i18n.t(translationKey)}</span>
      </button>
    ),
  ),
);

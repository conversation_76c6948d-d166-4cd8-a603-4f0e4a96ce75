@use '~styles/globals' as *;

$height: px-to-rem(40);

.genre-button {
  height: $height;
  padding: 0 px-to-rem(16);
  background-color: $dark-grey;
  border-radius: px-to-rem(7);
  border: solid px-to-rem(3) transparent;

  &.is-focused {
    background-color: $white;
  }

  &.selected {
    padding-left: 0;
  }
}

.label {
  color: $white;
  font-size: px-to-rem(25);
  font-family: $font-family-bold;
  line-height: $height;
  vertical-align: middle;

  &.is-focused {
    color: $black;
  }
}

.icon {
  position: relative;
  top: px-to-rem(-2);
  margin-left: px-to-rem(8);
  margin-right: px-to-rem(8);
}

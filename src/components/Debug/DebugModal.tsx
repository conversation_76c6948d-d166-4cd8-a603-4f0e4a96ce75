import { VerticalList } from '@components/List/VerticalList';
import { CenteredSpinner } from '@components/Spinner/CenteredSpinner';
import { FocusContext, useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import { workbenchLoader, WorkbenchURL } from '@routes/workbenchLoader';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useRemoteController } from 'react-remote-controller';
import { getKeyMap } from 'target';

import { IFocusState } from '../../focus';
import { Button } from '../Button/Button';
import { useCloseModal } from '../Modal/useCloseModal';
import styles from './debug-modal.module.scss';

export interface IWorkbenchModalProperties extends IFocusState {
  returnFocusKey: string;
}

export const DebugModal = ({ returnFocusKey }: IWorkbenchModalProperties) => {
  const { t } = useTranslation();
  const { focusKey, setFocus } = useFocusable({ isFocusBoundary: true });
  const { close } = useCloseModal(setFocus, returnFocusKey);
  const [data, setData] = useState<WorkbenchURL[]>();
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<boolean>(false);
  const [selectedItemIndex, setSelectedItemIndex] = useState(0);

  useRemoteController<ReturnType<typeof getKeyMap>>({
    listenTo: {
      back: () => {
        close();
      },
    },
  });

  useEffect(() => {
    if (data) setLoading(false);
  }, [data]);

  useEffect(() => {
    workbenchLoader()
      .then((response) => {
        setData(response);
      })
      .catch((error) => {
        setError(error.toString());
        setLoading(false);
      });
  }, []);

  /**
   * Merges query string parameters from a given URL with the browser's current URL parameters.
   * Parameters from the browser's URL will override parameters with the same name from the input URL.
   *
   * @param {string} url The input URL string, which can be absolute or protocol-relative (e.g., //domain/path).
   * @returns {string} The final URL string with merged and overridden query parameters.
   */
  function mergeUrlWithBrowserQueryParams(url: string): string {
    // Get parameters from the browser's current URL
    const browserParams = new URLSearchParams(window.location.search);

    // Prepare the input URL for parsing.
    // If the URL is protocol-relative (starts with '//'), prepend 'http:' to create a valid absolute URL for the URL constructor.
    // This allows for correct parsing while preserving the original relative nature for the output.
    let urlToParse = url;
    if (url.startsWith('//')) {
      urlToParse = `http:${url}`;
    }

    // Create a URL object from the input URL to easily access its components.
    // The second argument `window.location.href` provides a base URL for truly relative paths
    // (e.g., 'some/path' without a leading '/').
    const urlObj = new URL(urlToParse, window.location.href);

    // Get parameters from the input URL
    const inputParams = urlObj.searchParams;

    // Create a new URLSearchParams object, starting with the input URL's parameters.
    const mergedParams = new URLSearchParams(inputParams.toString());

    // Iterate over the browser's parameters and set them on the mergedParams object.
    // This will automatically override any existing parameters from the input URL
    Array.from(browserParams.entries()).forEach(function ([key, value]) {
      mergedParams.set(key, value);
    });

    // Reconstruct the base URL (origin + pathname) from the parsed input URL.
    // This ensures we keep the original path and domain/origin.
    let baseUrlWithoutQuery = urlObj.origin + urlObj.pathname;

    // If the original input URL was protocol-relative (started with '//'),
    // we need to revert the 'http:' prefix we added for parsing to maintain its protocol-relative nature in the final output.
    if (url.startsWith('//')) {
      baseUrlWithoutQuery = baseUrlWithoutQuery.substring(baseUrlWithoutQuery.indexOf('//'));
    }

    // Convert the merged parameters back to a query string.
    const queryString = mergedParams.toString();

    // Construct the final URL. Append the query string if it's not empty.
    let finalUrl = baseUrlWithoutQuery;
    if (queryString) {
      finalUrl += '?' + queryString;
    }

    return finalUrl;
  }

  function onEnterPress(url: string) {
    window.location.href = mergeUrlWithBrowserQueryParams(url);
  }

  function getButtons(data: WorkbenchURL[]) {
    return data.map((item, idx: number) => (
      <Button
        // eslint-disable-next-line react/no-array-index-key
        key={idx}
        label={`${idx + 1}: ${item.title}`}
        onEnterPress={() => onEnterPress(item.url)}
        onFocus={() => setSelectedItemIndex(idx)}
      />
    ));
  }

  return (
    <FocusContext.Provider value={focusKey}>
      <div className={styles.wrapper}>
        <h2>
          Debug
          <span>Total: {data?.length}</span>
        </h2>
        {loading && <CenteredSpinner />}
        {!loading && (
          <>
            {error && <p>{error}</p>}
            <div className={styles.actions}>
              <Button label={t('cancel')} focusOnMount onEnterPress={close} />
            </div>
            {data && data.length > 0 && (
              <div className={styles.list}>
                <VerticalList scrollToEnd={true} totalItems={data.length} currentItem={selectedItemIndex}>
                  {getButtons(data)}
                </VerticalList>
              </div>
            )}
          </>
        )}
      </div>
    </FocusContext.Provider>
  );
};

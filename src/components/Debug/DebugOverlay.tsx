import { useEffect, useRef } from 'react';

import styles from './debug-overlay.module.scss';
import { useCaptureLogs } from './useCaptureLogs';

export function DebugOverlay() {
  const logs = useCaptureLogs();
  const ref = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    if (!ref.current) return;
    ref.current.scrollTop = ref.current.scrollHeight;
  };

  useEffect(() => {
    scrollToBottom();
  }, [logs]);

  return (
    <div className={styles.wrapper}>
      <div ref={ref} className={styles.logs}>
        {logs.map(({ log, level }, index) => (
          // eslint-disable-next-line react/no-array-index-key
          <p key={index}>
            [{level}] {log}
          </p>
        ))}
      </div>
    </div>
  );
}

@use '~styles/globals' as *;

$gap: px-to-rem(32);

.wrapper {
  margin: 0 auto;
  padding: calc($gap * 3);
  width: 100%;
  height: 100%;
  background-color: $primary-background;

  h2 {
    margin-bottom: $gap;

    span {
      margin-left: px-to-rem(16);
      color: $primary-accent;
      font-size: smaller;
    }
  }

  button {
    margin: 0 $gap $gap 0;
  }
}

.actions {
  margin-bottom: calc($gap * 2);
}

.list {
  overflow: hidden;
}

import { useEffect, useState } from 'react';

export interface ILogEntry {
  log: string;
  level: string;
}

interface IConsoleMethods {
  log: (...args: unknown[]) => void;
  error: (...args: unknown[]) => void;
  warn: (...args: unknown[]) => void;
  info: (...args: unknown[]) => void;
}

function formatLog<T>(...args: T[]): string {
  return args.map(String).join(' ');
}

function overrideConsoleMethods(consoleMethods: IConsoleMethods, handleLog: (logEntry: ILogEntry) => void) {
  console.log = function <T>(...args: T[]) {
    const log = formatLog(...args);
    handleLog({ log, level: 'log' });
    consoleMethods.log.apply(console, args);
  };

  console.error = function <T>(...args: T[]) {
    const log = formatLog(...args);
    handleLog({ log: `[Error] ${log}`, level: 'error' });
    consoleMethods.error.apply(console, args);
  };

  console.warn = function <T>(...args: T[]) {
    const log = formatLog(...args);
    handleLog({ log: `[Warning] ${log}`, level: 'warn' });
    consoleMethods.warn.apply(console, args);
  };

  console.info = function <T>(...args: T[]) {
    const log = formatLog(...args);
    handleLog({ log: `[Info] ${log}`, level: 'info' });
    consoleMethods.info.apply(console, args);
  };
}

function resetConsoleMethods(consoleMethods: IConsoleMethods): void {
  console.log = consoleMethods.log;
  console.error = consoleMethods.error;
  console.warn = consoleMethods.warn;
  console.info = consoleMethods.info;
}

export const useCaptureLogs = () => {
  const [logs, setLogs] = useState<ILogEntry[]>([]);

  useEffect(() => {
    const consoleMethods = {
      log: console.log,
      error: console.error,
      warn: console.warn,
      info: console.info,
    };

    function handleLog(logEntry: ILogEntry) {
      setLogs((prevLogMessages: ILogEntry[]) => [...prevLogMessages, logEntry]);
    }

    overrideConsoleMethods(consoleMethods, handleLog);

    return () => {
      resetConsoleMethods(consoleMethods);
    };
  }, []);

  return logs;
};

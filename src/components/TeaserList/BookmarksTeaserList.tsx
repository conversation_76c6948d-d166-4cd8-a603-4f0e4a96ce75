import { useContext, useEffect } from 'react';

import { BookmarksContext } from '../../providers/BookmarksContext';
import { TeaserList, TeaserListProperties } from './TeaserList';

const MAX_BOOKMARKS = 20;

export function BookmarksTeaserList(props: TeaserListProperties) {
  const { bookmarks, setBookmarksZoneIndex } = useContext(BookmarksContext);

  useEffect(() => {
    setBookmarksZoneIndex(props.zoneIndex);

    return () => {
      // Reset bookmarks zone index on unmount.
      setBookmarksZoneIndex(-1);
    };
  }, [props.zoneIndex, setBookmarksZoneIndex]);

  return <TeaserList {...props} teasers={bookmarks?.slice(0, MAX_BOOKMARKS) || []} />;
}

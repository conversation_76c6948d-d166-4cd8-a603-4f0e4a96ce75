import { Spinner } from '@components/Spinner/Spinner';
import { getNextZonePage } from '@data/source';
import { storeZoneTeasers } from '@util/NavHistory';
import { useCallback, useState } from 'react';

import styles from './paginated-teaser-list.module.scss';
import { TeaserList, TeaserListProperties } from './TeaserList';

const TEASERS_BEFORE_SPINNER = 3;

export function PaginatedTeaserList(props: TeaserListProperties) {
  const [allTeasers, setAllTeasers] = useState(props.teasers); // initially loaded + paginated
  const { pageId, title } = props;
  const { pages, teaser_count: teasersPerPage, id: zoneId, showItemTitle, template } = props.zone;
  const [pagesLoaded, setPagesLoaded] = useState(teasersPerPage ? Math.ceil(props.teasers.length / teasersPerPage) : 1);
  const [loading, setLoading] = useState(false);
  const [showLoading, setShowLoading] = useState(false);

  const onTeaserFocused = useCallback(
    (teaserIndex: number) => {
      async function fetchNextPage(page: number) {
        // prepare query string for the pagination request
        const params = new URLSearchParams();
        params.set('page', page.toString());
        // request next page in a zone
        setLoading(true);
        const pageResponse = await getNextZonePage(zoneId, params.toString());
        setLoading(false);
        if (pageResponse?.data) {
          // pass additional data to loaded teasers
          const loadedTeasersWithZoneProps = pageResponse.data.map((teaser) => ({
            ...teaser,
            showItemTitle: showItemTitle,
            template: template,
          }));
          // update state
          const oldAndNewTeasersCombined = [...allTeasers, ...loadedTeasersWithZoneProps];
          setAllTeasers(oldAndNewTeasersCombined);
          // update nav history
          storeZoneTeasers(pageId, zoneId, oldAndNewTeasersCombined);
        }
      }

      const currentPage = Math.floor(teaserIndex / teasersPerPage) + 1;
      const itIsLastLoadedPage = currentPage === pagesLoaded; // are we on the last loaded page?
      const thereAreMorePagesToLoad = currentPage < pages;
      const pageWasScrolled = teaserIndex % teasersPerPage > 0; // A zone page is considered scrolled if we moved away from the first teaser of that page

      // 1. Are there any more pages to load?
      // 2. Are we on the last loaded page?
      // 3. Have we scrolled meaning we want to see some more teasers?
      const shouldLoadNextPage = thereAreMorePagesToLoad && itIsLastLoadedPage && pageWasScrolled;

      // Load the next page if the answer to the 3 questions is YES!
      if (shouldLoadNextPage) {
        const nextPage = currentPage + 1;
        fetchNextPage(nextPage);
        setPagesLoaded(nextPage);
      }

      // Show the loading spinner when users get close enough to the last teaser
      setShowLoading(loading && teaserIndex >= allTeasers.length - TEASERS_BEFORE_SPINNER);
    },
    [allTeasers, loading, pageId, pages, pagesLoaded, showItemTitle, teasersPerPage, template, zoneId],
  );

  return (
    <div className={styles['paginated-teaser-list']}>
      {showLoading && title && (
        <div className={styles.spinner}>
          <Spinner small />
        </div>
      )}
      <TeaserList {...props} onTeaserFocused={onTeaserFocused} teasers={allTeasers} />
    </div>
  );
}

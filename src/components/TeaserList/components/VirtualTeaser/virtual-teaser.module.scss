@use '~styles/globals' as *;

.virtual-teaser {
  margin-right: $rail-item-gap;

  &.horizontal-landscapeBig,
  &.horizontal-landscapeBigWithSubtitle {
    width: $teaser-horizontal-landscape-big-width;

    @media (min-width: 1920px) {
      width: $teaser-horizontal-landscape-big-width-fhd;
    }
  }

  &.horizontal-landscape,
  &.vertical-landscape {
    width: $teaser-horizontal-landscape-width;

    &.show-item-title {
      height: $teaser-horizontal-landscape-fixed-height;
    }

    @media (min-width: 1920px) {
      width: $teaser-horizontal-landscape-width-fhd;

      &.show-item-title {
        height: $teaser-horizontal-landscape-fixed-height-fhd;
      }
    }
  }

  &.horizontal-portrait {
    width: $teaser-horizontal-portrait-width;

    @media (min-width: 1920px) {
      width: $teaser-horizontal-portrait-width-fhd;
    }
  }

  &.horizontal-square {
    width: $teaser-horizontal-square-width;

    @media (min-width: 1920px) {
      width: $teaser-horizontal-square-width-fhd;
    }
  }
}

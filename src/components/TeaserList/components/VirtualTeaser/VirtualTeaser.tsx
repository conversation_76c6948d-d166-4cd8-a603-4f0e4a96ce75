import { ZoneTemplate } from '@apptypes/PageResponseBody';
import classNames from 'classnames';

import styles from './virtual-teaser.module.scss';

interface IVirtualTeaserProps {
  template: ZoneTemplate;
  showItemTitle?: boolean;
}

/**
 * An empty div that has the size of a normal teaser for a given template type
 */
export const VirtualTeaser = ({ template, showItemTitle }: IVirtualTeaserProps) => (
  <div
    className={classNames(styles['virtual-teaser'], {
      [styles[template]]: true,
      [styles['show-item-title']]: showItemTitle,
    })}
  ></div>
);

import { ZoneLink } from '@apptypes/PageResponseBody';
import { useFocusable, UseFocusableConfig } from '@noriginmedia/norigin-spatial-navigation';
import { BookmarksContext } from '@providers/BookmarksContext';
import { PageContext } from '@providers/PageContext';
import { SearchContext } from '@providers/SearchContext';
import { Tracking } from '@tracking/Tracking';
import { getDataKind } from '@util/dataAttributes';
import {
  getHorizontalNavIndex,
  getVerticalNavIndex,
  recoverMyVideosHorizontalPosition,
  setNavHistoryHorizontalIndex,
  setNavHistoryVerticalIndex,
} from '@util/NavHistory';
import { isInRange } from '@util/number';
import { zoneHasPagination } from '@util/zones';
import classNames from 'classnames';
import { useCallback, useContext, useEffect, useMemo, useState } from 'react';

import { THEMES } from '../../constants';
import { teaserFocusKey, tvGuideTeaserFocusGuard } from '../../focus';
import { ITeaserProperties, ITeaserResponse, UserContentType, Zone } from '../../types';
import { Teaser, useTeaserAction } from '../Teaser';
import { TitledList } from '../TitledList/TitledList';
import { VirtualTeaser } from './components/VirtualTeaser/VirtualTeaser';
import styles from './teaser-list.module.scss';

/**
 * A very simple logic to limit the number of visible teasers per swimlane
 * If it's not fast enough we can always reduce that number for certain teaser types
 */
const MAX_VISIBLE_TEASERS = 11;

const showVirtualTeaser = (
  horizontal: boolean,
  index: number,
  focusedTeaserIndex: number,
  authenticatedContent?: UserContentType,
): boolean => {
  if (!horizontal || authenticatedContent) {
    // virtual teasers are not available on:
    // - vertical lists
    // - user swimlanes
    return false;
  }

  // checks if a given teaser is out of range
  const min = focusedTeaserIndex - MAX_VISIBLE_TEASERS / 2;
  const max = min + MAX_VISIBLE_TEASERS;
  return !isInRange(index, min, max);
};

export interface TeaserListProperties
  extends UseFocusableConfig,
    Pick<Zone, 'title' | 'template' | 'authenticatedContent'> {
  teasers: ITeaserResponse[];
  focusOnMount?: boolean;
  className?: string;
  zoneIndex: number;
  lastZone: boolean;
  horizontal?: boolean;
  scrollStartIndex?: number;
  searchMenuPresent?: boolean;
  theme?: string;
  getCurrentIndex?: (index: number) => void;
  listFocusKey?: string;
  link?: ZoneLink;
  scrollToEnd?: boolean;
  moveChildOffsetToCenter?: boolean;
  bottomPeekSize?: number;
  onTeaserFocused?: (teaserIndex: number) => void;
  zone: Zone;
  zoneId: string;
  pageId: string;
}

function TeaserList({
  teasers,
  className,
  template,
  title,
  focusOnMount,
  onFocus,
  zoneIndex,
  lastZone,
  horizontal = true,
  scrollStartIndex,
  authenticatedContent,
  pageId,
  zoneId,
  scrollToEnd,
  searchMenuPresent,
  theme,
  getCurrentIndex,
  listFocusKey,
  link,
  bottomPeekSize,
  moveChildOffsetToCenter = false,
  onTeaserFocused,
  zone,
}: TeaserListProperties) {
  const [focusedTeaserIndex, setFocusedTeaserIndex] = useState(
    horizontal ? getHorizontalNavIndex(pageId, zoneId) : getVerticalNavIndex(pageId),
  );
  const [focused, setFocused] = useState(false);
  const { setFocus } = useFocusable();
  const { handleTeaserPress, blockExtraKeys } = useTeaserAction();
  const { teaserFocusGuard } = useContext(PageContext);
  const { searchResults } = useContext(SearchContext);
  const paginated = zoneHasPagination(zone);
  const { bookmarks } = useContext(BookmarksContext);

  getCurrentIndex && getCurrentIndex(focusedTeaserIndex);
  const addTeaser = useCallback(
    (teaser: ITeaserResponse, index: number, link?: ZoneLink) => {
      const key = `${zoneIndex}-${index}`;
      if (paginated && showVirtualTeaser(horizontal, index, focusedTeaserIndex, authenticatedContent)) {
        return <VirtualTeaser key={key} template={template} showItemTitle={teaser.showItemTitle}></VirtualTeaser>;
      }

      const fKey = teaserFocusKey(zoneIndex, index);
      return (
        <Teaser
          key={key}
          focusKey={fKey}
          {...teaser}
          zoneIndex={zoneIndex}
          lastZone={lastZone}
          position={index}
          setFocus={setFocus}
          trackChildren={true}
          onFocus={() => {
            setFocusedTeaserIndex(index);
            setFocused(true);
            onTeaserFocused?.(index);
          }}
          onEnterPress={() => {
            handleTeaserPress(teaser, false, link);
          }}
          onMouseClick={(event) => {
            event.preventDefault();
            handleTeaserPress(teaser, false, link);
          }}
          template={template}
          authenticatedContent={authenticatedContent}
          onArrowPress={(direction) =>
            template === 'tableview-guide'
              ? tvGuideTeaserFocusGuard(direction, zoneIndex, index, setFocus, searchResults)
              : teaserFocusGuard(direction, zoneIndex, index, setFocus, searchResults)
          }
          shouldBlockExtraKeys={blockExtraKeys}
          theme={theme}
          link={link && link?.deeplink ? link : undefined}
          fixedHeight={paginated}
          dataKind={getDataKind(teaser)}
        />
      );
    },
    [
      zoneIndex,
      lastZone,
      setFocus,
      handleTeaserPress,
      template,
      authenticatedContent,
      searchResults,
      blockExtraKeys,
      theme,
      setFocusedTeaserIndex,
      setFocused,
      teaserFocusGuard,
      focusedTeaserIndex,
      onTeaserFocused,
      horizontal,
      paginated,
    ],
  );

  const teaserElements = useMemo(() => {
    const mappedTeasers = teasers?.map((teaser: ITeaserResponse, index) => addTeaser(teaser, index));

    // Add a link if it exists and if the list is not empty
    if (link && link?.deeplink && teasers?.length) {
      mappedTeasers.push(addTeaser(teasers[teasers?.length - 1], teasers.length, link));
    }

    // Add an empty teaser
    if (theme === THEMES.SHOWEMPTYZONE && !teasers.length) {
      mappedTeasers.push(
        addTeaser(
          {
            authenticatedContent: authenticatedContent,
            image: 'http://foo.bar', // any url
            template: template,
            emptyState: true,
          } as ITeaserProperties,
          0,
        ),
      );
    }

    return mappedTeasers;
  }, [teasers, link, theme, addTeaser, authenticatedContent, template]);

  useEffect(() => {
    // Set a max number on horizontal index retrieved from nav history.
    // It's needed in case the zone has shrunk while users were on a different page.
    const horizontalIndex = Math.min(getHorizontalNavIndex(pageId, zoneId), teaserElements.length - 1);
    setFocusedTeaserIndex(horizontalIndex);
    if (focusOnMount) {
      setFocus(teaserFocusKey(zoneIndex, horizontalIndex));
    }
  }, [focusOnMount, pageId, setFocus, teaserElements.length, zoneId, zoneIndex]);

  useEffect(() => {
    const recoveredPosition = recoverMyVideosHorizontalPosition(bookmarks?.length || 0);
    if (recoveredPosition && recoveredPosition > 0 && focusedTeaserIndex === 0) {
      setFocusedTeaserIndex(recoveredPosition);
      return;
    }

    horizontal
      ? setNavHistoryHorizontalIndex(pageId, zoneId, focusedTeaserIndex)
      : setNavHistoryVerticalIndex(focusedTeaserIndex, pageId);
  }, [focusedTeaserIndex, pageId, zoneId, horizontal, bookmarks]);

  useEffect(() => {
    Tracking.updateZonesInUsageTeasers(teasers, zoneId);
  }, [teasers, zoneId]);

  return (
    <div className={classNames(styles.wrapper, theme && styles[theme])}>
      <TitledList
        isFocused={focused}
        children={teaserElements}
        horizontal={Boolean(horizontal)}
        currentItem={focusedTeaserIndex}
        className={className}
        focusOnMount={focusOnMount}
        onFocus={onFocus}
        onBlur={() => setFocused(false)}
        title={title}
        scrollStartIndex={scrollStartIndex}
        scrollToEnd={scrollToEnd}
        totalItems={teaserElements.length}
        searchMenuPresent={searchMenuPresent}
        theme={theme}
        listFocusKey={listFocusKey}
        moveChildOffsetToCenter={moveChildOffsetToCenter}
        bottomPeekSize={bottomPeekSize}
      />
    </div>
  );
}

export { TeaserList };

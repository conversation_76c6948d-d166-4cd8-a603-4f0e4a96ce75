import { Button } from '@components/Button/Button';
import { getExitModal } from '@components/Modal/modal';
import { Spinner } from '@components/Spinner/Spinner';
import { FocusContext, useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import { useModalContext } from '@providers/ModalContext';
import { t } from 'i18next';
import React from 'react';

import { FOCUS_KEY_OFFLINE_MODAL, withFocusableContainer } from '../../../focus';
import styles from './offline-alert-view.module.scss';

interface IOfflineAlertViewProperties {
  checkingStatus?: boolean;
  intervalCount?: number;
  intervalTime?: number | null;
  retry?: () => void;
}

export const OfflineAlertView = withFocusableContainer(
  React.forwardRef<HTMLDivElement, IOfflineAlertViewProperties>(
    ({ checkingStatus, intervalCount, intervalTime, retry }, reference) => {
      const { focusKey, getCurrentFocusKey, setFocus } = useFocusable({ isFocusBoundary: true });
      const { showModal, hideModal } = useModalContext();

      return (
        <FocusContext.Provider value={focusKey}>
          <div className={styles.wrapper} ref={reference}>
            <div className={styles.content}>
              <div className={styles.title}>
                <h2>{t('error__offline-title')}</h2>
                {checkingStatus && (
                  <div className={styles.spinner}>
                    <Spinner small />
                  </div>
                )}
              </div>

              <p>{t('error__offline-text')}</p>
              <div className={styles.buttons}>
                <Button
                  className={styles.button}
                  label={`${t('retry')}${intervalTime !== null ? ` (${intervalCount})` : ''}`}
                  onEnterPress={() => {
                    retry?.();
                  }}
                  focusKey={FOCUS_KEY_OFFLINE_MODAL}
                />

                <Button
                  label={t('quit')}
                  onEnterPress={() => {
                    showModal({
                      content: getExitModal(hideModal, setFocus, getCurrentFocusKey()),
                    });
                  }}
                />
              </div>
            </div>
          </div>
        </FocusContext.Provider>
      );
    },
  ),
);

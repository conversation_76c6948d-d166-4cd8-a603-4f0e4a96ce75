@use '~styles/globals' as *;

.wrapper {
  display: table-cell;
  z-index: map-get($zindex, alert-overlay);
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba($black, 0.7);
  vertical-align: middle;
}

.content {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  margin: auto;
  padding: px-to-rem(64);
  max-width: 50%;
  height: px-to-rem(400);
  background-color: $primary-background;

  h2 {
    font-size: px-to-rem(55);
  }

  p {
    margin-top: px-to-rem(16);
    font-size: px-to-rem(40);
  }
}

.buttons {
  margin-top: px-to-rem(52);
  text-align: center;
}

.button {
  margin-right: px-to-rem(32);
}

.title {
  display: inline-block;
  vertical-align: middle;
}

.title h2 {
  display: inline;
  margin-right: px-to-rem(10);
}

.spinner {
  display: inline-block;
  height: $spinner-size-small;
}

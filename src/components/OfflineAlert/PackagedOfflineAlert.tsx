import { useBackJourney } from '@features/backjourney/useBackJourney';
import { useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import { useModalContext } from '@providers/ModalContext';
import { isVideoRouteByLocation } from '@routes/route';
import { useEffect, useRef, useState } from 'react';
import { useRemoteController } from 'react-remote-controller';
import { useLocation } from 'react-router-dom';
import { config, getKeyMap } from 'target';

import { EVENTS } from '../../constants';
import { FOCUS_KEY_OFFLINE_MODAL, FOCUS_KEY_PRIMARY } from '../../focus';
import EventBus, { EventData } from '../../util/EventBus';
import { OfflineAlertView } from './OfflineAlertView/OfflineAlertView';

export const PackagedOfflineAlert = () => {
  const { modalOpen, hideModal } = useModalContext();
  const { setFocus } = useFocusable();
  const { handleBack } = useBackJourney();
  const location = useLocation();
  const [isOffline, setIsOffline] = useState(false);
  const { pause: pauseRemoteController, resume: resumeRemoteController } = useRemoteController<
    ReturnType<typeof getKeyMap>
  >({ listenTo: {} });

  const backTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  const offlineAlertShown = isOffline && !modalOpen;

  useEffect(() => {
    function handleNetworkStatus<T>(eventData: EventData<T>) {
      if (eventData[EVENTS.OFFLINE]) {
        setIsOffline(true);
        pauseRemoteController();

        if (backTimeoutRef.current) {
          clearTimeout(backTimeoutRef.current);
        }
      } else {
        resumeRemoteController();
        hideModal(); // hide any remaining modals e.g an exit modal
        setFocus(FOCUS_KEY_PRIMARY);
        setIsOffline(false);
        if (config.offline?.goBackFromPlayback && isVideoRouteByLocation(location)) {
          // Delay going back to give the network some time to fully recover
          backTimeoutRef.current = setTimeout(() => {
            handleBack();
            backTimeoutRef.current = null;
          }, 2000);
        }
      }
    }

    EventBus.on(EVENTS.OFFLINE, handleNetworkStatus);

    return () => {
      EventBus.off(EVENTS.OFFLINE, handleNetworkStatus);
      if (backTimeoutRef.current) {
        clearTimeout(backTimeoutRef.current);
      }
    };
  }, [handleBack, hideModal, location, pauseRemoteController, resumeRemoteController, setFocus]);

  useEffect(() => {
    if (offlineAlertShown) {
      setFocus(FOCUS_KEY_OFFLINE_MODAL);
    }
  }, [offlineAlertShown, setFocus]);

  return offlineAlertShown ? <OfflineAlertView intervalTime={null} /> : null;
};

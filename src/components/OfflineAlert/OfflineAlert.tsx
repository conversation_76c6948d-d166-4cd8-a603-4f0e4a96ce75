import { EVENTS } from '@constants';
import { useBackJourney } from '@features/backjourney/useBackJourney';
import { useInterval } from '@hooks/useInterval';
import useTimeout from '@hooks/useTimeout';
import { useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import { useModalContext } from '@providers/ModalContext';
import { isVideoRouteByLocation } from '@routes/route';
import EventBus, { EventData } from '@util/EventBus';
import { isOnlineBackupCheck } from '@util/offlineEvent';
import { useCallback, useEffect, useState } from 'react';
import { useRemoteController } from 'react-remote-controller';
import { useLocation } from 'react-router-dom';
import { getKeyMap } from 'target';
import { config } from 'target';

import { FOCUS_KEY_OFFLINE_MODAL, FOCUS_KEY_PRIMARY } from '../../focus';
import { OfflineAlertView } from './OfflineAlertView/OfflineAlertView';

const INTERVAL_SECONDS_LIMIT = 15;

export const OfflineAlert = ({ triggeredByError = false }) => {
  const [isOffline, setIsOffline] = useState(false);
  const [intervalTime, setIntervalTime] = useState<number | null>(null);
  const [intervalCount, setIntervalCount] = useState(INTERVAL_SECONDS_LIMIT);
  const [checkingStatus, setCheckingStatus] = useState(false);
  const [allowShow, setAllowShow] = useState(true);
  const { pause: pauseRemoteController, resume: resumeRemoteController } = useRemoteController<
    ReturnType<typeof getKeyMap>
  >({ listenTo: {} });
  const { modalOpen, hideModal } = useModalContext();
  const { pause, resume, setFocus } = useFocusable();
  const [timeoutDuration, setTimeoutDuration] = useState<number | null>(null);
  const { handleBack } = useBackJourney();
  const location = useLocation();
  const [shouldHandleBack, setShouldHandleBack] = useState(false);

  useInterval(() => {
    intervalCount <= 0 && checkIfBackOnline();

    setIntervalCount((prevCount) => prevCount - 1);
  }, intervalTime);

  useEffect(() => {
    if (!modalOpen && isOffline) {
      setIntervalTime(1000);
      setIntervalCount(INTERVAL_SECONDS_LIMIT);
    } else {
      setIntervalTime(null);
    }
  }, [modalOpen, isOffline]);

  useEffect(() => {
    setAllowShow(!modalOpen);
    if (!modalOpen && isOffline) {
      setFocus(FOCUS_KEY_OFFLINE_MODAL);
    }
  }, [modalOpen, isOffline, setFocus]);

  const handleOffline = useCallback(() => {
    hideModal(); // close any existing open modals before displaying offline modal
    pauseRemoteController();
    setIsOffline(true);
    setFocus(FOCUS_KEY_OFFLINE_MODAL);
  }, [pauseRemoteController, setFocus, hideModal]);

  const handleOnline = useCallback(() => {
    hideModal();
    if (config.offline?.goBackFromPlayback && isVideoRouteByLocation(location)) {
      setShouldHandleBack(true); // defer handleBack until modal state is updated
    }
    resume();
    resumeRemoteController();
    setIsOffline(false);
    setFocus(FOCUS_KEY_PRIMARY);
    if (triggeredByError) handleBack();
  }, [resume, resumeRemoteController, setFocus, handleBack, triggeredByError, location, hideModal]);

  const verifyOnlineStatus = useCallback(
    async (expectedStatus: boolean) => {
      const onlineStatus = await isOnlineBackupCheck();
      if (onlineStatus === expectedStatus) {
        expectedStatus ? handleOnline() : handleOffline();
      } else {
        expectedStatus ? handleOffline() : handleOnline();
      }
    },
    [handleOnline, handleOffline],
  );

  useEffect(() => {
    function handleOfflineStatus<T>(eventData: EventData<T>) {
      if (eventData[EVENTS.OFFLINE]) {
        verifyOnlineStatus(false);
      } else {
        verifyOnlineStatus(true);
      }
    }

    EventBus.on(EVENTS.OFFLINE, handleOfflineStatus);

    return () => {
      EventBus.off(EVENTS.OFFLINE, handleOfflineStatus);
      setTimeoutDuration(null);
    };
  }, [pause, pauseRemoteController, resume, resumeRemoteController, verifyOnlineStatus, setIsOffline, setAllowShow]);

  const checkIfBackOnline = useCallback(async () => {
    setIntervalTime(null);
    setCheckingStatus(true);
    pause();

    setTimeoutDuration(3000);
  }, [pause]);

  useTimeout(async () => {
    const onlineStatus = await isOnlineBackupCheck();

    if (onlineStatus) {
      handleOnline();
    } else {
      setFocus(FOCUS_KEY_OFFLINE_MODAL);
      setIntervalTime(1000);
      setIntervalCount(INTERVAL_SECONDS_LIMIT);
    }
    setCheckingStatus(false);
    resume();
    setTimeoutDuration(null);
  }, timeoutDuration);

  // we need to handle the back journey after modalOpen state has updated to
  // ensure the back journey works as expected
  useEffect(() => {
    if (!modalOpen && shouldHandleBack) {
      setShouldHandleBack(false);
      handleBack();
    }
  }, [modalOpen, shouldHandleBack, handleBack]);

  useEffect(() => {
    triggeredByError && handleOffline();
  }, [triggeredByError, handleOffline]);

  return isOffline && allowShow ? (
    <OfflineAlertView
      retry={checkIfBackOnline}
      checkingStatus={checkingStatus}
      intervalCount={intervalCount}
      intervalTime={intervalTime}
    />
  ) : null;
};

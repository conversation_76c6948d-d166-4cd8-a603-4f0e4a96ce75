import classNames from 'classnames';
import React, { ReactNode, useState } from 'react';

import { IFocusState, withFocusableContainer } from '../../focus';
import { chunk } from '../../util/array';
import styles from './grid-layout.module.scss';

export interface IGridLayoutProperties extends IFocusState {
  children: React.ReactNode;
  rowLength: number;
  currentItem?: number;
  wrapperClassName?: string;
  className?: string;
  rowClassName?: string;
  scrollStartIndex?: number;
  behavior?: 'scroll' | 'static';
  testid?: string;
}

export const GridLayout = withFocusableContainer(
  React.forwardRef<HTMLDivElement, IGridLayoutProperties>(
    (
      {
        children,
        rowLength,
        currentItem = 0,
        wrapperClassName,
        className,
        rowClassName,
        scrollStartIndex = 0,
        behavior = 'scroll',
        testid,
      },
      reference,
    ) => {
      const [offset, setOffset] = useState(0);
      const currentRow = Math.floor(currentItem / rowLength);
      const callbackReference = (node: HTMLDivElement) => {
        if (behavior === 'static' && node) {
          let dynamicOffset;
          if (node.children.length !== 2 || currentRow === 0) {
            dynamicOffset = currentRow > 0 ? -1 * (node.children[1] as HTMLElement)?.offsetTop ?? 0 : 0;
          } else {
            const nodeHeight = (node as HTMLElement).offsetHeight;
            const lastRowHeight = (node.children[node.children.length - 1] as HTMLElement)?.clientHeight;
            const lastRowTopOffset = (node.children[node.children.length - 1] as HTMLElement)?.offsetTop;
            let parentOffsetTop = 0;
            const parentNode = (node as HTMLElement).parentNode;
            if (parentNode && (parentNode as HTMLElement).parentNode) {
              const superiorParent = (parentNode as HTMLElement).parentNode;
              parentOffsetTop = (superiorParent as HTMLElement)?.offsetTop;
            }

            dynamicOffset = Math.ceil(lastRowTopOffset + lastRowHeight - nodeHeight - parentOffsetTop);
          }

          setOffset(dynamicOffset || 0);
          return;
        }

        if (node && currentRow > scrollStartIndex) {
          if (!node.children[currentRow - scrollStartIndex]) {
            setOffset(0);
            return;
          }

          const dynamicOffset = -1 * (node.children[currentRow - scrollStartIndex] as HTMLElement)?.['offsetTop'] ?? 0;
          setOffset(dynamicOffset || 0);
          return;
        }

        setOffset(0);
      };

      return (
        <div ref={reference} className={wrapperClassName}>
          <div
            data-testid={testid}
            className={classNames(styles['grid-layout'], className)}
            ref={callbackReference}
            style={{ top: offset }}
          >
            {chunk(React.Children.toArray(children), rowLength).map((rowChildren: ReactNode, index: number) => (
              // eslint-disable-next-line react/no-array-index-key
              <div className={classNames(styles.row, rowClassName)} key={index}>
                {rowChildren}
              </div>
            ))}
          </div>
        </div>
      );
    },
  ),
);

import EventBus, { EventData } from '@util/EventBus';
import classNames from 'classnames';
import { useEffect, useState } from 'react';

import { EVENTS } from '../../constants';
import styles from './full-screen-spinner.module.scss';
import { Spinner } from './Spinner';

export function FullScreenSpinner() {
  const [visible, setVisible] = useState(false);

  useEffect(function showFullScreenSpinnerListener() {
    function handleShowSpinner<T>(eventData: EventData<T>) {
      setVisible(!!eventData[EVENTS.SHOW_FULL_SCREEN_SPINNER]);
    }

    EventBus.on(EVENTS.SHOW_FULL_SCREEN_SPINNER, handleShowSpinner);

    return () => {
      EventBus.off(EVENTS.SHOW_FULL_SCREEN_SPINNER, handleShowSpinner);
    };
  }, []);

  return (
    <div className={classNames(styles.wrapper, { [styles.visible]: visible })}>
      <Spinner />
    </div>
  );
}

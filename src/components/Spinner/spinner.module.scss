@use '~styles/globals' as *;

.spinner {
  z-index: map-get($zindex, spinner);
  position: relative;
  width: $spinner-size;
  height: $spinner-size;
  background-image: url('@assets/img/spinner.gif');
  background-size: $spinner-size;
  background-repeat: no-repeat;

  &.small {
    width: $spinner-size-small;
    height: $spinner-size-small;
    background-size: $spinner-size-small;
  }
}

@supports (transform: rotate(0deg)) and (animation: 1s) {
  .spinner {
    border-width: 3px;
    border-style: solid;
    border-color: rgb(255, 255, 255) rgb(255, 255, 255) rgb(255, 255, 255) transparent;
    border-image: none;
    border-radius: 50%;
    background: transparent;
    animation: 700ms cubic-bezier(0.07, 1.02, 0.99, 0.31) 0ms infinite normal none running animation-spin;
  }

  @keyframes animation-spin {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }
}

import classNames from 'classnames';
import { CSSProperties, FunctionComponent, ReactNode } from 'react';

import styles from './centered-layout.module.scss';

interface ICenteredLayoutProperties {
  children: ReactNode;
  childIsText?: boolean;
  className?: string;
  style?: CSSProperties;
  testId?: string;
}

export const CenteredLayout: FunctionComponent<ICenteredLayoutProperties> = ({
  children,
  childIsText = false,
  className,
  style,
  testId,
}: ICenteredLayoutProperties) => {
  return (
    <div
      data-testid={testId}
      className={classNames(styles['centered-layout'], childIsText && styles['child-is-text'], className)}
      style={style}
    >
      {children}
    </div>
  );
};

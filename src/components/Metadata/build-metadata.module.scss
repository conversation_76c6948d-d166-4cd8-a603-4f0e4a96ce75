@use '~styles/globals' as *;

.table {
  border-collapse: collapse;
  font-family: $font-family-bold;
  font-size: px-to-rem(24);

  th {
    padding-right: px-to-rem(20);
    text-align: left;
  }
}

$menu-z-index: map-get($zindex, menu);
$zi: calc($menu-z-index + 1);

.wrapper {
  z-index: $zi;
  position: absolute;
  padding: px-to-rem(60);
  top: 0;
  height: 100%;
  width: 100%;
  background-color: $primary-background;
}

import { currentUserAgent } from '@util/platform';
import { getTargetAndSubsetName } from '@util/target';

import styles from './build-metadata.module.scss';

export function BuildMetadata() {
  return (
    <div className={styles.wrapper}>
      <table className={styles.table}>
        <tbody>
          <tr>
            <th>target</th>
            <td>{getTargetAndSubsetName()}</td>
          </tr>
          <tr>
            <th>version</th>
            <td>{process.env.VERSION}</td>
          </tr>
          <tr>
            <th>ua</th>
            <td>{currentUserAgent}</td>
          </tr>
          <tr>
            <th>branch</th>
            <td>{process.env.BRANCH_NAME}</td>
          </tr>
          <tr>
            <th>hash</th>
            <td>{process.env.COMMIT_HASH}</td>
          </tr>
        </tbody>
      </table>
    </div>
  );
}

import styles from './numbers-field.module.scss';

interface INumbersField {
  code: string;
  size: number;
}

const nonBreakingSpace = '\u00A0';

const NumbersField = ({ code, size }: INumbersField) => {
  const fields = [...new Array(size)];

  return (
    <p>
      {fields.map((_, index) => (
        // eslint-disable-next-line react/no-array-index-key
        <span className={styles['code-number']} key={index}>
          {code.at(index) || nonBreakingSpace}
        </span>
      ))}
    </p>
  );
};

export { NumbersField };

import { EVENTS } from '@constants';
import EventBus, { EventData } from '@util/EventBus';
import { ReactNode, useEffect, useState } from 'react';
import { useRemoteController } from 'react-remote-controller';
import { getKeyMap } from 'target';

import { ToastNotification } from './ToastNotification';
import { ToastNotificationLayout } from './ToastNotificationLayout/ToastNotificationLayout';

export const Toaster = () => {
  const [toast, setToast] = useState<ReactNode>();

  useEffect(() => {
    function onToastAddEvent<T>(eventData: EventData<T>) {
      const data: unknown = eventData[EVENTS.TOAST_ADD];
      const message = data as string;
      setToast(
        <ToastNotification
          message={message}
          icon="check-green"
          key={`Toast ${Math.round(Math.random() * 99999999)}`} // Force a re-render to restart the animation
        />,
      );
    }

    function onToastRemoveEvent() {
      setToast(undefined);
    }

    EventBus.on(EVENTS.TOAST_ADD, onToastAddEvent);
    EventBus.on(EVENTS.TOAST_REMOVE, onToastRemoveEvent);
    return () => {
      EventBus.off(EVENTS.TOAST_ADD, onToastAddEvent);
      EventBus.off(EVENTS.TOAST_REMOVE, onToastRemoveEvent);
    };
  }, []);

  useRemoteController<ReturnType<typeof getKeyMap>>({
    listenTo: {
      red: () => {
        setToast(undefined);
      },
    },
  });

  return <ToastNotificationLayout>{toast}</ToastNotificationLayout>;
};

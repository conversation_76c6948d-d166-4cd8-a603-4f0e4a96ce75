@use '~styles/globals' as *;

$backgroundColor: #f5f5f5;

.toast-notification {
  height: px-to-rem(70);
  background-color: $backgroundColor;
  color: $primary-background;
  font-size: px-to-rem(32);
}

.icon {
  margin-left: px-to-rem(22);
}

.message {
  display: inline-block;
  margin: auto px-to-rem(13);
  line-height: px-to-rem(66);
}

.color-button {
  margin-right: px-to-rem(40);
}

.progress-bar {
  position: absolute;
  bottom: 0;
  height: px-to-rem(4);
  background-color: $arte-highlight;
  animation: progressAnimation 5s linear;

  @keyframes progressAnimation {
    0% {
      width: 0%;
    }

    100% {
      width: 100%;
    }
  }
}

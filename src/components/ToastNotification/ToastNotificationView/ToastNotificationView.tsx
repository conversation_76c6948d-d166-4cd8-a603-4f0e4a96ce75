import { ColorButton } from '@components/ColorButton/ColorButton';
import { Icon, IconType } from '@components/Icon/Icon';

import styles from './toast-notification-view.module.scss';

export interface IToastNotificationViewProperties {
  message: string;
  icon: IconType;
}

export const ToastNotificationView = ({ message, icon }: IToastNotificationViewProperties) => (
  <div className={styles['toast-notification']}>
    <Icon type={icon} size="XS" className={styles.icon} />
    <span className={styles.message}>{message}</span>
    <ColorButton color="red" className={styles['color-button']} />
    <div className={styles['progress-bar']} />
  </div>
);

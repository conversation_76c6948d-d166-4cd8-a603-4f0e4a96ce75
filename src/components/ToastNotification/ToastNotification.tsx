import { EVENTS } from '@constants';
import useTimeout from '@hooks/useTimeout';
import EventBus from '@util/EventBus';

import { IToastNotificationViewProperties, ToastNotificationView } from './ToastNotificationView/ToastNotificationView';

type IToastNotificationProperties = IToastNotificationViewProperties;
const TOAST_DURATION = 5000; // 5s

export const ToastNotification = (properties: IToastNotificationProperties) => {
  useTimeout(() => {
    EventBus.emit(EVENTS.TOAST_REMOVE, {});
  }, TOAST_DURATION);

  return <ToastNotificationView {...properties} />;
};

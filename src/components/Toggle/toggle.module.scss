@use '~styles/globals' as *;

$track-width: px-to-rem(52);
$track-height: px-to-rem(21);
$thumb-metric: px-to-rem(32);

.wrapper {
  position: relative;
  display: inline-block;
  width: $track-width;
  height: $track-height;
  border-radius: calc($track-height / 2);
  background-color: $grey;

  > div {
    position: absolute;
    top: 0;
    bottom: 0;
    margin: auto 0;
    width: $thumb-metric;
    height: $thumb-metric;
    border-radius: $thumb-metric;
    background-image: url('@assets/img/icon/Cross.png');
    background-size: px-to-rem(12) px-to-rem(12);
    background-repeat: no-repeat;
    background-position: center center;
    background-color: $white;
  }

  &.on {
    background-color: #ff9870;

    > div {
      right: 0;
      background-image: url('@assets/img/icon/Check-40x40.png');
      background-color: $primary-accent;
      background-size: px-to-rem(20) px-to-rem(20);
    }
  }
}

import React, { ButtonHTMLAttributes } from 'react';

import { focusClassNames, IFocusState, withFocusable } from '../../focus';
import styles from './tick-button.module.scss';

interface ITickButton extends ButtonHTMLAttributes<HTMLButtonElement>, IFocusState {
  label: string;
  isActive?: boolean;
  onEnterPress: () => void;
  testIdFocused?: boolean;
}

export const TickButton = withFocusable(
  React.forwardRef<HTMLButtonElement, ITickButton>(({ label, testIdFocused, ...properties }, reference) => {
    return (
      <button
        ref={reference}
        className={focusClassNames(styles, properties, styles.button)}
        data-testid-focused={testIdFocused ? 'true' : undefined}
        onClick={properties.onEnterPress}
      >
        <span>{label}</span>
      </button>
    );
  }),
);

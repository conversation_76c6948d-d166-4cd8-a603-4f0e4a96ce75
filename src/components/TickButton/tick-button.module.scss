@use '~styles/globals' as *;

.button {
  margin-right: px-to-rem(16);
  padding: px-to-rem(16) px-to-rem(40);
  border: none;
  border-radius: px-to-rem(7);
  font-size: px-to-rem(30);
  font-family: $font-family-bold;
  color: $white;
  background-color: $dark-grey;

  &.is-focused {
    color: $black;
    background-color: $white;
  }

  &.is-active {
    padding-left: px-to-rem(64);
    background-image: url('~@assets/img/icon/Check-40x40.png');
    background-repeat: no-repeat;
    background-position: px-to-rem(24) px-to-rem(16);
    background-size: px-to-rem(40) px-to-rem(40);

    &.is-focused {
      background-image: url('~@assets/img/icon/Check-focused-40x40.png');
    }

    span {
      margin-left: px-to-rem(18);
    }
  }
}

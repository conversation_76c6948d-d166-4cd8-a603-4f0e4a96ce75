import { VerticalList } from '@components/List/VerticalList';

import { settingsMenuFocusKey } from '../../focus';
import i18n from '../../i18n';
import styles from './settings-menu.module.scss';

interface ISettingsMenu {
  menuItems: JSX.Element[];
  activeItemIndex: number;
}

export function SettingsMenu({ menuItems, activeItemIndex }: ISettingsMenu) {
  return (
    <div className={styles['settings-menu-wrapper']}>
      <div className={styles['settings-menu-heading']}>{i18n.t('settings')}</div>
      <VerticalList
        className={styles['settings-menu-list']}
        scrollToEnd={false}
        preferredChildFocusKey={settingsMenuFocusKey(activeItemIndex)}
        saveLastFocusedChild={false}
      >
        {menuItems}
      </VerticalList>
    </div>
  );
}

import React from 'react';

import { focusClassNames, withFocusable } from '../../focus';
import { SettingsMenuItem } from '../../types';
import styles from './settings-menu-list-item.module.scss';

interface ISettingsMenuListItemProperties {
  menuItem: SettingsMenuItem;
  isFocused: boolean;
  isActive: boolean;
  className?: string;
  onEnterPress: () => void;
  testIdFocused?: boolean;
  testId: string;
}

export const SettingsMenuListItem = withFocusable(
  React.forwardRef<HTMLDivElement, ISettingsMenuListItemProperties>(
    (properties: ISettingsMenuListItemProperties, reference) => {
      const { menuItem } = properties;
      return (
        <div ref={reference}>
          <button
            data-testid={properties.testId}
            data-testid-focused={properties.testIdFocused ? 'true' : undefined}
            className={focusClassNames(styles, properties, styles.button)}
            onClick={() => properties.onEnterPress()}
          >
            {menuItem}
          </button>
        </div>
      );
    },
  ),
);

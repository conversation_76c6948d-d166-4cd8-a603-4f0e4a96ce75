import { FunctionComponent } from 'react';

import styles from './progress-teaser.module.scss';

interface IProgressTeaserProperties {
  viewedProgress: number;
}

export const ProgressTeaser: FunctionComponent<IProgressTeaserProperties> = ({ viewedProgress }) => {
  if (!viewedProgress || viewedProgress === 0) return null;

  return (
    <div className={styles['progress-container']}>
      <div
        className={styles['progress-bar']}
        style={{
          width: `${Math.max(0, Math.min(100, viewedProgress * 100 || 0))}%`,
        }}
      ></div>
    </div>
  );
};

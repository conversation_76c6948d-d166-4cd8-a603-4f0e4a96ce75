import classnames from 'classnames';
import React from 'react';

import { IFocusState } from '../../focus';
import { AudioVersionCode } from '../../types/ITeaserResponse';
import styles from './icon.module.scss';

export type IconType =
  | 'favourites-add'
  | 'favourites-remove'
  | 'reminder-add'
  | 'reminder-remove'
  | 'play'
  | 'check'
  | 'check-green'
  | 'close'
  | 'info'
  | 'arrow-left'
  | 'arrow-up'
  | 'arrow-down'
  | 'sustainability'
  // menu
  | 'concert'
  | 'category'
  | 'direct'
  | 'exit'
  | 'guide'
  | 'home'
  | 'myarte'
  | 'search'
  | 'settings'
  | 'myvideos'
  // search
  | 'space'
  | 'backspace'
  // audio
  | AudioVersionCode
  // player
  | 'pause'
  | 'next-content'
  | 'previous-content'
  | 'language-versions'
  | 'player-favourites-add'
  | 'player-favourites-remove'
  | 'player-info';

type IconSize = 'XL' | 'L' | 'ML' | 'M' | 'S' | 'XS';

interface IIconProperties extends IFocusState {
  type: IconType;
  size?: IconSize;
  className?: string;
  imageSrc?: string;
}

export const Icon: React.FC<IIconProperties> = ({ type, size, className, imageSrc, ...properties }) => (
  <div
    className={classnames(
      styles.icon,
      {
        [styles[type]]: type,
        [styles['is-focused']]: properties.isFocused || properties.isActive,
        ...(size && { [styles[size]]: true }),
        [styles['circle-mask']]: imageSrc,
      },
      className,
    )}
    style={imageSrc ? { backgroundImage: `url(${imageSrc})` } : {}}
  />
);

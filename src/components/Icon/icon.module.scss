@use '~styles/globals' as *;

.icon {
  display: inline-block;
  background-repeat: no-repeat;
  vertical-align: middle;
}

.circle-mask {
  border-radius: 50%;
  overflow: hidden;
}

.XS {
  @include icon-size(map-get($icon-size-map, xs-hd));

  @media (min-width: 1920px) {
    @include icon-size(map-get($icon-size-map, xs-fhd));
  }
}

.S {
  @include icon-size(map-get($icon-size-map, s-hd));

  @media (min-width: 1920px) {
    @include icon-size(map-get($icon-size-map, s-fhd));
  }
}

.M {
  @include icon-size(map-get($icon-size-map, m-hd));

  @media (min-width: 1920px) {
    @include icon-size(map-get($icon-size-map, m-fhd));
  }
}

.ML {
  @include icon-size(map-get($icon-size-map, ml-hd));

  @media (min-width: 1920px) {
    @include icon-size(map-get($icon-size-map, ml-fhd));
  }
}

.L {
  @include icon-size(map-get($icon-size-map, l-hd));

  @media (min-width: 1920px) {
    @include icon-size(map-get($icon-size-map, l-fhd));
  }
}

.XL {
  @include icon-size(map-get($icon-size-map, xl-hd));

  @media (min-width: 1920px) {
    @include icon-size(map-get($icon-size-map, xl-fhd));
  }
}

.favourites-add {
  @include icon-XS-focusable('@assets/img/icon/', 'FavouritesAdd');
}

.reminder-add {
  @include icon-XS-focusable('@assets/img/icon/', 'ReminderAdd');
}

.favourites-remove {
  @include icon-XS-focusable('@assets/img/icon/', 'FavouritesRemove');
}

.reminder-remove {
  @include icon-XS-focusable('@assets/img/icon/', 'ReminderRemove');
}

.player-favourites-add {
  &.M {
    @include icon-M('@assets/img/icon/player/', 'PlayerFavouritesAdd');
  }
}

.player-favourites-remove {
  &.M {
    @include icon-M('@assets/img/icon/player/', 'PlayerFavouritesRemove');
  }
}

.player-info {
  &.M {
    @include icon-M('@assets/img/icon/player/', 'PlayerInfo');
  }
}

.play {
  &.XS {
    @include icon-XS-focusable('@assets/img/icon/', 'Play');
  }

  &.M {
    @include icon-M('@assets/img/icon/', 'Play');
  }
}

.check {
  &.XS {
    @include icon-XS-focusable('@assets/img/icon/', 'Check');
  }

  &.S {
    @include icon-S-focusable('@assets/img/icon/', 'Check');
  }
}

.check-green {
  @include icon-XS('@assets/img/icon/', 'CheckGreen');
}

.close {
  @include icon-XS-focusable('@assets/img/icon/', 'Close');
}

.info {
  @include icon-XS-focusable('@assets/img/icon/', 'Info');
}

.arrow-left {
  @include icon-M('@assets/img/icon/', 'arrow-left');
}

.arrow-up {
  @include icon-S('@assets/img/icon/', 'arrow-up');
}

.arrow-down {
  @include icon-S('@assets/img/icon/', 'arrow-down');
}

.sustainability {
  @include icon-XS('@assets/img/icon/', 'sustainability');
}

// menu icons
.concert {
  @include icon-M-focusable('@assets/img/menu/', 'ArteConcert');
}

.category {
  background-image: url('@assets/img/menu/Category.png');

  &.is-focused {
    background-image: url('@assets/img/menu/Category-focused.png');
  }
}

.direct {
  background-image: url('@assets/img/menu/Direct.png');

  &.is-focused {
    background-image: url('@assets/img/menu/Direct-focused.png');
  }
}

.exit {
  @include icon-M-focusable('@assets/img/menu/', 'Exit');
}

.guide {
  @include icon-M-focusable('@assets/img/menu/', 'GuideTV');
}

.home {
  @include icon-M-focusable('@assets/img/menu/', 'Home');
}

.myarte {
  @include icon-M-focusable('@assets/img/menu/', 'MyArte');
}

.myvideos {
  @include icon-M-focusable('@assets/img/menu/', 'MyVideos');
}

.search {
  @include icon-M-focusable('@assets/img/menu/', 'Search');
}

.settings {
  @include icon-M-focusable('@assets/img/menu/', 'Settings');
}

// search
.space {
  @include icon-M-focusable('@assets/img/icon/search/', 'Space');
}

.backspace {
  @include icon-M-focusable('@assets/img/icon/search/', 'Backspace');
}

// audio
.AD {
  @include icon-ML('@assets/img/icon/audio/', 'Audio-description');
}

.STM {
  @include icon-ML('@assets/img/icon/audio/', 'Hearing-impaired');
}

.VF {
  @include icon-ML('@assets/img/icon/audio/', 'vf');
}

.OV {
  @include icon-ML('@assets/img/icon/audio/', 'ov');
}

.VO {
  @include icon-ML('@assets/img/icon/audio/', 'vo');
}

.UT {
  @include icon-ML('@assets/img/icon/audio/', 'ut');
}

.ST {
  @include icon-ML('@assets/img/icon/audio/', 'subtitle');
}

.DE {
  @include icon-ML('@assets/img/icon/audio/', 'de');
}

// player
.pause {
  @include icon-M('@assets/img/icon/player/', 'Pause');
}

.next-content {
  @include icon-M('@assets/img/icon/player/', 'Next');
}

.previous-content {
  @include icon-M('@assets/img/icon/player/', 'Previous');
}

.language-versions {
  @include icon-M('@assets/img/icon/player/', 'Version');
}

import { IInitialResults, IPageResults } from '@apptypes/pagination';
import { useMouseWheel } from '@components/MouseNavigation/useMouseWheel';
import { setVerticalScrollIndex } from '@components/MouseNavigation/verticalScrollIndex';
import { PageTitle } from '@components/Pages/PageTitle/PageTitle';
import { useNormalizedParams } from '@hooks/useNormalizedParams';
import { useFocusable, UseFocusableResult } from '@noriginmedia/norigin-spatial-navigation';
import { MouseContext } from '@providers/MouseProvider';
import { buildPageId } from '@util/buildPageId';
import { getGridFocusedItem, getGridHorizontalDiff, setGridFocusedItem, setGridHorizontalDiff } from '@util/NavHistory';
import { handleOnFocus } from '@util/navigation';
import { useContext, useEffect, useMemo, useState } from 'react';

import { FOCUS_KEY_PRIMARY, teaserFocusKey, useReturnFocusOnUnmount } from '../../focus';
import { ITeaserResponse, ZoneTemplate } from '../../types';
import { GridLayout } from '../GridLayout/GridLayout';
import { Teaser, useTeaserAction } from '../Teaser';
import styles from './grid-view.module.scss';

interface IGridViewProperties {
  initialResults: IInitialResults;
  fetchNextPage: () => void;
  busyFetchingPage: boolean;
  pageResults: IPageResults;
  setCurrentGridItemIndex: (index: number | undefined) => void;
  currentGridItemIndex: number | undefined;
  resultsRowLength: number;
  focusKey: string;
  focusOnMount?: boolean;
  focusGuard: (
    teaser: ITeaserResponse,
    direction: string,
    index: number,
    rowLength: number,
    setFocus: UseFocusableResult['setFocus'],
  ) => boolean;
  testid?: string;
}

/**
 * load next page when the user is PAGINATION_ROW_THRESHOLD number of rows away from the bottom end of the grid
 */
const PAGINATION_ROW_THRESHOLD = 2;

export const GridView = ({
  initialResults,
  fetchNextPage,
  busyFetchingPage,
  pageResults,
  setCurrentGridItemIndex,
  currentGridItemIndex,
  resultsRowLength,
  focusKey,
  focusOnMount,
  focusGuard,
  testid,
}: IGridViewProperties) => {
  const { setFocus } = useFocusable();
  const { handleTeaserPress } = useTeaserAction();
  const [currentItem, setCurrentItem] = useState<number>();
  const routeParams = useNormalizedParams();
  const { isMouseActive } = useContext(MouseContext);

  useMouseWheel();

  useReturnFocusOnUnmount(FOCUS_KEY_PRIMARY, 'focus-key-zone-');
  setVerticalScrollIndex(currentGridItemIndex || 0);

  const teaserElements = useMemo(() => {
    const teaserFakeData = [];
    if (busyFetchingPage) {
      const totalValidDataLength = initialResults.results.length + pageResults.data.length;
      let fakeIteratingLimit = Math.min(2, initialResults.results.length);
      const remainder = totalValidDataLength % resultsRowLength;
      if (remainder !== 0) {
        fakeIteratingLimit += resultsRowLength - remainder;
      }

      for (let index = 0; index <= fakeIteratingLimit; index++) {
        const teaser: ITeaserResponse = initialResults.results[index] as ITeaserResponse;
        teaserFakeData.push({
          ...teaser,
          placeholderMode: true,
          image: '',
          duration: null,
        });
      }
    }

    const combinedData = [...initialResults.results, ...pageResults.data, ...teaserFakeData];
    const combinedDataLength = combinedData.length;
    const previousRow = Math.floor(Math.max(0, (currentItem || 0) - resultsRowLength) / resultsRowLength);
    const currentRow = Math.floor((currentItem || 0) / resultsRowLength);
    const nextRow = currentRow + 1;
    const rowAfterNext = currentRow + 2;

    const startIndex = previousRow * resultsRowLength;
    const teaserElements = [];
    for (let index = startIndex; index < combinedDataLength; index++) {
      const teaser: ITeaserResponse = combinedData[index] as ITeaserResponse;
      const fKey = teaserFocusKey(0, index);
      const itemRow = Math.floor(index / resultsRowLength);

      if (itemRow === previousRow || itemRow === currentRow || itemRow === nextRow || itemRow === rowAfterNext) {
        teaserElements.push(
          <Teaser
            watchOrResumeLabel=""
            key={!teaser.placeholderMode ? index : `placeholder-${index}`}
            focusKey={fKey}
            isDisabled={teaser.placeholderMode}
            fixedHeight={true}
            {...teaser}
            showItemTitle={!teaser.placeholderMode ? initialResults.showItemTitle : false}
            zoneIndex={0}
            position={index}
            setFocus={setFocus}
            onFocus={(layout, props, focusDetails) => {
              setGridHorizontalDiff(buildPageId(routeParams), index - itemRow * resultsRowLength);

              const indexOfFirstItemInRow = itemRow * resultsRowLength;
              handleOnFocus(focusDetails, indexOfFirstItemInRow, setCurrentItem);
            }}
            onEnterPress={() => {
              !teaser.placeholderMode && handleTeaserPress(teaser);
            }}
            onMouseClick={(event) => {
              event.preventDefault();
              !teaser.placeholderMode && handleTeaserPress(teaser);
            }}
            template={initialResults.template as ZoneTemplate}
            onArrowPress={(direction) => focusGuard(teaser, direction, index, resultsRowLength, setFocus)}
          />,
        );
      } else {
        if (itemRow > rowAfterNext) {
          break;
        }
      }
    }
    return teaserElements;
  }, [
    busyFetchingPage,
    initialResults,
    pageResults,
    currentItem,
    setCurrentItem,
    resultsRowLength,
    setFocus,
    handleTeaserPress,
    focusGuard,
    routeParams,
  ]);

  useEffect(() => {
    const pageId = buildPageId(routeParams);
    if (pageId) {
      currentItem !== undefined && setGridFocusedItem(pageId, currentItem);
    }
  }, [currentItem, routeParams]);

  useEffect(() => {
    if (focusOnMount) {
      const focusedItemOnMount =
        Number(getGridFocusedItem(routeParams.pageId as string)) +
        Number(getGridHorizontalDiff(routeParams.pageId as string));
      setFocus(teaserFocusKey(0, focusedItemOnMount || 0));
    }
  }, [focusOnMount, routeParams, setFocus]);

  useEffect(() => {
    if (currentItem) {
      const totalItems = initialResults.results.length - 1 + pageResults.data.length - 1;
      if (currentItem > totalItems - PAGINATION_ROW_THRESHOLD * resultsRowLength) {
        fetchNextPage();
      }
    }
  }, [currentItem, fetchNextPage, initialResults, pageResults, resultsRowLength]);

  useEffect(() => {
    if (currentItem) {
      setCurrentGridItemIndex(currentItem);
    }

    return () => {
      setCurrentGridItemIndex(undefined);
    };
  }, [currentItem, setCurrentGridItemIndex]);

  useEffect(() => {
    setCurrentItem(getGridFocusedItem(routeParams.pageId as string) || 0);
  }, [routeParams.pageId]);

  return (
    <div
      onMouseEnter={() => {
        // focus results to allow vertical scrolling via mouse
        isMouseActive && setFocus(focusKey);
      }}
    >
      {initialResults.title && <PageTitle title={initialResults.title} />}
      <GridLayout
        rowLength={resultsRowLength}
        wrapperClassName={styles['grid-wrapper']}
        className={styles.grid}
        rowClassName={styles['grid-row']}
        currentItem={currentItem}
        focusKey={focusKey}
        behavior="static"
        testid={testid}
      >
        {teaserElements}
      </GridLayout>
    </div>
  );
};

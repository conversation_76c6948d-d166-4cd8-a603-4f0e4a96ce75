import { ReactNode } from 'react';

import styles from './full-screen-overlay.module.scss';

interface IFullScreenOverlayProperties {
  children: ReactNode;
}

export function FullScreenOverlay({ children }: IFullScreenOverlayProperties) {
  return (
    <div className={styles.overlay}>
      <div className={styles.wrapper}>
        <div className={styles.content}>{children}</div>
      </div>
    </div>
  );
}

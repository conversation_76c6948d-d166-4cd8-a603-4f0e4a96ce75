import classNames from 'classnames';

import { THEMES } from '../../constants';
import styles from './title.module.scss';

export interface ITitleProperties {
  title?: string;
  theme?: string;
  className?: string;
}

const getLogoImage = () => {
  try {
    return require('@assets/img/arte-logo_vertical.png');
  } catch (error) {
    return '';
  }
};

export const Title = ({ title, theme }: ITitleProperties) => {
  const withLogo = theme && theme === 'theme-info';
  const hasTheme = theme && theme !== THEMES.SHOWEMPTYZONE;

  return (
    <>
      {theme && withLogo && <img src={getLogoImage()} alt="theme" className={styles['title-image']} />}

      {title && (
        <h2
          className={classNames(
            styles,
            styles['list-title'],
            { [styles['list-title-with-theme']]: hasTheme },
            { [styles['list-title-with-logo']]: withLogo },
          )}
        >
          {title}
        </h2>
      )}
    </>
  );
};

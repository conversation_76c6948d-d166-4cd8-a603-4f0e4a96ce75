import classNames from 'classnames';
import React, { ButtonHTMLAttributes } from 'react';

import { focusClassNames, IFocusState, withFocusable } from '../../focus';
import { Icon, IconType } from '../Icon/Icon';
import styles from './button.module.scss';

export interface IButtonProperties extends ButtonHTMLAttributes<HTMLButtonElement>, IFocusState {
  label: string;
  icon?: IconType;
  iconPlacement?: 'left' | 'right';
  onEnterPress: () => void;
  small?: boolean;
  testId?: string;
  testIdFocused?: boolean;
}

const renderIcon = (
  icon: IButtonProperties['icon'],
  iconPlacement: IButtonProperties['iconPlacement'],
  focused?: boolean,
) =>
  icon && (
    <Icon
      type={icon}
      size="XS"
      isFocused={focused}
      className={classNames(styles.icon, [styles[`icon-${iconPlacement}`]], {
        [styles['close']]: icon === 'close',
      })}
    />
  );

export const Button = withFocusable(
  React.forwardRef<HTMLButtonElement, IButtonProperties>(
    ({ small, label, icon, iconPlacement = 'left', className, style, ...properties }, reference) => {
      const getIcon = () => renderIcon(icon, iconPlacement, properties.isFocused);
      return (
        <button
          data-testid={properties.testId}
          data-testid-focused={properties.testIdFocused ? 'true' : undefined}
          ref={reference}
          style={style}
          onClick={() => {
            if (properties.onEnterPress) properties.onEnterPress();
          }}
          className={focusClassNames(
            styles,
            properties,
            styles.button,
            {
              [styles[`icon-${iconPlacement}`]]: icon,
              [styles.small]: small,
            },
            className,
          )}
        >
          {iconPlacement === 'left' && getIcon()}
          <span className={focusClassNames(styles, properties, styles.label, { [styles.small]: small })}>{label}</span>
          {iconPlacement === 'right' && getIcon()}
        </button>
      );
    },
  ),
);

@use '~styles/globals' as *;

$horizontal-spacing-with-icon: px-to-rem(40);
$horizontal-spacing-without-icon: px-to-rem(21);
$icon-gap: calc(0.5 * $horizontal-spacing-without-icon);

.button {
  height: px-to-rem(82);
  padding: 0 $horizontal-spacing-with-icon;
  background-color: #333;
  border-radius: px-to-rem(6);
  border: none;
  font-family: $font-family-bold;

  &.small {
    height: auto;
    padding: px-to-rem(8) px-to-rem(16);
  }

  &.is-focused {
    background-color: $white;
  }

  &.icon-left {
    padding-left: $horizontal-spacing-without-icon;
    padding-right: $horizontal-spacing-without-icon;
  }

  &.icon-right {
    padding-left: $horizontal-spacing-without-icon;
    padding-right: $horizontal-spacing-without-icon;
  }
}

.label {
  color: $white;
  font-size: px-to-rem(35);
  vertical-align: middle;

  &.small {
    font-size: px-to-rem(28);
  }

  &.is-focused {
    color: $black;
  }
}

.icon {
  vertical-align: middle;

  &.icon-left {
    margin-right: $icon-gap;
  }

  &.icon-right {
    margin-left: $icon-gap;
  }

  // the close icon in a button needs special styling because it looks bigger than other icons
  &.close {
    @include icon-size(px-to-rem(24));

    margin-left: px-to-rem(3);
    margin-right: px-to-rem(14);
  }
}

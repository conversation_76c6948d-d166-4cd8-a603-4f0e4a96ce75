import { TEST_ID } from '@constants';
import classNames from 'classnames';
import { FunctionComponent } from 'react';

import styles from './progress-bar.module.scss';

export interface IProgressBarProperties {
  progressInPx: number;
  className?: string;
  edgy?: boolean;
}

export const ProgressBar: FunctionComponent<IProgressBarProperties> = ({ progressInPx, className, edgy }) => (
  <div className={classNames(styles['progress-bar'], className, edgy && styles.edgy)}>
    <div
      data-testid={TEST_ID.PLAYER.PROGRESS_BAR}
      className={styles.progress}
      style={{
        width: `${progressInPx}px`,
      }}
    />
  </div>
);

import classNames from 'classnames';
import { ReactNode } from 'react';

import styles from './image-placeholder.module.scss';

type PlaceholderSize = 'small' | 'large';

interface IImagePlaceholder {
  children: ReactNode;
  size?: PlaceholderSize;
}

export function ImagePlaceholder({ children, size = 'small' }: IImagePlaceholder) {
  return <div className={classNames(styles.wrapper, styles[size])}>{children}</div>;
}

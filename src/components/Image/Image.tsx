import React, { useState } from 'react';

import { focusClassNames, IFocusState } from '../../focus';
import styles from './image.module.scss';

export interface IImageProperties extends React.ImgHTMLAttributes<HTMLImageElement>, IFocusState {
  src: string;
  className?: string;
  loadingStrategy?: 'eager' | 'lazy';
  fallbackUrl?: string;
}

export const Image = React.forwardRef<HTMLImageElement, IImageProperties>((properties, reference) => {
  const [error, setError] = useState(false);

  return (
    <img
      width={properties.width}
      height={properties.height}
      ref={reference}
      src={error ? properties.fallbackUrl || ' ' : properties.src}
      className={focusClassNames(styles, properties, styles['image'], properties.className)}
      loading={properties.loadingStrategy}
      onError={() => {
        setError(true);
      }}
      alt=""
    />
  );
});

import { GridLayout } from '@components/GridLayout/GridLayout';
import { IconType } from '@components/Icon/Icon';
import { Button } from '@components/Keyboard/Button/Button';
import { KeyboardView } from '@components/Keyboard/KeyboardView';
import { TEST_ID } from '@constants';
import { useFocusable, UseFocusableConfig } from '@noriginmedia/norigin-spatial-navigation';
import React, { useEffect } from 'react';

import { FOCUS_KEY_MENU_MYARTE, FOCUS_KEY_PRIMARY, withFocusableContainer } from '../../focus';
import { IKeyboardProperties } from '../../types/keyboard';
import styles from './keyboard.module.scss';
import { KEYBOARD_ROW_LENGTH, LOGIN_KEYBOARD_PRIMARY_FOCUS_CHARACTER, searchNumbers } from './keyboardConfig';
import { LargeButton } from './LargeButton/LargeButton';

const FOCUS_KEY_LARGE_BUTTON = 'FOCUS_KEY_LARGE_BUTTON';

export const LoginKeyboard = withFocusableContainer(
  React.forwardRef<HTMLDivElement, IKeyboardProperties>(
    ({ onKeyboardPress, onBackspacePress, title, focusBackspaceButton }, reference) => {
      const { setFocus } = useFocusable();

      useEffect(() => {
        if (focusBackspaceButton) {
          setFocus(FOCUS_KEY_LARGE_BUTTON);
        }
      }, [focusBackspaceButton, setFocus]);

      const buttons = (characters: string[]) => {
        return characters.map((character, index) => (
          <Button
            character={character}
            key={character}
            onEnterPress={() => onKeyboardPress(character)}
            focusOnMount={character === LOGIN_KEYBOARD_PRIMARY_FOCUS_CHARACTER}
            focusKey={character === LOGIN_KEYBOARD_PRIMARY_FOCUS_CHARACTER ? FOCUS_KEY_PRIMARY : undefined}
            onArrowPress={(direction) => {
              switch (direction) {
                case 'up':
                case 'down':
                  return true;
                case 'left':
                  if (index % KEYBOARD_ROW_LENGTH) {
                    return true;
                  } else {
                    setFocus(FOCUS_KEY_MENU_MYARTE);
                    return false;
                  }
                case 'right':
                  return index % KEYBOARD_ROW_LENGTH !== KEYBOARD_ROW_LENGTH - 1;
                default:
                  return false;
              }
            }}
          />
        ));
      };

      const largeButton = (className: string, iconType: IconType, onEnterPress: UseFocusableConfig['onEnterPress']) => (
        <LargeButton
          focusKey={FOCUS_KEY_LARGE_BUTTON}
          className={className}
          iconType={iconType}
          testId={TEST_ID.KEYBOARD.BACKSPACE}
          onEnterPress={onEnterPress}
          onArrowPress={(direction) => {
            switch (direction) {
              case 'up':
                return false;
              case 'down':
                setFocus(FOCUS_KEY_PRIMARY);
                return false;
              case 'left':
                setFocus(FOCUS_KEY_MENU_MYARTE);
                return false;
              default:
                return false;
            }
          }}
        />
      );

      return (
        <KeyboardView title={title}>
          <div ref={reference}>
            {largeButton(styles['backspace-button'], 'backspace', () => onBackspacePress())}
            <GridLayout
              rowLength={KEYBOARD_ROW_LENGTH}
              className={styles.keyboard}
              rowClassName={styles['keyboard-row']}
            >
              {buttons(searchNumbers)}
            </GridLayout>
          </div>
        </KeyboardView>
      );
    },
  ),
);

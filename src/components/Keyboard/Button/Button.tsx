import React, { ButtonHTMLAttributes } from 'react';

import { focusClassNames, IFocusState, withFocusable } from '../../../focus';
import styles from './button.module.scss';

interface IButtonProperties extends ButtonHTMLAttributes<HTMLButtonElement>, IFocusState {
  character: string;
  onEnterPress: () => void;
  testIdFocused?: boolean;
}

export const Button = withFocusable(
  React.forwardRef<HTMLButtonElement, IButtonProperties>(({ character, testIdFocused, ...properties }, reference) => {
    return (
      <button
        ref={reference}
        data-testid-focused={testIdFocused ? 'true' : undefined}
        className={focusClassNames(styles, properties, styles.button)}
        onClick={() => properties.onEnterPress()}
      >
        {character}
      </button>
    );
  }),
);

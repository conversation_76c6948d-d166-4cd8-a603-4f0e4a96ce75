import { Icon, IconType } from '@components/Icon/Icon';
import React, { ButtonHTMLAttributes } from 'react';

import { focusClassNames, IFocusState, withFocusable } from '../../../focus';
import styles from './large-button.module.scss';

interface ILargeButtonProperties extends ButtonHTMLAttributes<HTMLButtonElement>, IFocusState {
  iconType: IconType;
  className?: string;
  onEnterPress: () => void;
  testId?: string;
}

export const LargeButton = withFocusable(
  React.forwardRef<HTMLButtonElement, ILargeButtonProperties>(({ iconType, className, ...properties }, reference) => {
    return (
      <button
        ref={reference}
        className={focusClassNames(styles, properties, styles['large-button'], className)}
        onClick={() => properties.onEnterPress()}
        data-testid={properties.testId}
      >
        <Icon type={iconType} size="M" isFocused={properties.isFocused} className={styles.icon} />
      </button>
    );
  }),
);

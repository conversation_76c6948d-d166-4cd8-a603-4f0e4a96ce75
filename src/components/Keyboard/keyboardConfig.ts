import { FOCUS_KEY_PRIMARY, FOCUS_KEY_PRIMARY_LEFTHAND, FOCUS_KEY_PRIMARY_RIGHTHAND } from '../../focus';

export const searchLetters = [
  'A',
  'B',
  'C',
  'D',
  'E',
  'F',
  'G',
  'H',
  'I',
  'J',
  'K',
  'L',
  'M',
  'N',
  'O',
  'P',
  'Q',
  'R',
  'S',
  'T',
  'U',
  'V',
  'W',
  'X',
  'Y',
  'Z',
  '‘',
  '-',
];

export const searchNumbers = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'];

export const KEYBOARD_ROW_LENGTH = 5;

export const SEARCH_KEYBOARD_PRIMARY_FOCUS_CHARACTER = searchLetters[0];

export const LOGIN_KEYBOARD_PRIMARY_FOCUS_CHARACTER = searchNumbers[0];

export const computeKeyboardFocusKey = (character: string | undefined) => {
  if (character === SEARCH_KEYBOARD_PRIMARY_FOCUS_CHARACTER) {
    return FOCUS_KEY_PRIMARY;
  }

  if (character && ['E', 'J', 'O', 'T', 'Y', '-', '5', '0', 'backspace'].includes(character)) {
    return FOCUS_KEY_PRIMARY_RIGHTHAND + '_' + character;
  }

  if (character && ['space', 'A', 'F', 'K', 'P', 'U', 'Z', '1', '6'].includes(character)) {
    return FOCUS_KEY_PRIMARY_LEFTHAND + '_' + character;
  }

  return undefined;
};

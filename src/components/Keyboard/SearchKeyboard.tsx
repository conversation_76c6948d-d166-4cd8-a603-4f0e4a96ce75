import { IKeyboardProperties } from '@apptypes/keyboard';
import { PageResponseBody } from '@apptypes/PageResponseBody';
import { GridLayout } from '@components/GridLayout/GridLayout';
import { IconType } from '@components/Icon/Icon';
import { Button } from '@components/Keyboard/Button/Button';
import { KeyboardView } from '@components/Keyboard/KeyboardView';
import { LargeButton } from '@components/Keyboard/LargeButton/LargeButton';
import { validSearchResults } from '@components/Search/searchUtil';
import { useNormalizedParams } from '@hooks/useNormalizedParams';
import { useFocusable, UseFocusableConfig } from '@noriginmedia/norigin-spatial-navigation';
import { SearchContext } from '@providers/SearchContext';
import { buildPageId } from '@util/buildPageId';
import {
  getHorizontalNavIndex,
  getSearchKeyboardNavExitKeyRight,
  getVerticalNavIndex,
  setSearchKeyboardNavExitKeyLeft,
  setSearchKeyboardNavExitKeyRight,
} from '@util/NavHistory';
import React, { useCallback, useContext, useEffect, useState } from 'react';
import { useLoaderData } from 'react-router-dom';

import { FOCUS_KEY_MENU_SEARCH, FOCUS_KEY_PRIMARY, teaserFocusKey, withFocusableContainer } from '../../focus';
import styles from './keyboard.module.scss';
import {
  computeKeyboardFocusKey,
  KEYBOARD_ROW_LENGTH,
  SEARCH_KEYBOARD_PRIMARY_FOCUS_CHARACTER,
  searchLetters,
  searchNumbers,
} from './keyboardConfig';

export const SearchKeyboard = withFocusableContainer(
  React.forwardRef<HTMLDivElement, IKeyboardProperties>(({ onKeyboardPress, onBackspacePress, title }, reference) => {
    const { setFocus } = useFocusable();
    const { currentGridItemIndex, searchResults } = useContext(SearchContext);
    const routeParams = useNormalizedParams();
    const { zones: loaderZones } = useLoaderData() as PageResponseBody;
    const [focusedChar, setFocusedChar] = useState<string | null>(null);

    useEffect(() => {
      setSearchKeyboardNavExitKeyLeft(SEARCH_KEYBOARD_PRIMARY_FOCUS_CHARACTER);
    }, []);

    const getPageZoneAndHorizontalIndex = useCallback(() => {
      const pageId = buildPageId(routeParams);
      if (!pageId) return {};
      const verticalIndex = getVerticalNavIndex(pageId);
      const zoneId = loaderZones[verticalIndex].id;
      const horizontalIndex = getHorizontalNavIndex(pageId, zoneId);
      return { pageId, zoneId, horizontalIndex, verticalIndex };
    }, [loaderZones, routeParams]);

    const shouldFocusOnMount = (character: string) => {
      const { horizontalIndex, verticalIndex } = getPageZoneAndHorizontalIndex();
      if (verticalIndex !== 0 || horizontalIndex !== 0) {
        return false;
      }

      if (getSearchKeyboardNavExitKeyRight()) {
        return false;
      }

      return character === SEARCH_KEYBOARD_PRIMARY_FOCUS_CHARACTER;
    };

    const buttons = (characters: string[]) => {
      return characters.map((character, index) => (
        <Button
          character={character}
          key={character}
          onEnterPress={() => onKeyboardPress(character)}
          focusOnMount={shouldFocusOnMount(character)}
          focusKey={computeKeyboardFocusKey(character)}
          onFocus={(prop) => {
            prop?.node?.innerText && setFocusedChar(prop.node.innerText);
          }}
          onArrowPress={(direction) => {
            switch (direction) {
              case 'up':
                if (focusedChar === '5') {
                  setFocus(computeKeyboardFocusKey('-') || FOCUS_KEY_PRIMARY);
                  return false;
                }
                return true;
              case 'down':
                return true;
              case 'left':
                if (index % KEYBOARD_ROW_LENGTH) {
                  return true;
                } else {
                  setSearchKeyboardNavExitKeyLeft(character);
                  setFocus(FOCUS_KEY_MENU_SEARCH);
                  return false;
                }
              case 'right':
                if (index % KEYBOARD_ROW_LENGTH === KEYBOARD_ROW_LENGTH - 1) {
                  const { horizontalIndex, verticalIndex } = getPageZoneAndHorizontalIndex();
                  setSearchKeyboardNavExitKeyRight(character);

                  if (validSearchResults(searchResults)) {
                    setFocus(teaserFocusKey(0, currentGridItemIndex || 0));
                  } else {
                    setFocus(teaserFocusKey(verticalIndex || 0, horizontalIndex || 0));
                  }

                  return false;
                } else {
                  return true;
                }
              default:
                return false;
            }
          }}
        />
      ));
    };

    const largeButton = (
      className: string,
      iconType: IconType,
      onEnterPress: UseFocusableConfig['onEnterPress'],
      leftmost?: boolean,
    ) => (
      <LargeButton
        className={className}
        iconType={iconType}
        onEnterPress={onEnterPress}
        focusKey={computeKeyboardFocusKey(iconType)}
        onArrowPress={(direction) => {
          switch (direction) {
            case 'up':
              return false;
            case 'down':
              setFocus(FOCUS_KEY_PRIMARY);
              return false;
            case 'left':
              if (leftmost) {
                setSearchKeyboardNavExitKeyLeft(iconType);
                setFocus(FOCUS_KEY_MENU_SEARCH);
                return false;
              }
              return true;
            case 'right':
              if (leftmost) {
                return true;
              } else {
                setSearchKeyboardNavExitKeyRight(iconType);
                const { horizontalIndex, verticalIndex } = getPageZoneAndHorizontalIndex();
                if (validSearchResults(searchResults)) {
                  setFocus(teaserFocusKey(0, currentGridItemIndex || 0));
                } else {
                  setFocus(teaserFocusKey(verticalIndex || 0, horizontalIndex || 0));
                }
                return false;
              }
            default:
              return false;
          }
        }}
      />
    );

    return (
      <KeyboardView title={title}>
        <div ref={reference}>
          {largeButton(styles['space-button'], 'space', () => onKeyboardPress(' '), true)}
          {largeButton(styles['backspace-button'], 'backspace', () => onBackspacePress())}
          <GridLayout rowLength={KEYBOARD_ROW_LENGTH} className={styles.keyboard} rowClassName={styles['keyboard-row']}>
            {buttons(searchLetters)}
          </GridLayout>
          <div className={styles['horizontal-line']} />
          <GridLayout rowLength={KEYBOARD_ROW_LENGTH} className={styles.keyboard} rowClassName={styles['keyboard-row']}>
            {buttons(searchNumbers)}
          </GridLayout>
        </div>
      </KeyboardView>
    );
  }),
);

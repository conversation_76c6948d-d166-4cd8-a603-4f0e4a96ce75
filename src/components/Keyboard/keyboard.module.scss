@use '~styles/globals' as *;

.space-button,
.backspace-button {
  margin-top: px-to-rem(50);
  display: inline-block;
  margin-right: px-to-rem(32);
}

.keyboard {
  left: px-to-rem(-8);
  margin-top: px-to-rem(50);

  > :not(:first-child) {
    margin-top: px-to-rem(35);
  }
}

.keyboard-row {
  > :not(:first-child) {
    margin-left: px-to-rem(24);
  }
}

.horizontal-line {
  width: px-to-rem(269);
  height: px-to-rem(1);
  background-color: $middle-grey;
  margin-top: px-to-rem(50);
}

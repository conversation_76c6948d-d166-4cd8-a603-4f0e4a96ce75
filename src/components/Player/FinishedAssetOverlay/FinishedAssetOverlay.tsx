import { useCustomNavigate } from '@hooks/useCustomNavigate';
import { FocusContext, useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import { useModalContext } from '@providers/ModalContext';
import { FunctionComponent, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useRemoteController } from 'react-remote-controller';
import { useLoaderData } from 'react-router-dom';
import { getKeyMap } from 'target';

import { ROUTES } from '../../../constants';
import { useBackJourney } from '../../../features/backjourney/useBackJourney';
import { defaultFocusGuard } from '../../../focus';
import { useInterval } from '../../../hooks/useInterval';
import { VideoLoaderResponse } from '../../../routes/videoLoader';
import { autoplayEnabled } from '../../../util/cookies';
import { Button } from '../../Button/Button';
import { getAdjacentProgramFromPlaylist } from '../ControlsOverlay/Buttons/Buttons.helper';
import styles from './finished-asset-overlay.module.scss';
import { Heading } from './Heading/Heading';

const AUTOPLAY_DELAY_IN_SECONDS = 5;

export const FinishedAssetOverlay: FunctionComponent = () => {
  const navigate = useCustomNavigate();
  const { t } = useTranslation();
  const { focusKey } = useFocusable({ isFocusBoundary: true });
  const { handleBack } = useBackJourney();
  const { hideModal } = useModalContext();
  const [secRemaining, setSecRemaining] = useState(AUTOPLAY_DELAY_IN_SECONDS);
  const [intervalTime, setIntervalTime] = useState(autoplayEnabled() ? 1000 : null);

  const { playlistData, videoId } = useLoaderData() as VideoLoaderResponse;
  const [imgUrl, setImgUrl] = useState('');

  const nextProgramInPlaylist = getAdjacentProgramFromPlaylist({ playlistData, videoId, direction: 'next' });

  useEffect(() => {
    hideModal();
  }, [hideModal]);

  useEffect(() => {
    if (nextProgramInPlaylist) {
      const images = nextProgramInPlaylist?.images;
      images && images[0].url && setImgUrl(images[0].url);
    }
  }, [nextProgramInPlaylist]);

  useInterval(() => {
    if (secRemaining > 0) {
      setSecRemaining(secRemaining - 1);
    } else {
      setIntervalTime(null);
      handleNextVideo();
    }
  }, intervalTime);

  const handleNextVideo = () => {
    if (nextProgramInPlaylist && nextProgramInPlaylist?.providerId) {
      navigate(`${ROUTES.VERIFICATION}/${nextProgramInPlaylist.providerId}`, { replace: true });
    }
  };

  const getAutoplayButttonLabel = () => {
    if (autoplayEnabled()) {
      return t('player__next_countdown').replace('{{0}}', String(secRemaining));
    }
    return t('player__next');
  };

  const goBack = () => {
    setIntervalTime(null);
    handleBack();
  };

  useRemoteController<ReturnType<typeof getKeyMap>>({
    listenTo: {
      back: () => goBack(),
    },
  });

  return (
    <div className={styles.overlay}>
      <div className={styles.inner}>
        <Heading />
        <FocusContext.Provider value={focusKey}>
          <Button
            label={t('cancel')}
            className={styles['button']}
            onEnterPress={() => goBack()}
            onArrowPress={defaultFocusGuard}
          />
          <Button
            label={getAutoplayButttonLabel()}
            className={styles['button']}
            icon="play"
            onEnterPress={handleNextVideo}
            onArrowPress={defaultFocusGuard}
            focusOnMount
          />
        </FocusContext.Provider>
      </div>
      <div className={styles['image-container']}>
        <img src={imgUrl} className={styles.image} />
      </div>
    </div>
  );
};

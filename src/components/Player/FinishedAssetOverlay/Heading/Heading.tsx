import { FunctionComponent } from 'react';
import { useLoaderData } from 'react-router-dom';

import i18n from '../../../../i18n';
import { VideoLoaderResponse } from '../../../../routes/videoLoader';
import { getAdjacentProgramFromPlaylist } from '../../ControlsOverlay/Buttons/Buttons.helper';
import styles from './heading.module.scss';

export const Heading: FunctionComponent = () => {
  const { playlistData, videoId } = useLoaderData() as VideoLoaderResponse;
  const nextProgramInPlaylist = getAdjacentProgramFromPlaylist({ playlistData, videoId, direction: 'next' });

  return (
    <div className={styles.metadata}>
      <h2 className={styles['title']}>{i18n.t('player__next')}</h2>
      <h3 className={styles['subtitle']}>{nextProgramInPlaylist?.title}</h3>
    </div>
  );
};

@use '~styles/globals' as *;

.overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  width: 100%;
  height: 100%;
}

.inner {
  position: relative;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.button {
  font-size: px-to-rem(35);
  margin-right: px-to-rem(25);
}

.button img {
  margin-right: px-to-rem(5) !important;
}

.image-container {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: 0;
  padding: 0;
  z-index: -1;
  overflow: hidden;
}

.image-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: $transparent_overlay_color;
  z-index: 1;
}

.image {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
}

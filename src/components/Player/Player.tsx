import { VideoOverlay } from '@components/Video/VideoOverlay';
import { useBackJourney } from '@features/backjourney/useBackJourney';
import { useModalContext } from '@providers/ModalContext';
import { getVideoQualityCookie } from '@util/cookies';
import EventBus, { EventData } from '@util/EventBus';
import { hideFullScreenSpinner, showFullScreenSpinner } from '@util/showFullScreenSpinnerEvent';
import { getBitrate, VideoQuality } from '@util/videoQuality';
import { FunctionComponent, useCallback, useEffect, useState } from 'react';
import { useRemoteController } from 'react-remote-controller';
import { config, getKeyMap } from 'target';

import { EVENTS } from '../../constants';
import { trackAppLoadingEnds } from '../../tracking/appstart/appstart';
import { IVideoObjectConfig } from '../../videoplayer/video-object/types';
import { Video, VideoContextProvider } from '../Video';
import { DashVideoFactory } from '../Video/videofactory';

function getPlayerSettings(): window.dashjs.MediaPlayerSettingClass {
  const userVideoQuality = getVideoQualityCookie() as VideoQuality;
  const bitrate = getBitrate(userVideoQuality);

  // note v3 config is here for reference
  // see: https://github.com/ArteGEIE/HbbTV-v3/blob/bf3d2175e321e7737ad7702d6f3f35e0d1d1f736/ressources/js/controllers/crossPlatformPlayer.js#L324

  const config: window.dashjs.MediaPlayerSettingClass = {
    streaming: {
      abr: {
        initialBitrate: { audio: -1, video: bitrate || -1 }, // use -1 to let the player decide
        autoSwitchBitrate: { audio: true, video: true },
      },
      buffer: {
        flushBufferAtTrackSwitch: true,
      },
      text: {
        defaultEnabled: false,
        webvtt: { customRenderingEnabled: 1 },
      },
    },
  };

  // Note that setting no max bitrate means dash will default to auto for adaptive bitrate
  if (userVideoQuality !== VideoQuality.MAX) {
    config.streaming.abr.maxBitrate = { video: bitrate };
  }

  return config;
}

export const Player: FunctionComponent = () => {
  const { handleBack } = useBackJourney();
  const { hideModal } = useModalContext();
  const [isOffline, setIsOffline] = useState(false);
  const [isOverlayVisible, setIsOverlayVisible] = useState(true);

  const playerConfig = {
    playerSettings: getPlayerSettings(),
  };

  const [videoObject] = useState(DashVideoFactory(playerConfig as IVideoObjectConfig));

  const handleOfflineStatus = useCallback(
    <T,>(eventData: EventData<T>) => {
      const isCurrentlyOffline = Boolean(eventData[EVENTS.OFFLINE]);
      setIsOffline(isCurrentlyOffline);
    },
    [setIsOffline],
  );

  useEffect(() => {
    trackAppLoadingEnds();
  }, []);

  useEffect(() => {
    config.offline?.handleOfflineEventInVideo && EventBus.on(EVENTS.OFFLINE, handleOfflineStatus);

    return () => {
      config.offline?.handleOfflineEventInVideo && EventBus.off(EVENTS.OFFLINE, handleOfflineStatus);
    };
  }, [handleOfflineStatus]);

  useRemoteController<ReturnType<typeof getKeyMap>>({
    listenTo: {
      stop: () => handleBack(),
    },
  });

  useEffect(() => {
    function playerUnmount() {
      setIsOverlayVisible(false);
      showFullScreenSpinner();
    }

    EventBus.on(EVENTS.PLAYER_BACK, playerUnmount);

    return () => {
      EventBus.off(EVENTS.PLAYER_BACK, playerUnmount);
      hideFullScreenSpinner();
    };
  }, [videoObject]);

  const appToBackground = useCallback(() => {
    hideModal();
  }, [hideModal]);

  const appToForeground = useCallback(() => {
    if (!isOffline) {
      handleBack();
    }
  }, [handleBack, isOffline]);

  useEffect(() => {
    if (config.lifecycle?.goBackFromPlayback) {
      EventBus.on(EVENTS.APP_TO_BACKGROUND, appToBackground);
      EventBus.on(EVENTS.APP_TO_FOREGROUND, appToForeground);
      return () => {
        EventBus.off(EVENTS.APP_TO_BACKGROUND, appToBackground);
        EventBus.off(EVENTS.APP_TO_FOREGROUND, appToForeground);
      };
    }
  }, [appToBackground, appToForeground]);

  return (
    <VideoContextProvider videoObject={videoObject}>
      <Video isOffline={isOffline} />
      {isOverlayVisible && <VideoOverlay isOffline={isOffline} />}
    </VideoContextProvider>
  );
};

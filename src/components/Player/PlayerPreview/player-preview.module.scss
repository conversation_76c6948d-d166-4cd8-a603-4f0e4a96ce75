@use '~styles/globals' as *;

.play,
.image-container,
.transparent-overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.image-container {
  img {
    width: 100%;
    height: 100%;
  }
}

.transparent-overlay {
  background: $transparent_overlay_color;
}

.play {
  margin: auto;
  width: $big_play_button_width;
  height: $big_play_button_height;
}

.back {
  position: absolute;
  top: px-to-rem(898);
  left: px-to-rem(100);
}

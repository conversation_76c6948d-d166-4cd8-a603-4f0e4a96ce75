import { BackButton } from '@components/Player/ControlsOverlay/Buttons/components';
import { PlayButton } from '@components/Player/ControlsOverlay/Buttons/components/PlayButton/PlayButton';
import { Heading } from '@components/Player/ControlsOverlay/Heading/Heading';
import { overlayType } from '@components/Player/useOverlayTypeDetect';
import { useVideoContext, useVideoControls } from '@components/Video';
import { useBackJourney } from '@features/backjourney/useBackJourney';
import { FocusContext, useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import { FunctionComponent, useEffect, useState } from 'react';
import { useRemoteController } from 'react-remote-controller';
import { getKeyMap } from 'target';

import { FOCUS_KEY_PLAYER_BACK, FOCUS_KEY_PRIMARY } from '../../../focus';
import styles from './player-preview.module.scss';

export const PlayerPreview: FunctionComponent = () => {
  const { focusKey } = useFocusable({ isFocusBoundary: true });
  const { handleBack } = useBackJourney();
  const { videoData } = useVideoContext();
  const { play } = useVideoControls();
  const [imgUrl, setImgUrl] = useState('');
  const { setFocus } = useFocusable();
  const { setCurrentOverlayType } = useVideoContext();

  useEffect(() => {
    const videoObject = document.getElementById('video');
    if (videoObject) {
      videoObject.style.display = 'none';
    }
    return () => {
      if (videoObject) {
        videoObject.style.display = 'block';
      }
    };
  }, []);

  useEffect(() => {
    if (videoData) {
      const images = videoData?.attributes?.metadata?.images;
      images && images[0].url && setImgUrl(images[0].url);
    }
  }, [videoData]);

  const goBack = () => {
    handleBack();
  };

  useRemoteController<ReturnType<typeof getKeyMap>>({
    listenTo: {
      back: () => goBack(),
    },
  });

  return (
    <>
      <div className={styles['image-container']}>
        <img src={imgUrl} />
        <div className={styles['transparent-overlay']} />
      </div>
      <Heading />
      <FocusContext.Provider value={focusKey}>
        <PlayButton
          focusOnMount={true}
          focusKey={FOCUS_KEY_PRIMARY}
          onEnterPress={() => {
            setCurrentOverlayType(overlayType.CONTROLS);
            play();
          }}
          onArrowPress={(direction) => {
            switch (direction) {
              case 'down':
                setFocus(FOCUS_KEY_PLAYER_BACK);
                return false;
              default:
                return false;
            }
          }}
          className={styles.play}
        />
        <div className={styles.back}>
          <BackButton
            onArrowPress={(direction) => {
              switch (direction) {
                case 'up':
                  setFocus(FOCUS_KEY_PRIMARY);
                  return false;
                default:
                  return false;
              }
            }}
          />
        </div>
      </FocusContext.Provider>
    </>
  );
};

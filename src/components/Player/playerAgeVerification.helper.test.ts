import { describe } from 'vitest';

import { Error, Warning } from '../../types';
import { getAge, isAgeRestricted } from './PlayerAgeVerification.helper';

const ERROR_AGE_RESTRICTION_18: Partial<Error> = {
  code: 'ERROR_AGE_RESTRICTION_18',
};

const ERROR_AGE_RESTRICTION_16: Partial<Error> = {
  code: 'ERROR_AGE_RESTRICTION_16',
};

const WARNING_AGE_RESTRICTION_12: Partial<Warning> = {
  code: 'WARNING_AGE_RESTRICTION_12',
};

describe('isAgeRestricted', () => {
  it('should return true for an age error', () => {
    const actual = isAgeRestricted(ERROR_AGE_RESTRICTION_18 as Error);
    const expected = true;
    expect(actual).toEqual(expected);
  });

  it('should return true for an age warning', () => {
    const actual = isAgeRestricted(WARNING_AGE_RESTRICTION_12 as Warning);
    const expected = true;
    expect(actual).toEqual(expected);
  });
});

describe('getAge', () => {
  it('should return 18', () => {
    const actual = getAge(ERROR_AGE_RESTRICTION_18 as Error);
    const expected = '18';
    expect(actual).toEqual(expected);
  });

  it('should return 16', () => {
    const actual = getAge(ERROR_AGE_RESTRICTION_16 as Error);
    const expected = '16';
    expect(actual).toEqual(expected);
  });
});

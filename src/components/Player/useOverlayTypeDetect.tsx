import { useVideoContext } from '@components/Video';
import { useBackJourney } from '@features/backjourney/useBackJourney';
import useTimeout from '@hooks/useTimeout';
import { VideoLoaderResponse } from '@routes/videoLoader';
import EventBus from '@util/EventBus';
import { VideoObjectEvent } from '@videoplayer/video-object/types';
import { useCallback, useEffect, useState } from 'react';
import { useLoaderData, useLocation } from 'react-router-dom';
import { config } from 'target';

import { VideoObject } from '../../videoplayer';
import { getAdjacentProgramFromPlaylist } from './ControlsOverlay/Buttons/Buttons.helper';

const overlayType = {
  CONTROLS: 'CONTROLS',
  FINISHED_ASSET: 'FINISHED_ASSET',
  NONE: 'NONE',
  PLAYER_PREVIEW: 'PLAYER_PREVIEW',
};

type timeOutController = {
  time: number | null;
  triggerOverlay: string;
};

const useOverlayTypeDetect = (videoObject: VideoObject | undefined, videoLoaderData: VideoLoaderResponse) => {
  const [currentOverlayType, setCurrentOverlayType] = useState<string | null>(null);
  const { playlistData, videoId } = useLoaderData() as VideoLoaderResponse;
  const { isPlaying } = useVideoContext();
  const { handleBack } = useBackJourney();
  const location = useLocation();
  const [showControlsTimeout, setShowControlsTimeout] = useState<timeOutController>({
    time: null,
    triggerOverlay: overlayType.CONTROLS,
  });

  useTimeout(() => {
    setCurrentOverlayType(showControlsTimeout?.triggerOverlay);
  }, showControlsTimeout?.time);

  const handleAssetEnd = useCallback(() => {
    if (location.state?.isTrailer) {
      handleBack();
      return;
    }

    const nextProgramInPlaylist = getAdjacentProgramFromPlaylist({ playlistData, videoId, direction: 'next' });
    nextProgramInPlaylist
      ? setCurrentOverlayType(overlayType.FINISHED_ASSET)
      : setCurrentOverlayType(overlayType.CONTROLS);
  }, [playlistData, videoId, setCurrentOverlayType, handleBack, location.state?.isTrailer]);

  useEffect(() => {
    const onAssetEnded = () => {
      !config.timeBasedVideoEnd && handleAssetEnd();
    };

    videoObject?.on(VideoObjectEvent.ENDED, onAssetEnded);

    return () => {
      videoObject?.off(VideoObjectEvent.ENDED, onAssetEnded);
    };
  }, [videoObject, location.state?.isTrailer, handleAssetEnd]);

  useEffect(() => {
    setShowControlsTimeout({ time: 1000, triggerOverlay: overlayType.CONTROLS });
  }, [videoLoaderData]);

  useEffect(() => {
    const playbackNotAllowed = () => {
      videoObject?.hideVideoElement();
      setShowControlsTimeout({ time: 10, triggerOverlay: overlayType.PLAYER_PREVIEW });
    };

    const onTimeUpdate = (event: Event) => {
      const { time, duration } = (event as CustomEvent).detail;
      if (!!time && !!duration) {
        if (config.timeBasedVideoEnd) {
          if (time >= duration - 1) {
            handleAssetEnd();
          }
        }
      }
    };

    EventBus.on(VideoObjectEvent.PLAYBACKNOTALLOWED, playbackNotAllowed);
    videoObject?.on(VideoObjectEvent.TIMEUPDATE, onTimeUpdate);
    return () => {
      EventBus.off(VideoObjectEvent.PLAYBACKNOTALLOWED, playbackNotAllowed);
      videoObject?.off(VideoObjectEvent.TIMEUPDATE, onTimeUpdate);
    };
  }, [videoObject, handleAssetEnd]);

  useEffect(() => {
    if (isPlaying) {
      if (currentOverlayType === overlayType.PLAYER_PREVIEW) {
        setCurrentOverlayType(overlayType.CONTROLS);
      }
    }
  }, [isPlaying, currentOverlayType]);

  return { currentOverlayType, setCurrentOverlayType };
};

export { overlayType, useOverlayTypeDetect };

import { Error, Warning } from '../../types';

const AGE_RESTRICTION = {
  ERROR: 'ERROR_AGE_RESTRICTION',
  WARNING: 'WARNING_AGE_RESTRICTION',
};

const VALID_AGES = ['16', '18'];

export function isAgeRestricted(response: Error | Warning) {
  const { code } = response;
  return code.startsWith(AGE_RESTRICTION.ERROR) || code.startsWith(AGE_RESTRICTION.WARNING);
}

export function getAge(response: Error | Warning) {
  const { code } = response;
  const age = code.slice(-2);
  return VALID_AGES.includes(age) ? age : '';
}

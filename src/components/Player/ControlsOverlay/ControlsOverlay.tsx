import { VideoData } from '@apptypes/VideoResponseBody';
import Background from '@assets/img/player-background.png';
import { isLiveTestStream } from '@features/queryParams/queryParamsLookup';
import { useSkipSegment } from '@hooks/useSkipSegment';
import { useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import { isCurrentTimeWithinRange } from '@util/isCurrentTimeWithinRange';
import classNames from 'classnames';
import { FunctionComponent, useEffect, useRef } from 'react';

import { FOCUS_KEY_PRIMARY } from '../../../focus';
import { useVideoContext } from '../../Video';
import { Buttons, LiveButtons } from './Buttons';
import styles from './controls-overlay.module.scss';
import { Heading } from './Heading/Heading';
import { SkipButton } from './SkipButton/SkipButton';
import { LiveTimeBar, TimeBar } from './TimeBar';
import { useControlsOverlayTimeout } from './useControlsOverlayTimeout';
import { VideoMenu } from './VideoMenu/VideoMenu';

const isLive = (videoData: VideoData): boolean => {
  // test stream specified via query string
  if (isLiveTestStream()) return true;

  const availabilityRights = videoData?.attributes?.rights;
  const stickers = videoData?.attributes?.stickers;
  if (!availabilityRights || !stickers) {
    return false;
  }

  const hasLiveSticker = !!stickers.find((sticker) => sticker.code === 'LIVE_EVENT' || sticker.code === 'LIVE');
  const isWithinTimeRange = isCurrentTimeWithinRange(availabilityRights);

  return hasLiveSticker && isWithinTimeRange;
};

interface IVideoControlsOverlay {
  isOffline: boolean;
}

export const ControlsOverlay: FunctionComponent<IVideoControlsOverlay> = ({ isOffline }) => {
  const { videoData, videoMenu, isPlaying } = useVideoContext();
  const liveControls = isLive(videoData as VideoData);
  const isVisible = useControlsOverlayTimeout({ hasFocusableTimeBar: !liveControls, isOffline });
  const { setFocus, pause, resume } = useFocusable();
  const keyLockedCount = useRef(0);
  const isPlayingRef = useRef(isPlaying);
  isPlayingRef.current = isPlaying;
  const { skipSegmentActive, triggerSeek } = useSkipSegment();
  const hasSkipButton = skipSegmentActive?.type && skipSegmentActive?.begin && skipSegmentActive?.end && !videoMenu;

  /**
   * When rapidly pressing keys but no video is playing, the focus will disappear
   * this will poll the player state and resume key presses the moment we have playback or
   * we have waited for 25 iterating cycles
   */
  useEffect(() => {
    const timer = setInterval(() => {
      if (isPlayingRef.current || keyLockedCount.current > 25) {
        setFocus(FOCUS_KEY_PRIMARY);
        resume();
        clearInterval(timer);
      } else {
        keyLockedCount.current = keyLockedCount.current + 1;
      }
    }, 500);
    return () => clearInterval(timer);
  }, [resume, setFocus]);

  useEffect(() => {
    pause();
  }, [pause]);

  return (
    <>
      {hasSkipButton && (
        <SkipButton otherControlsVisible={isVisible} skipSegmentActive={skipSegmentActive} triggerSeek={triggerSeek} />
      )}
      <div className={classNames(styles.wrapper, isVisible ? '' : styles['hidden'])}>
        <img src={Background} className={styles.background} />
        <Heading />
        {videoData && (
          <>
            {liveControls ? <LiveTimeBar /> : <TimeBar hasSkipButton={hasSkipButton} />}
            {liveControls ? <LiveButtons /> : <Buttons />}
            {videoMenu && <VideoMenu type={videoMenu} />}
          </>
        )}
      </div>
    </>
  );
};

import { Attributes } from '@apptypes/VideoResponseBody';
import { getClip, getTrailer } from '@components/Teaser/getStickerInformation';
import { Label } from '@components/Teaser/Label/Label';
import { useVideoContext } from '@components/Video';
import { FunctionComponent } from 'react';

import styles from './heading.module.scss';

function getLabel(attributes: Attributes): string | undefined {
  const trailerLabel = getTrailer(attributes)?.label;
  if (trailerLabel) return trailerLabel;

  return getClip(attributes)?.label;
}

export const Heading: FunctionComponent = () => {
  const { videoData } = useVideoContext();
  const attributes = videoData?.attributes;

  if (!attributes) {
    return null;
  }

  const { title, subtitle } = attributes?.metadata;
  const label = getLabel(attributes);

  return (
    <div className={styles.metadata}>
      {title && <h2>{title}</h2>}
      {subtitle && <h3>{subtitle}</h3>}
      {label && (
        <div className={styles.label}>
          <Label label={label} properties={attributes} />
        </div>
      )}
    </div>
  );
};

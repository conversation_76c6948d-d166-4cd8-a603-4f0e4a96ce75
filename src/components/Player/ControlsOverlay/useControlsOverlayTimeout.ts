import { EVENTS } from '@constants';
import { useBackJourney } from '@features/backjourney/useBackJourney';
import { useSkipSegment } from '@hooks/useSkipSegment';
import { useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import { useModalContext } from '@providers/ModalContext';
import { MouseContext } from '@providers/MouseProvider';
import EventBus from '@util/EventBus';
import { isObjectEmpty } from '@util/object';
import { useCallback, useContext, useEffect, useRef, useState } from 'react';
import { useRemoteController } from 'react-remote-controller';
import { getKeyMap } from 'target';

import {
  FOCUS_KEY_PLAYER_BACK,
  FOCUS_KEY_PLAYER_SKIP,
  FOCUS_KEY_PLAYER_TIME_BAR,
  FOCUS_KEY_PRIMARY,
} from '../../../focus';
import { useVideoContext, useVideoControls } from '../../Video';

const TIMEOUT = 5000;

interface IControlsOverlayTimeoutOptions {
  hasFocusableTimeBar: boolean;
  isOffline: boolean;
}

export function useControlsOverlayTimeout(options: IControlsOverlayTimeoutOptions) {
  const [isVisible, setIsVisible] = useState(true);
  const [skipButtonShown, setSkipButtonShown] = useState(false);
  const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const { setFocus } = useFocusable();
  const { setControlsActive, videoMenu, setVideoMenu, videoObject } = useVideoContext();
  const { skipSegmentActive } = useSkipSegment();
  const { handleBack: handleBackJourney } = useBackJourney();
  const { isInterrupted } = useVideoControls();
  const { modalOpen } = useModalContext();
  const { isMouseActive } = useContext(MouseContext);

  useEffect(() => {
    setSkipButtonShown(!isObjectEmpty(skipSegmentActive));
  }, [skipSegmentActive]);

  const resetTimeout = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      if (isInterrupted) return;

      !options.isOffline && (skipButtonShown ? setFocus(FOCUS_KEY_PLAYER_SKIP) : setFocus(FOCUS_KEY_PRIMARY));
      setVideoMenu(undefined);
      setIsVisible(false);
      setControlsActive(false);
    }, TIMEOUT);
  }, [isInterrupted, setFocus, setIsVisible, setControlsActive, skipButtonShown, setVideoMenu, options]);

  function handleKeyDown() {
    if (modalOpen) return;

    if (!isVisible) {
      setIsVisible(true);
      setControlsActive(true);
    }
    resetTimeout();
  }

  if (isMouseActive) handleKeyDown();

  function handleLeft() {
    if (videoMenu) {
      closeVideoMenu();
      return;
    }
    handleKeyDown();
    focusTimeBar();
  }

  function handleRight() {
    handleKeyDown();
    focusTimeBar();
  }

  function handleFastForward() {
    if (modalOpen) return;

    handleKeyDown();
    focusTimeBar();
  }

  function handleRewind() {
    if (modalOpen) return;

    handleKeyDown();
    focusTimeBar();
  }

  function focusSkipButton() {
    handleKeyDown();
    if (isVisible) return;
    setFocus(FOCUS_KEY_PLAYER_SKIP);
  }

  function focusDefaultComponent() {
    handleKeyDown();
    if (isVisible) return;
    setFocus(FOCUS_KEY_PRIMARY);
  }

  function focusTimeBar() {
    if (isVisible || !options.hasFocusableTimeBar) return;
    setFocus(FOCUS_KEY_PLAYER_TIME_BAR);
  }

  function closeVideoMenu() {
    setVideoMenu(undefined);
    !isObjectEmpty(skipSegmentActive) ? setFocus(FOCUS_KEY_PLAYER_SKIP) : setFocus(FOCUS_KEY_PRIMARY);
  }
  function handleBack() {
    handleKeyDown();
    if (isVisible) {
      if (videoMenu) {
        closeVideoMenu();
        return;
      }
      if (modalOpen) return;
      EventBus.emit(EVENTS.PLAYER_BACK, {});
      handleBackJourney();
      return;
    }
    setFocus(FOCUS_KEY_PLAYER_BACK);
  }

  function shouldHideVideoElement() {
    return isVisible && !modalOpen && !videoMenu;
  }

  useRemoteController<ReturnType<typeof getKeyMap>>({
    listenTo: {
      back: () => {
        if (shouldHideVideoElement()) videoObject?.hideVideoElement();
        handleBack();
      },
      enter: () => focusDefaultComponent(),
      up: () => (skipButtonShown ? focusSkipButton() : focusDefaultComponent()),
      down: () => (skipButtonShown ? focusSkipButton() : focusDefaultComponent()),
      left: () => handleLeft(),
      right: () => handleRight(),
      pause: () => handleKeyDown(),
      play: () => handleKeyDown(),
      fastForward: () => handleFastForward(),
      rewind: () => handleRewind(),
      playPause: () => handleKeyDown(),
      playPauseToggle: () => handleKeyDown(),
    },
  });

  useEffect(() => {
    resetTimeout();

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [resetTimeout]);

  return isVisible;
}

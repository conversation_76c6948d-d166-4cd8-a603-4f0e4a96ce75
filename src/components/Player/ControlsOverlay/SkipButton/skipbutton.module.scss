@use '~styles/globals' as *;

.skipButtonContainer {
  z-index: map-get($zindex, player-skip-button-control);
  position: absolute;
  right: px-to-rem(120);
}

.skip-action-button {
  height: px-to-rem(82);
  padding: 0 px-to-rem(40);
  background-color: rgba(155, 155, 155, 0.2);
  border-radius: px-to-rem(6);
  border: px-to-rem(2) solid rgba(255, 255, 255, 0);
  font-size: px-to-rem(30);
  color: $white;

  &.is-focused {
    border-color: rgba(255, 255, 255, 1);
    background-color: rgba(155, 155, 155, 0.4);
  }
}

.skipButtonContainerWithControls {
  top: px-to-rem(692);
}

.skipButtonContainerWithoutControls {
  top: px-to-rem(901);
}

.skipButtonHide {
  display: none;
}

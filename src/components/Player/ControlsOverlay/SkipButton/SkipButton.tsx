import { useCustomNavigate } from '@hooks/useCustomNavigate';
import { useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import classNames from 'classnames';
import React, { FunctionComponent, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLoaderData } from 'react-router-dom';

import { ROUTES } from '../../../../constants';
import {
  FOCUS_KEY_PLAYER_SKIP,
  FOCUS_KEY_PLAYER_TIME_BAR,
  FOCUS_KEY_PRIMARY,
  useReturnFocusOnUnmount,
} from '../../../../focus';
import { useInterval } from '../../../../hooks/useInterval';
import { VideoLoaderResponse } from '../../../../routes/videoLoader';
import { Tracking } from '../../../../tracking/Tracking';
import { useVideoContext } from '../../../Video';
import { Segment, SegmentType } from '../../../Video/types/Segmets';
import { getAdjacentProgramFromPlaylist } from '../Buttons/Buttons.helper';
import { SkipActionButton } from './SkipActionButton';
import styles from './skipbutton.module.scss';

interface ISkipButtonProperties {
  otherControlsVisible: boolean;
  skipSegmentActive: Segment | object;
  triggerSeek: () => void;
}

const HIDE_INTERVAL_IN_MS = 10000;

export const SkipButton: FunctionComponent<ISkipButtonProperties> = ({
  otherControlsVisible,
  skipSegmentActive,
  triggerSeek,
}) => {
  // Gives focus back to time bar in case the skip button is still focused on unmount
  useReturnFocusOnUnmount(FOCUS_KEY_PLAYER_TIME_BAR, FOCUS_KEY_PLAYER_SKIP);

  const { setFocus } = useFocusable();
  const navigate = useCustomNavigate();
  const { fakeSeeking, isPlaying, videoData } = useVideoContext();
  const [hideInterval, setHideInterval] = React.useState<number | null>(null);
  const [hidden, setHidden] = React.useState<boolean>(false);
  const [label, setLabel] = React.useState<string>('');
  const { playlistData, videoId } = useLoaderData() as VideoLoaderResponse;
  const metadata = videoData?.attributes.metadata;
  const { t } = useTranslation();

  const handleAction = () => {
    if (metadata?.episodic && skipSegmentActive.type === SegmentType.END_CREDITS) {
      const nextProgramInPlaylist = getAdjacentProgramFromPlaylist({ playlistData, videoId, direction: 'next' });
      if (nextProgramInPlaylist && nextProgramInPlaylist?.providerId) {
        navigate(`${ROUTES.VERIFICATION}/${nextProgramInPlaylist?.providerId}`, { replace: true });
      }
    } else {
      Tracking.trackSkipButton(skipSegmentActive.type);
      triggerSeek();
      setFocus(FOCUS_KEY_PRIMARY);
    }
  };

  useEffect(() => {
    if (metadata?.episodic && skipSegmentActive.type === SegmentType.END_CREDITS) {
      setLabel(t('button__skip_endCredits'));
    }

    if (skipSegmentActive.type === SegmentType.SUMMARY) {
      setLabel(t('button__skip_summary'));
    }

    if (skipSegmentActive.type === SegmentType.OPENING_CREDITS) {
      setLabel(t('button__skip_openingCredits'));
    }
  }, [skipSegmentActive, t, metadata, setLabel]);

  useInterval(() => {
    setHidden(true);
    return () => {
      setHideInterval(null);
    };
  }, hideInterval);

  useEffect(() => {
    if (skipSegmentActive && fakeSeeking && !isPlaying) {
      setHidden(true);
    }
  }, [fakeSeeking, skipSegmentActive, isPlaying]);

  useEffect(() => {
    if (!otherControlsVisible && isPlaying) {
      setFocus(FOCUS_KEY_PLAYER_SKIP);
      setHideInterval(HIDE_INTERVAL_IN_MS);
    } else {
      setHideInterval(null);
    }
    setHidden(false);
  }, [otherControlsVisible, setFocus, isPlaying]);

  return (
    <div
      className={classNames(
        styles.skipButtonContainer,
        otherControlsVisible ? styles['skipButtonContainerWithControls'] : styles['skipButtonContainerWithoutControls'],
        hidden ? styles['skipButtonHide'] : '',
      )}
    >
      <SkipActionButton
        focusKey={FOCUS_KEY_PLAYER_SKIP}
        autoRestoreFocus={true}
        label={label}
        onEnterPress={handleAction}
        onArrowPress={(direction) => {
          switch (direction) {
            case 'right':
            case 'down':
              return true;
            case 'left':
              setFocus(FOCUS_KEY_PLAYER_TIME_BAR);
              return false;
            default:
              return false;
          }
        }}
      />
    </div>
  );
};

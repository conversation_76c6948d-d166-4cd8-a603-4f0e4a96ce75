import React, { ButtonHTMLAttributes } from 'react';

import { focusClassNames, IFocusState, withFocusable } from '../../../../focus';
import styles from './skipbutton.module.scss';

interface ISkipButtonProperties extends ButtonHTMLAttributes<HTMLButtonElement>, IFocusState {
  label: string;
  selected: boolean;
  onEnterPress: () => void;
}

export const SkipActionButton = withFocusable(
  React.forwardRef<HTMLButtonElement, ISkipButtonProperties>(
    ({ label, selected, className, style, ...properties }, reference) => (
      <button
        ref={reference}
        style={style}
        onClick={() => properties.onEnterPress()}
        className={focusClassNames(
          styles,
          properties,
          styles['skip-action-button'],
          {
            [styles['selected']]: selected,
          },
          className,
        )}
      >
        <span className={focusClassNames(styles, properties, styles.label)}>{label}</span>
      </button>
    ),
  ),
);

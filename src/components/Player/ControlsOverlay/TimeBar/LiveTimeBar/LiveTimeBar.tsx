import { ProgressBar } from '@components/ProgressBar/ProgressBar';
import { FunctionComponent } from 'react';

import styles from './live-time-bar.module.scss';
import { LiveLabel } from './LiveLabel/LiveLabel';

export const LiveTimeBar: FunctionComponent = () => {
  return (
    <div className={styles['live-time-bar']}>
      <LiveLabel className={styles.label} />
      <div className={styles['progress-bar-wrapper']}>
        <ProgressBar progressInPx={0} className={styles['progress-bar']} />
      </div>
    </div>
  );
};

import classNames from 'classnames';
import { useTranslation } from 'react-i18next';

import styles from './live-label.module.scss';

interface ILiveLabelProperties {
  className?: string;
}

export const LiveLabel = ({ className }: ILiveLabelProperties) => {
  const { t } = useTranslation();

  return (
    <div className={classNames(styles['live-label'], className)}>
      <div className={styles.cirle}></div>
      <span className={styles.label}>{(t('direct') as string).toLocaleUpperCase()}</span>
    </div>
  );
};

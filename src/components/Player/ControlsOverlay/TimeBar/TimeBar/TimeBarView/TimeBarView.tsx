import { useSeek, useVideoContext } from '@components/Video';
import { TEST_ID } from '@constants';
import useTimeout from '@hooks/useTimeout';
import formatSecondsAsString from '@util/formatSecondsAsString';
import classNames from 'classnames';
import React, { useEffect, useRef, useState } from 'react';

import { focusClassNames, IFocusState, withFocusable } from '../../../../../../focus';
import { CenteredLayout } from '../../../../../CenteredLayout/CenteredLayout';
import { IProgressBarProperties, ProgressBar } from '../../../../../ProgressBar/ProgressBar';
import styles from './time-bar-view.module.scss';

export interface ITimeBarViewProperties extends IFocusState, IProgressBarProperties {
  time: number;
  duration: number;
  className?: string;
  progress: number;
}

export const TimeBarView = withFocusable(
  React.forwardRef<HTMLDivElement, ITimeBarViewProperties>(
    ({ time, progress, duration, className, ...properties }, ref) => {
      const { videoData } = useVideoContext();
      const progressContainerRef = useRef(null);
      const [progressInPx, setProgressInPx] = useState(0);
      const { immediateScrubToPercentage } = useSeek();
      const [clickedAtPercentage, setClickedAtPercentage] = useState<number>(-1);
      const [fakeSeekingTimeout, setFakeSeekingTimeout] = useState<number | null>(null);
      const [fakeSeeking, setFakeSeeking] = useState<boolean>(false);

      useEffect(() => {
        if (progressContainerRef.current?.clientWidth) {
          !fakeSeeking && setProgressInPx(Math.round(progressContainerRef.current.clientWidth * (progress / 100)));
        }
      }, [progress, fakeSeeking]);

      const handleScrubBarClick = (event) => {
        if (fakeSeeking) return;
        if (videoData?.attributes?.live) return;

        const progressContainerLeft = progressContainerRef.current?.getBoundingClientRect().left || 0;
        const mouseX = Math.max(0, event.clientX - progressContainerLeft);
        const clickedAtPercentage = Math.min(Math.round((mouseX / progressContainerRef.current.clientWidth) * 100), 95);
        immediateScrubToPercentage(clickedAtPercentage);
        setProgressInPx(Math.round(progressContainerRef.current.clientWidth * (clickedAtPercentage / 100)));
        setFakeSeeking(true);
        setFakeSeekingTimeout(3000);
        setClickedAtPercentage(clickedAtPercentage);
      };

      useTimeout(() => {
        setFakeSeeking(false);
        setFakeSeekingTimeout(null);
      }, fakeSeekingTimeout);

      return (
        <div ref={ref} className={classNames(styles['time-bar-view'], className)}>
          <div className={styles['scrub-bar']} ref={progressContainerRef} onClick={handleScrubBarClick}>
            <ProgressBar className={styles['progress-bar']} progressInPx={progressInPx} {...properties}></ProgressBar>

            <span
              data-testid={TEST_ID.PLAYER.CURRENT_TIME}
              className={classNames(styles.time, styles.time1)}
              style={{ left: `${progressInPx}px` }}
            >
              {!fakeSeeking
                ? formatSecondsAsString(time)
                : formatSecondsAsString(duration * (clickedAtPercentage / 100))}
            </span>

            <div className={styles['thumb-container']} style={{ left: `${progressInPx}px` }}>
              <CenteredLayout className={focusClassNames(styles, properties, styles['thumb-rect'])}>
                <div className={styles.thumb} />
                {progress > 1 && <span className={styles['scrub-bar-line']} />}
              </CenteredLayout>
            </div>
          </div>

          <span data-testid={TEST_ID.PLAYER.DURATION} className={classNames(styles.time, styles.time2)}>
            {formatSecondsAsString(duration)}
          </span>
        </div>
      );
    },
  ),
);

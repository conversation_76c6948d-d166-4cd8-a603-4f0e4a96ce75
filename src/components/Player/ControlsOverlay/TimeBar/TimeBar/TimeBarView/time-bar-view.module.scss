@use '~styles/globals' as *;

$player-time-bar-thumb: px-to-rem(41);
$progress-bar-height: px-to-rem(9);
$half-the-progress-bar-height: calc(0.5 * $progress-bar-height);
$time-height: px-to-rem(42);
$half-the-time-height: calc(0.5 * $time-height);
$time-width: px-to-rem(120);
$half-the-time-width: calc(0.5 * $time-width);
$thumb-container-size: px-to-rem(62);
$half-the-thumb-container-size: calc(0.5 * $thumb-container-size);

.time-bar-view {
  .scrub-bar {
    display: inline-block;
    position: relative;
    width: calc(100% - $time-width);
    margin-right: px-to-rem(40);
  }

  .progress-bar {
    height: $progress-bar-height;
  }

  .thumb-container {
    position: relative;
    margin-left: calc($half-the-thumb-container-size * -1);
    margin-top: calc(($half-the-thumb-container-size + $half-the-progress-bar-height) * -1);
  }

  .thumb-rect {
    width: $thumb-container-size;
    height: $thumb-container-size;

    &.is-focused {
      background-color: $player-button-background-color;
      border: $player-button-border;
      border-color: $player-button-border-color;
    }
  }

  .thumb {
    height: $player-time-bar-thumb;
    width: $player-time-bar-thumb;
    border-radius: $player-time-bar-thumb;
    background-color: $white;
  }

  .scrub-bar-line {
    position: absolute;
    display: block;
    background-color: $player-button-border-color;
    width: px-to-rem(10);
    height: px-to-rem(8);
    top: px-to-rem(27);
    border-radius: $player-time-bar-thumb;
  }

  .time {
    font-size: px-to-rem(32);
    line-height: $time-height;
    color: $white;
    width: $time-width;
  }

  .time1 {
    position: absolute;
    margin-top: calc(($half-the-progress-bar-height + $half-the-thumb-container-size + $time-height) * -1);
    margin-left: calc($half-the-time-width * -1);
    text-align: center;
  }

  .time2 {
    position: absolute;
    top: calc($half-the-progress-bar-height - $half-the-time-height);
  }
}

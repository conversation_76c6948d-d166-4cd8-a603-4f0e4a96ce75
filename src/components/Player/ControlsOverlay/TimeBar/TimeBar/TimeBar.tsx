import { useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import { isMagentaTv } from '@util/platform';
import { FunctionComponent, useCallback, useEffect, useMemo, useState } from 'react';

import { KEY_DOWN, KEY_LEFT, KEY_RIGHT, KEY_UP } from '../../../../../../target/keys';
import { FOCUS_KEY_PLAYER_SKIP, FOCUS_KEY_PLAYER_TIME_BAR, FOCUS_KEY_PRIMARY } from '../../../../../focus';
import { useContinuousKeyPress } from '../../../../../hooks/useContinuousKeyPress';
import { VideoObjectEvent } from '../../../../../videoplayer/video-object/types';
import { useSeek, useVideoContext, useVideoControls } from '../../../../Video';
import { SKIP_SEEKING_TARGET_DEFAULT } from '../../../../Video/VideoContext';
import { SeekIconView } from './SeekIconView/SeekIconView';
import styles from './time-bar.module.scss';
import { TimeBarView } from './TimeBarView/TimeBarView';

interface ITimeBarProperties {
  hasSkipButton: boolean;
  shouldReset?: boolean;
}

export const TimeBar: FunctionComponent<ITimeBarProperties> = ({ shouldReset, hasSkipButton }) => {
  const { setFocus } = useFocusable();
  const { videoObject, fakeSeeking, setFakeSeeking, skipSeekingTarget, setSkipSeekingTarget, videoData } =
    useVideoContext();
  const { seeking, seekTime, scrubTo } = useSeek();
  const { isInterrupted } = useVideoControls();

  const [currentTime, setCurrentTime] = useState<number>(0);
  const [duration, setDuration] = useState<number>(0);
  const [focused, setFocused] = useState<boolean>(false);
  const [seekGap, setSeekGap] = useState<number>(0);
  const [direction, setDirection] = useState<string>('');
  const [time, setTime] = useState<number>(0);

  useEffect(() => {
    if (isMagentaTv()) {
      setCurrentTime(0);
      setFakeSeeking(false);
    }
  }, [videoData, setFakeSeeking]);

  // set video time and duration on time update
  useEffect(() => {
    const onTimeUpdate = (event: Event) => {
      const customEvent = event as CustomEvent;
      const { time, duration } = customEvent?.detail;
      if (!duration) return;

      !fakeSeeking && setCurrentTime(time);
      setDuration(duration);
    };

    videoObject?.on(VideoObjectEvent.TIMEUPDATE, onTimeUpdate);

    return () => {
      videoObject?.off(VideoObjectEvent.TIMEUPDATE, onTimeUpdate);
    };
  }, [fakeSeeking, videoObject]);

  useEffect(() => {
    setTime(seeking ? seekTime : currentTime);
  }, [seekTime, seeking, currentTime, videoObject, videoData]);

  useContinuousKeyPress({ duration, focused, shouldReset, seeking }, (event) => {
    if (isInterrupted) return;

    const { fakeDirection, seekJump, seekGap } = event;

    setFocus(FOCUS_KEY_PLAYER_TIME_BAR);
    setDirection(fakeDirection);
    setSeekGap(seekGap);
    setFakeSeeking(true);
    triggerSeek(fakeDirection, seekJump);
  });

  const triggerSeek = useCallback(
    (fakeDirection, seekJump) => {
      let targetSeekTime;
      if (fakeDirection === KEY_RIGHT) {
        targetSeekTime = time + seekJump;
        if (targetSeekTime > duration) targetSeekTime = duration - 10;
      }

      if (fakeDirection === KEY_LEFT) {
        targetSeekTime = time - seekJump;
        if (targetSeekTime < 0) targetSeekTime = 0;
      }

      setFakeSeeking(true);
      setCurrentTime(targetSeekTime);
      scrubTo(targetSeekTime);
    },
    [time, duration, scrubTo, setFakeSeeking, setCurrentTime],
  );

  useEffect(() => {
    if (!seeking) {
      setFakeSeeking(false);
    }
  }, [seeking, setFakeSeeking]);

  useEffect(() => {
    if (fakeSeeking && skipSeekingTarget !== SKIP_SEEKING_TARGET_DEFAULT) {
      setCurrentTime(skipSeekingTarget);
      setFakeSeeking(false);
      setSkipSeekingTarget(SKIP_SEEKING_TARGET_DEFAULT);
    }
  }, [skipSeekingTarget, fakeSeeking, setFakeSeeking, setSkipSeekingTarget]);

  const progress = useMemo(() => (time / duration) * 100, [time, duration]);

  return (
    <>
      {fakeSeeking && skipSeekingTarget === SKIP_SEEKING_TARGET_DEFAULT && (
        <SeekIconView seekStep={seekGap} direction={direction} />
      )}

      <TimeBarView
        time={time}
        duration={duration}
        progress={progress}
        onArrowPress={(direction) => {
          switch (direction) {
            case KEY_DOWN:
              !fakeSeeking && setFocus(FOCUS_KEY_PRIMARY);
              return false;
            case KEY_UP:
              if (!hasSkipButton) return false;
              setFocus(FOCUS_KEY_PLAYER_SKIP);
              return false;
            default:
              return false;
          }
        }}
        onBlur={() => {
          setFocused(false);
        }}
        onFocus={() => setFocused(true)}
        focusKey={FOCUS_KEY_PLAYER_TIME_BAR}
        className={styles['time-bar']}
      />
    </>
  );
};

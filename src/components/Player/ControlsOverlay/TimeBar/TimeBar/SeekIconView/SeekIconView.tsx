import ffw10 from '@assets/img/icon/player/ffw10.png';
import ffw30 from '@assets/img/icon/player/ffw30.png';
import ffw90 from '@assets/img/icon/player/ffw90.png';
import rwd10 from '@assets/img/icon/player/rwd10.png';
import rwd30 from '@assets/img/icon/player/rwd30.png';
import rwd90 from '@assets/img/icon/player/rwd90.png';
import { FunctionComponent } from 'react';

import { KEY_LEFT, KEY_RIGHT } from '../../../../../../../target/keys';
import styles from './seek-icon-view.module.scss';

const iconsFwd = [ffw10, ffw30, ffw90];
const iconsRwd = [rwd10, rwd30, rwd90];

interface SeekIconViewProps {
  seekStep: number;
  direction: typeof KEY_LEFT | typeof KEY_RIGHT;
}

export const SeekIconView: FunctionComponent<SeekIconViewProps> = ({ seekStep, direction }) => {
  let icon;
  if (direction === KEY_RIGHT) {
    icon = iconsFwd[seekStep];
  }

  if (direction === KEY_LEFT) {
    icon = iconsRwd[seekStep];
  }

  return (
    <div className={styles['seek-icon-view']}>
      <img src={icon} />
    </div>
  );
};

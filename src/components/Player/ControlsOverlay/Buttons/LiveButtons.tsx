import { FunctionComponent } from 'react';
import { useLoaderData } from 'react-router-dom';

import { FOCUS_KEY_PRIMARY, livePlayerButtonFocusGuard } from '../../../../focus';
import { VideoLoaderResponse } from '../../../../routes/videoLoader';
import { anonPersonalisationAllowed } from '../../../../util/anonPersonalisationAllowed';
import styles from './buttons.module.scss';
import { BackButton, FavouriteButton, InfoButton, NextButton, PrevButton } from './components';
import { ButtonsLayout } from './layout';

export const LiveButtons: FunctionComponent = () => {
  const { videoId } = useLoaderData() as VideoLoaderResponse;

  const Left = () => (
    <BackButton
      onArrowPress={(direction) => {
        switch (direction) {
          case 'right':
          case 'down':
            return true;
          default:
            return false;
        }
      }}
    />
  );

  const Middle = () => (
    <>
      <PrevButton onArrowPress={livePlayerButtonFocusGuard} />
      <NextButton onArrowPress={livePlayerButtonFocusGuard} />
    </>
  );

  const Right = () => (
    <>
      {anonPersonalisationAllowed() && (
        <FavouriteButton programId={videoId} onArrowPress={livePlayerButtonFocusGuard} />
      )}
      <InfoButton onArrowPress={livePlayerButtonFocusGuard} focusKey={FOCUS_KEY_PRIMARY} canPauseVideo={false} />
    </>
  );

  return (
    <ButtonsLayout className={styles.buttons}>
      <Left />
      <Middle />
      <Right />
    </ButtonsLayout>
  );
};

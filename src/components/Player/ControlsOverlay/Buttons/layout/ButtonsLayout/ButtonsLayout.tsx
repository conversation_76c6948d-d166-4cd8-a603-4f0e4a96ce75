import React, { FunctionComponent, ReactNode } from 'react';

import styles from './buttons-layout.module.scss';

export interface IButtonsLayoutProperties {
  children: ReactNode;
  className?: string;
}

export const ButtonsLayout: FunctionComponent<IButtonsLayoutProperties> = ({ children, className }) => {
  const [left, middle, right] = React.Children.toArray(children);
  return (
    <div className={className}>
      <div className={styles.left}>{left}</div>
      <div className={styles.middle}>{middle}</div>
      <div className={styles.right}>{right}</div>
    </div>
  );
};

import { ROUTES, TEST_ID } from '@constants';
import { useCustomNavigate } from '@hooks/useCustomNavigate';
import { VideoLoaderResponse } from '@routes/videoLoader';
import { useTranslation } from 'react-i18next';
import { useRemoteController } from 'react-remote-controller';
import { useLoaderData } from 'react-router-dom';
import { getKeyMap } from 'target';

import { FOCUS_KEY_PLAYER_NEXT_PROGRAM } from '../../../../../../focus';
import { getAdjacentProgramFromPlaylist } from '../../Buttons.helper';
import { Button } from '../Button/Button';
import { IButtonProperties } from '../IButtonProperties';
import styles from './next-button.module.scss';

export function NextButton({ onArrowPress }: IButtonProperties) {
  const navigate = useCustomNavigate();
  const { t } = useTranslation();
  const { playlistData, videoId } = useLoaderData() as VideoLoaderResponse;

  const nextProgramInPlaylist = getAdjacentProgramFromPlaylist({ playlistData, videoId, direction: 'next' });

  function handleNextVideo() {
    nextProgramInPlaylist &&
      nextProgramInPlaylist?.providerId &&
      navigate(`${ROUTES.VERIFICATION}/${nextProgramInPlaylist?.providerId}`, { replace: true });
  }

  useRemoteController<ReturnType<typeof getKeyMap>>({
    listenTo: {
      next: () => handleNextVideo(),
    },
  });

  return (
    <Button
      className={nextProgramInPlaylist ? '' : styles.hidden}
      isDisabled={!nextProgramInPlaylist}
      icon="next-content"
      label={t('player__next')}
      onEnterPress={handleNextVideo}
      onArrowPress={onArrowPress}
      focusKey={FOCUS_KEY_PLAYER_NEXT_PROGRAM}
      testId={TEST_ID.PLAYER.NEXT}
    />
  );
}

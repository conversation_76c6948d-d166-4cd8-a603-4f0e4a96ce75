import { useVideoContext, useVideoControls } from '@components/Video';
import { TEST_ID } from '@constants';
import { useModalContext } from '@providers/ModalContext';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useRemoteController } from 'react-remote-controller';
import { getKeyMap } from 'target';

import { defaultFocusGuard, FOCUS_KEY_PRIMARY } from '../../../../../../focus';
import { Button } from '../Button/Button';

export const PlayPauseButton = () => {
  const { isPlaying, controlsActive } = useVideoContext();
  const { modalOpen } = useModalContext();
  const { t } = useTranslation();
  const { pause, play, togglePlayPause } = useVideoControls();

  // Play/pause is available only when this button is present in the video controls
  useRemoteController<ReturnType<typeof getKeyMap>>({
    listenTo: {
      play: () => !modalOpen && play(),
      pause: () => !modalOpen && pause(),
      playPause: () => !modalOpen && togglePlayPause(),
    },
  });

  const handleEnterPress = useCallback(() => {
    controlsActive && togglePlayPause();
  }, [controlsActive, togglePlayPause]);

  const handleArrowPress = useCallback((direction: string): boolean => {
    return defaultFocusGuard(direction);
  }, []);

  const memoizedButton = useMemo(() => {
    const commonProps = {
      onEnterPress: handleEnterPress,
      onArrowPress: handleArrowPress,
      focusKey: FOCUS_KEY_PRIMARY,
      testId: TEST_ID.PLAYER.PLAY_PAUSE,
    };
    return isPlaying ? (
      <Button icon={'pause'} label={t('pause')} {...commonProps} />
    ) : (
      <Button icon={'play'} label={t('play')} {...commonProps} />
    );
  }, [handleArrowPress, handleEnterPress, isPlaying, t]);

  return memoizedButton;
};

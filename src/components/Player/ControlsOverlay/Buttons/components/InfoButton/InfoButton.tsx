import { TEST_ID } from '@constants';
import { useTranslation } from 'react-i18next';
import { useRemoteController } from 'react-remote-controller';
import { getKeyMap } from 'target';

import { FOCUS_KEY_PRIMARY } from '../../../../../../focus';
import { useModalContext } from '../../../../../../providers/ModalContext';
import { CollectionModal } from '../../../../../Modal/CollectionModal/CollectionModal';
import { ScrollableModal } from '../../../../../Modal/ScrollableModal/ScrollableModal';
import { useVideoContext, useVideoControls } from '../../../../../Video';
import { Button } from '../Button/Button';
import { IButtonProperties } from '../IButtonProperties';

interface IInfoButtonProperties extends IButtonProperties {
  canPauseVideo: boolean;
  focusKey: string;
}

export function InfoButton({ canPauseVideo, focusKey, onArrowPress }: IInfoButtonProperties) {
  const { t } = useTranslation();
  const { videoData, controlsActive } = useVideoContext();
  const { showModal } = useModalContext();
  const { interrupt, cancelInterrupt } = useVideoControls();

  useRemoteController<ReturnType<typeof getKeyMap>>({
    listenTo: {
      back: () => cancelInterrupt(),
      enter: () => cancelInterrupt(),
    },
  });

  return (
    <Button
      icon={'player-info'}
      label={t('moreInfos__short')}
      onEnterPress={() => {
        if (!controlsActive) return;
        if (videoData?.attributes?.metadata) {
          interrupt(canPauseVideo);
          showModal({
            content: (
              <ScrollableModal returnFocusKey={FOCUS_KEY_PRIMARY} closeOnEnter={true}>
                <CollectionModal testId={TEST_ID.PLAYER.INFO_MODAL} {...videoData.attributes.metadata} />
              </ScrollableModal>
            ),
          });
        }
      }}
      onArrowPress={onArrowPress}
      focusKey={focusKey}
      testId={TEST_ID.PLAYER.INFO}
    />
  );
}

import { ROUTES, TEST_ID } from '@constants';
import { useCustomNavigate } from '@hooks/useCustomNavigate';
import { useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import { VideoLoaderResponse } from '@routes/videoLoader';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useRemoteController } from 'react-remote-controller';
import { useLoaderData } from 'react-router-dom';
import { getKeyMap } from 'target';

import { FOCUS_KEY_PLAYER_PREVIOUS_PROGRAM, FOCUS_KEY_PRIMARY } from '../../../../../../focus';
import { getAdjacentProgramFromPlaylist } from '../../Buttons.helper';
import { Button } from '../Button/Button';
import { IButtonProperties } from '../IButtonProperties';
import styles from './prev-button.module.scss';

export function PrevButton({ onArrowPress }: IButtonProperties) {
  const navigate = useCustomNavigate();
  const { t } = useTranslation();
  const { playlistData, videoId } = useLoaderData() as VideoLoaderResponse;
  const { setFocus } = useFocusable();

  const previousProgramInPlaylist = getAdjacentProgramFromPlaylist({
    playlistData,
    videoId,
    direction: 'previous',
  });

  const handlePreviousVideo = useCallback(() => {
    previousProgramInPlaylist &&
      previousProgramInPlaylist?.providerId &&
      navigate(`${ROUTES.VERIFICATION}/${previousProgramInPlaylist?.providerId}`, { replace: true });
  }, [navigate, previousProgramInPlaylist]);

  const onEnterPress = useCallback(() => {
    setFocus(FOCUS_KEY_PRIMARY);
    handlePreviousVideo();
  }, [handlePreviousVideo, setFocus]);

  useRemoteController<ReturnType<typeof getKeyMap>>({
    listenTo: {
      prev: () => handlePreviousVideo(),
    },
  });

  return (
    <Button
      className={previousProgramInPlaylist ? '' : styles.hidden}
      isDisabled={!previousProgramInPlaylist}
      icon="previous-content"
      label={t('player__previous')}
      onEnterPress={onEnterPress}
      onArrowPress={onArrowPress}
      focusKey={FOCUS_KEY_PLAYER_PREVIOUS_PROGRAM}
      testId={TEST_ID.PLAYER.PREV}
    />
  );
}

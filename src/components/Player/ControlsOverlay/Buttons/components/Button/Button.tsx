import React from 'react';

import { focusClassNames, IFocusState, withFocusable } from '../../../../../../focus';
import { Icon } from '../../../../../Icon/Icon';
import styles from './button.module.scss';

type PlayerIconType =
  | 'play'
  | 'pause'
  | 'previous-content'
  | 'next-content'
  | 'language-versions'
  | 'player-favourites-add'
  | 'player-favourites-remove'
  | 'player-info';

interface IButtonProperties extends IFocusState {
  icon: PlayerIconType;
  label: string;
  onEnterPress: () => void;
  className?: string;
  testId?: string;
  testIdFocused?: boolean;
}

export const Button = withFocusable(
  React.forwardRef<HTMLButtonElement, IButtonProperties>(({ className, icon, label, ...properties }, ref) => (
    <div className={styles['button-wrapper']}>
      <button
        ref={ref}
        className={focusClassNames(styles, properties, styles.button, className)}
        onClick={() => {
          properties.onEnterPress();
        }}
        data-testid={properties.testId}
        data-testid-focused={properties.testIdFocused ? 'true' : undefined}
      >
        <div className={focusClassNames(styles, properties)}>
          <Icon type={icon} size="M" />
        </div>
      </button>
      {properties.isFocused && <div className={styles.label}>{label}</div>}
    </div>
  )),
);

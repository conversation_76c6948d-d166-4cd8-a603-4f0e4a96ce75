@use '~styles/globals' as *;

$label-width: px-to-rem(300);

.button-wrapper {
  display: inline-block;
  width: $player-button-dimension;
  vertical-align: top;
  text-align: center;

  button {
    width: $player-button-dimension;
    height: $player-button-dimension;
    background-color: transparent;
    border-color: transparent;

    &:hover,
    &.is-focused {
      background-color: $player-button-background-color;
      border: $player-button-border;
      border-color: $player-button-border-color;
    }
  }
}

.label {
  width: $label-width;
  margin-top: px-to-rem(8);
  margin-left: -(calc($label-width / 2 - $player-button-dimension / 2));
  font-size: px-to-rem(32);
  line-height: px-to-rem(33);
}

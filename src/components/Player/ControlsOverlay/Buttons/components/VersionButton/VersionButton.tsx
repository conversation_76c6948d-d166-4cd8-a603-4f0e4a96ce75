import { useVideoContext } from '@components/Video';
import { TEST_ID } from '@constants';
import { useTranslation } from 'react-i18next';

import { defaultFocusGuard, FOCUS_KEY_PLAYER_AUDIO_SUBTITLES } from '../../../../../../focus';
import { Button } from '../Button/Button';

export const VersionButton = () => {
  const { setVideoMenu } = useVideoContext();
  const { t } = useTranslation();

  return (
    <Button
      icon="language-versions"
      label={t('player__versions')}
      focusKey={FOCUS_KEY_PLAYER_AUDIO_SUBTITLES}
      onArrowPress={defaultFocusGuard}
      onEnterPress={() => {
        setVideoMenu('audio/subtitles');
      }}
      testId={TEST_ID.PLAYER.VERSION}
    />
  );
};

import { useVideoContext } from '@components/Video/VideoContext';
import { TEST_ID } from '@constants';
import { useContext } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';

import { FOCUS_KEY_PLAYER_FAVOURITE } from '../../../../../../focus';
import { BookmarksContext } from '../../../../../../providers/BookmarksContext';
import { Attributes, Bookmark } from '../../../../../../types';
import { Button } from '../Button/Button';
import { IButtonProperties } from '../IButtonProperties';

interface IFavouriteButton extends IButtonProperties {
  programId: string;
}

/**
 * Creates a bookmark from player config metadata
 * @param programId programId
 * @param metadata player config metadata
 * @returns Bookmark which can be added in the Bookmarks Context
 */
const getBookmarkFromVideoMetadata = (programId: string, isTrailer: boolean, attributes?: Attributes): Bookmark => {
  const metadata = attributes?.metadata;
  const imageUrl = metadata?.images?.[0]?.url;
  const formatDuration = (seconds = 0) => `${Math.ceil(seconds / 60)} Min.`;

  return {
    // if it's a trailer, get duration of the main content from 'durationReplay'
    duration: formatDuration(isTrailer ? metadata?.durationReplay?.seconds : metadata?.duration?.seconds),
    program_id: programId,
    // sets an image and removes its default size
    landscapeImage: imageUrl?.substring(0, imageUrl?.lastIndexOf('/') + 1) + '__SIZE__?type=TEXT',
    title: metadata?.title,
    subtitle: metadata?.subtitle,
  } as Bookmark;
};

export function FavouriteButton({ programId, onArrowPress }: IFavouriteButton) {
  const { t } = useTranslation();
  const { videoData } = useVideoContext();
  const location = useLocation();
  const bookmark = getBookmarkFromVideoMetadata(programId, location.state?.isTrailer, videoData?.attributes);
  const { addBookmark, removeBookmark, isBookmarked } = useContext(BookmarksContext);

  return (
    <Button
      icon={isBookmarked(bookmark) ? 'player-favourites-remove' : 'player-favourites-add'}
      label={isBookmarked(bookmark) ? t('delete') : t('add')}
      onEnterPress={() => {
        isBookmarked(bookmark) ? removeBookmark(bookmark) : addBookmark(bookmark);
      }}
      onArrowPress={onArrowPress}
      focusKey={FOCUS_KEY_PLAYER_FAVOURITE}
      testId={TEST_ID.PLAYER.FAVOURITE}
    />
  );
}

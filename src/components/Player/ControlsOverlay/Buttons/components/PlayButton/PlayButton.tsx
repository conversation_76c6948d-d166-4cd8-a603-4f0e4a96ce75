import PlayGraphic from '@assets/img/icon/player/Play.png';
import { useVideoControls } from '@components/Video';
import { useModalContext } from '@providers/ModalContext';
import React from 'react';
import { useRemoteController } from 'react-remote-controller';
import { getKeyMap } from 'target';

import { focusClassNames, IFocusState, withFocusable } from '../../../../../../focus';
import styles from './play-button.module.scss';

interface IPlayButtonProperties extends IFocusState {
  onEnterPress: () => void;
  className?: string;
}

export const PlayButton = withFocusable(
  React.forwardRef<HTMLButtonElement, IPlayButtonProperties>(({ className, ...properties }, ref) => {
    const { modalOpen } = useModalContext();
    const { play, togglePlayPause } = useVideoControls();

    useRemoteController<ReturnType<typeof getKeyMap>>({
      listenTo: {
        play: () => play(),
        playPause: () => !modalOpen && togglePlayPause(),
      },
    });

    return (
      <div className={styles['button-wrapper']}>
        <button
          ref={ref}
          className={focusClassNames(styles, properties, styles.button, className)}
          onClick={() => {
            properties.onEnterPress();
          }}
        >
          <div className={focusClassNames(styles, properties)}>
            <img src={PlayGraphic} className={styles.graphic} />
          </div>
        </button>
      </div>
    );
  }),
);

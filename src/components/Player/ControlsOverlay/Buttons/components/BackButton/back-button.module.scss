@use '~styles/globals' as *;

.back-button {
  display: table-cell;
  vertical-align: middle;
  height: $player-button-dimension;
  padding-left: px-to-rem(35);
  padding-right: px-to-rem(35);
  border: $player-button-border transparent;
  text-decoration: none;
  color: $white;

  &:hover,
  &.is-focused {
    background-color: $player-button-background-color;
    border-color: $player-button-border-color;
  }
}

.icon {
  margin-right: px-to-rem(6);
  margin-left: px-to-rem(-16);
  vertical-align: middle;
}

.label {
  font-size: px-to-rem(32);
  vertical-align: middle;
}

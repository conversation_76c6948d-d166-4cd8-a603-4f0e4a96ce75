import { Icon } from '@components/Icon/Icon';
import { useVideoContext } from '@components/Video';
import { EVENTS, TEST_ID } from '@constants';
import { useBackJourney } from '@features/backjourney/useBackJourney';
import EventBus from '@util/EventBus';
import React from 'react';
import { useTranslation } from 'react-i18next';

import { FOCUS_KEY_PLAYER_BACK, focusClassNames, IFocusState, withFocusable } from '../../../../../../focus';
import { IButtonProperties } from '../IButtonProperties';
import styles from './back-button.module.scss';

interface IBackButtonViewProperties extends IFocusState {
  onEnterPress: () => void;
  testIdFocused?: boolean;
}

const BackButtonView = withFocusable(
  React.forwardRef<HTMLAnchorElement, IBackButtonViewProperties>(({ onEnterPress, ...properties }, ref) => {
    const { t } = useTranslation();

    return (
      <a
        href="#"
        onClick={(event) => {
          event.preventDefault();
          onEnterPress();
        }}
        ref={ref}
        className={focusClassNames(styles, properties, styles['back-button'])}
        data-testid={TEST_ID.PLAYER.BACK}
        data-testid-focused={properties.testIdFocused ? 'true' : undefined}
      >
        <Icon type="arrow-left" size="M" className={styles.icon} />
        <span className={styles.label}>{t('back')}</span>
      </a>
    );
  }),
);

export const BackButton = ({ onArrowPress }: IButtonProperties) => {
  const { handleBack } = useBackJourney();
  const { videoObject } = useVideoContext();

  const onEnterPress = () => {
    // in order to prevent broadcast video from flashing we hide video element as early as possible
    videoObject?.hideVideoElement();
    EventBus.emit(EVENTS.PLAYER_BACK, {});
    handleBack();
  };

  return <BackButtonView focusKey={FOCUS_KEY_PLAYER_BACK} onEnterPress={onEnterPress} onArrowPress={onArrowPress} />;
};

import { PlayListData } from '../../../../types/PlayListResponseBody';
import { Item } from '../../../../types/PlayListResponseBody';

type Direction = 'next' | 'previous';

interface AdjacentProgramIdOptions {
  playlistData: PlayListData | undefined;
  videoId: string;
  direction: Direction;
}

export function getAdjacentProgramFromPlaylist({
  playlistData,
  videoId,
  direction,
}: AdjacentProgramIdOptions): Item | undefined {
  if (!playlistData) return;
  const { items } = playlistData.attributes || [];
  if (!items.length) return;

  const index = items.findIndex((item) => item.providerId === videoId);

  if (direction === 'next' && items[index + 1]) {
    return items[index + 1];
  }

  if (direction === 'previous' && items[index - 1]) {
    return items[index - 1];
  }
}

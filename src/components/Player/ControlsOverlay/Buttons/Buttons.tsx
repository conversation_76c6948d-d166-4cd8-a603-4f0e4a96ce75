import { FunctionComponent } from 'react';
import { useLoaderData } from 'react-router-dom';

import { hasVersions } from '../../../../features/streams/streams';
import { defaultFocusGuard, FOCUS_KEY_PLAYER_INFO } from '../../../../focus';
import { VideoLoaderResponse } from '../../../../routes/videoLoader';
import { Stream } from '../../../../types';
import { anonPersonalisationAllowed } from '../../../../util/anonPersonalisationAllowed';
import { useVideoContext } from '../../../Video';
import styles from './buttons.module.scss';
import {
  BackButton,
  FavouriteButton,
  InfoButton,
  NextButton,
  PlayPauseButton,
  PrevButton,
  VersionButton,
} from './components';
import { ButtonsLayout } from './layout';

export const Buttons: FunctionComponent = () => {
  const { videoObject } = useVideoContext();
  const { videoId } = useLoaderData() as VideoLoaderResponse;

  const Left = () => (
    <BackButton
      onArrowPress={(direction) => {
        switch (direction) {
          case 'up':
          case 'right':
          case 'down':
            return true;
          default:
            return false;
        }
      }}
    />
  );

  const Middle = () => (
    <>
      <PrevButton onArrowPress={defaultFocusGuard} />
      <PlayPauseButton />
      <NextButton onArrowPress={defaultFocusGuard} />
    </>
  );

  const Right = () => {
    const stream = videoObject?.getStream() as Stream;
    const hasStreamVersions = hasVersions(stream);

    return (
      <>
        {hasStreamVersions && <VersionButton />}
        {anonPersonalisationAllowed() && <FavouriteButton programId={videoId} onArrowPress={defaultFocusGuard} />}
        <InfoButton onArrowPress={defaultFocusGuard} focusKey={FOCUS_KEY_PLAYER_INFO} canPauseVideo={false} />
      </>
    );
  };

  return (
    <ButtonsLayout className={styles.buttons}>
      <Left />
      <Middle />
      <Right />
    </ButtonsLayout>
  );
};

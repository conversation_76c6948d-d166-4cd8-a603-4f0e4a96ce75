import { IVideoMenuNavigationItem, VideoMenuType } from '../../../../../types';
import { useAudioSubtitlesMenuData } from './audiosubtitles/useAudioSubtitlesMenuData';

export const useVideoMenuData = (type: VideoMenuType): IVideoMenuNavigationItem[] => {
  const getAudioSubtitlesNavigationData = useAudioSubtitlesMenuData();

  switch (type) {
    case 'audio/subtitles':
      return getAudioSubtitlesNavigationData();
    case 'quality/speed':
      console.warn('playback quality/speed not implemented');
      return [];
  }
};

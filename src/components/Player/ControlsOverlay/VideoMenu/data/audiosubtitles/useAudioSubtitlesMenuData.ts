import { useCallback } from 'react';

import i18n from '../../../../../../i18n';
import { IVideoMenuNavigationItem } from '../../../../../../types';
import { useVideoContext } from '../../../../../Video';
import { heading } from '../util';
import { getAudioMenuItems } from './audio';
import { getSubtitlesMenuItems } from './subtitles';

export function useAudioSubtitlesMenuData(): () => IVideoMenuNavigationItem[] {
  const { videoObject } = useVideoContext();

  const getAudioSubtitlesNavigationData = useCallback(() => {
    return [
      heading(i18n.t('player__audio')),
      ...getAudioMenuItems(videoObject),
      heading(i18n.t('player__subtitles')),
      ...getSubtitlesMenuItems(videoObject),
    ];
  }, [videoObject]);

  return getAudioSubtitlesNavigationData;
}

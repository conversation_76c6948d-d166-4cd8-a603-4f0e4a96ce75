import { logError } from '../../../../../../errors';
import { IVideoMenuNavigationItem, SubtitlesTrack } from '../../../../../../types';
import { resetSubtitlePreference } from '../../../../../../util/cookies';
import { VideoObject } from '../../../../../../videoplayer';
import { label, setSubtitleForCookies } from './util';

export const getAudioMenuItems = (videoObject?: VideoObject): IVideoMenuNavigationItem[] => {
  const audioTracks: window.dashjs.MediaInfo[] = videoObject?.getAvailableAudioTracks();

  let currentAudioTrack: window.dashjs.MediaInfo;
  try {
    currentAudioTrack = videoObject?.getCurrentAudioTrack();
  } catch (e) {
    logError(new Error('cannot retrieve the current audio track'), 'WARNING');
  }

  /**
   * When switching audio tracks, if forced subtitle was active for the previous track, we need to switch to the forced subtitle for the new track
   * If no forced subtitle is available for the new track we disable subtitles and no previous selected subtitles we do nothing
   * If the current selected subtitle is not forced then we are keeping it
   */
  const updateSubtitlesForNewAudioTrack = (audioTrack: window.dashjs.MediaInfo) => {
    const currentSubtitles: SubtitlesTrack = videoObject?.getCurrentSubtitles();
    if (currentSubtitles && currentSubtitles.roles?.includes('forced-subtitle')) {
      const availableSubtitles = videoObject?.getAvailableSubtitles();

      const availableForcedSubsForSelectedAudio: SubtitlesTrack = availableSubtitles.find(
        (subtitle) => subtitle.roles.includes('forced-subtitle') && subtitle.lang === audioTrack.lang,
      );

      if (availableForcedSubsForSelectedAudio) {
        const foundIndex = availableSubtitles.indexOf(availableForcedSubsForSelectedAudio);
        foundIndex !== -1 && setSubtitleForCookies(availableSubtitles[foundIndex]);
        videoObject?.setCurrentSubtitles(foundIndex);
      } else {
        resetSubtitlePreference();
        videoObject?.setCurrentSubtitles(-1);
      }
    }
  };

  return (
    audioTracks?.map((audioTrack: window.dashjs.MediaInfo) => ({
      heading: false,
      label: label(audioTrack),
      selected: audioTrack === currentAudioTrack,
      update: () => {
        videoObject?.setCurrentAudioTrack(audioTrack);
        updateSubtitlesForNewAudioTrack(audioTrack);
      },
    })) || []
  );
};

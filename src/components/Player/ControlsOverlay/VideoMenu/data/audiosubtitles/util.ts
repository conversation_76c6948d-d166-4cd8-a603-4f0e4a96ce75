import { AudioTrack, SubtitlesTrack } from '../../../../../../types';
import { getSubtitlePreference, setSubtitlePreference } from '../../../../../../util/cookies';

export const label = (item: AudioTrack | SubtitlesTrack) => item?.labels[0]?.text;

export const setSubtitleForCookies = (subtitle: SubtitlesTrack) => {
  subtitle &&
    setSubtitlePreference(
      JSON.stringify({
        lang: subtitle.lang,
        role: subtitle.roles?.includes('forced-subtitle') ? 'forced-subtitle' : 'subtitle',
        label: label(subtitle),
      }),
    );
};

export const parseSavedSubtitle = () => {
  const savedSubtitle = getSubtitlePreference();
  return savedSubtitle ? JSON.parse(savedSubtitle) : null;
};

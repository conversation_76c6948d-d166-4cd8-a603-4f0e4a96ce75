import { logError } from '../../../../../../errors';
import { readAppData } from '../../../../../../features/appdata/appdata';
import i18n from '../../../../../../i18n';
import { IVideoMenuNavigationItem, SubtitlesTrack } from '../../../../../../types';
import { DASHJS_TEXT_SETTINGS_COOKIE, resetSubtitlePreference } from '../../../../../../util/cookies';
import { VideoObject } from '../../../../../../videoplayer';
import { label, parseSavedSubtitle, setSubtitleForCookies } from './util';

const areTracksEqual = (t1: SubtitlesTrack, t2: window.dashjs.MediaInfo | null): boolean => {
  if (!t1 || !t2) {
    return t1 === t2;
  }

  let sameLang;
  let sameRoles;

  const dashTextSettings = readAppData(DASHJS_TEXT_SETTINGS_COOKIE);
  const dashTextSettingsObj = dashTextSettings ? JSON.parse(dashTextSettings)?.settings : null;

  if (dashTextSettingsObj) {
    sameLang = t1.lang === dashTextSettingsObj.lang;
    sameRoles = t1.roles?.toString().includes(dashTextSettingsObj.role?.toString());
  } else {
    sameLang = t1.lang === t2.lang;
    sameRoles = t1.roles?.toString() === t2.roles?.toString();
  }
  return sameLang && sameRoles;
};

const findTrueSubtitleIndex = (availableSubtitles: SubtitlesTrack[], currentSavedSubtitle): number =>
  availableSubtitles.findIndex((subtitle) => {
    return subtitle.lang === currentSavedSubtitle.lang && label(subtitle) === currentSavedSubtitle.label;
  });

const shouldNoneBeSelected = (videoObject: VideoObject | undefined): boolean => {
  const currentSavedSubtitle = parseSavedSubtitle();
  if (!currentSavedSubtitle) return true;

  if (findTrueSubtitleIndex(videoObject?.getAvailableSubtitles(), currentSavedSubtitle) === -1) return true;

  return currentSavedSubtitle.role === 'forced-subtitle';
};

export const getSubtitlesMenuItems = (videoObject?: VideoObject): IVideoMenuNavigationItem[] => {
  const availableSubtitles: SubtitlesTrack[] = videoObject?.getAvailableSubtitles();
  const currentSavedSubtitle = parseSavedSubtitle();
  let trueSubtitleIndex = -1;
  if (currentSavedSubtitle) {
    trueSubtitleIndex = findTrueSubtitleIndex(availableSubtitles, currentSavedSubtitle);
  }

  const trueSubtitles =
    availableSubtitles.map((subtitle, index) => {
      return {
        heading: false,
        label: label(subtitle),
        lang: subtitle.lang,
        role: subtitle.roles,
        selected: areTracksEqual(subtitle, availableSubtitles[trueSubtitleIndex] || null),
        update: () => {
          setSubtitleForCookies(subtitle);
          videoObject?.setCurrentSubtitles(index);
        },
        index,
      };
    }) || [];

  const forcedSubtitles = trueSubtitles.filter((subtitle) => subtitle?.role?.includes('forced-subtitle'));
  const captionOrSubtitles = trueSubtitles.filter(
    (subtitle) => subtitle?.role?.includes('subtitle') || subtitle?.role?.includes('caption'),
  );

  const noSubtitlesItem = {
    heading: false,
    label: i18n.t('player__subtitles_off'),
    selected: shouldNoneBeSelected(videoObject),
    update: () => {
      try {
        const currentAudioTrack: window.dashjs.MediaInfo = videoObject?.getCurrentAudioTrack();
        const defaultForced = forcedSubtitles.find((subtitle) => subtitle.lang === currentAudioTrack.lang);
        defaultForced ? videoObject?.setCurrentSubtitles(defaultForced.index) : videoObject?.setCurrentSubtitles(-1);
        resetSubtitlePreference();
      } catch (e) {
        videoObject?.setCurrentSubtitles(-1);
        logError(new Error('cannot retrieve the current audio track'), 'WARNING');
      }
    },
  };

  return [noSubtitlesItem as IVideoMenuNavigationItem, ...captionOrSubtitles];
};

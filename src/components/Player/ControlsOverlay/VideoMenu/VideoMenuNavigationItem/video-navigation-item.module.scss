@use '~styles/globals' as *;

$height: px-to-rem(72);

.video-navigation-item {
  color: $grey;
  min-height: $height;
  font-size: px-to-rem(32);
  padding-left: px-to-rem(40);
  display: table;
  padding-top: px-to-rem(12);
  padding-bottom: px-to-rem(12);

  h3 {
    font-family: $font-family-regular;
    font-weight: 400;
  }

  &.is-focused {
    color: $white;
    background-color: $middle-grey;
  }

  &.is-active {
    color: $white;
  }
}

.icon-wrapper {
  display: table-cell;
  vertical-align: middle;
}

.icon {
  position: relative;
  display: block;
  vertical-align: middle;

  &.hidden {
    visibility: hidden;
  }
}

.label {
  line-height: px-to-rem(42);
  padding-left: px-to-rem(10);
  display: table-cell;
  vertical-align: middle;

  &.is-heading {
    font-family: $font-family-bold;
    font-size: px-to-rem(40);
    padding-top: px-to-rem(20);
    color: $white;
  }
}

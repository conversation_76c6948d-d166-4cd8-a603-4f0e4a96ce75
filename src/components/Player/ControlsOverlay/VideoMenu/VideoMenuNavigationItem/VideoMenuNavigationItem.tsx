import classNames from 'classnames';
import React from 'react';

import { focusClassNames, IFocusState, withFocusable } from '../../../../../focus';
import { IVideoMenuNavigationItem } from '../../../../../types';
import { Icon } from '../../../../Icon/Icon';
import styles from './video-navigation-item.module.scss';

export type IVideoMenuNavigationItemProperties = IVideoMenuNavigationItem & IFocusState;

const VideoMenuItem = React.forwardRef<HTMLAnchorElement, IVideoMenuNavigationItemProperties>(
  ({ heading, label, selected, ...properties }, ref) => (
    <a
      href="#"
      ref={ref}
      className={focusClassNames(styles, properties, styles['video-navigation-item'])}
      onClick={(event) => {
        event.preventDefault();
        properties.onEnterPress();
      }}
    >
      <div className={styles['icon-wrapper']}>
        <Icon type="check" size="S" className={classNames(styles.icon, { [styles['hidden']]: !selected })} />
      </div>
      <h3 className={classNames(styles.label, { [styles['is-heading']]: heading })}>{label}</h3>
    </a>
  ),
);

export const VideoMenuHeading = VideoMenuItem; // headings cannot be focused
export const VideoMenuOption = withFocusable(VideoMenuItem); // options can be focused

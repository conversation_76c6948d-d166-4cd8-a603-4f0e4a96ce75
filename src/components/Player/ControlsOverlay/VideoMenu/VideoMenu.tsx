import { VerticalList } from '@components/List/VerticalList';
import { TEST_ID } from '@constants';
import { logError } from '@errors/errorLogging';
import { useSkipSegment } from '@hooks/useSkipSegment';
import { useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import { Tracking } from '@tracking/Tracking';
import { isObjectEmpty } from '@util/object';
import { remToPx } from '@util/pxToRem';
import React, { useEffect, useState } from 'react';

import { FOCUS_KEY_PLAYER_SKIP, FOCUS_KEY_PRIMARY } from '../../../../focus';
import { VideoMenuType } from '../../../../types';
import { useVideoContext } from '../../../Video';
import { useVideoMenuData } from './data/useVideoMenuData';
import styles from './video-menu.module.scss';
import { VideoMenuHeading, VideoMenuOption } from './VideoMenuNavigationItem/VideoMenuNavigationItem';

interface IVideoMenuProperties {
  type: VideoMenuType;
}

/**
 * This will make the vertical list "peek" when the last item is focused
 */
const VIDEO_MENU_VERTICAL_BOTTOM_PEEK_SIZE = remToPx(2);

export const VideoMenu: React.FC<IVideoMenuProperties> = ({ type }) => {
  const { setFocus } = useFocusable();
  const { skipSegmentActive } = useSkipSegment();
  const [selectedItemIndex, setSelectedItemIndex] = useState(0);
  const navigationData = useVideoMenuData(type);
  const { videoObject, setVideoMenu } = useVideoContext();

  const navigationItems =
    navigationData &&
    navigationData.map((itemData, index) =>
      itemData.heading ? (
        // eslint-disable-next-line react/no-array-index-key
        <VideoMenuHeading {...itemData} key={index} />
      ) : (
        <VideoMenuOption
          {...itemData}
          // eslint-disable-next-line react/no-array-index-key
          key={index}
          focusOnMount={index === 1}
          onFocus={() => setSelectedItemIndex(index)}
          isActive={itemData.selected}
          onEnterPress={() => {
            itemData.update();
            setVideoMenu(undefined);

            !isObjectEmpty(skipSegmentActive) ? setFocus(FOCUS_KEY_PLAYER_SKIP) : setFocus(FOCUS_KEY_PRIMARY);
          }}
          onArrowPress={(direction) => {
            switch (direction) {
              case 'right':
              case 'down':
                return true;
              case 'up':
                return index > 1 ? true : false;
              default:
                return false;
            }
          }}
        />
      ),
    );

  useEffect(() => {
    return () => {
      try {
        Tracking.trackAudioSubtitleChange({
          audio: videoObject?.getCurrentAudioTrack(),
          text: videoObject?.getCurrentSubtitles(),
        });
      } catch (e) {
        logError(e, 'WARNING');
      }
      setVideoMenu(undefined);
    };
  }, [setVideoMenu, videoObject]);

  return (
    <VerticalList
      testId={TEST_ID.PLAYER.MENU}
      currentItem={selectedItemIndex}
      scrollToEnd={false}
      scrollStartIndex={7}
      className={styles['video-menu']}
      bottomPeekSize={VIDEO_MENU_VERTICAL_BOTTOM_PEEK_SIZE}
    >
      {navigationItems}
    </VerticalList>
  );
};

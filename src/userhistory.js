import { SsoRequestUnauthorizedError } from '@errors/SsoRequestUnauthorizedError';
import { readAppData, writeAppData } from '@features/appdata/appdata';
import { anonPersonalisationAllowed } from '@util/anonPersonalisationAllowed';
import { COOKIE_ANON_TOKEN, COOKIE_USER_TOKEN } from '@util/cookies';
import { getSsoUrl } from '@util/getSsoUrl';
import { userHistoryScheduler } from '@util/userHistoryScheduler';
import i18n from 'i18next';

import { isErrorOfType, logError } from './errors';

/**
 * token endpoint is used to get both anonymous and user tokens
 * the token type returned is determined by the form data sent in the request body
 * @param formData
 * @returns {Promise<Response>}
 */
async function requestToken(formData) {
  const url = getSsoUrl('/token');

  const headers = {
    accept: 'application/json',
    client: 'hbbtv',
    'accept-language': 'en',
    authorization: 'DRNINjAqS8yihOVQb6DwMW7XjtzpmfDK',
    'Content-Type': 'application/x-www-form-urlencoded',
  };

  try {
    return await fetch(url, {
      method: 'POST',
      headers,
      body: formData,
    });
  } catch (error) {
    console.log('[userhistory][requestToken]', error);
    throw error;
  }
}

const UserHistory = function (proxy, debug) {
  // eslint-disable-next-line @typescript-eslint/no-this-alias
  var self = this;

  self.proxy = false;

  if (proxy === true) {
    self.proxy = true;
  }

  self.debug = false;

  if (debug === true) {
    self.debug = true;
  }

  self.history = null;

  self.expireCookie = 152; /* 6 months */

  self.url = {
    favorites: getSsoUrl('/favorites'),
    magazine: getSsoUrl('/magazines'),
    clearHistory: getSsoUrl('/lastvieweds/purge'),
    history: getSsoUrl('/lastvieweds'),
    history_proxy: 'http://dev56.teravolt.it/hab/arte_proxy/history.php?',
    personalzone: getSsoUrl('/personalzone'),
    nextEpisode: getSsoUrl('/nextEpisode'),
    nextEpisode_proxy: 'http://dev56.teravolt.it/hab/arte_proxy/next_episode.php?',
    meData: getSsoUrl('/me/data/'),
    meData_proxy: 'http://dev56.teravolt.it/hab/arte_proxy/me_data.php?',
    me: getSsoUrl('/me'),
  };

  self.userTokenExist = function (ifNot) {
    if (!self.getToken()) {
      typeof ifNot === 'function' && ifNot();
      return false;
    }
    return true;
  };

  var stringConstructor = 'test'.constructor;
  var arrayConstructor = [].constructor;
  var objectConstructor = {}.constructor;

  self.whatIsIt = function (object) {
    if (object === null) {
      return 'null';
    } else if (object === undefined) {
      return 'undefined';
    } else if (object.constructor === stringConstructor) {
      return 'String';
    } else if (object.constructor === arrayConstructor) {
      return 'Array';
    } else if (object.constructor === objectConstructor) {
      return 'Object';
    } else {
      return "don't know";
    }
  };

  self.addToFavourites = async function (programId) {
    if (!self.userTokenExist()) {
      throw new Error('User token does not exist.');
    }

    const url = this.url.favorites;

    if (self.debug) {
      console.log('[userhistory] addToFavourites ' + programId);
    }

    const auth_token = 'Bearer ' + self.getToken();
    const headersData = {
      client: 'hbbtv',
      authorization: auth_token,
    };

    const rawData = `programId=${programId}&language=${self.getLanguage()}`;

    const response = await fetch(url, {
      method: 'PUT',
      body: rawData,
      headers: headersData,
    });

    if (response.ok) {
      return await response.json();
    } else {
      switch (response.status) {
        case 401:
          throw new SsoRequestUnauthorizedError();
        default:
          logError('Failed to add a bookmark.', 'WARNING');
      }
    }
  };

  self.removeFromFavourites = async function (programId) {
    if (!self.userTokenExist()) {
      throw new Error('User token does not exist.');
    }

    const url = `${this.url.favorites}/${programId}`;

    if (self.debug) {
      console.log('[userhistory] removeFromFavourites ' + programId);
    }

    const auth_token = 'Bearer ' + self.getToken();
    const headersData = {
      client: 'hbbtv',
      authorization: auth_token,
    };

    const response = await fetch(url, {
      method: 'DELETE',
      headers: headersData,
    });

    if (response.ok) {
      return await response.json();
    } else {
      switch (response.status) {
        case 401:
          throw new SsoRequestUnauthorizedError();
        default:
          logError('Failed to remove a bookmark.', 'WARNING');
      }
    }
  };

  self.getMe = async function () {
    if (!self.userTokenExist()) {
      throw new Error('Cannot get /me. User token does not exist.');
    }

    let url = this.url.me;

    if (self.debug) {
      console.log('[userhistory] getMe ' + url);
    }

    const auth_token = 'Bearer ' + self.getToken();
    const headersData = {
      client: 'hbbtv',
      authorization: auth_token,
    };

    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: headersData,
      });

      return await response.json();
    } catch (error) {
      throw new Error(`/me request failed`);
    }
  };

  self.getMeData = async function () {
    if (!self.userTokenExist()) {
      throw new Error('Cannot get MeData. User token does not exist.');
    }

    let url = self.proxy ? this.url.meData_proxy : this.url.meData;
    url += self.getLanguage();

    if (self.debug) {
      console.log('[userhistory] getMeData ' + url);
    }

    const auth_token = 'Bearer ' + self.getToken();
    const headersData = {
      client: 'hbbtv',
      authorization: auth_token,
    };

    const response = await fetch(url, {
      method: 'GET',
      headers: headersData,
    });

    if (response.ok) {
      return await response.json();
    } else {
      switch (response.status) {
        case 401:
          throw new SsoRequestUnauthorizedError();
        default:
          logError('MeData request failed.', 'WARNING');
      }
    }
  };

  self.getNextEpisode = async function (programId) {
    if (!self.userTokenExist()) {
      throw new Error('User token does not exist.');
    }

    let url = self.proxy ? this.url.nextEpisode_proxy : this.url.nextEpisode;
    url += '/' + self.getLanguage() + '/' + programId;

    if (self.debug) {
      console.log('[userhistory] getNextEpisode ' + url);
    }

    const auth_token = 'Bearer ' + self.getToken();
    const headersData = {
      client: 'hbbtv',
      authorization: auth_token,
    };

    const response = await fetch(url, {
      method: 'GET',
      headers: headersData,
    });

    if (response.ok) {
      return await response.json();
    } else {
      switch (response.status) {
        case 401:
          throw new SsoRequestUnauthorizedError();
        default:
          logError('Failed to get the next episode.', 'WARNING');
      }
    }
  };

  self.clearHistory = async function () {
    if (!self.userTokenExist()) {
      return;
    }

    const url = this.url.clearHistory;

    const auth_token = 'Bearer ' + self.getToken();
    const headersData = {
      client: 'hbbtv',
      authorization: auth_token,
      'Content-Type': 'application/json',
    };

    const errorMessage = 'Failed to clear history';

    fetch(url, {
      method: 'DELETE',
      headers: headersData,
    })
      .then((response) => {
        // ok
        if (response.ok) {
          response.json().then((data) => {
            if (typeof callback === 'function') {
              callback(data);
            }
          });
        } else {
          // error
          switch (response.status) {
            case 401:
              throw new SsoRequestUnauthorizedError();
            default:
              logError(new Error(`${errorMessage} - response status ${response.status}`), 'WARNING');
              break;
          }
        }
      })
      .catch((e) => {
        logError(new Error(`${errorMessage} - the endpoint could not be reached`), 'WARNING');
        if (typeof failback === 'function') {
          failback(e);
        }
      });
  };

  self.parseUserHistory = function (response, keepBackend) {
    if (!response || !response.data) {
      return;
    }

    var backendList = response.data && response.data.length ? response.data : [];
    var list = [];
    if (self.whatIsIt(self.history) === 'Array' && self.history.length) {
      list = self.history.concat(backendList);

      var _programIdExist = function (id, history) {
        for (var i = 0; i < history.length; i++) {
          if (history[i].programId === id) {
            return i;
          }
        }
        return -1;
      };

      var ids = [];
      // var onlyInBackend = [],
      // 	onlyInLocal = [];
      for (var i = 0; i < list.length; i++) {
        var item = list[i];
        var id = item.programId;

        // FOR DEBUG
        var idInLocalHis = _programIdExist(id, self.history),
          idInBackendHis = _programIdExist(id, backendList);
        if (idInLocalHis > -1 && idInBackendHis > -1) {
          var localVersion = self.history[idInLocalHis],
            backendVersion = backendList[idInBackendHis];
          var isDifferent = localVersion.lastviewed.timecode !== backendVersion.lastviewed.timecode;
          if (isDifferent && keepBackend) {
            localVersion.lastviewed = backendVersion.lastviewed;
          }
        }

        if (ids.indexOf(id) > -1) {
          list.splice(i, 1);
          i--;
        } else {
          ids.push(id);
        }
      }
    } else {
      list = backendList;
    }

    if (list.length) {
      var cdata = [];
      list.forEach(function (item) {
        cdata.push({
          programId: item.programId,
          lastviewed: {
            timecode: item.lastviewed.timecode,
            progress: item.lastviewed.progress,
          },
        });
      });
      self.history = cdata;
    } else {
      self.history = null;
    }
  };

  self.requestAddHistory = function (programId, time, callback, failback) {
    if (!self.userTokenExist()) {
      return;
    }

    if (!self.proxy) {
      var url = this.url.history;
    } else {
      var url = this.url.history_proxy;
    }

    var auth_token = 'Bearer ' + self.getToken();
    var headersData = {
      client: 'hbbtv',
      authorization: auth_token,
      'Content-Type': 'application/json',
    };
    var formData = '{ "programId": "' + programId + '", "timecode": ' + time + '}';

    // needed for error handling
    const errorMessage = 'Failed to save a resume position';
    fetch(url, {
      method: 'PUT',
      headers: headersData,
      body: formData,
    })
      .then((response) => {
        // ok
        if (response.ok) {
          response.json().then((data) => {
            userHistoryScheduler.queueItemRemoved(programId);
            if (typeof callback === 'function') {
              callback(data);
            }
          });
        } else {
          // error
          switch (response.status) {
            case 401:
              throw new SsoRequestUnauthorizedError();
            case 429:
              logError(new Error(`${errorMessage} - too many requests`), 'WARNING');
              break;
            default:
              logError(new Error(`${errorMessage} - response status ${response.status}`), 'WARNING');
              break;
          }
        }
      })
      .catch((e) => {
        const actionableErrorTypes = [SsoRequestUnauthorizedError];
        logError(new Error(errorMessage), 'WARNING');

        // Handle known error types. Ignore other errors e.g CORS errors
        if (typeof failback === 'function' && isErrorOfType(e, actionableErrorTypes)) {
          failback(e);
        }
      });
  };

  self.setUserHistory = function (program_id, position, duration, failback) {
    if (self.getToken()) {
      if (program_id && position) {
        var timeProgress;
        if (position) {
          timeProgress = position;
          if ((position + 2000) / 60 >= duration) {
            timeProgress = '"complete"';
          }
        } else {
          return;
        }

        if (self.debug) {
          console.log(
            '[userhistory] setUserHistory - program_id: ' +
              program_id +
              ' position ' +
              position +
              ' duration ' +
              duration +
              ' = progress ' +
              timeProgress,
          );
        }

        function callback(response) {
          self.parseUserHistory(response, true);
        }

        self.requestAddHistory(program_id, timeProgress, callback, failback);
      } else {
        if (self.debug) {
          console.log('[userhistory] ERROR: setUserHistory missing userToken');
        }
      }
    }
  };

  self.getToken = function () {
    try {
      return readAppData(COOKIE_USER_TOKEN) || readAppData(COOKIE_ANON_TOKEN);
    } catch (e) {
      if (self.debug) {
        console.log('[userhistory] ERROR getToken :' + e);
      }
      return undefined;
    }
  };

  self.requestUserToken = async function (code) {
    let formData = `grant_type=urn%3Aassertion%3Atvlogin&assertion=${code}`;
    const anonymousToken = readAppData(COOKIE_ANON_TOKEN);

    if (!!anonymousToken) {
      formData += '&anonymous_token=' + anonymousToken;
    }
    return requestToken(formData);
  };

  self.requestAnonymousToken = async function () {
    const formData = 'grant_type=anonymous';
    return requestToken(formData);
  };

  self.getAnonymousToken = async function () {
    if (self.debug) {
      console.log('[history] getAnonymousToken');
    }

    if (!anonPersonalisationAllowed()) {
      return;
    }

    const userToken = self.getToken();

    if (userToken) {
      return;
    }

    try {
      const response = await self.requestAnonymousToken();
      const data = await response.json();
      const { access_token: anonymousToken } = data;
      writeAppData(COOKIE_ANON_TOKEN, anonymousToken, self.expireCookie);
      return;
    } catch (error) {
      throw new Error('failed to retrieve anonymous token');
    }
  };

  self.getLanguage = function () {
    return i18n.language;
  };
};

export { UserHistory };

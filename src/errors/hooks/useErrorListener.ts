import EventBus, { EventData } from '@util/EventBus';
import { useEffect } from 'react';

import { EVENTS } from '../..//constants';
import { CustomError, useErrors } from '..';

/**
 * Listens to errors reported via EventBus and throws them further to be caught in the context of React.
 * Without this approach, react has no knowledge of errors happening outside of react.
 */
export const useErrorListener = () => {
  const { setError } = useErrors();

  useEffect(() => {
    function handleError<T>(eventData: EventData<T>) {
      const data: unknown = eventData[EVENTS.ERROR];
      const error = data as CustomError;
      setError(error);
    }

    EventBus.on(EVENTS.ERROR, handleError);
    return () => {
      EventBus.off(EVENTS.ERROR, handleError);
    };
  }, [setError]);
};

import { createErrorBase } from './ErrorConstructor';
import { IError } from './IError';

class LiveStreamUnavailable<T = unknown> implements IError<T> {
  name: string;
  message: string;
  private readonly params: T | undefined;

  private readonly instance: IError<T>;

  constructor(availability: T, localError: boolean = false) {
    const BaseErrorClass = createErrorBase(localError);
    // @ts-expect-error TypeScript cannot infer the type due to dynamic class instantiation
    this.instance = new BaseErrorClass('livestream unavailable', availability);

    this.name = this.instance.name;
    this.message = this.instance.message;
    this.params = this.instance.getParams();
  }

  getParams(): T | undefined {
    return this.params;
  }
}

export { LiveStreamUnavailable };

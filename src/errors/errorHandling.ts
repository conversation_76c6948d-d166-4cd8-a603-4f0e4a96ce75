import { PageResponseBody } from '../types';
import { LegalModalResponseBody } from '../types/LegalModalResponseBody';
import { InformationTitlesResponse } from '../types/SettingsResponseBody';
import { InvalidPageError } from './InvalidPageError';

function responseToJson<T>(response: T): T {
  return typeof response === 'string' ? JSON.parse(response) : response;
}

export const failOnPageResponseError = (pageResponse: PageResponseBody | null) => {
  const response = responseToJson(pageResponse);

  switch (true) {
    case response?.type === 'error':
    case response?.id !== 'tv_guide' && (!response?.zones || response?.zones.length === 0):
      throw new InvalidPageError(response?.message || '');
    default:
  }
};

type InformationResponseType = InformationTitlesResponse | LegalModalResponseBody;

export const failOnInformationPageResponseError = (informationPageResponse: InformationResponseType) => {
  const response = responseToJson(informationPageResponse);
  if (response.type === 'error') {
    throw new InvalidPageError(response.message || '');
  }
};

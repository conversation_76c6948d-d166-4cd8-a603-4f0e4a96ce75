import { IPageResults } from '@apptypes/pagination';
import { FeatureFlags } from '@apptypes/SSOResponse';
import { SsoRequestUnauthorizedError } from '@errors/SsoRequestUnauthorizedError';
import { getMe } from '@features/usercontent/userContentData';
import { fetchWithRetry } from '@routes/utils';
import { insertIf } from '@util/array';
import { isLoggedIn } from '@util/cookies';
import { protocol } from '@util/protocol';
import { middlewareUrl, playerUrl } from '@util/url';
import i18n from 'i18next';

import { logError } from '../errors';
import { ISearchResponse, ITvGuideDayData, MenuResponseBody, VideoResponseBody } from '../types';
import { LanguageResponseBody } from '../types/language';
import { PlayListResponseBody } from '../types/PlayListResponseBody';

/**
 * An async function to call an api endpoint
 *
 * @returns promise with data of type T
 */
export async function apiResource<T>(resourceUrl: string, requestInit?: RequestInit): Promise<T> {
  const response = await fetchWithRetry(resourceUrl, false, requestInit);
  return await response.json();
}

export async function getAgeVerificationToken() {
  if (!isLoggedIn()) return '0';

  try {
    const { data } = await getMe();
    const { ageVerificationToken } = data[0];
    return ageVerificationToken || '0';
  } catch (error) {
    switch (true) {
      case error instanceof SsoRequestUnauthorizedError:
        throw error;
      default:
        logError(error, 'WARNING');
    }
    return '0';
  }
}

export const getPlaybackConfig = (videoId: string, isTrailer: boolean, isLive: boolean): Promise<VideoResponseBody> => {
  if (isTrailer) return getTrailer(videoId);
  if (isLive) return getLiveVideo();
  return getVideo(videoId);
};

async function getVideoRequestInit(): Promise<RequestInit> {
  const ageVerificationToken = await getAgeVerificationToken();
  return {
    headers: {
      Authorization: 'Bearer YTNkMmJlZTZiYzU0Njg0MGNiYzU3ZGUzZGYzMzY0N2JiZDM4ZTA2OWVhNTE1NDM5ZDc5ZWY3ZjBiYTkzYTRhYw',
      'x-validated-age': ageVerificationToken,
    },
  };
}

export const getVideo = async (videoId: string) => {
  const requestInit = await getVideoRequestInit();
  const url = playerUrl('/config', videoId);
  return apiResource<VideoResponseBody>(url, requestInit);
};

export const getLiveVideo = async () => {
  const requestInit = await getVideoRequestInit();
  const url = `${protocol}api.arte.tv/api/player/v2/config/${i18n.language}/LIVE`;
  return apiResource<VideoResponseBody>(url, requestInit);
};

export const getTrailer = async (videoId: string) => {
  const requestInit = await getVideoRequestInit();
  const url = playerUrl('/trailer', videoId);
  return apiResource<VideoResponseBody>(url, requestInit);
};

export const getPlayList = (programId: string) => apiResource<PlayListResponseBody>(playerUrl('/playlist', programId));

// menu
export const getMenu = () =>
  apiResource<MenuResponseBody>(middlewareUrl('/skeletons/menu')).catch(() =>
    logError(new Error('The menu cannot be loaded'), 'WARNING'),
  );

// search
export const search = (query: string, genre?: number) => {
  const queryParams = [`query=${query}`, ...insertIf(!!genre, [`genre=${genre}`])].join('&');
  console.log('queryParams: ' + queryParams);
  const url = middlewareUrl('/skeletons/pages/search', { query: queryParams });
  return apiResource<ISearchResponse>(url).catch(() => logError(new Error('Search failed'), 'WARNING'));
};

// pagination
export const getNextZonePage = (id: string, queryParams: string) => {
  const url = middlewareUrl(`/zones/${id}`, { query: queryParams });
  return apiResource<IPageResults>(url).catch(() =>
    logError(new Error(`Requesting a next page failed: ${url}`), 'WARNING'),
  );
};

export const getFeatureFlags = () =>
  apiResource<FeatureFlags>(
    middlewareUrl(`/feature-flags/${process.env.TARGET.toLowerCase()}`, { omitLang: true }),
  ).catch(() => logError(new Error('The menu cannot be loaded'), 'WARNING'));

// tv guide
export const getTvGuideDay = (date: string) => apiResource<ITvGuideDayData>(middlewareUrl(`/tvguide_days/${date}`));

export const getLanguage = () => apiResource<LanguageResponseBody>(middlewareUrl(`/language`, { omitLang: true }));

import { useEffect, useState } from 'react';

/**
 * On mount, loads any kind of external resource of type T
 */
export function useDataSource<T>(getResourceFunc: () => Promise<T>): T | undefined {
  const [resource, setResource] = useState<T>();

  useEffect(() => {
    (async () => {
      const result: T = await getResourceFunc();
      setResource(result);
    })();
  }, [getResourceFunc]);

  return resource;
}

import { AbstractVideoObjectImpl } from '../video-object/impl/abstract';
import { IVideoStreamProperties, VideoObjectEvent } from '../video-object/types';
import { isNumeric } from '../video-object/utils';

export default class MockVideoObjectImpl extends AbstractVideoObjectImpl {
	/**
	 * Current time
	 */
	public currentTime: number = NaN;

	/**
	 * Paused
	 */
	public paused: boolean = false;

	/**
	 * Time update interval
	 */
	public timeUpdate?: NodeJS.Timer;

	/**
	 * Bind events
	 */
	// eslint-disable-next-line class-methods-use-this
	protected bindEvents(): void {}

	/**
	 * Unbind events
	 */
	// eslint-disable-next-line class-methods-use-this
	protected unbindEvents(): void {}

	/**
	 * Destroy
	 * @returns Promise
	 */
	public destroy() {
		this.stream = undefined;
		this.stopTimeUpdate();
		return Promise.resolve();
	}

	/**
	 * Load stream
	 * @param stream - Video stream
	 * @returns Promise
	 */
	public load(stream: IVideoStreamProperties): Promise<void> {
		this.stream = stream;

		// timeout needs to be startet before load to schedule execution
		// after promise chain resolves so state is changed before
		setTimeout(() => {
			this.currentTime = isNumeric(this.stream.startTime) ? (this.stream.startTime as number) : 0;
			this.startTimeUpdate();
			this.trigger(VideoObjectEvent.PLAYING);
		}, 1000);

		return Promise.resolve();
	}

	/**
	 * Pause playback
	 */
	public pause() {
		setTimeout(() => {
			this.paused = true;
			this.stopTimeUpdate();
			this.trigger(VideoObjectEvent.PAUSE);
		}, 1000);
	}

	/**
	 * Reset video object
	 */
	public reset() {
		return new Promise<void>((resolve) => {
			setTimeout(() => {
				super.reset().then(() => {
					resolve();
				});
			}, 1000);
		});
	}

	/**
	 * Resume playback
	 */
	public resume() {
		setTimeout(() => {
			this.paused = false;
			this.trigger(VideoObjectEvent.PLAYING);
			this.startTimeUpdate();
		}, 1000);
	}

	/**
	 * Set time
	 * @param time - Time in seconds
	 */
	public setTime(time: number) {
		this.trigger(VideoObjectEvent.SEEKING);
		setTimeout(() => {
			this.currentTime = time;
			this.trigger(VideoObjectEvent.SEEKED);
		}, 1000);
	}

	/**
	 * Stop
	 * @returns Promise
	 */
	public stop() {
		return new Promise<void>((resolve) => {
			setTimeout(() => {
				this.stopTimeUpdate();
				this.trigger(VideoObjectEvent.STOPPED);
				resolve();
			}, 1000);
		});
	}

	/**
	 * Get duration
	 * @returns duration in seconds
	 */
	// eslint-disable-next-line class-methods-use-this
	public getDuration() {
		return 60 * 60; // 1 hour
	}

	/**
	 * Get time
	 * @returns time in seconds
	 */
	public getTime() {
		return this.currentTime;
	}

	/**
	 * Is paused
	 * @returns is paused
	 */
	public isPaused() {
		return !!this.paused;
	}

	/**
	 * Start internal time update
	 */
	private startTimeUpdate() {
		if (!this.timeUpdate) {
			this.timeUpdate = setInterval(() => {
				this.currentTime += 1;
			}, 1000);
		}
	}

	/**
	 * Stop internal time update
	 */
	private stopTimeUpdate() {
		if (this.timeUpdate) {
			clearInterval(this.timeUpdate);
			this.timeUpdate = undefined;
		}
	}
}

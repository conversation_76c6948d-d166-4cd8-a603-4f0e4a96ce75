import '@ungap/event-target';

import { IEventBus, IEventBusListener } from './types';

export class EventBus implements IEventBus {
	/**
	 * ALL events
	 */
	static ALL_EVENTS: string = '*';

	/**
	 * Event delegator
	 */
	private delegate: DocumentFragment;

	/**
	 * Event listeners
	 */
	private eventListeners: Map<string, Set<EventListenerOrEventListenerObject>>;

	/**
	 * Constructor
	 */
	constructor() {
		this.eventListeners = new Map();
		this.delegate = document.createDocumentFragment();
		this.delegate.addEventListener(EventBus.ALL_EVENTS, this.onEventListener);
	}

	/**
	 * Public methods
	 */

	/**
	 * Get registered event types
	 *
	 * @returns array of registered event types
	 */
	public events(): string[] {
		return Array.from(this.eventListeners.keys());
	}

	/**
	 * Get event listeners for event
	 *
	 * @param type - Event type
	 * @returns array of all event listeners
	 */
	public listeners(type?: string): EventListenerOrEventListenerObject[] {
		if (type !== undefined) {
			return this.eventListeners.has(type) ? Array.from(this.eventListeners.get(type)) : [];
		}

		return Array.from(this.eventListeners.values())
			.map((set) => Array.from(set))
			.flat();
	}

	/**
	 * Remove event listener
	 *
	 * @param type - Event type
	 * @param callback - Event callback
	 */
	public off(type?: string, callback?: EventListenerOrEventListenerObject): void {
		if (type) {
			if (this.eventListeners.has(type)) {
				if (callback) {
					const callbacks = this.eventListeners.get(type);

					if (callbacks.has(callback)) callbacks.delete(callback);
					if (callbacks.size === 0) this.eventListeners.delete(type);
				} else {
					this.eventListeners.delete(type);
				}
			}
		} else {
			this.eventListeners.clear();
		}
	}

	/**
	 * Add event listener
	 *
	 * @param type - Event type
	 * @param callback  - Event callback
	 */
	public on(type: string, callback: EventListenerOrEventListenerObject, unique?: boolean): void {
		const callbacks = this.eventListeners.has(type)
			? this.eventListeners.get(type)
			: this.eventListeners.set(type, new Set<EventListenerOrEventListenerObject>()).get(type);

		if (unique) {
			if (!callbacks.has(callback)) {
				callbacks.add(callback);
			}
		} else {
			callbacks.add(callback);
		}
	}

	/**
	 * Add once triggered event lister
	 *
	 * @param type - Event type
	 * @param callback  - Event callback
	 * @returns function to remove handler
	 */
	public once(type: string, callback: EventListenerOrEventListenerObject): IEventBusListener {
		const handleEventOnce = (evt: Event) => {
			// console.log(this.constructor.name, 'handleEventOnce', evt);
			this.off(type, handleEventOnce);

			if (typeof callback === 'function') {
				callback(evt);
			} else {
				callback.handleEvent(evt);
			}
		};

		this.on(type, handleEventOnce);

		return {
			callback: handleEventOnce,
			type,
		};
	}

	/**
	 * Get size of event listeners
	 *
	 * @param type - Event type
	 * @returns size of event listeners
	 */
	public size(type?: string): number {
		if (type) {
			return this.eventListeners.get(type)?.size || 0;
		}

		return Array.from(this.eventListeners.values()).reduce((memo, set) => memo + set.size, 0);
	}

	/**
	 * Dispatch / trigger event
	 *
	 * @param type - Event type
	 * @param detail - Event detail
	 * @returns returns true if either event's cancelable attribute value is false or its preventDefault() method was not invoked, and false otherwise.
	 */
	public trigger<T = any>(type: string, detail?: T): boolean {
		return this.delegate.dispatchEvent(new CustomEvent(EventBus.ALL_EVENTS, { detail: { ...detail, type } }));
	}

	/**
	 * Private methods
	 */

	/**
	 * Generic event listener for all events
	 *
	 * @param event - Event
	 */
	private onEventListener = (event: CustomEvent): void => {
		// console.log(this.constructor.name, 'eventListener', event);
		const { type, ...detail } = event.detail;

		// handle event
		if (this.eventListeners.has(type) || this.eventListeners.has(EventBus.ALL_EVENTS)) {
			const handleCallback = (callback: EventListenerOrEventListenerObject) => {
				if (typeof callback === 'function') {
					callback(new CustomEvent(type, { detail }));
				} else {
					callback.handleEvent(new CustomEvent(type, { detail }));
				}
			};

			// call event listeners
			let callbacks = this.eventListeners.get(type);

			if (callbacks && callbacks.size !== 0) callbacks.forEach(handleCallback);

			// call all events listeners
			callbacks = this.eventListeners.get(EventBus.ALL_EVENTS);

			if (callbacks && callbacks.size !== 0) callbacks.forEach(handleCallback);
		}
	};
}

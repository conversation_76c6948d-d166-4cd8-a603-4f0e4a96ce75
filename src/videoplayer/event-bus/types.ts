export interface IEventBusListener {
	callback: EventListenerOrEventListenerObject;
	type: string;
}

export interface IEventBus {
	events: () => string[];
	listeners: (type?: string) => EventListenerOrEventListenerObject[];
	off(type?: string, callback?: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
	on(type: string, callback: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
	once: (
		type: string,
		callback: EventListenerOrEventListenerObject,
		options?: AddEventListenerOptions | boolean,
	) => IEventBusListener;
	size: (type?: string) => number;
	trigger: <T>(type: string, args?: T) => boolean;
}

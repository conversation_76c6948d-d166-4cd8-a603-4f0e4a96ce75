import { EventBus } from './event-bus';

/**
 * Proxy event map mapping function
 */
type ProxyEventMapFn = <E extends CustomEvent>(event?: E) => { type: string; detail?: any } | void;

/**
 * Prox< event map
 */
export type ProxyEventMap = Record<string, boolean | string | ProxyEventMapFn>;

/**
 * Event proxy
 */
export abstract class EventProxy extends EventBus {
	/**
	 * Event map to proxy events
	 */
	protected eventMap: ProxyEventMap;

	/**
	 * Constructor
	 *
	 * @param eventMap - Event
	 */
	/* istanbul ignore next */
	constructor(eventMap: ProxyEventMap = {}) {
		super();

		this.eventMap = eventMap;
	}

	/**
	 * Protected methods
	 */

	/**
	 * Proxy event via EventTarget
	 *
	 * @param event - event to proxy
	 */
	protected proxyEvent = (event: CustomEvent): boolean => {
		// console.log('proxy native event', event);
		const typeOrFn = this.eventMap?.[event.type];

		if (typeof typeOrFn === 'function') {
			const { type, detail } = typeOrFn(event) || {};

			if (type) {
				/*
				if (type !== 'timeupdate') {
					console.log(this.constructor.name, 'proxy native event', event.type, 'as', type, 'with', detail);
				}
				*/
				return this.trigger(type, { ...event.detail, ...detail });
			}

			/*
			if (type !== 'timeupdate') {
				console.log(this.constructor.name, 'discard native event', event.type);
			}
			*/
			return false;
		}

		if (typeOrFn === true) {
			// if (event.type !== 'timeupdate') console.log(this.constructor.name, 'proxy native event', event.type);
			return this.trigger(event.type, event.detail);
		}

		if (typeOrFn === 'error') {
			return this.trigger(typeOrFn, event);
		}

		if (typeOrFn) {
			/*
			if (typeOrFn !== 'timeupdate') {
				console.log(this.constructor.name, 'proxy native event', event.type, 'as', typeOrFn);
			}
			*/
			return this.trigger(typeOrFn, event.detail);
		}

		console.warn(this.constructor.name, 'No type found proxing event');
		return false;
	};
}

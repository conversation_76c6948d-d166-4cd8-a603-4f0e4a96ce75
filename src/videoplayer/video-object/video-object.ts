import { EventProxy } from '../event-bus';
import { EndedState, ErrorState, IdleState, LoadingState, PausedState, PlayingState, StoppedState } from './states';
import {
	IVideoObject,
	IVideoObjectImpl,
	IVideoObjectState,
	IVideoStreamProperties,
	VideoObjectStateEvent,
	VideoObjectStateId,
} from './types';
import { VideoData } from '@apptypes/VideoResponseBody';

export class VideoObject extends EventProxy implements IVideoObject {
	/**
	 * State of video object
	 */
	private state: IVideoObjectState;

	/**
	 * States of video object
	 */
	// private states: { [key in VideoObjectStateId]: IVideoObjectState };
	private states: Record<VideoObjectStateId, IVideoObjectState>;

	/**
	 * Video object
	 */
	private videoObjectImpl: IVideoObjectImpl;

	/**
	 * Constructor
	 *
	 * @param videoObjectImpl - Video object implemenation (html5 (native/dash))
	 */
	/* istanbul ignore next */
	constructor(videoObjectImpl: IVideoObjectImpl) {
		super();

		this.videoObjectImpl = videoObjectImpl;

		this.states = {
			[VideoObjectStateId.IDLE]: new IdleState(this.videoObjectImpl),
			[VideoObjectStateId.LOADING]: new LoadingState(this.videoObjectImpl),
			[VideoObjectStateId.PLAYING]: new PlayingState(this.videoObjectImpl),
			[VideoObjectStateId.PAUSED]: new PausedState(this.videoObjectImpl),
			[VideoObjectStateId.ENDED]: new EndedState(this.videoObjectImpl),
			[VideoObjectStateId.ERROR]: new ErrorState(this.videoObjectImpl),
			[VideoObjectStateId.STOPPED]: new StoppedState(this.videoObjectImpl),
		};

		const keys = Object.keys(this.states);

		for (let i = 0; i < keys.length; i++) {
			this.states[keys[i] as VideoObjectStateId].on(EventProxy.ALL_EVENTS, (event: CustomEvent) => {
				if (event.type === VideoObjectStateEvent.CHANGE) {
					this.proxyStateChange(event.detail);
				} else {
					this.proxyEvent(event);
				}
			});
		}

		this.state = this.states[VideoObjectStateId.IDLE].onEnter(VideoObjectStateId.IDLE);
	}

	/**
	 * Public methods
	 */

	public showVideoElement(): void{
		return this.videoObjectImpl.showVideoElement();
	}

	public hideVideoElement(): void{
		return this.videoObjectImpl.hideVideoElement();

	}

	/**
	 * Destroy video element
	 */
	public destroy(): void {
		return this.state.destroy();
	}

	/**
	 * Get duration of video
	 */
	public getDuration(): number {
		return this.state.getDuration();
	}

	/**
	 * Get current video stream
	 *
	 * @returns current video stream
	 */
	getStream(): IVideoStreamProperties {
		return this.videoObjectImpl.stream;
	}

	/**
	 * Get current time of video
	 */
	public getTime(): number {
		return this.state.getTime();
	}

	/**
	 * Instance of video object implementation
	 * @param instance - Video object implementation
	 * @returns true if used video object implementation of instance
	 */
	public instanceOf(instance: any) {
		// console.log('instanceOf', this.videoObjectImpl instanceof instance);
		return this.videoObjectImpl instanceof instance;
	}

	/**
	 * Is in idle state
	 */
	public isIdle(): boolean {
		return this.state.id === VideoObjectStateId.IDLE;
	}

	/**
	 * Is paused
	 */
	public isPaused(): boolean {
		return this.state.isPaused();
	}

	/**
	 * Is playing
	 */
	public isPlaying(): boolean {
		return this.state.isPlaying();
	}

	/**
	 * Load video
	 *
	 * @param stream - Video stream
	 */
	public load(stream: IVideoStreamProperties, videoData: VideoData | undefined): void {
		return this.state.load(stream, videoData);
	}

	/**
	 * Pause video
	 */
	public pause(): void {
		return this.state.pause();
	}

	/**
	 * Destroy video element
	 */
	public reset() {
		return this.state.reset();
	}

	/**
	 * Resume video
	 */
	public resume(): void {
		return this.state.resume();
	}

	/**
	 * Set time of video in seconds
	 */
	public setTime(value: number): void {
		return this.getDuration() > value ? this.state.setTime(value) : this.state.setTime(0);
	}

	/**
	 * Stop video
	 */
	public stop(): void {
		return this.state.stop();
	}

	public getAvailableAudioTracks(): any {
		return this.videoObjectImpl.getAvailableAudioTracks();
	}

	public getCurrentAudioTrack(): any {
		return this.videoObjectImpl.getCurrentAudioTrack();
	}

	public setCurrentAudioTrack(value: any) {
		this.videoObjectImpl.setCurrentAudioTrack(value);
	}

	public getAvailableSubtitles(): any {
		return this.videoObjectImpl.getAvailableSubtitles();
	}

	public getCurrentSubtitles(): any {
		return this.videoObjectImpl.getCurrentSubtitles();
	}

	public setCurrentSubtitles(value: any) {
		this.videoObjectImpl.setCurrentSubtitles(value);
	}

	/**
	 * Proxy event via EventProxy (overriden)
	 *
	 * @param event - custom event to proxy
	 */
	protected proxyEvent = (event: CustomEvent): boolean => {
		return this.trigger(event.type, event.detail);
	};

	/**
	 * Private methods
	 */

	/**
	 * Proxy (video object) state change
	 *
	 * @param stateId - Next state (id) of video object
	 * @returns Flag if state was set
	 */
	private proxyStateChange({ id, error }: { error?: Error; id: VideoObjectStateId }): void {
		if (id && id !== this.state.id) {
			const previous = this.state.onLeave(id).id;
			this.state = this.states[id];
			this.state.onEnter(previous, error);
			this.trigger(VideoObjectStateEvent.CHANGE, { error, id, previous });
		}
	}
}

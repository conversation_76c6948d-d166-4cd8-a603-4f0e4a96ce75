import { IEventBusListener, EventProxy } from '../../../event-bus';
import { ActionNames, ActionType, createPauseAction, createSetTimeAction } from '../../actions';
import { VideoObjectImplError, VIDEO_URL_IS_MISSING } from '../../errors';
import { IVideoObjectImpl, IVideoObjectConfig, IVideoObjectProperties, IVideoStreamProperties } from '../../types';
import { VideoData } from '@apptypes/VideoResponseBody';

export abstract class AbstractVideoObjectImpl extends EventProxy implements IVideoObjectImpl {
	/**
	 * Managed actions
	 */
	// protected actions: { [key in ActionNames]?: ActionType } = {};
	protected actions: Partial<Record<ActionNames, ActionType>> = {};

	/**
	 * Configuration
	 */
	protected config: IVideoObjectConfig;

	/**
	 * Internal event listsners
	 */
	protected internalEventListeners: IEventBusListener[] = [];

	/**
	 * DOM parent node
	 */
	protected parentNode: HTMLElement;

	/**
	 * Current stream
	 */
	public stream: IVideoStreamProperties;

	/**
	 * Constructor
	 *
	 * @param param0 - Video object properties
	 */
	constructor({ config, eventMap, parentNode }: IVideoObjectProperties = {}) {
		super(eventMap);

		this.config = config || {};
		this.parentNode = parentNode;

		// actions
		this.actions.pause = createPauseAction(this);
		this.actions.setTime = createSetTimeAction(this);
	}

	/*
	 * Public methods
	 */

	/**
	 * Destroy video object
	 */
	public destroy(): Promise<void> {
		this.unbindEvents();
		this.destroyDomObject();

		return Promise.resolve();
	}

	/**
	 * Get length of the element's media in seconds
	 *
	 * @returns length of the element's media in seconds
	 */
	public abstract getDuration(): number;

	/**
	 * Get playback time in seconds
	 *
	 * @returns playback time in seconds or NaN
	 */
	public abstract getTime(): number;

	/**
	 * Is video stream paused
	 *
	 */
	public abstract isPaused(): boolean;

	/**
	 * Load new video
	 *
	 * @param stream - Video stream properties
	 */
	public abstract load(stream: IVideoStreamProperties, videoData: VideoData): Promise<void>;

	/**
	 * Pause video
	 *
	 */
	public abstract pause(): void;

	/**
	 * Reset video object
	 */
	public reset(): Promise<void> {
		// console.log(this.constructor.name, 'reset');
		this.stream = undefined;
		this.removeInternalEventListeners(); // remove all internal event listeners
		return Promise.resolve();
	}

	/**
	 * Resume video
	 *
	 */
	public abstract resume(): void;

	/**
	 * Set (playback) time in seconds
	 *
	 * @param seconds - (playback) time in seconds
	 */
	public abstract setTime(seconds: number): void;

	/**
	 * Stop video
	 *
	 */
	public abstract stop(): Promise<void>;

	/*
	 * Protected methods
	 */

	/**
	 * Attach source to video object
	 *
	 * @param stream - Video stream
	 */
	// eslint-disable-next-line class-methods-use-this
	protected attachSource({ url }: IVideoStreamProperties): void {
		if (!url) throw new VideoObjectImplError(VIDEO_URL_IS_MISSING);
	}

	/**
	 * Bind event handlers
	 */
	protected abstract bindEvents(): void;

	/**
	 * Destroy video dom object
	 */
	// eslint-disable-next-line class-methods-use-this
	protected destroyDomObject(): void {}

	/**
	 * Remove internal event listeners
	 */
	protected removeInternalEventListeners(): void {
		this.internalEventListeners.forEach(({ type, callback }) => {
			this.off(type, callback);
		});
		this.internalEventListeners = [];
	}

	/**
	 * Unbind event handlers
	 */
	protected abstract unbindEvents(): void;
}

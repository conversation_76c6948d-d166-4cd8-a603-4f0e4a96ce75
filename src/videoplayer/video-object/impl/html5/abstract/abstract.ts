import { SetTimeAction } from '../../../actions/setTime';
import * as ATTRIBUTES from '../../../attributes';
import {
	IVideoObjectDomProperties,
	IVideoObjectProperties,
	IVideoStreamProperties,
	VideoObjectEvent,
	VideoStreamMediaType,
} from '../../../types';
import { isNumeric } from '../../../utils';
import { AbstractVideoObjectImpl } from '../../abstract';
import { HTMLVideoElementStub } from '../native/stub';
import { VideoData } from '@apptypes/VideoResponseBody';

/**
 * Abstract class for HTML5 / DASH video object
 *
 */
export abstract class AbstractHtml5VideoObjectImpl extends AbstractVideoObjectImpl {
	/**
	 * Stub for video element
	 *
	 */
	static stubVideoElement = new HTMLVideoElementStub();

	/**
	 * Used stream type
	 *
	 */
	protected streamType: VideoStreamMediaType = VideoStreamMediaType.UNKNOWN;

	/**
	 * Reference to video element
	 *
	 */
	protected videoElement: HTMLVideoElement = AbstractHtml5VideoObjectImpl.stubVideoElement;

	/**
	 * Constructor
	 */
	constructor(properties: IVideoObjectProperties = {}) {
		const { eventMap = {}, ...rest } = properties;

		super({
			...rest,
			eventMap: {
				[VideoObjectEvent.STOPPED]: true,
				...eventMap,
			},
		});
	}

	/*
	 * Protected methods
	 */

	/**
	 * Create video dom element
	 *
	 * @param param0 - Video object dom properties
	 */
	protected createDomObject({
		autoplay = true,
		height = '0',
		id = 'video-object',
		left = '0',
		position = 'fixed',
		top = '0',
		width = '0',
	}: IVideoObjectDomProperties = {}): void {
		if (!(this.videoElement instanceof HTMLVideoElementStub)) return;

		this.videoElement = document.createElement('video');

		if (this.streamType !== VideoStreamMediaType.UNKNOWN) {
			this.setAttribute(ATTRIBUTES.TYPE, this.streamType);
		}

		this.setAttribute(ATTRIBUTES.ID, id);
		this.setAttribute(ATTRIBUTES.AUTOPLAY, autoplay ? 'true' : 'false');

		const { style } = this.videoElement;
		style.position = position;
		style.top = top;
		style.left = left;
		style.width = width;
		style.height = height;

		if (this.parentNode) {
			this.parentNode.appendChild(this.videoElement);
		} else {
			window.console.warn('No parentnode to append video element');
		}
	}

	/**
	 * Destroy video dom object
	 */
	protected destroyDomObject(): void {
		if (this.videoElement.parentNode) {
			this.videoElement.parentNode.removeChild(this.videoElement);
		}

		this.videoElement = AbstractHtml5VideoObjectImpl.stubVideoElement;
	}

	/**
	 * Detach source from video object
	 */
	protected abstract detachSource(): void;

	/**
	 * Get video attribute
	 *
	 * @param key - key of attribute
	 * @returns value of attribute
	 */
	protected getAttribute(key: string): string {
		const result = this.videoElement.getAttribute(key);

		return result === undefined ? (this.videoElement as { [key: string]: any })[key] : result;
	}

	/**
	 *Initialize playback
	 *
	 * @param stream - Video stream
	 */
	protected abstract initialize(stream: IVideoStreamProperties, videoData: VideoData): void;

	/**
	 * Set video attribute
	 *
	 * @param key - key of attribute
	 * @param value - value to set for attribute
	 */
	protected setAttribute(key: string, value: string) {
		this.videoElement.setAttribute(key, value);

		if (this.videoElement.getAttribute(key) === undefined) {
			(this.videoElement as { [key: string]: any })[key] = value;
		}
	}

	/*
	 * Public methods
	 */

	/**
	 * Get length of the element's media in seconds
	 *
	 * @returns length of the element's media in seconds
	 */
	public getDuration(): number {
		return isNumeric(this.videoElement.duration) ? this.videoElement.duration : NaN;
	}

	/**
	 * Is video stream paused
	 *
	 */
	public isPaused(): boolean {
		return this.videoElement.paused;
	}

	/**
	 * Load new video
	 *
	 * @param stream - Video stream properties
	 * @param domProperties - Video object dom properties
	 */
	public load(stream: IVideoStreamProperties, videoData: VideoData): Promise<void> {
		if (this.config.reuseVideoObject && !(this.videoElement instanceof HTMLVideoElementStub)) {
			return this.reset().then(() => {
				this.initialize(stream, videoData);
			});
		}

		try {
			this.unbindEvents();
			this.destroyDomObject();
		} catch (error: unknown) {
			// ignore
		}

		try {
			this.createDomObject({});
			this.bindEvents();

			this.initialize(stream, videoData);
		} catch (error) {
			return Promise.reject(error);
		}

		return Promise.resolve();
	}

	/**
	 * Stop video
	 */
	public stop(): Promise<void> {
		return this.actions
			.pause()
			.then(() => (this.actions.setTime as SetTimeAction)(0))
			.then(() => {
				this.trigger(VideoObjectEvent.STOPPED);
			});
	}
}

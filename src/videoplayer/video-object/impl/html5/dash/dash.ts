import { IVideoObjectProperties, IVideoStreamProperties, VideoObjectEvent, VideoStreamMediaType } from '../../../types';
import { AbstractHtml5VideoObjectImpl } from '../abstract/abstract';
import { HTMLVideoElementStub } from '@videoplayer/video-object/impl/html5/native/stub';
import { Tracking } from '@tracking/Tracking';
import EventBus from '@util/EventBus';
import { VideoData } from '@apptypes/VideoResponseBody';
import {SEEK_SECONDS_LIMIT_BEFORE_END} from '@util/videoSeekProtect';

const SUBTITLES_DIV_ID = 'dashVideoSubtitles';


/**
 * Wrapper for dash.js
 *
 * ```typescript
 * const dashVideoObject = new DashVideoObject();
 * ```
 */
export class DashHtml5VideoObjectImpl extends AbstractHtml5VideoObjectImpl {
	/**
	 * Reference to dash media player
	 */
	protected player: window.dashjs.MediaPlayerClass;

	/**
	 * Used stream type
	 */
	protected streamType: VideoStreamMediaType = VideoStreamMediaType.DASH;

	private textTrackList: window.dashjs.MediaInfo[] = [];
	private subtitlesEnabled = true;

	/**
	 * Constructor
	 */
	constructor(properties: IVideoObjectProperties = {}) {
		const { eventMap = {}, ...rest } = properties;

		super({
			...rest,
			eventMap: {
				[window.dashjs.MediaPlayer.events.ERROR]: VideoObjectEvent.ERROR,
				[window.dashjs.MediaPlayer.events.PLAYBACK_ENDED]: VideoObjectEvent.ENDED,
				[window.dashjs.MediaPlayer.events.PLAYBACK_ERROR]: VideoObjectEvent.ERROR,
				[window.dashjs.MediaPlayer.events.PLAYBACK_PLAYING]: VideoObjectEvent.PLAYING,
				[window.dashjs.MediaPlayer.events.PLAYBACK_PAUSED]: VideoObjectEvent.PAUSE,
				[window.dashjs.MediaPlayer.events.PLAYBACK_SEEKED]: VideoObjectEvent.SEEKED,
				[window.dashjs.MediaPlayer.events.PLAYBACK_SEEKING]: VideoObjectEvent.SEEKING,
				[window.dashjs.MediaPlayer.events.PLAYBACK_TIME_UPDATED]: () => ({
					detail: {
						duration: this.getDuration(),
						time: this.getTime(),
					},
					type: VideoObjectEvent.TIMEUPDATE,
				}),
				[window.dashjs.MediaPlayer.events.PLAYBACK_WAITING]: VideoObjectEvent.WAITING,
				...eventMap,
			},
		});

		this.player = window.dashjs.MediaPlayer().create();

		if (properties.playerSettings) {
			this.player.updateSettings(properties.playerSettings);
		}

		this.player.on(window.dashjs.MediaPlayer.events.ERROR, (error) => {
			console.log(error);
		});

		/**
		 * Here, we are directly listening to the event from dash.js
		 * We relay that event further using the universal EventBus used throughout the application
		 * Ideally this should have been served by the proxyEvent method but somehow this failed to correctly work
		 * See protected proxyEvent = (event: CustomEvent) from html5.ts
		 */
		this.player.on(window.dashjs.MediaPlayer.events.PLAYBACK_NOT_ALLOWED, () => {
			EventBus.emit(VideoObjectEvent.PLAYBACKNOTALLOWED, { });
		});

		this.player.on(window.dashjs.MediaPlayer.events.CAN_PLAY, () => {
			this.showVideoElement();
		});

		this.player.on(window.dashjs.MediaPlayer.events.TEXT_TRACKS_ADDED, (e) => {
			this.textTrackList = [...this.textTrackList, ...e.tracks];
		});
	}

	/*
	 * Protected methods
	 */

	/**
	 * Attach source to video object
	 *
	 * @param stream - Video stream
	 */
	protected attachSource(stream: IVideoStreamProperties): void {
		super.attachSource(stream);

		this.player.attachSource(stream.url, stream.startTime);
	}

	/**
	 * Bind native events
	 */
	protected bindEvents(): void {
		// bind proxied events
		Object.keys(this.eventMap).forEach((key: string) => {
			this.player?.on(key, this.proxyEvent);
		});
	}

	/**
	 * Detach source from video object
	 *
	 */
	protected detachSource() {
		this.player.attachSource(null);
	}

	/**
	 * Initialize playback
	 *
	 * @param param0 - Video stream properties
	 */
	protected initialize({ url, startTime }: IVideoStreamProperties, videoData: VideoData): void {
		const referenceDuration = videoData?.attributes?.metadata?.duration?.seconds;
		const hasLiveSticker = videoData?.attributes?.stickers?.find(sticker => sticker.code === 'LIVE');

		let referenceStartTime = startTime;
		if(!!referenceDuration && !!startTime) {
			if(startTime > (referenceDuration - SEEK_SECONDS_LIMIT_BEFORE_END)) {
				referenceStartTime = 0;
			}
		}

		const computedStartTime = hasLiveSticker ? undefined : referenceStartTime;

		this.player.initialize(this.videoElement, url, true, computedStartTime);

		this.addSubtitlesDiv();
		// trackign dash.js player instance
		Tracking.trackDashjs(this.player);
	}

	/**
	 * Unbind event handlers
	 *
	 */
	protected unbindEvents(): void {
		// unbind proxied events
		Object.keys(this.eventMap).forEach((key: string) => {
			this.player?.off(key, this.proxyEvent);
		});
	}

	/*
	 * Public methods
	 */

	/**
	 * Destroy video object
	 */
	public destroy(): Promise<void> {
		if (this.player) {
			this.player.destroy();
			this.player = null;
		}

		this.removeSubtitlesDiv();

		return super.destroy();
	}

	/**
	 * Get length of the element's media in seconds
	 *
	 * @returns length of the element's media in seconds
	 */
	public getDuration(): number {
		return this.player ? this.player?.duration() : NaN;
	}

	/**
	 * Get playback time in seconds
	 *
	 * @returns playback time in seconds or NaN
	 */
	public getTime(): number {
		return this.player ? this.player.time() : NaN;
	}

	/**
	 * Pause video
	 */
	public pause(): void {
		this.player.pause();
	}

	/**
	 * Reset video object (stopped and source detached)
	 */
	public reset(): Promise<void> {
		this.hideVideoElement();
		const result = super.reset();
		this.detachSource();
		return result;
	}

	/**
	 * Resume video
	 */
	public resume(): void {
		this.player.play();
	}

	/**
	 * Set (playback) time in seconds
	 *
	 * @param seconds - (playback) time in seconds
	 */
	public setTime(seconds: number): void {
		this.player.seek(seconds);
	}

	public getAvailableAudioTracks(): window.dashjs.MediaInfo[] {
		return this.player.getTracksFor('audio');
	}

	public getCurrentAudioTrack(): window.dashjs.MediaInfo | null {
		return this.player.getCurrentTrackFor('audio');
	}

	public setCurrentAudioTrack(value: window.dashjs.MediaInfo) {
		this.player.setCurrentTrack(value);
	}

	public getAvailableSubtitles(): window.dashjs.MediaInfo[]  {
		return this.textTrackList;
	}

	public getCurrentSubtitles(): window.dashjs.MediaInfo | null {
		const currentSubtitles = this.player.getCurrentTrackFor('text');
		return this.subtitlesEnabled ? currentSubtitles : null;
	}

	public setCurrentSubtitles(value: number | undefined) {
		this.subtitlesEnabled = value === undefined ? false : true;
		this.player.setTextTrack(value === undefined ? -1 : value);
	}

	private removeSubtitlesDiv() {
		const subtitlesDiv = document.getElementById(SUBTITLES_DIV_ID);
		if (subtitlesDiv) {
			subtitlesDiv.parentElement?.removeChild(subtitlesDiv);
		}
	}


	public showVideoElement(): void {
		if (this.videoElement && !(this.videoElement instanceof HTMLVideoElementStub)) {
			setTimeout(() => {
				this.videoElement.style.width = '100%';
				this.videoElement.style.height = '100%';
			}, 500);
		}
	}

	public hideVideoElement(): void {
		if (this.videoElement && !(this.videoElement instanceof HTMLVideoElementStub)) {
			this.videoElement.style.width = '0';
			this.videoElement.style.height = '0';
		}
	}

	private addSubtitlesDiv() {
		this.removeSubtitlesDiv();

		const subtitlesDiv: HTMLDivElement = document.createElement('div');
		subtitlesDiv.id = SUBTITLES_DIV_ID;
		this.videoElement.parentElement?.appendChild(subtitlesDiv);
		this.player.attachVttRenderingDiv(subtitlesDiv);
	}
}

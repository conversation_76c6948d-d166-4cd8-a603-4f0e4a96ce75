import { IVideoObjectImpl, VideoObjectEvent } from '../../types';
import { Html5VideoObjectImpl } from './html5';

const BIG_BUCK_BUNNY_VOD_MP4 = 'http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4';

describe('HTML5 video object', () => {
	let obj: IVideoObjectImpl;

	beforeEach(() => {
		obj = new Html5VideoObjectImpl({ parentNode: document.body });
	});

	afterEach((done) => {
		if (obj) {
			obj.destroy().finally(done);
		} else {
			done();
		}
	});

	it('should load a VOD', (done) => {
		const onPlaying = jasmine.createSpy('onPlaying');

		obj.once(VideoObjectEvent.PLAYING, onPlaying);
		obj.once(VideoObjectEvent.PLAYING, () => {
			expect(onPlaying).toHaveBeenCalled();

			done();
		});

		obj.load({
			url: BIG_BUCK_BUNNY_VOD_MP4,
		});
	});

	it('should load a VOD with initial position', (done) => {
		const onPlaying = jasmine.createSpy('onPlaying');
		const onSeeked = jasmine.createSpy('onSeeked');

		obj.once(VideoObjectEvent.SEEKED, onSeeked);
		obj.once(VideoObjectEvent.PLAYING, onPlaying);
		obj.once(VideoObjectEvent.PLAYING, () => {
			expect(onSeeked).toHaveBeenCalled();
			expect(onPlaying).toHaveBeenCalled();
			expect(Math.floor(obj.getTime())).toBe(30);

			done();
		});

		obj.load({
			startTime: 30,
			url: BIG_BUCK_BUNNY_VOD_MP4,
		});
	});
});

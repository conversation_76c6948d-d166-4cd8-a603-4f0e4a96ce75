import { VideoObjectImplError, VIDEO_TYPE_NOT_SUPPORTED } from '../../errors';
import { VideoObjectEvent, IVideoStreamProperties, IVideoObjectProperties } from '../../types';
import { matchFileExtention } from '../../utils';
import { AbstractVideoObjectImpl } from '../abstract';
import { DashHtml5VideoObjectImpl } from './dash/dash';
import { NativeHtml5VideoObjectImpl } from './native/native';
import { isSpecificTVModel } from '@util/isSpecificTVModel';
import { VideoData } from '@apptypes/VideoResponseBody';

/**
 * Wrapper for html5 (native / dash) video object
 *
 * ```typescript
 * const html5VideoObject = new HTML5VideoObject();
 * ```
 */
export class Html5VideoObjectImpl extends AbstractVideoObjectImpl {
	/**
	 * Video object implementation
	 */
	private videoObjectImpl: NativeHtml5VideoObjectImpl | DashHtml5VideoObjectImpl;

	/**
	 * Constructor
	 */
	constructor({ config, eventMap = {}, ...rest }: IVideoObjectProperties = {}) {
		super({
			config,
			...rest,
			eventMap: {
				[VideoObjectEvent.CANPLAY]: true,
				[VideoObjectEvent.ENDED]: true,
				[VideoObjectEvent.ERROR]: true,
				[VideoObjectEvent.PLAYING]: true,
				[VideoObjectEvent.PAUSE]: true,
				[VideoObjectEvent.SEEKED]: true,
				[VideoObjectEvent.SEEKING]: true,
				[VideoObjectEvent.STOPPED]: true,
				[VideoObjectEvent.TIMEUPDATE]: true,
				[VideoObjectEvent.WAITING]: true,
				[VideoObjectEvent.PLAYBACKNOTALLOWED]: true,
				...eventMap,
			},
		});
	}

	/*
	 * Public methods
	 */

	/**
	 * Destroy video object
	 */
	public destroy(): Promise<void> {
		return this.videoObjectImpl?.destroy();
	}

	/**
	 * Get length of the element's media in seconds
	 *
	 * @returns length of the element's media in seconds
	 */
	public getDuration() {
		return this.videoObjectImpl ? this.videoObjectImpl.getDuration() : NaN;
	}

	/**
	 * Get (playback) time in seconds
	 *
	 * @returns (playback) time in seconds
	 */
	public getTime() {
		return this.videoObjectImpl ? this.videoObjectImpl.getTime() : NaN;
	}

	/**
	 * Is video stream paused
	 *
	 */
	public isPaused(): boolean {
		return this.videoObjectImpl?.isPaused();
	}

	/**
	 * Load new video
	 *
	 * @param stream - Video stream properties
	 */
	public load(stream: IVideoStreamProperties, videoData: VideoData): Promise<void> {
		let result = Promise.resolve();

		this.stream = stream;

		if (matchFileExtention(stream.url, 'mp4')) {
			if (!this.videoObjectImpl || this.videoObjectImpl instanceof DashHtml5VideoObjectImpl) {
				result = this.videoObjectImpl ? this.videoObjectImpl.destroy() : result;
				result.then(() => {
					this.unbindEvents();
					this.videoObjectImpl = new NativeHtml5VideoObjectImpl({ ...this.config, parentNode: this.parentNode });
					this.bindEvents();
				});
			}
		} else if (matchFileExtention(stream.url, 'mpd')) {
			if (!this.videoObjectImpl || this.videoObjectImpl instanceof NativeHtml5VideoObjectImpl) {
				result = this.videoObjectImpl ? this.videoObjectImpl.destroy() : result;
				result.then(() => {
					this.unbindEvents();
					this.videoObjectImpl = new DashHtml5VideoObjectImpl({ ...this.config, parentNode: this.parentNode });
					this.bindEvents();
				});
			}
		} else {
			console.warn('Video type not supported');
			result = Promise.reject(new VideoObjectImplError(VIDEO_TYPE_NOT_SUPPORTED));
		}

		return result.then(() => {
			this.videoObjectImpl.load(stream, videoData);
		});
	}

	/**
	 * Pause video
	 */
	public pause(): void {
		this.videoObjectImpl?.pause();
	}

	/**
	 * Reset video
	 */
	public reset(): Promise<void> {
		return this.videoObjectImpl?.reset();
	}

	/**
	 * Resume video
	 */
	public resume(): void {
		this.videoObjectImpl?.resume();
	}

	/**
	 * Set (playback) time in seconds
	 *
	 * @param seconds - (playback) time in seconds
	 */
	public setTime(seconds: number) {
		return this.videoObjectImpl?.setTime(seconds);
	}

	public showVideoElement(): void {
		return this.videoObjectImpl?.showVideoElement();
	}

	public hideVideoElement(): void {
		return this.videoObjectImpl?.hideVideoElement();

	}
	/**
	 * Stop video
	 */
	public stop(): Promise<void> {
		return this.videoObjectImpl.stop();
	}

	public getAvailableAudioTracks(): any {
		return this.videoObjectImpl?.getAvailableAudioTracks();
	}

	public getCurrentAudioTrack(): any {
		return this.videoObjectImpl?.getCurrentAudioTrack();
	}

	public setCurrentAudioTrack(value: any) {
		this.videoObjectImpl?.setCurrentAudioTrack(value);
	}

	public getAvailableSubtitles(): any {
		return this.videoObjectImpl?.getAvailableSubtitles();
	}

	public getCurrentSubtitles(): any {
		return this.videoObjectImpl?.getCurrentSubtitles();
	}

	public setCurrentSubtitles(value: any) {
		this.videoObjectImpl?.setCurrentSubtitles(value);
	}

	/*
	 * Protected methods
	 */

	/**
	 * Bind event handlers
	 */
	protected bindEvents(): void {
		// bind proxied events
		Object.keys(this.eventMap).forEach((key: string) => {
			this.videoObjectImpl?.on(key, this.proxyEvent);
		});
	}

	/**
	 * Proxy event via EventProxy (overriden)
	 *
	 * @param event - custom event to proxy
	 */
	protected proxyEvent = (event: CustomEvent): boolean => {
		// console.log(this.constructor.name, 'proxyEvent', event.type, event.detail);
		return this.trigger(event.type, event.detail);
	};

	/**
	 * Unbind event handlers
	 */
	protected unbindEvents(): void {
		// unbind proxied events
		Object.keys(this.eventMap).forEach((key: string) => {
			this.videoObjectImpl?.off(key, this.proxyEvent);
		});
	}
}

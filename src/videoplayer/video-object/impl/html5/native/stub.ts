/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable class-methods-use-this */

export class HTMLVideoElementStub implements HTMLVideoElement {
	role: any;

	onbeforeinput: any;

	oncancel: any;

	disablePictureInPicture: boolean;

	height: number;

	onenterpictureinpicture: (this: HTMLVideoElement, ev: Event) => any;

	onleavepictureinpicture: (this: HTMLVideoElement, ev: Event) => any;

	playsInline: boolean;

	poster: string;

	videoHeight: number;

	videoWidth: number;

	width: number;

	cancelVideoFrameCallback(handle: number): void {
		throw new Error('Method not implemented.');
	}

	getVideoPlaybackQuality(): VideoPlaybackQuality {
		throw new Error('Method not implemented.');
	}

	requestPictureInPicture(): Promise<PictureInPictureWindow> {
		throw new Error('Method not implemented.');
	}

	requestVideoFrameCallback(callback: any): number {
		throw new Error('Method not implemented.');
	}

	addEventListener<K extends keyof HTMLVideoElementEventMap>(
		type: K,
		listener: (this: HTMLVideoElement, ev: HTMLVideoElementEventMap[K]) => any,
		options?: boolean | AddEventListenerOptions,
	): void;

	addEventListener(
		type: string,
		listener: EventListenerOrEventListenerObject,
		options?: boolean | AddEventListenerOptions,
	): void;

	addEventListener(type: unknown, listener: unknown, options?: unknown): void {
		throw new Error('Method not implemented.');
	}

	removeEventListener<K extends keyof HTMLVideoElementEventMap>(
		type: K,
		listener: (this: HTMLVideoElement, ev: HTMLVideoElementEventMap[K]) => any,
		options?: boolean | EventListenerOptions,
	): void;

	removeEventListener(
		type: string,
		listener: EventListenerOrEventListenerObject,
		options?: boolean | EventListenerOptions,
	): void;

	removeEventListener(type: unknown, listener: unknown, options?: unknown): void {
		throw new Error('Method not implemented.');
	}

	autoplay: boolean;

	buffered: TimeRanges;

	controls: boolean;

	crossOrigin: string;

	currentSrc: string;

	currentTime: number;

	defaultMuted: boolean;

	defaultPlaybackRate: number;

	disableRemotePlayback: boolean;

	duration: number;

	ended: boolean;

	error: MediaError;

	loop: boolean;

	mediaKeys: MediaKeys;

	muted: boolean;

	networkState: number;

	onencrypted: (this: HTMLMediaElement, ev: MediaEncryptedEvent) => any;

	onwaitingforkey: (this: HTMLMediaElement, ev: Event) => any;

	paused: boolean;

	playbackRate: number;

	played: TimeRanges;

	preload: '' | 'auto' | 'none' | 'metadata';

	preservesPitch: boolean;

	readyState: number;

	remote: RemotePlayback;

	seekable: TimeRanges;

	seeking: boolean;

	src: string;

	srcObject: MediaProvider;

	textTracks: TextTrackList;

	volume: number;

	addTextTrack(kind: TextTrackKind, label?: string, language?: string): TextTrack {
		throw new Error('Method not implemented.');
	}

	canPlayType(type: string): CanPlayTypeResult {
		throw new Error('Method not implemented.');
	}

	fastSeek(time: number): void {
		throw new Error('Method not implemented.');
	}

	load(): void {
		throw new Error('Method not implemented.');
	}

	pause(): void {
		throw new Error('Method not implemented.');
	}

	play(): Promise<void> {
		throw new Error('Method not implemented.');
	}

	setMediaKeys(mediaKeys: MediaKeys): Promise<void> {
		throw new Error('Method not implemented.');
	}

	HAVE_CURRENT_DATA: number;

	HAVE_ENOUGH_DATA: number;

	HAVE_FUTURE_DATA: number;

	HAVE_METADATA: number;

	HAVE_NOTHING: number;

	NETWORK_EMPTY: number;

	NETWORK_IDLE: number;

	NETWORK_LOADING: number;

	NETWORK_NO_SOURCE: number;

	accessKey: string;

	accessKeyLabel: string;

	autocapitalize: string;

	dir: string;

	draggable: boolean;

	hidden: boolean;

	inert: boolean;

	innerText: string;

	lang: string;

	offsetHeight: number;

	offsetLeft: number;

	offsetParent: Element;

	offsetTop: number;

	offsetWidth: number;

	outerText: string;

	spellcheck: boolean;

	title: string;

	translate: boolean;

	attachInternals(): ElementInternals {
		throw new Error('Method not implemented.');
	}

	click(): void {
		throw new Error('Method not implemented.');
	}

	attributes: NamedNodeMap;

	classList: DOMTokenList;

	className: string;

	clientHeight: number;

	clientLeft: number;

	clientTop: number;

	clientWidth: number;

	id: string;

	localName: string;

	namespaceURI: string;

	onfullscreenchange: (this: Element, ev: Event) => any;

	onfullscreenerror: (this: Element, ev: Event) => any;

	outerHTML: string;

	ownerDocument: Document;

	part: DOMTokenList;

	prefix: string;

	scrollHeight: number;

	scrollLeft: number;

	scrollTop: number;

	scrollWidth: number;

	shadowRoot: ShadowRoot;

	slot: string;

	tagName: string;

	attachShadow(init: ShadowRootInit): ShadowRoot {
		throw new Error('Method not implemented.');
	}

	closest(selectors: unknown): any {
		throw new Error('Method not implemented.');
	}

	getAttribute(qualifiedName: string): string {
		throw new Error('Method not implemented.');
	}

	getAttributeNS(namespace: string, localName: string): string {
		throw new Error('Method not implemented.');
	}

	getAttributeNames(): string[] {
		throw new Error('Method not implemented.');
	}

	getAttributeNode(qualifiedName: string): Attr {
		throw new Error('Method not implemented.');
	}

	getAttributeNodeNS(namespace: string, localName: string): Attr {
		throw new Error('Method not implemented.');
	}

	getBoundingClientRect(): DOMRect {
		throw new Error('Method not implemented.');
	}

	getClientRects(): DOMRectList {
		throw new Error('Method not implemented.');
	}

	getElementsByClassName(classNames: string): HTMLCollectionOf<Element> {
		throw new Error('Method not implemented.');
	}

	getElementsByTagName(qualifiedName: unknown): any {
		throw new Error('Method not implemented.');
	}

	getElementsByTagNameNS(
		namespaceURI: 'http://www.w3.org/1999/xhtml',
		localName: string,
	): HTMLCollectionOf<HTMLElement>;

	getElementsByTagNameNS(namespaceURI: 'http://www.w3.org/2000/svg', localName: string): HTMLCollectionOf<SVGElement>;

	getElementsByTagNameNS(namespace: string, localName: string): HTMLCollectionOf<Element>;

	getElementsByTagNameNS(
		namespace: unknown,
		localName: unknown,
	): HTMLCollectionOf<Element> | HTMLCollectionOf<HTMLElement> | HTMLCollectionOf<SVGElement> {
		throw new Error('Method not implemented.');
	}

	hasAttribute(qualifiedName: string): boolean {
		throw new Error('Method not implemented.');
	}

	hasAttributeNS(namespace: string, localName: string): boolean {
		throw new Error('Method not implemented.');
	}

	hasAttributes(): boolean {
		throw new Error('Method not implemented.');
	}

	hasPointerCapture(pointerId: number): boolean {
		throw new Error('Method not implemented.');
	}

	insertAdjacentElement(where: InsertPosition, element: Element): Element {
		throw new Error('Method not implemented.');
	}

	insertAdjacentHTML(position: InsertPosition, text: string): void {
		throw new Error('Method not implemented.');
	}

	insertAdjacentText(where: InsertPosition, data: string): void {
		throw new Error('Method not implemented.');
	}

	matches(selectors: string): boolean {
		throw new Error('Method not implemented.');
	}

	releasePointerCapture(pointerId: number): void {
		throw new Error('Method not implemented.');
	}

	removeAttribute(qualifiedName: string): void {
		throw new Error('Method not implemented.');
	}

	removeAttributeNS(namespace: string, localName: string): void {
		throw new Error('Method not implemented.');
	}

	removeAttributeNode(attr: Attr): Attr {
		throw new Error('Method not implemented.');
	}

	requestFullscreen(options?: FullscreenOptions): Promise<void> {
		throw new Error('Method not implemented.');
	}

	requestPointerLock(): void {
		throw new Error('Method not implemented.');
	}

	scroll(options?: ScrollToOptions): void;

	scroll(x: number, y: number): void;

	scroll(x?: unknown, y?: unknown): void {
		throw new Error('Method not implemented.');
	}

	scrollBy(options?: ScrollToOptions): void;

	scrollBy(x: number, y: number): void;

	scrollBy(x?: unknown, y?: unknown): void {
		throw new Error('Method not implemented.');
	}

	scrollIntoView(arg?: boolean | ScrollIntoViewOptions): void {
		throw new Error('Method not implemented.');
	}

	scrollTo(options?: ScrollToOptions): void;

	scrollTo(x: number, y: number): void;

	scrollTo(x?: unknown, y?: unknown): void {
		throw new Error('Method not implemented.');
	}

	setAttribute(qualifiedName: string, value: string): void {
		throw new Error('Method not implemented.');
	}

	setAttributeNS(namespace: string, qualifiedName: string, value: string): void {
		throw new Error('Method not implemented.');
	}

	setAttributeNode(attr: Attr): Attr {
		throw new Error('Method not implemented.');
	}

	setAttributeNodeNS(attr: Attr): Attr {
		throw new Error('Method not implemented.');
	}

	setPointerCapture(pointerId: number): void {
		throw new Error('Method not implemented.');
	}

	toggleAttribute(qualifiedName: string, force?: boolean): boolean {
		throw new Error('Method not implemented.');
	}

	webkitMatchesSelector(selectors: string): boolean {
		throw new Error('Method not implemented.');
	}

	baseURI: string;

	childNodes: NodeListOf<ChildNode>;

	firstChild: ChildNode;

	isConnected: boolean;

	lastChild: ChildNode;

	nextSibling: ChildNode;

	nodeName: string;

	nodeType: number;

	nodeValue: string;

	parentElement: HTMLElement;

	parentNode: ParentNode;

	previousSibling: ChildNode;

	textContent: string;

	appendChild<T extends Node>(node: T): T {
		throw new Error('Method not implemented.');
	}

	cloneNode(deep?: boolean): Node {
		throw new Error('Method not implemented.');
	}

	compareDocumentPosition(other: Node): number {
		throw new Error('Method not implemented.');
	}

	contains(other: Node): boolean {
		throw new Error('Method not implemented.');
	}

	getRootNode(options?: GetRootNodeOptions): Node {
		throw new Error('Method not implemented.');
	}

	hasChildNodes(): boolean {
		throw new Error('Method not implemented.');
	}

	insertBefore<T extends Node>(node: T, child: Node): T {
		throw new Error('Method not implemented.');
	}

	isDefaultNamespace(namespace: string): boolean {
		throw new Error('Method not implemented.');
	}

	isEqualNode(otherNode: Node): boolean {
		throw new Error('Method not implemented.');
	}

	isSameNode(otherNode: Node): boolean {
		throw new Error('Method not implemented.');
	}

	lookupNamespaceURI(prefix: string): string {
		throw new Error('Method not implemented.');
	}

	lookupPrefix(namespace: string): string {
		throw new Error('Method not implemented.');
	}

	normalize(): void {
		throw new Error('Method not implemented.');
	}

	removeChild<T extends Node>(child: T): T {
		throw new Error('Method not implemented.');
	}

	replaceChild<T extends Node>(node: Node, child: T): T {
		throw new Error('Method not implemented.');
	}

	ATTRIBUTE_NODE: number;

	CDATA_SECTION_NODE: number;

	COMMENT_NODE: number;

	DOCUMENT_FRAGMENT_NODE: number;

	DOCUMENT_NODE: number;

	DOCUMENT_POSITION_CONTAINED_BY: number;

	DOCUMENT_POSITION_CONTAINS: number;

	DOCUMENT_POSITION_DISCONNECTED: number;

	DOCUMENT_POSITION_FOLLOWING: number;

	DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC: number;

	DOCUMENT_POSITION_PRECEDING: number;

	DOCUMENT_TYPE_NODE: number;

	ELEMENT_NODE: number;

	ENTITY_NODE: number;

	ENTITY_REFERENCE_NODE: number;

	NOTATION_NODE: number;

	PROCESSING_INSTRUCTION_NODE: number;

	TEXT_NODE: number;

	dispatchEvent(event: Event): boolean {
		throw new Error('Method not implemented.');
	}

	ariaAtomic: string;

	ariaAutoComplete: string;

	ariaBusy: string;

	ariaChecked: string;

	ariaColCount: string;

	ariaColIndex: string;

	ariaColIndexText: string;

	ariaColSpan: string;

	ariaCurrent: string;

	ariaDisabled: string;

	ariaExpanded: string;

	ariaHasPopup: string;

	ariaHidden: string;

	ariaInvalid: string;

	ariaKeyShortcuts: string;

	ariaLabel: string;

	ariaLevel: string;

	ariaLive: string;

	ariaModal: string;

	ariaMultiLine: string;

	ariaMultiSelectable: string;

	ariaOrientation: string;

	ariaPlaceholder: string;

	ariaPosInSet: string;

	ariaPressed: string;

	ariaReadOnly: string;

	ariaRequired: string;

	ariaRoleDescription: string;

	ariaRowCount: string;

	ariaRowIndex: string;

	ariaRowIndexText: string;

	ariaRowSpan: string;

	ariaSelected: string;

	ariaSetSize: string;

	ariaSort: string;

	ariaValueMax: string;

	ariaValueMin: string;

	ariaValueNow: string;

	ariaValueText: string;

	animate(keyframes: Keyframe[] | PropertyIndexedKeyframes, options?: number | KeyframeAnimationOptions): Animation {
		throw new Error('Method not implemented.');
	}

	getAnimations(options?: GetAnimationsOptions): Animation[] {
		throw new Error('Method not implemented.');
	}

	after(...nodes: (string | Node)[]): void {
		throw new Error('Method not implemented.');
	}

	before(...nodes: (string | Node)[]): void {
		throw new Error('Method not implemented.');
	}

	remove(): void {
		throw new Error('Method not implemented.');
	}

	replaceWith(...nodes: (string | Node)[]): void {
		throw new Error('Method not implemented.');
	}

	innerHTML: string;

	nextElementSibling: Element;

	previousElementSibling: Element;

	childElementCount: number;

	children: HTMLCollection;

	firstElementChild: Element;

	lastElementChild: Element;

	append(...nodes: (string | Node)[]): void {
		throw new Error('Method not implemented.');
	}

	prepend(...nodes: (string | Node)[]): void {
		throw new Error('Method not implemented.');
	}

	querySelector(selectors: unknown): any {
		throw new Error('Method not implemented.');
	}

	querySelectorAll(selectors: unknown): any {
		throw new Error('Method not implemented.');
	}

	replaceChildren(...nodes: (string | Node)[]): void {
		throw new Error('Method not implemented.');
	}

	assignedSlot: HTMLSlotElement;

	oncopy: (this: DocumentAndElementEventHandlers, ev: ClipboardEvent) => any;

	oncut: (this: DocumentAndElementEventHandlers, ev: ClipboardEvent) => any;

	onpaste: (this: DocumentAndElementEventHandlers, ev: ClipboardEvent) => any;

	style: CSSStyleDeclaration;

	contentEditable: string;

	enterKeyHint: string;

	inputMode: string;

	isContentEditable: boolean;

	onabort: (this: GlobalEventHandlers, ev: UIEvent) => any;

	onanimationcancel: (this: GlobalEventHandlers, ev: AnimationEvent) => any;

	onanimationend: (this: GlobalEventHandlers, ev: AnimationEvent) => any;

	onanimationiteration: (this: GlobalEventHandlers, ev: AnimationEvent) => any;

	onanimationstart: (this: GlobalEventHandlers, ev: AnimationEvent) => any;

	onauxclick: (
		this: GlobalEventHandlers,
		ev: MouseEvent,
		/* eslint-disable class-methods-use-this */
	) => any;

	onblur: (this: GlobalEventHandlers, ev: FocusEvent) => /* eslint-disable class-methods-use-this */ any;

	oncanplay: (this: GlobalEventHandlers, ev: Event) => any;

	oncanplaythrough: (this: GlobalEventHandlers, ev: Event /* eslint-disable class-methods-use-this */) => any;

	onchange: (this: GlobalEventHandlers, ev: Event) => any /* eslint-disable class-methods-use-this */;

	onclick: (this: GlobalEventHandlers, ev: MouseEvent) => any;

	onclose: (this: GlobalEventHandlers, ev: Event) => any;

	oncontextmenu: (this: GlobalEventHandlers, ev: MouseEvent) => any;

	oncuechange: (this: GlobalEventHandlers, ev: Event) => /* eslint-disable class-methods-use-this */ any;

	ondblclick: (
		this: GlobalEventHandlers,
		ev: MouseEvent,
		/* eslint-disable class-methods-use-this */
	) => any;

	ondrag: (this: GlobalEventHandlers, ev: DragEvent) => any;

	ondragend: (this: GlobalEventHandlers, ev: DragEvent) => any;

	ondragenter: (
		this: GlobalEventHandlers,
		ev: DragEvent,
		/* eslint-disable class-methods-use-this */
	) => any;

	ondragleave: (
		this: GlobalEventHandlers,
		ev: DragEvent,
		/* eslint-disable class-methods-use-this */
	) => any;

	ondragover: (this: GlobalEventHandlers, ev: DragEvent) => any;

	ondragstart: (
		this: GlobalEventHandlers,
		ev: DragEvent,
		/* eslint-disable class-methods-use-this */
	) => any;

	ondrop: (this: GlobalEventHandlers, ev: DragEvent) => any;

	ondurationchange: (this: GlobalEventHandlers, ev: Event /* eslint-disable class-methods-use-this */) => any;

	onemptied: (this: GlobalEventHandlers, ev: Event) => any;

	onended: (this: GlobalEventHandlers, ev: Event) => any;

	onerror: OnErrorEventHandlerNonNull;

	onfocus: (this: GlobalEventHandlers, ev: FocusEvent) => any;

	onformdata: (this: GlobalEventHandlers, ev: FormDataEvent) => any;

	ongotpointercapture: (this: GlobalEventHandlers, ev: PointerEvent) => any;

	oninput: (this: GlobalEventHandlers, ev: Event) => any;

	oninvalid: (this: GlobalEventHandlers, ev: Event) => any;

	onkeydown: (this: GlobalEventHandlers, ev: KeyboardEvent) => any;

	onkeypress: (this: GlobalEventHandlers, ev: KeyboardEvent) => any;

	onkeyup: (
		this: GlobalEventHandlers,
		ev: KeyboardEvent,
		/* eslint-disable class-methods-use-this */
	) => any;

	onload: (this: GlobalEventHandlers, ev: Event) => any;

	onloadeddata: (this: GlobalEventHandlers, ev: Event) => any;

	onloadedmetadata: (this: GlobalEventHandlers, ev: Event /* eslint-disable class-methods-use-this */) => any;

	onloadstart: (this: GlobalEventHandlers, ev: Event) => /* eslint-disable class-methods-use-this */ any;

	onlostpointercapture: (this: GlobalEventHandlers, ev: PointerEvent) => any;

	onmousedown: (this: GlobalEventHandlers, ev: MouseEvent /* eslint-disable class-methods-use-this */) => any;

	onmouseenter: (this: GlobalEventHandlers, ev: MouseEvent) => any;

	onmouseleave: (this: GlobalEventHandlers, ev: MouseEvent) => any;

	onmousemove: (this: GlobalEventHandlers, ev: MouseEvent /* eslint-disable class-methods-use-this */) => any;

	onmouseout: (
		this: GlobalEventHandlers,
		ev: MouseEvent,
		/* eslint-disable class-methods-use-this */
	) => any;

	onmouseover: (this: GlobalEventHandlers, ev: MouseEvent /* eslint-disable class-methods-use-this */) => any;

	onmouseup: (this: GlobalEventHandlers, ev: MouseEvent) => any;

	onpause: (this: GlobalEventHandlers, ev: Event) => any;

	onplay: (this: GlobalEventHandlers, ev: Event) => any;

	onplaying: (this: GlobalEventHandlers, ev: Event) => any;

	onpointercancel: (this: GlobalEventHandlers, ev: PointerEvent) => any;

	onpointerdown: (this: GlobalEventHandlers, ev: PointerEvent) => any;

	onpointerenter: (this: GlobalEventHandlers, ev: PointerEvent) => any;

	onpointerleave: (this: GlobalEventHandlers, ev: PointerEvent) => any;

	onpointermove: (this: GlobalEventHandlers, ev: PointerEvent) => any;

	onpointerout: (this: GlobalEventHandlers, ev: PointerEvent) => any;

	onpointerover: (this: GlobalEventHandlers, ev: PointerEvent) => any;

	onpointerup: (this: GlobalEventHandlers, ev: PointerEvent) => any;

	onprogress: (this: GlobalEventHandlers, ev: ProgressEvent<EventTarget>) => any;

	onratechange: (this: GlobalEventHandlers, ev: Event) => any;

	onreset: (this: GlobalEventHandlers, ev: Event) => any;

	onresize: (this: GlobalEventHandlers, ev: UIEvent) => any;

	onscroll: (this: GlobalEventHandlers, ev: Event) => any /* eslint-disable class-methods-use-this */;

	onsecuritypolicyviolation: (
		this: GlobalEventHandlers,
		/* eslint-disable class-methods-use-this */
		ev: SecurityPolicyViolationEvent,
	) => any;

	onseeked: (this: GlobalEventHandlers, ev: Event) => any /* eslint-disable class-methods-use-this */;

	onseeking: (this: GlobalEventHandlers, ev: Event) => any;

	onselect: (this: GlobalEventHandlers, ev: Event) => any /* eslint-disable class-methods-use-this */;

	onselectionchange: (this: GlobalEventHandlers, ev: Event) => any;

	onselectstart: (this: GlobalEventHandlers, ev: Event) => any;

	onslotchange: (this: GlobalEventHandlers, ev: Event) => any;

	onstalled: (this: GlobalEventHandlers, ev: Event) => any;

	onsubmit: (this: GlobalEventHandlers, ev: SubmitEvent) => any;

	onsuspend: (this: GlobalEventHandlers, ev: Event) => any;

	ontimeupdate: (this: GlobalEventHandlers, ev: Event) => any;

	ontoggle: (this: GlobalEventHandlers, ev: Event) => any /* eslint-disable class-methods-use-this */;

	ontouchcancel?: (this: GlobalEventHandlers, ev: TouchEvent) => any;

	ontouchend?: (this: GlobalEventHandlers, ev: TouchEvent /* eslint-disable class-methods-use-this */) => any;

	ontouchmove?: (this: GlobalEventHandlers, ev: TouchEvent) => any;

	ontouchstart?: (this: GlobalEventHandlers, ev: TouchEvent) => any;

	ontransitioncancel: (this: GlobalEventHandlers, ev: TransitionEvent) => any;

	ontransitionend: (this: GlobalEventHandlers, ev: TransitionEvent) => any;

	ontransitionrun: (this: GlobalEventHandlers, ev: TransitionEvent) => any;

	ontransitionstart: (this: GlobalEventHandlers, ev: TransitionEvent) => any;

	onvolumechange: (this: GlobalEventHandlers, ev: Event) => any;

	onwaiting: (this: GlobalEventHandlers, ev: Event) => any;

	onwebkitanimationend: (this: GlobalEventHandlers, ev: Event) => any;

	onwebkitanimationiteration: (
		this: GlobalEventHandlers,
		/* eslint-disable class-methods-use-this */
		/* eslint-disable class-methods-use-this */ ev: Event,
	) => any;

	onwebkitanimationstart: (this: GlobalEventHandlers, ev: Event) => any;

	onwebkittransitionend: (this: GlobalEventHandlers, ev: /* eslint-disable class-methods-use-this */ Event) => any;

	onwheel: (this: GlobalEventHandlers, ev: WheelEvent) => any;

	autofocus: boolean;

	dataset: DOMStringMap;

	nonce?: string;

	tabIndex: number;

	blur(): void {
		throw new Error('Method not implemented.');
	}

	focus(options?: FocusOptions): void {
		throw new Error('Method not implemented.');
	}
}

export enum Html5VideoEvent {
	ABORT = 'abort',
	CANPLAY = 'canplay',
	CANPLAYTHROUGH = 'canplaythrough',
	DURATIONCHANGE = 'durationchange',
	EMPTIED = 'emptied',
	ENDED = 'ended',
	ERROR = 'error',
	LOADEDDATA = 'loadeddata',
	LOADEDMETADATA = 'loadedmetadata',
	LOADSTART = 'loadstart',
	PAUSE = 'pause',
	PLAY = 'play',
	PLAYING = 'playing',
	PROGRESS = 'progress',
	RATECHANGE = 'ratechange',
	SEEKED = 'seeked',
	SEEKING = 'seeking',
	STALLED = 'stalled',
	SUSPEND = 'suspend',
	TIMEUPDATE = 'timeupdate',
	VOLUMECHANGE = 'volumechange',
	WAITING = 'waiting',
}

import { SetTimeAction } from '../../../actions';
import * as ATTRIBUTES from '../../../attributes';
import { IVideoObjectProperties, IVideoStreamProperties, VideoObjectEvent, VideoStreamMediaType } from '../../../types';
import { isNumeric } from '../../../utils';
import { AbstractHtml5VideoObjectImpl } from '../abstract/abstract';
import { Html5VideoEvent } from './types';

/**
 * Video element ready states
 */
const VIDEO_ELEMENT_READY_STATES = {
	HAVE_NOTHING: 0, //	No information is available about the media resource.
	HAVE_METADATA: 1, // Enough of the media resource has been retrieved that the metadata attributes are initialized. Seeking will no longer raise an exception.
	HAVE_CURRENT_DATA: 2, // Data is available for the current playback position, but not enough to actually play more than one frame.
	HAVE_FUTURE_DATA: 3, // Data for the current playback position as well as for at least a little bit of time into the future is available (in other words, at least two frames of video, for example).
	HAVE_ENOUGH_DATA: 4, // Enough data is available—and the download rate is high enough—that the media can be played through to the end without interruption.
};

/**
 * Map readyState => Video obect event
 */
const READY_STATES_TO_EVENT_NAMES = new Map([
	[VIDEO_ELEMENT_READY_STATES.HAVE_METADATA, VideoObjectEvent.LOADEDMETADATA],
	[VIDEO_ELEMENT_READY_STATES.HAVE_CURRENT_DATA, VideoObjectEvent.LOADEDDATA],
	[VIDEO_ELEMENT_READY_STATES.HAVE_FUTURE_DATA, VideoObjectEvent.CANPLAY],
	[VIDEO_ELEMENT_READY_STATES.HAVE_ENOUGH_DATA, VideoObjectEvent.CANPLAYTHROUGH],
]);

/**
 * Wrapper for HTML5 video object
 *
 * ```typescript
 * const html5VideoObject = new HTML5VideoObject();
 * ```
 */
export class NativeHtml5VideoObjectImpl extends AbstractHtml5VideoObjectImpl {
	/**
	 * Cached currentTime
	 */
	private cachedCurrentTime: number = NaN;

	/**
	 * Used stream type
	 */
	protected streamType: VideoStreamMediaType = VideoStreamMediaType.MP4;

	/**
	 * Constructor
	 */
	constructor(properties: IVideoObjectProperties = {}) {
		const { eventMap = {}, ...rest } = properties;

		super({
			...rest,
			eventMap: {
				[Html5VideoEvent.CANPLAY]: VideoObjectEvent.CANPLAY, // necessary to set (start) time
				[Html5VideoEvent.CANPLAYTHROUGH]: VideoObjectEvent.CANPLAYTHROUGH, // necessary to set (start) time
				[Html5VideoEvent.LOADEDDATA]: VideoObjectEvent.LOADEDDATA, // necessary to set (start) time
				[Html5VideoEvent.LOADEDMETADATA]: VideoObjectEvent.LOADEDMETADATA, // necessary to set (start) time
				[Html5VideoEvent.ENDED]: VideoObjectEvent.ENDED,
				[Html5VideoEvent.ERROR]: () => {
					this.cachedCurrentTime = NaN;
					return {
						detail: { error: this.videoElement.error },
						type: VideoObjectEvent.ERROR,
					};
				},
				[Html5VideoEvent.PLAYING]: VideoObjectEvent.PLAYING,
				[Html5VideoEvent.PAUSE]: VideoObjectEvent.PAUSE,
				[Html5VideoEvent.SEEKED]: () => {
					this.cachedCurrentTime = NaN;
					return {
						type: VideoObjectEvent.SEEKED,
					};
				},
				[Html5VideoEvent.SEEKING]: VideoObjectEvent.SEEKING,
				[Html5VideoEvent.TIMEUPDATE]: () => ({
					detail: {
						duration: this.getDuration(),
						time: this.getTime(),
					},
					type: VideoObjectEvent.TIMEUPDATE,
				}),
				[Html5VideoEvent.WAITING]: VideoObjectEvent.WAITING,
				...eventMap,
			},
		});
	}

	/*
	 * Protected methods
	 */

	/**
	 * Attach source to video object
	 *
	 * @param stream - Video stream
	 */
	protected attachSource = (stream: IVideoStreamProperties): void => {
		super.attachSource(stream);

		const { startTime, url } = stream;
		this.setAttribute(ATTRIBUTES.SOURCE, url);

		if (typeof startTime === 'number' && startTime >= 0) {
			this.setTime(startTime);
		}
	};

	/**
	 * Bind event handlers
	 */
	protected bindEvents(): void {
		// bind proxied events
		Object.keys(this.eventMap).forEach((key: string) => {
			this.videoElement.addEventListener(key, this.proxyEvent, false);
		});
	}

	/**
	 * Detach source from video object
	 *
	 */
	protected detachSource(): void {
		this.videoElement.removeAttribute(ATTRIBUTES.SOURCE);
	}

	/**
	 * Initialize playback
	 *
	 * @param stream - Video stream properties
	 */
	protected initialize(stream: IVideoStreamProperties): void {
		this.attachSource(stream);
	}

	/**
	 * Unbind event handlers
	 */
	protected unbindEvents(): void {
		// unbind proxied events
		Object.keys(this.eventMap).forEach((key: string) => {
			this.videoElement.removeEventListener(key, this.proxyEvent, false);
		});
	}

	/*
	 * Public methods
	 */

	/**
	 * Destroy video object
	 */
	public destroy(): Promise<void> {
		return this.reset().then(() => {
			return super.destroy();
		});
	}

	/**
	 * Get playback time in seconds
	 *
	 * @returns playback time in seconds or NaN
	 */
	public getTime(): number {
		return isNumeric(this.cachedCurrentTime) ? this.cachedCurrentTime : this.videoElement.currentTime;
	}

	/**
	 * Pause video
	 *
	 */
	public pause(): void {
		this.videoElement.pause();
	}

	public showVideoElement(): void {};

	public hideVideoElement(): void {};

	/**
	 * Reset video object (stopped and source detached)
	 */
	public reset(): Promise<void> {
		return this.actions
			.pause()
			.then(() => (this.actions.setTime as SetTimeAction)(0))
			.then(() => {
				const result = super.reset();
				this.detachSource();
				this.videoElement.load();
				return result;
			});
	}

	/**
	 * Resume video
	 *
	 */
	public resume(): void {
		const promise = this.videoElement.play();

		if (promise && promise.catch && typeof Promise !== 'undefined') {
			promise.catch((error) => {
				if (error.name === 'NotAllowedError') {
					this.trigger(VideoObjectEvent.PLAYBACKNOTALLOWED);
				}
			});
		}
	}

	/**
	 * Set (playback) time in seconds
	 *
	 * @param seconds - (playback) time in seconds
	 */
	public setTime(seconds: number): void {
		// console.log(this.constructor.name, 'setTime', seconds);
		this.cachedCurrentTime = seconds;
		this.waitForReadyState(VIDEO_ELEMENT_READY_STATES.HAVE_METADATA, () => {
			// console.log(this.constructor.name, 'waited for ready state', seconds);

			// fail gracefully when current time same as time to set
			if (this.videoElement.currentTime === this.cachedCurrentTime) {
				this.cachedCurrentTime = NaN;
				this.trigger(VideoObjectEvent.SEEKINGNOTAVAILABLE);
				return;
			}

			try {
				this.videoElement.currentTime = this.cachedCurrentTime;
			} catch (error) {
				/* Hack from dash js */
				if (this.videoElement.readyState === 0 && error.code === error.INVALID_STATE_ERR) {
					setTimeout(() => {
						this.videoElement.currentTime = this.cachedCurrentTime;
					}, 400);
				}
			}
		});
	}

	public getAvailableAudioTracks(): any {
		console.warn('getAvailableAudioTracks not implemented');
	}

	public getCurrentAudioTrack(): any {
		console.warn('getCurrentAudioTrack not implemented');
	}

	public setCurrentAudioTrack(value: any) {
		console.warn('setCurrentAudioTrack not implemented');
	}

	public getAvailableSubtitles(): any  {
		console.warn('getAvailableSubtitles not implemented');
	}

	public getCurrentSubtitles(): any {
		console.warn('getCurrentSubtitles not implemented');
	}

	public setCurrentSubtitles(value: any) {
		console.warn('setCurrentSubtitles not implemented');
	}

	/*
	 * Private methods
	 */

	/**
	 * Wait for ready state
	 *
	 * @param readyState - Ready state to wait for
	 * @param callback - Callback to trigger when ready state is reached
	 */
	private waitForReadyState(readyState: number, callback: EventListenerOrEventListenerObject): void {
		// console.log(this.constructor.name, 'waitForReadyState', `${this.videoElement.readyState} >= ${readyState}`);

		if (readyState === VIDEO_ELEMENT_READY_STATES.HAVE_NOTHING || this.videoElement.readyState >= readyState) {
			if (typeof callback === 'function') {
				callback(undefined);
			} else {
				callback?.handleEvent(undefined);
			}
		} else {
			this.internalEventListeners.push(this.once(READY_STATES_TO_EVENT_NAMES.get(readyState), callback));
		}
	}
}

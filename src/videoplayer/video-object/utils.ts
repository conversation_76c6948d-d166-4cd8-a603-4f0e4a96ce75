/**
 * Check if value is not NaN, null or undefined
 *
 * @param value - Number to check
 * @returns Value is numeric
 */
export const isNumeric = (value: any) => !(Number.isNaN(value) || value == null);

/**
 * Helper function to match file extention with optional url parameters at the end
 *
 * @param url - URL to match
 * @param ext - Extention to find
 * @returns - flag if extention is found in URL
 */
export const matchFileExtention = (url: string, ext: string): boolean => {
	return !!url?.match(new RegExp(`\\.${ext}(\\?+.*)?$`));
};

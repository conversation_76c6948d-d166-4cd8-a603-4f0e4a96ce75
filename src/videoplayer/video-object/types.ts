import { IEventBus, ProxyEventMap } from '../event-bus';
import { MediaPlayerSettingClass } from "dashjs";
import { VideoData } from '@apptypes/VideoResponseBody';

/**
 * Generic video object events
 */
export enum VideoObjectEvent {
	ABORT = 'abort', // document event types
	CANPLAY = 'canplay',
	CANPLAYTHROUGH = 'canplaythrough',
	DURATIONCHANGE = 'durationchange',
	ENDED = 'ended',
	EMPTIED = 'emptied',
	ERROR = 'error',
	LOADEDDATA = 'loadeddata',
	LOADEDMETADATA = 'loadedmetadata',
	PAUSE = 'pause',
	PLAYING = 'playing',
	PLAYBACKNOTALLOWED = 'playbacknotallowed',
	PROGRESS = 'progress',
	RATECHANGE = 'ratechange',
	SEEKED = 'seeked',
	SEEKING = 'seeking',
	SEEKINGNOTAVAILABLE = 'seekingnotavailable',
	STOPPED = 'stopped',
	TIMEUPDATE = 'timeupdate',
	WAITING = 'waiting',
}

/**
 * Generic video object state events
 */
export enum VideoObjectStateEvent {
	// CHANGE
	CHANGE = 'STATE_CHANGE',
	// ENTER
	ENTER_IDLE = 'STATE_ENTER_IDLE',
	ENTER_LOADING = 'STATE_ENTER_LOADING',
	ENTER_PLAYING = 'STATE_ENTER_PLAYING',
	ENTER_PAUSED = 'STATE_ENTER_PAUSED',
	ENTER_STOPPED = 'STATE_ENTER_STOPPED',
	ENTER_ENDED = 'STATE_ENTER_ENDED',
	ENTER_ERROR = 'STATE_ENTER_ERROR',
	// LEAVE
	LEAVE_IDLE = 'STATE_LEAVE_IDLE',
	LEAVE_LOADING = 'STATE_LEAVE_LOADING',
	LEAVE_PLAYING = 'STATE_LEAVE_PLAYING',
	LEAVE_PAUSED = 'STATE_LEAVE_PAUSED',
	LEAVE_STOPPED = 'STATE_LEAVE_STOPPED',
	LEAVE_ENDED = 'STATE_LEAVE_ENDED',
	LEAVE_ERROR = 'STATE_LEAVE_ERROR',
}

/**
 * Generic video object implementation
 */
export interface IVideoObjectImpl extends IEventBus {
	destroy(): Promise<void>;
	getDuration(): number;
	getTime(): number;
	isPaused(): boolean;
	load(stream: IVideoStreamProperties, videoData: VideoData): Promise<void>;
	pause(): void;
	reset(): Promise<void>;
	resume(): void;
	setTime(time: number | string): void;
	stop(): Promise<void>;
	getAvailableAudioTracks(): any;
	getCurrentAudioTrack(): any;
	setCurrentAudioTrack(value: any): void;
	getAvailableSubtitles(): any;
	getCurrentSubtitles(): any;
	setCurrentSubtitles(value:any): void;
	stream?: IVideoStreamProperties;
	showVideoElement(): void;
	hideVideoElement(): void;
}

/**
 * Generic video object state
 */
export interface IVideoObjectState extends Omit<IVideoObjectImpl, 'destroy' | 'load' | 'reset' | 'stop' | 'stream'> {
	destroy(): void;
	id: VideoObjectStateId;
	isPlaying(): boolean;
	load(stream: IVideoStreamProperties, videoData?: VideoData): void;
	onEnter(previous: VideoObjectStateId, error?: Error): IVideoObjectState;
	onLeave(next: VideoObjectStateId): IVideoObjectState;
	reset(): void;
	stop(): void;
}

/**
 * Generic video object
 */
export interface IVideoObject extends Omit<IVideoObjectState, 'id' | 'onEnter' | 'onLeave'> {
	isIdle(): boolean;
	instanceOf(instance: any): boolean;
	getStream(): IVideoStreamProperties;
}

/**
 * Video object properties
 */
export interface IVideoObjectConfig {
	innerHTML?: boolean;
	reuseVideoObject?: boolean;
	timer?: number;
}

/**
 * Video object properties
 */
export interface IVideoObjectProperties {
	config?: IVideoObjectConfig;
	eventMap?: ProxyEventMap;
	parentNode?: HTMLElement | null;
	playerSettings?: MediaPlayerSettingClass;
}

/**
 * Dom properties of video object
 */
export interface IVideoObjectDomProperties {
	autoplay?: boolean;
	height?: string;
	id?: string;
	left?: string;
	position?: string;
	top?: string;
	width?: string;
}

/**
 * Video stream properties
 */
export interface IVideoStreamProperties {
	livePlayPositionOffset?: number;
	startTime?: number | string;
	url: string;
}

/**
 * Video stream media type
 */
export enum VideoStreamMediaType {
	DASH = 'application/dash+xml',
	HEVC = 'video/H265',
	HLS = 'application/vnd.apple.mpegurl',
	MP4 = 'video/mp4',
	UNKNOWN = 'unknown',
}

/**
 * Ids of states of video object
 */
export enum VideoObjectStateId {
	IDLE = 'IDLE',
	LOADING = 'LOADING',
	PLAYING = 'PLAYING',
	PAUSED = 'PAUSED',
	// SEEKING = 'SEEKING', // for now seeking is part of playback state
	ENDED = 'ENDED',
	ERROR = 'ERROR',
	STOPPED = 'STOPPED',
	PLAYBACKNOTALLOWED = 'playbacknotallowed',
}

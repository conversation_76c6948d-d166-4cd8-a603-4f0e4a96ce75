import { createCancellableSignal, ICancellableSignal, onCancelCallback } from './cancellable';

export const createWaitingSignal = (milliSeconds: number, onCancel?: onCancelCallback): ICancellableSignal => {
	const cancelable = createCancellableSignal();

	const signal = new Promise<void>((resolve, reject) => {
		const timeOut = setTimeout(() => {
			reject(new Error(`Waited for ${milliSeconds}ms`));
		}, milliSeconds);

		cancelable.signal.catch((err) => {
			onCancel?.(err);
			resolve();
			clearTimeout(timeOut);
		});
	});

	return {
		cancel: cancelable.cancel,
		signal,
	};
};

export interface ICancellableSignal {
	cancel: () => void;
	signal: Promise<unknown>;
}

export type onCancelCallback = (err: Error) => void;

export const createCancellableSignal = (onCancel?: onCancelCallback): ICancellableSignal => {
	let cancel: () => void;

	const signal = new Promise((resolve, reject) => {
		cancel = () => {
			const err = new Error('Promise was cancelled');
			onCancel?.(err);
			reject(err);
		};
	});

	return {
		cancel,
		signal,
	};
};

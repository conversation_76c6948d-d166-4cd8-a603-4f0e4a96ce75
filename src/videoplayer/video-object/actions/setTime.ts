import { IEventBusListener } from '../../event-bus';
import { IVideoObjectImpl, VideoObjectEvent } from '../types';
import { Action } from './action';
import { ICancellableSignal } from './signals/cancellable';
import { createWaitingSignal } from './signals/waiting';

/**
 * SetTime managed action
 */
export type SetTimeAction = Action<number>;

/**
 * Create managed setTime action
 *
 * @param videoObjectImpl - Video object implementation
 * @returns SetTimeAction
 */
export const createSetTimeAction = (videoObjectImpl: IVideoObjectImpl): SetTimeAction => {
	// blocked flag
	let blocked: boolean;

	// waiting signal
	let waiting: ICancellableSignal;

	// event handlers
	let onSeekingNotAvailable: IEventBusListener;
	let onSeeked: IEventBusListener;
	let onError: IEventBusListener;

	// cleanup function
	const cleanup = () => {
		blocked = false;

		waiting?.cancel();
		waiting = undefined;

		if (onSeekingNotAvailable) {
			videoObjectImpl.off(onSeekingNotAvailable.type, onSeekingNotAvailable.callback);
			onSeekingNotAvailable = undefined;
		}

		if (onSeeked) {
			videoObjectImpl.off(onSeeked.type, onSeeked.callback);
			onSeeked = undefined;
		}

		if (onError) {
			videoObjectImpl.off(onError.type, onError.callback);
			onError = undefined;
		}
	};

	// action
	const setTimeAction: SetTimeAction = (seconds, options) => {
		if (blocked) return Promise.reject(new Error('SetTime unavailable'));

		// action options
		const { cancel, timeOut = 5 } = options || {};

		// block action till it resolves/rejects
		blocked = true;

		// create new waiting signal
		waiting = timeOut !== undefined ? createWaitingSignal(timeOut * 1000) : undefined;

		// action result
		return new Promise<void>((resolve, reject) => {
			// on finished handler
			const onFinished = (error?: Error): void => {
				cleanup();

				if (error) {
					reject(error);
				} else {
					resolve();
				}
			};

			// on seeking not available event
			onSeekingNotAvailable = videoObjectImpl.once(VideoObjectEvent.SEEKINGNOTAVAILABLE, () => onFinished());

			// on seeked event
			onSeeked = videoObjectImpl.once(VideoObjectEvent.SEEKED, () => onFinished());

			// on error event
			onError = videoObjectImpl.once(VideoObjectEvent.ERROR, (evt: CustomEvent) => {
				onFinished(evt?.detail?.error || new Error('SetTime failed'));
			});

			// on waiting signal
			waiting?.signal.catch(() => onFinished(new Error('SetTime timed out')));

			// on cancel signal
			cancel?.signal.catch(onFinished);

			// action
			videoObjectImpl.setTime(seconds);
		});
	};

	return setTimeAction;
};

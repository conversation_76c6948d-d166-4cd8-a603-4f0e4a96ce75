/**
 * Create batched (halted) actions
 *
 * @param actions - Actions to batch
 * @returns PauseAction
 */
export const batchActions = (actions: (() => Promise<void>)[]) => {
	// blocked flag
	let blocked: boolean;

	const execute = (): Promise<void> => {
		if (blocked) return Promise.reject(new Error('Actions halted'));

		blocked = true;

		return actions
			.reduce((p, action) => p.then(() => action()), Promise.resolve())
			.finally(() => {
				blocked = false;
			});
	};

	return execute;
};

import { IEventBusListener } from '../../event-bus';
import { IVideoObjectImpl, VideoObjectEvent } from '../types';
import { Action } from './action';
import { ICancellableSignal } from './signals/cancellable';
import { createWaitingSignal } from './signals/waiting';

/**
 * Pause action
 */
export type PauseAction = Action<void>;

/**
 * Create managed pause action
 *
 * @param videoObjectImpl - Video object implementation
 * @returns PauseAction
 */
export const createPauseAction = (videoObjectImpl: IVideoObjectImpl): PauseAction => {
	// blocked flag
	let blocked: boolean;

	// waiting signal
	let waiting: ICancellableSignal;

	// event handlers
	let onPause: IEventBusListener;
	let onError: IEventBusListener;

	// cleanup function
	const cleanup = () => {
		blocked = false;

		waiting?.cancel();
		waiting = undefined;

		if (onPause) {
			videoObjectImpl.off(onPause.type, onPause.callback);
			onPause = undefined;
		}
		if (onError) {
			videoObjectImpl.off(onError.type, onError.callback);
			onError = undefined;
		}
	};

	// action
	const pauseAction: PauseAction = (value, options) => {
		// block if blocked
		if (blocked) return Promise.reject(new Error('Pause unavailable'));

		// resolve if already paused
		if (videoObjectImpl.isPaused()) return Promise.resolve();

		// action options
		const { cancel, timeOut = 5 } = options || {};

		// block action till it resolves/rejects
		blocked = true;

		// create new waiting signal
		waiting = timeOut !== undefined ? createWaitingSignal(timeOut * 1000) : undefined;

		// action result
		return new Promise<void>((resolve, reject) => {
			// on finished handler
			const onFinished = (error?: Error): void => {
				cleanup();

				if (error) {
					console.log('onFinished', 'error', error);
					reject(error);
				} else {
					resolve();
				}
			};

			// on pause event
			onPause = videoObjectImpl.once(VideoObjectEvent.PAUSE, () => onFinished());

			// on error event
			onError = videoObjectImpl.once(VideoObjectEvent.ERROR, (evt: CustomEvent) => {
				onFinished(evt?.detail?.error || new Error('Pause failed'));
			});

			// on waiting signal
			waiting?.signal.catch((error) => {
				console.log('waiting', 'error', error);
				onFinished(new Error('Pause timed out'));
			});

			// on cancel signal
			cancel?.signal.catch(() => {
				onFinished();
			});

			// action
			videoObjectImpl.pause();
		});
	};

	return pauseAction;
};

import { PauseAction as PauseActionType } from './pause';
import { SetTimeAction as SetTimeActionType } from './setTime';

export type PauseAction = PauseActionType;
export type SetTimeAction = SetTimeActionType;

export type ActionType = PauseActionType | SetTimeActionType;

export type ActionNames = 'pause' | 'resume' | 'setTime';

export { createPauseAction } from './pause';
export { createSetTimeAction } from './setTime';

import React from 'react';

import { useParams } from 'react-router-dom';

import { Html5VideoObjectImpl } from './impl/html5';
import { IVideoObject, IVideoStreamProperties } from './types';
import { VideoObject } from './video-object';
import {useNormalizedParams} from "@hooks/useNormalizedParams";

let videoObject: IVideoObject;

const ReactVideoObjectFactory: React.FC<IVideoStreamProperties> = ({ ...rest }) => {
	const params = useNormalizedParams();

	let startTime = params.startTime ? Number(params.startTime) : undefined;
	startTime = !Number.isNaN(startTime) ? startTime : undefined;

	console.log('startTime', startTime);

	if (!videoObject || !videoObject.instanceOf(Html5VideoObjectImpl)) {
		videoObject?.destroy();

		// eslint-disable-next-line @typescript-eslint/dot-notation, no-multi-assign
		window['video'] = videoObject = new VideoObject(
			new Html5VideoObjectImpl({ parentNode: document.getElementById('video') }),
		);
		videoObject.on('playing', () => console.log('ReactVideoObjectFactory', 'ON PLAYING'));
		videoObject.on('waiting', () => console.log('ReactVideoObjectFactory', 'ON WAITING'));
		console.log('load', startTime, rest);
		videoObject.load({ ...rest, startTime });
	}

	return null;
};

export { ReactVideoObjectFactory };

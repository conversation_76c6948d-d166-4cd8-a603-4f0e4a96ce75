import { VideoObjectStateId } from '../../types';
import { AbstractVideoObjectState } from '../state';

/**
 * Abstract state when video stream is stoppable
 */
export abstract class StoppableState extends AbstractVideoObjectState {
	/**
	 * Stop video
	 */
	public stop(): void {
		if (!this.isBlocked) {
			this.isBlocked = true;

			this.videoObjectImpl
				.stop()
				.then(() => {
					this.triggerStateChange(VideoObjectStateId.STOPPED);
				})
				.catch((error) => {
					this.triggerStateChange(VideoObjectStateId.ERROR, undefined, error);
				})
				.finally(() => {
					this.isBlocked = false;
				});
		}
	}
}

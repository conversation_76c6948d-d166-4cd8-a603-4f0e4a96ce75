import {
	IVideoObjectImpl,
	IVideoObjectProperties,
	IVideoObjectState,
	VideoObjectEvent,
	VideoObjectStateId,
} from '../../types';
import { AbstractVideoObjectState } from '../state';

/**
 * Abstract state when video is playing or paused (aka playback)
 */
export abstract class PlayblackState extends AbstractVideoObjectState {
	/**
	 * Constructor
	 */
	constructor(videoObjectImpl: IVideoObjectImpl, properties: IVideoObjectProperties = {}) {
		const { eventMap = {}, ...rest } = properties;

		super(videoObjectImpl, {
			...rest,
			eventMap: {
				...eventMap,
				[VideoObjectEvent.CANPLAY]: true,
				[VideoObjectEvent.SEEKED]: true,
				[VideoObjectEvent.SEEKING]: true,
				[VideoObjectEvent.TIMEUPDATE]: true,
				[VideoObjectEvent.WAITING]: true,
			},
		});
	}

	/**
	 * On entering state
	 *
	 * @param previous - Previous state
	 * @returns VideoObjectState
	 */
	public onEnter(previous: VideoObjectStateId): IVideoObjectState {
		super.onEnter(previous);

		// console.log(this.constructor.name, 'onEnter', 'stream', this.videoObjectImpl.stream);

		// on ended event change state to ended state
		this.triggerStateChange(VideoObjectStateId.ENDED, VideoObjectEvent.ENDED);

		return this;
	}

	/**
	 * Set time video
	 */
	public setTime(value: number): void {
		this.videoObjectImpl.setTime(value);
	}

	/**
	 * Stop video
	 */
	public stop(): void {
		if (!this.isBlocked) {
			this.isBlocked = true;

			this.videoObjectImpl
				.stop()
				.then(() => {
					this.triggerStateChange(VideoObjectStateId.STOPPED);
				})
				.catch((error) => {
					this.triggerStateChange(VideoObjectStateId.ERROR, undefined, error);
				})
				.finally(() => {
					this.isBlocked = false;
				});
		}
	}
}

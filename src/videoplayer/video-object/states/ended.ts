import { IVideoObjectState, VideoObjectEvent, VideoObjectStateId } from '../types';
import { IdleState } from './idle';

/**
 * State when video stream ended
 */
export class EndedState extends IdleState {
	/**
	 * State id
	 */
	public id = VideoObjectStateId.ENDED;

	/**
	 * On entering state
	 *
	 * @param previous - Previous state
	 * @returns VideoObjectState
	 */
	public onEnter(previous: VideoObjectStateId): IVideoObjectState {
		super.onEnter(previous);

		this.trigger(VideoObjectEvent.ENDED);

		return this;
	}

	/**
	 * Resume video
	 */
	public resume() {
		if (!this.isBlocked) {
			this.isBlocked = true;

			// on playing event change state to playing state
			this.triggerStateChange(VideoObjectStateId.PLAYING, VideoObjectEvent.PLAYING);
			this.videoObjectImpl.resume();
		}
	}
}

import { IVideoObjectState, VideoObjectEvent, VideoObjectStateId } from '../types';
import { matchFileExtention } from '../utils';
import { StoppedState } from './stopped';

/**
 * State when video stream is initial (first time) loading
 */
export class LoadingState extends StoppedState {
	/**
	 * State id
	 */
	public id = VideoObjectStateId.LOADING;

	/**
	 * On entering state
	 *
	 * @param previous - Previous state
	 * @returns IVideoObjectState
	 */
	public onEnter(previous: VideoObjectStateId): IVideoObjectState {
		super.onEnter(previous);

		this.trigger(VideoObjectEvent.WAITING);

		if (matchFileExtention(this.videoObjectImpl.stream.url, 'mp4') && this.videoObjectImpl.stream.startTime) {
			// on seeked event change state to playing state
			this.triggerStateChange(VideoObjectStateId.PLAYING, VideoObjectEvent.SEEKED);
		} else {
			// on playing event change state to playing state
			this.triggerStateChange(VideoObjectStateId.PLAYING, VideoObjectEvent.PLAYING);
		}

		return this;
	}
}

import { IVideoStreamProperties, VideoObjectStateId } from '../types';
import { AbstractVideoObjectState } from './state';
import { VideoData } from '@apptypes/VideoResponseBody';

/**
 * State when video stream is idle (no video information)
 */
export class IdleState extends AbstractVideoObjectState {
	/**
	 * State id
	 */
	public id = VideoObjectStateId.IDLE;

	/**
	 * Load video
	 *
	 * @param stream - Video stream
	 */
	public load(stream: IVideoStreamProperties, videoData:VideoData): void {
		// console.log(`${this.constructor.name} load ${stream} is ${this.isBlocked}`);

		if (!this.isBlocked) {
			this.isBlocked = true;

			this.videoObjectImpl
				.load(stream, videoData)
				.then(() => {
					// console.log('loading');
					// change state to loading state
					this.triggerStateChange(VideoObjectStateId.LOADING);
				})
				.catch((error) => {
					// change state to error state
					console.log('load', error);
					this.triggerStateChange(VideoObjectStateId.ERROR, undefined, error);
				});
		}
	}
}

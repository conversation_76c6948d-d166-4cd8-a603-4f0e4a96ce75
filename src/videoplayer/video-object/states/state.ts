/* eslint-disable class-methods-use-this */

import { IEventBusListener, EventProxy } from '../../event-bus';
import { VideoObjectError } from '../errors';
import {
	IVideoStreamProperties,
	VideoObjectStateId,
	VideoObjectEvent,
	IVideoObjectProperties,
	IVideoObjectImpl,
	IVideoObjectState,
	VideoObjectStateEvent,
} from '../types';
import { emitOfflineEvent, isOnlineBackupCheck } from '@util/offlineEvent';

/**
 * Map video object state id => video object state enter event
 */
const ENTER_STATE_EVENT_NAMES = new Map([
	[VideoObjectStateId.IDLE, VideoObjectStateEvent.ENTER_IDLE],
	[VideoObjectStateId.LOADING, VideoObjectStateEvent.ENTER_LOADING],
	[VideoObjectStateId.PLAYING, VideoObjectStateEvent.ENTER_PLAYING],
	[VideoObjectStateId.PAUSED, VideoObjectStateEvent.ENTER_PAUSED],
	[VideoObjectStateId.STOPPED, VideoObjectStateEvent.ENTER_STOPPED],
	[VideoObjectStateId.ENDED, VideoObjectStateEvent.ENTER_ENDED],
	[VideoObjectStateId.ERROR, VideoObjectStateEvent.ENTER_ERROR],
]);

/**
 * Map video object state id => video object state leave event
 */
const LEAVE_STATE_EVENT_NAMES = new Map([
	[VideoObjectStateId.IDLE, VideoObjectStateEvent.LEAVE_IDLE],
	[VideoObjectStateId.LOADING, VideoObjectStateEvent.LEAVE_LOADING],
	[VideoObjectStateId.PLAYING, VideoObjectStateEvent.LEAVE_PLAYING],
	[VideoObjectStateId.PAUSED, VideoObjectStateEvent.LEAVE_PAUSED],
	[VideoObjectStateId.STOPPED, VideoObjectStateEvent.LEAVE_STOPPED],
	[VideoObjectStateId.ENDED, VideoObjectStateEvent.LEAVE_ENDED],
	[VideoObjectStateId.ERROR, VideoObjectStateEvent.LEAVE_ERROR],
]);

/**
 * Abstract state of video object
 */
export abstract class AbstractVideoObjectState extends EventProxy implements IVideoObjectState {
	/**
	 * State id
	 */
	public abstract id: VideoObjectStateId;

	/**
	 * Internal event listsners
	 */
	protected internalEventListeners: IEventBusListener[] = [];

	/**
	 * Blocked flag
	 */
	protected isBlocked = false;

	/**
	 * Video object implementation
	 */
	protected videoObjectImpl: IVideoObjectImpl;

	/**
	 * Constructor
	 *
	 * @param videoObjectImpl - Video object implemenation (html5 (native/dash))
	 */
	constructor(videoObjectImpl: IVideoObjectImpl, { eventMap }: IVideoObjectProperties = {}) {
		super(eventMap);

		this.videoObjectImpl = videoObjectImpl;
	}

	/*
	 * Public methods
	 */

	/**
	 * Destroy video element
	 */
	public destroy() {
		// Note, we don't check if the state is blocked.
		// We want the video to be destroyed regardless of its state.
		this.videoObjectImpl
			.destroy()
			.then(() => {
				this.triggerStateChange(VideoObjectStateId.IDLE);
			})
			.catch((error) => {
				this.triggerStateChange(VideoObjectStateId.ERROR, undefined, error);
			});
	}

	/**
	 * Get duration of video
	 */
	public getDuration(): number {
		return this.videoObjectImpl.getDuration();
	}

	/**
	 * Get current time of video
	 */
	public getTime(): number {
		return this.videoObjectImpl.getTime();
	}

	/**
	 * Is paused
	 */
	public isPaused(): boolean {
		return false;
	}

	/**
	 * Is playing
	 */
	public isPlaying(): boolean {
		return false;
	}

	/**
	 * Load video
	 *
	 * @param stream - Video stream properties
	 */
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	public async load(stream: IVideoStreamProperties): void {
		const isBackupOffline = await isOnlineBackupCheck();
		if(isBackupOffline){
			throw new VideoObjectError(`Invalid action`);
		}else{
			emitOfflineEvent(true)
		}
	}

	/**
	 * On entering state
	 *
	 * @param previous - Previous state
	 * @returns VideoObjectState
	 */
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	public onEnter(previous: VideoObjectStateId, error?: Error): IVideoObjectState {
		// console.log(this.constructor.name, 'on enter state');

		if (previous !== this.id) {
			this.trigger(ENTER_STATE_EVENT_NAMES.get(this.id));
		}

		this.bindEvents();

		// on error change state to error state
		this.triggerStateChange(VideoObjectStateId.ERROR, VideoObjectEvent.ERROR);

		return this;
	}

	/**
	 * On leaving state
	 *
	 * @param next - Next state id
	 * @returns VideoObjectState
	 */
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	public onLeave(next: VideoObjectStateId): IVideoObjectState {
		// console.log(`${this.constructor.name} onLeave()  - isBlocked ${this.isBlocked} set = false`);

		this.isBlocked = false;
		this.unbindEvents();

		if (next !== this.id) {
			this.trigger(LEAVE_STATE_EVENT_NAMES.get(this.id));
		}

		return this;
	}

	/**
	 * Pause video
	 */
	public async pause(): void {
		const isBackupOffline = await isOnlineBackupCheck();
		if(isBackupOffline){
			throw new VideoObjectError(`Invalid action`);
		}else{
			emitOfflineEvent(true)
		}
	}

	/**
	 * Reset video
	 */
	public reset() {
		if (!this.isBlocked) {
			this.isBlocked = true;

			this.videoObjectImpl
				.reset()
				.then(() => {
					this.triggerStateChange(VideoObjectStateId.IDLE);
				})
				.catch((error) => {
					this.triggerStateChange(VideoObjectStateId.ERROR, undefined, error);
				})
				.finally(() => {
					console.log(`${this.constructor.name}reset() - isBlocked ${this.isBlocked} set = false`);

					this.isBlocked = false;
				});
		}
	}

	/**
	 * Resume video
	 */
	public async resume(): void {
		const isBackupOffline = await isOnlineBackupCheck();
		if(isBackupOffline){
			throw new VideoObjectError(`Invalid action`);
		}else{
			emitOfflineEvent(true)
		}
	}

	/**
	 * Set time
	 */
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	public async setTime(value: number): void {
		const isBackupOffline = await isOnlineBackupCheck();
		if(isBackupOffline){
			throw new VideoObjectError(`Invalid action`);
		}else{
			emitOfflineEvent(true)
		}
	}

	/**
	 * Stop video
	 */
	public async stop(): void {
		const isBackupOffline = await isOnlineBackupCheck();
		if(isBackupOffline){
			throw new VideoObjectError(`Invalid action`);
		}else{
			emitOfflineEvent(true)
		}
	}

	/*
	 * Protected methods
	 */

	/**
	 * Bind event handlers
	 */
	protected bindEvents(): void {
		// bind proxied events
		Object.keys(this.eventMap).forEach((key: string) => {
			this.videoObjectImpl.on(key, this.proxyEvent);
		});
	}

	/**
	 * Trigger (video object) state change event
	 *
	 * @param id - Video object state id to trigger
	 * @param event - Video object event to listen to
	 */
	protected triggerStateChange(id: VideoObjectStateId, event?: VideoObjectEvent, error?: Error) {
		if (event) {
			this.internalEventListeners.push(
				this.videoObjectImpl.once(event, (evt: CustomEvent) => {
					this.trigger(VideoObjectStateEvent.CHANGE, { error, ...evt.detail, id });
				}),
			);
		} else {
			this.trigger(VideoObjectStateEvent.CHANGE, { error, id });
		}
	}

	/**
	 * Unbind event handlers
	 */
	protected unbindEvents(): void {
		// unbind proxied events
		Object.keys(this.eventMap).forEach((key: string) => {
			this.videoObjectImpl.off(key, this.proxyEvent);
		});

		// unbind internal events
		this.internalEventListeners.forEach(({ type, callback }) => {
			this.off(type, callback);
		});
		this.internalEventListeners = [];
	}

}

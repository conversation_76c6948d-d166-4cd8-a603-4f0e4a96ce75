import { IVideoObjectState, VideoObjectEvent, VideoObjectStateId } from '../types';
import { PlayblackState } from './abstract/playback';

/**
 * State when video stream paused
 */
export class PausedState extends PlayblackState {
	/**
	 * State id
	 */
	public id = VideoObjectStateId.PAUSED;

	/**
	 * Is paused
	 */
	// eslint-disable-next-line class-methods-use-this
	public isPaused(): boolean {
		return true;
	}

	/**
	 * On entering state
	 *
	 * @param previous - Previous state id
	 * @returns VideoObjectState
	 */
	public onEnter(previous: VideoObjectStateId): IVideoObjectState {
		super.onEnter(previous);

		this.trigger(VideoObjectEvent.PAUSE);

		return this;
	}

	/**
	 * Resume video
	 */
	public resume() {
		if (!this.isBlocked) {
			this.isBlocked = true;

			// on playing event change state to playing state
			this.triggerStateChange(VideoObjectStateId.PLAYING, VideoObjectEvent.PLAYING);
			this.videoObjectImpl.resume();
		}
	}

	public pause(): void {
		console.warn('Called pause() in PausedState.');
	}
}

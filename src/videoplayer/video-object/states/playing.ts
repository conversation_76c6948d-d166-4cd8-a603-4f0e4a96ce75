import { IVideoObjectState, VideoObjectEvent, VideoObjectStateId } from '../types';
import { PlayblackState } from './abstract/playback';

/**
 * State when video stream is playing
 */
export class PlayingState extends PlayblackState {
	/**
	 * State id
	 */
	public id = VideoObjectStateId.PLAYING;

	/**
	 * Is playing
	 */
	// eslint-disable-next-line class-methods-use-this
	public isPlaying(): boolean {
		return true;
	}

	/**
	 * On entering state
	 *
	 * @param previous - Previous state
	 * @returns VideoObjectState
	 */
	public onEnter(previous: VideoObjectStateId): IVideoObjectState {
		super.onEnter(previous);

		this.trigger(VideoObjectEvent.PLAYING);

		return this;
	}

	/**
	 * Pause video
	 */
	public pause() {
		if (!this.isBlocked) {
			this.isBlocked = true;

			this.triggerStateChange(VideoObjectStateId.PAUSED, VideoObjectEvent.PAUSE);
			this.videoObjectImpl.pause();
		}
	}

	public resume() {
		console.warn('Called resume() in PlayingState.');
	}
}

import { IVideoObjectState, VideoObjectEvent, VideoObjectStateId } from '../types';
import { IdleState } from './idle';

/**
 * State when video stream throw an error
 */
export class ErrorState extends IdleState {
	/**
	 * State id
	 */
	public id = VideoObjectStateId.ERROR;

	/**
	 * On entering state
	 *
	 * @param previous - Previous state
	 * @returns VideoObjectState
	 */
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	public onEnter(previous: VideoObjectStateId, error?: Error): IVideoObjectState {
		super.onEnter(previous);

		// console.log(this.constructor.name, 'error', error);
		this.trigger(VideoObjectEvent.ERROR, { error });

		return this;
	}
}

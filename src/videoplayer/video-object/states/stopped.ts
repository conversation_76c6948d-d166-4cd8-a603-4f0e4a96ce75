import { VideoObjectEvent, VideoObjectStateId } from '../types';
import { IdleState } from './idle';

/**
 * State when video stream was stopped
 */
export class StoppedState extends IdleState {
	/**
	 * State id
	 */
	public id = VideoObjectStateId.STOPPED;

	/**
	 * Resume video
	 */
	public resume() {
		if (!this.isBlocked) {
			this.isBlocked = true;

			// on playing event change state to playing state
			this.triggerStateChange(VideoObjectStateId.PLAYING, VideoObjectEvent.PLAYING);
			this.videoObjectImpl.resume();
		}
	}
}

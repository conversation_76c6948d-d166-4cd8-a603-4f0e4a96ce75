import React from 'react';

import <PERSON>actD<PERSON> from 'react-dom/client';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';

import { ReactVideoObjectFactory } from './video-object/react-video-object-factory';

const VOD: React.FC<{}> = () => (
	<ReactVideoObjectFactory
		url="http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4"
	/>
);

const LIVE: React.FC<{}> = () => (
	<ReactVideoObjectFactory
		url="https://dash.akamaized.net/envivio/Envivio-dash2/manifest.mpd"
	/>
);

const root = ReactDOM.createRoot(document.getElementById('root') as HTMLElement);
root.render(
	<React.StrictMode>
		<Router>
			<Routes>
				<Route path="/">
					<Route index element={<>INDEX</>} />
					<Route path="html5">
						<Route index element={<>HTML5</>} />
						<Route path="mp4">
							<Route index element={<VOD />} />
							<Route path=":startTime" element={<VOD />} />
						</Route>
						<Route path="dash">
							<Route index element={<LIVE />} />
							<Route path=":startTime" element={<LIVE />} />
						</Route>
					</Route>
				</Route>
			</Routes>
		</Router>
	</React.StrictMode>,
);

// eslint-disable-next-line simple-import-sort/imports
import i18n, { InitOptions, Resource } from 'i18next';
import { initReactI18next } from 'react-i18next';
import Backend from 'i18next-http-backend';
import LanguageDetector from 'i18next-browser-languagedetector';
import { LanguageResponseBody, LanguageTranslation, SupportedLanguageType } from './types/language';
import { COOKIE_USER_LANGUAGE } from './util/cookies';
import { getLanguage } from './data';
import { logError } from './errors';
import { FALLBACK_LANGUAGE } from './constants';
import { config } from 'target';

export const SUPPORTED_LANGUAGES = new Map<SupportedLanguageType, LanguageTranslation>([
  ['fr', { code: 'fr', translationKey: 'i18n__french' }],
  ['de', { code: 'de', translationKey: 'i18n__german' }],
  ['es', { code: 'es', translationKey: 'i18n__spanish' }],
  ['pl', { code: 'pl', translationKey: 'i18n__polish' }],
  ['it', { code: 'it', translationKey: 'italian' }],
  ['en', { code: 'en', translationKey: 'i18n__english' }],
  ['ro', { code: 'ro', translationKey: 'i18n__romanian' }],
]);

let geoLocation: LanguageResponseBody;

export async function getGeoLocation(): Promise<LanguageResponseBody> {
  if (geoLocation) return geoLocation;

  try {
    geoLocation = await getLanguage();
    return geoLocation;
  } catch (error) {
    logError(error, 'WARNING');
    // create a fallback object that matches LanguageResponseBody
    return {
      type: 'fallback',
      version: '1.0',
      country: FALLBACK_LANGUAGE,
      language: FALLBACK_LANGUAGE as SupportedLanguageType,
    };
  }
}

function updateHtmlLangAttribute(lang: string) {
  document.documentElement.setAttribute('lang', lang);
}

export async function i18nInit() {
  const i18nConfig: InitOptions = {
    ...config.i18n,
    resources: config.i18n?.resources as Resource | undefined,
    supportedLngs: Array.from(SUPPORTED_LANGUAGES.keys()),
    fallbackLng: FALLBACK_LANGUAGE,
    interpolation: {
      escapeValue: false, // react already safe from xss
    },
    detection: {
      order: ['cookie', 'localStorage'],
      lookupCookie: COOKIE_USER_LANGUAGE,
      lookupLocalStorage: COOKIE_USER_LANGUAGE,
      // Given we do our own language detection at startup (see: util/languageDetection.ts) we use the empty array
      // here to prevent the library from writing the language to cookies/storage. We only require the lib to do
      // detection in order to load the correct translation file.
      caches: [],
    },
  };

  await i18n
    .use(initReactI18next) // passes i18n down to react-i18next
    .use(Backend)
    .use(LanguageDetector)
    .init(i18nConfig);

  updateHtmlLangAttribute(i18n.language);

  return true;
}

export default i18n;

// Copyright (c) 2020 einbliq.io (decover GmbH). All rights reserved.
var EinbliqIo={PlayStateIds:{CONNECTING:0,BUFFERING:1,CANPLAY:2,PLAYING:3,PAUSE:4,SEEKING:5,STOP:6,ENDED:7,STALLED:8,ABORT:9,ERROR:10,SWITCHED:11},ErrorSeverityIds:{INFORMATION:0,WARNING:1,ERROR:2,CRITICAL:3},adapter:{OIPF:function(e,t){var i={configuration:t,player:e,session:null,manager:function(){var e={lasts:{playPosition:null,playState:null,errorCode:null},intervalTracking:{active:!1,interval:15e3},isFullScreen:null};return{coupleAdapter:s,deCoupleAdapter:a};function s(){if(e={lasts:{playPosition:null,playState:null,errorCode:null},intervalTracking:{active:!1,interval:15e3},isFullScreen:null},i.utils.setMediaUrl(),i.session=EinbliqIo.getSession(t),!i.session.isDisabled()){if(function(){i.utils.core.isDefined(window)&&i.utils.core.isDefined(window.location)&&i.utils.core.isNotEmptyString(window.location.href)&&i.session.addCustomData({"hbbtv.referrer":window.location.href});i.session.addCustomData({"hbbtv.player":"oipf"}),i.utils.core.isNotEmptyString(i.player.width)&&i.utils.core.isNotEmptyString(i.player.height)&&i.session.addCustomData({"hbbtv.surfaceSize":i.player.width+"x"+i.player.height});i.utils.core.isDefined(window)&&i.utils.core.isNotNegativeNumber(window.innerWidth)&&i.utils.core.isNotNegativeNumber(window.innerHeight)&&i.session.addCustomData({"hbbtv.applicationSize":window.innerWidth.toString()+"x"+window.innerHeight.toString()});i.utils.core.isDefined(screen)&&i.utils.core.isNotNegativeNumber(screen.width)&&i.utils.core.isNotNegativeNumber(screen.height)&&i.session.addCustomData({"hbbtv.screenSize":screen.width.toString()+"x"+screen.height.toString()});r()}(),i.utils.trackByInterval())e.intervalTracking.active=!0,e.intervalTracking.interval=2e3,i.session.addCustomData({"hbbtv.oipf.intervalTrackingActive":!0});else try{i.player.addEventListener("PlayStateChange",n)}catch(t){e.intervalTracking.active=!0,e.intervalTracking.interval=2e3,i.session.addCustomData({"hbbtv.oipf.intervalTrackingActive":!0})}if(i.utils.core.isNotNegativeNumber(i.player.playState)){var s=i.player.playState;e.lasts.playState=s,e.playStateInterval=setInterval(function(){n()},e.intervalTracking.interval)}}}function a(){i.utils.core.isDefined(i.session)&&(i.session.terminate(),clearInterval(e.playStateInterval),i.utils.core.isTrueBool(e.intervalTracking.active)||i.player.removeEventListener("PlayStateChange",n))}function n(){if(function(){if(!i.utils.core.isNotNegativeNumber(i.player.error))return void(e.lasts.errorCode=null);var t=i.player.error;if(t===e.lasts.errorCode)return;var s="unknown error. code: "+t.toString();0===t&&(s="A/V format not supported");1===t&&(s="cannot connect to server or connection lost");2===t&&(s="unidentified error");3===t&&(s="insufficient resources");4===t&&(s="content corrupt or invalid");5===t&&(s="content not available");6===t&&(s="content not available at given position");i.session.addError(s,EinbliqIo.ErrorSeverityIds.CRITICAL),e.lasts.errorCode=t}(),r(),i.utils.core.isNotNegativeNumber(i.player.playState)){var t=i.player.playState;if(t!==e.lasts.playState)if(0===t&&5===e.lasts.playState||5===t&&0===e.lasts.playState)e.lasts.playState=t;else{var n=null;i.utils.core.isNotNegativeNumber(i.player.playPosition)&&(n=i.player.playPosition);var o=!1;0===t&&(i.session.addPlayState(EinbliqIo.PlayStateIds.STOP,n),o=!0),1===t&&i.session.addPlayState(EinbliqIo.PlayStateIds.PLAYING,n),2===t&&i.session.addPlayState(EinbliqIo.PlayStateIds.PAUSE,n),3===t&&i.session.addPlayState(EinbliqIo.PlayStateIds.CONNECTING,n),4===t&&i.session.addPlayState(EinbliqIo.PlayStateIds.BUFFERING,n),5===t&&(i.session.addPlayState(EinbliqIo.PlayStateIds.ENDED,n),o=!0),6===t&&i.session.addPlayState(EinbliqIo.PlayStateIds.ERROR,n),e.lasts.playState=t,e.lasts.playPosition=n,i.utils.core.isTrueBool(o)&&function(){var e=i.session.isDisabled();if(a(),e)return;s()}()}}}function r(){i.utils.core.isBoolean(i.player.isFullScreen)&&i.player.isFullScreen!==e.isFullScreen&&(e.isFullScreen=i.player.isFullScreen,i.session.addCustomData({"hbbtv.fullscreen":e.isFullScreen}))}}(),utils:function(){return{core:EinbliqIo.Helpers(),setMediaUrl:function(){i.utils.core.isNotEmptyString(i.player.data)&&(i.configuration.mediaUrl=i.player.data)},trackByInterval:function(){if(!i.utils.core.isDefined(navigator)||!i.utils.core.isNotEmptyString(navigator.userAgent))return!1;for(var e=0;e<i.constants.INTERVAL_TRACKING_ENABLED_DEVICES.length;e++){var t=i.constants.INTERVAL_TRACKING_ENABLED_DEVICES[e];if(i.utils.allSubstringsMatch(navigator.userAgent,t))return!0}return!1},allSubstringsMatch:function(e,t){for(var i=0;i<t.length;i++)if(-1===e.indexOf(t[i]))return!1;return!0}}}(),constants:{INTERVAL_TRACKING_ENABLED_DEVICES:[["TCL;","MT5651;"],["Hisense;","MT5651;"],["TCL;","MT9221;"],["TCL;","MT9653;"],["TCL;","MS86;"],["TCL;","RTK2851;"],["TCL;","RTK2841;"],["TCL;","RT51;"],["TCL;","2016FHD;"],["TCL;","2016UHD;"]]},exports:{terminateSession:function(){i.manager.deCoupleAdapter()},getSelf:function(){return i}}};return i.utils.core.isDefined(e)&&i.utils.core.isDefined(t)?(i.manager.coupleAdapter(),i.exports):i.exports},dashjs:function(e,t){var i={configuration:t,player:e,session:null,manager:function(){var e={lasts:{playPosition:null,playState:null,errorCode:null,muted:null,fullscreen:null,captionsActive:null},videoElement:{},processCmcd:!0};return{coupleAdapter:t,deCoupleAdapter:s};function t(){var t=[];try{e.videoElement=i.player.getVideoElement()}catch(e){t.push("dash.js getVideoElement() | "+e.toString())}try{i.configuration.mediaUrl=i.player.getSource()}catch(e){i.configuration.mediaUrl="unknown media url",t.push("dash.js getSource() | "+e.toString())}i.configuration.autoPreload=function(){if(!i.utils.core.isNotEmptyString(e.videoElement.preload))return!1;if("none"===e.videoElement.preload)return!1;return!0}(),i.session=EinbliqIo.getSession(i.configuration);var s=!1;try{s=i.player.isReady()}catch(e){t.push("dash.js isReady() | "+e.toString())}if(!s||t.length>0){i.utils.core.isDefined(window.MediaSource)||i.utils.core.isDefined(window.WebKitMediaSource)||i.session.addError("Browser does not support MSE API",EinbliqIo.ErrorSeverityIds.CRITICAL);for(var _=0;_<t.length;_++)i.session.addError(t[_],EinbliqIo.ErrorSeverityIds.CRITICAL)}else i.session.isDisabled()||(!function(){i.utils.core.isDefined(window)&&i.utils.core.isDefined(window.location)&&i.utils.core.isNotEmptyString(window.location.href)&&i.session.addCustomData({"hbbtv.referrer":window.location.href});i.session.addCustomData({"hbbtv.player":"dash.js"}),i.utils.core.isDefined(i.player.getVersion)&&i.utils.core.isNotEmptyString(i.player.getVersion())&&i.session.addCustomData({"hbbtv.player.version":i.player.getVersion()});i.utils.core.isNotNegativeNumber(e.videoElement.width)&&i.utils.core.isNotNegativeNumber(e.videoElement.height)&&i.session.addCustomData({"hbbtv.surfaceSize":e.videoElement.width+"x"+e.videoElement.height});i.utils.core.isDefined(window)&&i.utils.core.isNotNegativeNumber(window.innerWidth)&&i.utils.core.isNotNegativeNumber(window.innerHeight)&&i.session.addCustomData({"hbbtv.applicationSize":window.innerWidth.toString()+"x"+window.innerHeight.toString()});i.utils.core.isDefined(screen)&&i.utils.core.isNotNegativeNumber(screen.width)&&i.utils.core.isNotNegativeNumber(screen.height)&&i.session.addCustomData({"hbbtv.screenSize":screen.width.toString()+"x"+screen.height.toString()});N()}(),e.videoElement.addEventListener("loadstart",a),e.videoElement.addEventListener("playing",n),e.videoElement.addEventListener("canplay",r),e.videoElement.addEventListener("pause",o),e.videoElement.addEventListener("progress",u),i.player.on("playbackSeeking",S),i.player.on("qualityChangeRendered",l),i.player.on("error",p),i.player.on("playbackError",p),i.player.on("playbackEnded",f),i.player.on("playbackWaiting",c),i.player.on("captionRendered",d),i.player.on("fragmentLoadingCompleted",m),i.player.on("public_keySystemSelected",E),i.player.on("periodSwitchStarted",g))}function s(s){i.utils.core.isDefined(i.session)&&(i.session.terminate(),e.videoElement.removeEventListener("loadstart",a),e.videoElement.removeEventListener("playing",n),e.videoElement.removeEventListener("canplay",r),e.videoElement.removeEventListener("pause",o),e.videoElement.removeEventListener("progress",u),i.player.off("playbackSeeking",S),i.player.off("qualityChangeRendered",l),i.player.off("error",p),i.player.off("playbackError",p),i.player.off("playbackEnded",f),i.player.off("playbackWaiting",c),i.player.off("captionRendered",d),i.player.off("public_keySystemSelected",E),i.player.off("periodSwitchStarted",g),e={lasts:{playPosition:null,playState:null,errorCode:null,muted:null,fullscreen:null,captionsActive:null},videoElement:{},processCmcd:!0},s?t():i.player=null)}function a(){i.session.addPlayState(EinbliqIo.PlayStateIds.CONNECTING)}function n(){i.session.addPlayState(EinbliqIo.PlayStateIds.PLAYING,e.videoElement.currentTime,_())}function r(){i.session.addPlayState(EinbliqIo.PlayStateIds.CANPLAY)}function o(){i.session.addPlayState(EinbliqIo.PlayStateIds.PAUSE,e.videoElement.currentTime,_())}function l(){i.session.addPlayState(EinbliqIo.PlayStateIds.SWITCHED,e.videoElement.currentTime,_())}function u(){N()}function d(){i.utils.core.isTrueBool(e.lasts.captionsActive)||(e.lasts.captionsActive=!0,i.session.addCustomData({"hbbtv.subtitles.active":e.lasts.captionsActive}))}function f(){i.session.addPlayState(EinbliqIo.PlayStateIds.ENDED,e.videoElement.currentTime),i.session.terminate(),s(!0)}function c(){i.session.addPlayState(EinbliqIo.PlayStateIds.BUFFERING,e.videoElement.currentTime)}function S(){i.session.addPlayState(EinbliqIo.PlayStateIds.SEEKING,e.videoElement.currentTime)}function m(e){e&&e.request&&e.request.mediaType&&"video"==e.request.mediaType&&e.request.requestStartDate&&e.request.firstByteDate&&e.request.requestEndDate&&e.request.bytesTotal&&i.session.addNetworkMeasurement({requestStartTimeStamp:e.request.requestStartDate.getTime(),requestFirstByteTimeStamp:e.request.firstByteDate.getTime(),requestEndTimeStamp:e.request.requestEndDate.getTime(),totalBytes:e.request.bytesTotal,mediaType:e.request.mediaType})}function E(e){if(e&&e.data&&e.data.mksa&&e.data.ksConfiguration){var t={systemString:e.data.mksa.selectedSystemString,audioCapabilities:e.data.ksConfiguration.audioCapabilities,videoCapabilities:e.data.ksConfiguration.videoCapabilities,encryptionTypes:e.data.ksConfiguration.initDataTypes};i.session.addDrmInformation(t)}}function g(e){if(e&&e.toStreamInfo){var t={isLast:e.toStreamInfo.isLast,relativeStartInMillis:1e3*e.toStreamInfo.start,relativeEndInMillis:1e3*(e.toStreamInfo.start+e.toStreamInfo.duration),name:e.toStreamInfo.id};i.session.addPeriodInformation(t)}}function p(e){if(i.utils.core.isDefined(e)){var t="undefined error",s=EinbliqIo.ErrorSeverityIds.WARNING;if(i.utils.core.isDefined(e.error)){i.utils.core.isNotEmptyString(e.type)&&"error"===e.type&&(s=EinbliqIo.ErrorSeverityIds.CRITICAL),i.utils.core.isNotNegativeNumber(e.error.code)&&(t="Code: "+e.error.code.toString());var a="";i.utils.core.isNotEmptyString(e.error.message)&&(a=e.error.message),i.session.addError(t,s,a)}}}function _(){try{var e=i.player.getQualityFor("video"),t=i.player.getQualityFor("audio"),s=0;if(i.utils.core.isNotNegativeNumber(e)){var a=i.player.getBitrateInfoListFor("video");if(i.utils.core.isNotEmptyArray(a))for(var n=0;n<a.length;n++){var r=a[n];i.utils.core.isDefined(r)&&i.utils.core.isNotNegativeNumber(r.qualityIndex)&&e===r.qualityIndex&&i.utils.core.isNotNegativeNumber(r.bitrate)&&(s+=Math.round(r.bitrate/1e3))}}if(i.utils.core.isNotNegativeNumber(t)){var o=i.player.getBitrateInfoListFor("audio");if(i.utils.core.isNotEmptyArray(o))for(var l=0;l<o.length;l++){var u=o[l];i.utils.core.isDefined(u)&&i.utils.core.isNotNegativeNumber(u.qualityIndex)&&t===u.qualityIndex&&i.utils.core.isNotNegativeNumber(u.bitrate)&&(s+=Math.round(u.bitrate/1e3))}}return s}catch(e){return null}}function N(){i.utils.core.isDefined(i.player.isMuted)&&i.utils.core.isBoolean(i.player.isMuted())&&e.lasts.muted!==i.player.isMuted()&&(i.session.addCustomData({"hbbtv.mute":i.player.isMuted()}),e.lasts.muted=i.player.isMuted())}}(),utils:{core:EinbliqIo.Helpers()},exports:{getSelf:function(){return i},terminateSession:function(){i.manager.deCoupleAdapter(!1)}}};return i.utils.core.isDefined(i.player)&&i.utils.core.isDefined(i.configuration)?(i.manager.coupleAdapter(),i.exports):i.exports}},getSession:function(configuration){var _self={configuration:configuration,manager:SessionManager(),state:SessionState(),data:SessionData(),sender:SessionSender(),utils:Utils(),constants:Constants(),defaults:Defaults(),models:Models(),tests:Tests(),capabilities:Capabilities(),exports:{getSelf:getSelf,addPlayState:addPlayState,addError:addError,addCustomData:addCustomData,addDroppedFrames:addDroppedFrames,terminate:terminate,disable:disable,isDisabled:isDisabled,getVersion:getVersion,pause:pause,resume:resume,on:on,off:off,onCdnSwitch:onCdnSwitch,addNetworkMeasurement:addNetworkMeasurement,addDrmInformation:addDrmInformation,addPeriodInformation:addPeriodInformation}};return _self.utils.jsonPolyfill(),_self.utils.isDefined(configuration)?(_self.state.parseSessionConfiguration(configuration),_self.state.getState().valid?(_self.data.parseSessionConfiguration(configuration),_self.tests.testCapabilities(),_self.exports):_self.exports):_self.exports;function getSelf(){return _self}function addPlayState(e,t,i){if(!_self.state.isDisabled()&&_self.utils.isValidPlayStateId(e)){if(!_self.state.getState().active){if(!_self.manager.isSessionActivatingPlayStateId(e))return;_self.manager.activateSession(_self)}_self.data.addPlayState(e,t,i),_self.state.getState().running||_self.manager.startSession(_self)}}function addError(e,t,i){_self.state.isDisabled()||(_self.state.getState().active||_self.manager.activateSession(_self),_self.data.addError(e,t,i),_self.manager.onAddError(_self))}function addCustomData(e){_self.state.isDisabled()||_self.data.addCustomData(e)}function addDroppedFrames(e){_self.state.isDisabled()||_self.data.addDroppedFrames(e)}function addNetworkMeasurement(e){_self.state.isDisabled()||_self.data.addNetworkMeasurement(e)}function addDrmInformation(e){_self.state.isDisabled()||_self.data.addDrmInformation(e)}function addPeriodInformation(e){_self.state.isDisabled()||(_self.data.addPeriodInformation(e),_self.manager.onAddPeriod(_self))}function terminate(){_self.state.isDisabled()||_self.manager.onTerminate(_self)}function disable(){_self.state.isDisabled()||_self.manager.onDisable(_self)}function isDisabled(){return _self.state.isDisabled()}function getVersion(){return _self.constants.LIBRARY_VERSION}function pause(){_self.state.isDisabled()||_self.state.getState().active&&_self.state.getState().running&&_self.manager.pauseSession(_self)}function resume(){_self.state.isDisabled()||_self.state.getState().active&&!_self.state.getState().running&&_self.manager.resumeSession(_self)}function on(e,t){_self.state.isDisabled()||_self.manager.addListener(e,t)}function off(){_self.state.isDisabled()||_self.manager.removeListeners()}function onCdnSwitch(e){!_self.state.isDisabled()&&_self.utils.isNotEmptyString(e)&&(_self.data.addCdn(e),_self.state.onNewCdn(e))}function SessionManager(){var e={timer:null,listeners:{sendData:[]}};return{getManager:function(){return e},activateSession:function(t){t.state.onSessionActivate(),t.data.onSessionActivate(),t.state.getState().autoPreload||(e.timer=setTimeout(function(){t.state.getState().running||t.state.getState().terminated||(t.state.onSessionStartOrResume(),t.state.onIntervalTimeChange(!0),t.manager.onBeaconInterval(t,!1))},t.constants.SESSION_INTERVAL.INITIAL_SEND_DELAY_IN_MILLIS))},startSession:function(e){e.data.playbackStarted()&&!e.state.getState().running&&(e.state.onSessionStartOrResume(),e.state.onIntervalTimeChange(!0),e.manager.onBeaconInterval(e,!1))},pauseSession:function(e){e.state.onSessionPause(),e.manager.clearTimer(),e.manager.transferDataNow(e,!1)},resumeSession:function(t){e.timer=setTimeout(function(){t.state.getState().running||(t.state.onSessionStartOrResume(),t.state.onIntervalTimeChange(!0),t.manager.onBeaconInterval(t,!1))},t.constants.SESSION_INTERVAL.INITIAL_SEND_DELAY_IN_MILLIS)},onBeaconInterval:function(e,t){e.state.isDisabled()||e.manager.fireEvent(e.constants.Events.sendData);e.manager.clearTimer(),e.state.getState().intervalTimeChange&&e.state.onIntervalTimeUpdate();e.state.onBeaconSendStart(),!t&&e.utils.getCdnInformation(e)?e.sender.addCdnInfo(e):e.manager.createAndSendBeacon(e)},createAndSendBeacon:function(e){var t=e.models.createBeacon(e);e.state.getState().intervalBeaconCount<=e.constants.SESSION_INTERVAL.MAX_BEACON_COUNT&&e.sender.sendBeacon(e,t)},clearTimer:function(){null!==e.timer&&(clearTimeout(e.timer),e.timer=null)},setNextTimer:function(t){t.manager.clearTimer(),e.timer=setTimeout(function(){t.state.onIntervalTimeChange(!1),t.manager.onBeaconInterval(t,!1)},t.utils.getNextTimerIntervalInMillis(t))},onAddError:function(e){e.data.hasSevereError()&&(e.state.getState().running||e.state.onSessionStartOrResume(),e.manager.transferDataNow(e,!0))},onDisable:function(e){e.manager.removeListeners(),e.manager.clearTimer(),e.state.onSessionDisable()},onTerminate:function(e){e.manager.clearTimer(),e.manager.fireEvent(e.constants.Events.sendData),e.manager.removeListeners(),e.state.onSessionTerminate(),e.manager.transferDataNow(e,!1)},transferDataNow:function(e,t){var i=(new Date).getTime()-e.state.getState().lastRequestTimeStamp;if(e.state.getState().lastRequestPending||i<e.constants.SESSION_INTERVAL.MIN_TIME_DIFF_BETWEEN_BEACONS_IN_MILLIS){var s=e.constants.SESSION_INTERVAL.MIN_TIME_DIFF_BETWEEN_BEACONS_IN_MILLIS+1e3;return e.state.getState().lastRequestPending||(s=e.constants.SESSION_INTERVAL.MIN_TIME_DIFF_BETWEEN_BEACONS_IN_MILLIS-i),void setTimeout(function(){e.manager.transferDataNow(e,t)},s)}if(t&&!e.utils.isNotEmptyArray(e.data.getData().errors))return;e.state.onIntervalTimeChange(!0),e.manager.onBeaconInterval(e,!0)},fireEvent:function(t){if(e.listeners[t].length>0)for(var i=0;i<e.listeners[t].length;i++){var s=e.listeners[t][i];s()}},addListener:function(e,t){_self.utils.isNotEmptyString(e)&&_self.utils.isDefined(t)&&"function"==typeof t&&e===_self.constants.Events.sendData&&_self.manager.getManager().listeners[_self.constants.Events.sendData].push(t)},removeListeners:function(){_self.manager.getManager().listeners={sendData:[]}},isSessionActivatingPlayStateId:function(e){return e===EinbliqIo.PlayStateIds.CONNECTING||e===EinbliqIo.PlayStateIds.BUFFERING||e===EinbliqIo.PlayStateIds.PLAYING},onAddPeriod:function(e){if(!e.state.getState().running)return;e.manager.transferDataNow(e,!1)}}}function SessionState(){var e={valid:!1,active:!1,optOut:!1,running:!1,terminated:!1,autoPreload:!1,getCdnInfo:!0,cdnHeaderUrl:"",serverUrl:"",lastRequestPending:!1,lastRequestTimeStamp:0,intervalTimeSeconds:(new Date).getSeconds(),intervalBeaconCount:0,intervalTimeChange:!1,beaconSendErrorCount:0,settings:null,dataTransferId:null,customerId:"",features:{cdnDetection:!1},tests:{xmlHttpRequest:{name:"xmlHttpRequest",active:!1}}};return{getState:function(){return e},parseSessionConfiguration:function(t){if(!_self.utils.isNotEmptyString(t.customerId)||!_self.utils.isCustomerIdLength(t.customerId))return;if(e.customerId=t.customerId,!_self.utils.isNotEmptyString(t.mediaUrl))return;if(e.settings=function(e){var t=_self.utils.getOwnVersionSettings();if(!_self.utils.isNotEmptyArray(e))return t;for(var i=null,s=0;s<e.length;s++){var a=e[s];if(a&&_self.utils.isNotEmptyString(a.version)){if(_self.utils.isDefined(i))break;var n=a.version.toLowerCase();if("1.x"==n&&t.id<=2||"2.x"==n&&t.id>=3){i=a;break}for(var r=0;r<_self.defaults.HBBTV_VERSION_SETTINGS.length;r++){var o=_self.defaults.HBBTV_VERSION_SETTINGS[r];if(n===o.version){i=a;break}}}}_self.utils.isDefined(i)&&(_self.utils.isTrueBool(i.disable)&&(t.disable=!0),_self.utils.isValidHbbTVConfigTransfer(i.transfer)&&(t.transferModeId=_self.utils.getConfigTransferId(i.transfer)));return t}(t.versionSettings),e.settings.disable)return;e.valid=!0,e.cdnHeaderUrl=t.mediaUrl,e.serverUrl=_self.utils.createServerUrl(e.customerId,e.settings.transferModeId),e.autoPreload=_self.utils.isTrueBool(t.autoPreload),e.features=function(t){if(!_self.utils.isDefined(t))return e.features;var i=_self.defaults.FEATURE_PERCENTAGES.cdnDetection;_self.utils.isValidFeatureValue(t.enableCdnDetection)&&(i=_self.utils.getFeaturePercentageByValue(t.enableCdnDetection));return e.features.cdnDetection=_self.utils.enableByPercentage(i),e.features}(t.features),_self.utils.isTrueBool(e.features.cdnDetection)&&(e.getCdnInfo=!0);e.tests=function(){_self.utils.isTrueBool(e.features.cdnDetection)&&(e.tests.xmlHttpRequest.active=!0);return e.tests}()},onSessionActivate:function(){e.active=!0},onSessionStartOrResume:function(){e.running=!0},onSessionPause:function(){e.running=!1},onSessionDisable:function(){e.active=!1,e.running=!1,e.optOut=!0},onSessionTerminate:function(){e.active=!1,e.running=!1,e.terminated=!0},onDisableCdnInfo:function(){e.getCdnInfo=!1},onBeaconSendStart:function(){e.lastRequestPending=!0,e.lastRequestTimeStamp=(new Date).getTime()},onBeaconSendSuccess:function(){e.lastRequestPending=!1,e.intervalTimeChange?e.intervalBeaconCount++:e.intervalBeaconCount=0;e.beaconSendErrorCount=0},onBeaconSendError:function(){e.lastRequestPending=!1,e.beaconSendErrorCount++,e.settings.transferModeId===_self.constants.DATA_TRANSFER_MODES.AUTO&&(e.settings.transferModeId=_self.constants.DATA_TRANSFER_MODES.INSECURE,e.serverUrl=_self.utils.createServerUrl(e.customerId,e.settings.transferModeId))},onIntervalTimeUpdate:function(){e.intervalTimeSeconds=(new Date).getSeconds()},onIntervalTimeChange:function(t){e.intervalTimeChange=t},onNewCdn:function(t){e.cdnHeaderUrl=t,e.getCdnInfo=!0},isDisabled:function(){return!e.valid||e.terminated||e.optOut}}}function SessionData(){var e={sessionId:null,requestNumber:0,initialisationTimeStamp:0,customerId:null,mediaUrl:null,mediaId:null,mediaSeries:null,mediaTitle:null,mediaCategory:null,termId:null,crid:null,applicationVersion:null,applicationName:null,playStates:[],customData:[],networkMeasurements:[],drmModelData:null,periodModelData:null,capabilities:[],errors:[],countOfDroppedFrames:0,contentDeliveryNetworkHeader:null,contentDeliveryNetworks:[],tests:[]};return{getData:function(){return e},parseSessionConfiguration:function(t){if(e.sessionId=_self.utils.createSessionId(),!_self.utils.isDefined(t))return;e.mediaUrl=t.mediaUrl,_self.utils.isNotEmptyString(t.mediaId)&&(e.mediaId=t.mediaId);_self.utils.isNotEmptyString(t.mediaTitle)&&(e.mediaTitle=t.mediaTitle);_self.utils.isNotEmptyString(t.mediaSeries)&&(e.mediaSeries=t.mediaSeries);_self.utils.isNotEmptyString(t.mediaCategory)&&(e.mediaCategory=t.mediaCategory);_self.utils.isNotEmptyString(t.termId)&&(e.termId=t.termId);_self.utils.isNotEmptyString(t.crid)&&(e.crid=t.crid);_self.utils.isNotEmptyString(t.applicationVersion)&&(e.applicationVersion=t.applicationVersion);_self.utils.isNotEmptyString(t.applicationName)&&(e.applicationName=t.applicationName);_self.utils.isDefined(t.customData)&&e.customData.push(_self.models.createCustomData(0,t.customData));e.periodModelData=_self.models.createPeriodModel({})},onSessionActivate:function(){e.initialisationTimeStamp=(new Date).getTime()},playbackStarted:function(){for(var t=0;t<e.playStates.length;t++)if(e.playStates[t].getPlayState().playStateId===EinbliqIo.PlayStateIds.PLAYING)return!0;return!1},hasSevereError:function(){for(var t=0;t<e.errors.length;t++)if(e.errors[t].getError().errorSeverityId>=EinbliqIo.ErrorSeverityIds.ERROR)return!0;return!1},addPlayState:function(t,i,s){if(!_self.utils.isValidPlayStateId(t))return;var a=!1;if(_self.utils.isNotEmptyArray(e.playStates)){var n=e.playStates[e.playStates.length-1].getPlayState();n.playStateId===t&&(n.playStateId===EinbliqIo.PlayStateIds.SWITCHED&&n.currentPlaybackRateInKbps===s&&(a=!0),n.playStateId!==EinbliqIo.PlayStateIds.SWITCHED&&n.playStateId!==EinbliqIo.PlayStateIds.SEEKING&&(a=!0))}if(a)return;e.playStates.push(_self.models.createPlayState(e.initialisationTimeStamp,t,i,s))},addError:function(t,i,s){var a=_self.utils.isNotNegativeNumber(i)?i:EinbliqIo.ErrorSeverityIds.WARNING;_self.utils.isValidErrorSeverityId(a)&&e.errors.push(_self.models.createError(_self.initialisationTimeStamp,a,t,s))},addCustomData:function(t){_self.utils.isDefined(t)&&e.customData.push(_self.models.createCustomData(e.initialisationTimeStamp,t))},addDroppedFrames:function(t){_self.utils.isNotNegativeNumber(t)&&(e.countOfDroppedFrames+=t)},addNetworkMeasurement:function(t){if(!_self.utils.isDefined(t))return;_self.utils.isValidNetworkMeasurement(t)&&e.networkMeasurements.push(_self.models.createNetworkMeasurement(t))},addCdn:function(t){e.contentDeliveryNetworks.push(_self.models.createContentDeliveryNetwork(e.initialisationTimeStamp,t))},tryAddCdnInfo:function(t){var i=!1;if(!_self.utils.isDefined(t))return i;var s=t.getAllResponseHeaders();if(_self.utils.isNotEmptyString(s)){for(var a,n,r=0;r<_self.constants.HEADERS_CDN_NAME_KEYS.length;r++){var o=_self.constants.HEADERS_CDN_NAME_KEYS[r];s.indexOf(o)>=0&&_self.utils.isNotEmptyString(t.getResponseHeader(o))&&(a=t.getResponseHeader(o))}for(var r=0;r<_self.constants.HEADERS_CDN_IP_KEYS.length;r++){var l=_self.constants.HEADERS_CDN_IP_KEYS[r];s.indexOf(l)>=0&&_self.utils.isNotEmptyString(t.getResponseHeader(l))&&(n=t.getResponseHeader(l))}(i=_self.utils.isNotEmptyString(a)||_self.utils.isNotEmptyString(n))&&(e.contentDeliveryNetworkHeader=_self.models.createContentDeliveryNetworkHeader(a,n))}return i},onBeaconSendSuccess:function(){e.requestNumber++,t(),1===e.requestNumber&&_self.capabilities.addXmlCapabilities()},clearValues:t,addTest:function(t){_self.utils.isDefined(t)&&e.tests.push(t)},addCapability:function(t){_self.utils.isDefined(t)&&e.capabilities.push(t)},addDrmInformation:function(t){if(!_self.utils.isDefined(t))return;e.drmModelData=_self.models.createDrmModel(t)},addPeriodInformation:function(t){if(!_self.utils.isDefined(t))return;e.periodModelData=_self.models.createPeriodModel(t)}};function t(){e.playStates=_self.utils.getNotSentList(e.playStates),e.errors=_self.utils.getNotSentList(e.errors),e.customData=_self.utils.getNotSentList(e.customData),e.networkMeasurements=_self.utils.getNotSentList(e.networkMeasurements),e.contentDeliveryNetworks=_self.utils.getNotSentList(e.contentDeliveryNetworks),e.tests=_self.utils.getNotSentList(e.tests),e.capabilities=_self.utils.getNotSentList(e.capabilities),_self.utils.isNotNegativeNumber(e.countOfDroppedFrames)&&(e.countOfDroppedFrames=0),_self.utils.isDefined(e.contentDeliveryNetworkHeader)&&(e.contentDeliveryNetworkHeader=null),_self.utils.isDefined(e.drmModelData)&&(e.drmModelData=_self.utils.getNotSentObject(e.drmModelData)),_self.utils.isDefined(e.periodModelData)&&(e.periodModelData=_self.utils.getNotSentObject(e.periodModelData))}}function SessionSender(){return{addCdnInfo:function(t,i){try{var s=_self.constants.BEACON_SEND_STATES.IDLE;if(!_self.utils.isDefined(XMLHttpRequest))return void e(t);var s=_self.constants.BEACON_SEND_STATES.SENDING,a=setTimeout(function(){s===_self.constants.BEACON_SEND_STATES.SENDING&&(s=_self.constants.BEACON_SEND_STATES.EXPIRED,_self.utils.isTrueBool(t.state.getState().tests.xmlHttpRequest.active)&&t.data.addTest(_self.models.createTest(_self.state.getState().tests.xmlHttpRequest.name,"request succesfull",!1,"expired")),e(t))},1e4),n=!1,r=new XMLHttpRequest;if(!_self.utils.isDefined(r)||_self.utils.isTrueBool(i))return void e(t);r.open("HEAD",t.utils.createCdnInfoUrl(t.state.getState().cdnHeaderUrl,t.data.getData().requestNumber)),r.onreadystatechange=function(){if(t.utils.isDefined(r.readyState)&&t.utils.isDefined(r.status)&&!n&&s!==_self.constants.BEACON_SEND_STATES.EXPIRED)if(r.readyState>=t.constants.HTTP_READY_STATE_2&&r.status===t.constants.HTTP_STATUS_CODE_200){clearTimeout(a),n=!0,s=_self.constants.BEACON_SEND_STATES.SEND_SUCCESS,_self.utils.isTrueBool(t.state.getState().tests.xmlHttpRequest.active)&&t.data.addTest(_self.models.createTest(_self.state.getState().tests.xmlHttpRequest.name,"request succesfull",!0));var i=t.data.tryAddCdnInfo(r);i?t.manager.createAndSendBeacon(t):e(t)}else r.status>t.constants.HTTP_STATUS_CODE_200&&(clearTimeout(a),s=_self.constants.BEACON_SEND_STATES.SEND_ERROR,n=!0,_self.utils.isTrueBool(t.state.getState().tests.xmlHttpRequest.active)&&t.data.addTest(_self.models.createTest(_self.state.getState().tests.xmlHttpRequest.name,"request succesfull",!1,"bad status code: "+r.status.toString())),e(t))},r.ontimeout=function(){clearTimeout(a),s!==_self.constants.BEACON_SEND_STATES.EXPIRED&&(s=_self.constants.BEACON_SEND_STATES.SEND_ERROR,_self.utils.isTrueBool(t.state.getState().tests.xmlHttpRequest.active)&&t.data.addTest(_self.models.createTest(_self.state.getState().tests.xmlHttpRequest.name,"request succesfull",!1,"ontimeout")),e(t))},r.onerror=function(){clearTimeout(a),s!==_self.constants.BEACON_SEND_STATES.EXPIRED&&(s=_self.constants.BEACON_SEND_STATES.SEND_ERROR,_self.utils.isTrueBool(t.state.getState().tests.xmlHttpRequest.active)&&t.data.addTest(_self.models.createTest(_self.state.getState().tests.xmlHttpRequest.name,"request succesfull",!0)),e(t))},r.send()}catch(i){var o="unknown exception fetching cdn header";_self.utils.isDefined(i)&&_self.utils.isNotEmptyString(i.message)&&(o=i.message),t.data.addError("exception fetching cdn header",EinbliqIo.ErrorSeverityIds.WARNING,o),s=_self.constants.BEACON_SEND_STATES.SEND_ERROR,_self.utils.isTrueBool(t.state.getState().tests.xmlHttpRequest.active)&&t.data.addTest(_self.models.createTest(_self.state.getState().tests.xmlHttpRequest.name,"request succesfull",!1,o)),e(t)}},sendBeacon:function(e,i){try{var s=_self.constants.BEACON_SEND_STATES.IDLE,a=document.createElement("img");a.src=e.state.getState().serverUrl+i.getUriEncodedData(),s=_self.constants.BEACON_SEND_STATES.SENDING;var n=setTimeout(function(){s===_self.constants.BEACON_SEND_STATES.SENDING&&(s=_self.constants.BEACON_SEND_STATES.EXPIRED,t(e))},1e4);a.onload=function(){clearTimeout(n),s===_self.constants.BEACON_SEND_STATES.SENDING&&(s=_self.constants.BEACON_SEND_STATES.SEND_SUCCESS,function(e){e.state.onBeaconSendSuccess(),e.data.onBeaconSendSuccess(),e.state.getState().running&&e.manager.setNextTimer(e)}(e))},a.onerror=function(){clearTimeout(n),s=_self.constants.BEACON_SEND_STATES.SEND_ERROR,t(e)}}catch(t){e.state.onSessionDisable()}}};function e(e){e.state.getState().getCdnInfo&&(e.state.onDisableCdnInfo(),e.manager.createAndSendBeacon(e))}function t(e){e.data.addError(e.constants.EINBLIQIO_ERRORS.SENDER_BEACON_SEND_ERROR,EinbliqIo.ErrorSeverityIds.CRITICAL),e.state.onBeaconSendError(),e.state.getState().beaconSendErrorCount<=e.constants.BEACON_ERROR_COUNT_MAX?e.manager.setNextTimer(e):e.state.onSessionDisable()}}function Constants(){return{CUSTOMER_ID_LENGTH:8,HTTP_STATUS_CODE_200:200,HTTP_READY_STATE_2:2,HEADERS_CDN_NAME_KEYS:["CDN","cdn","X-EINBLIQIO-CDN-NAME","x-einbliqio-cdn-name"],HEADERS_CDN_IP_KEYS:["X-IP-ADDRESS","x-ip-address","X-GL-EDGEIP","x-gl-edgeip","X-EINBLIQIO-CDN-EDGE-IP","x-einbliqio-cdn-edge-ip"],EINBLIQIO_ERRORS:{ERROR_ERROR_MESSAGE_NOT_DEFINED:"error message not defined",SENDER_BEACON_SEND_ERROR:"error sending beacon to einbliq.io backend",SENDER_SWITCH_TO_UNSECURE_TRANSFER:"switching to unencrypted data transfer"},CDN_URL_NO_CACHE_PREFIX:"ern=",LIBRARY_TYPE:"hbbtv",SERVER_DOMAIN:"1bliq.io",LIBRARY_VERSION:"1.4.0",TIME:{SECONDS_IN_MILLIS:1e3},SESSION_INTERVAL:{MAX_BEACON_COUNT:10,INITIAL_SEND_DELAY_IN_MILLIS:5e3,REQUEST_NUMBER_INTERVAL_MAPPINGS:{ONE:5,TWO:10,THREE:15,DEFAULT:60},MIN_TIME_DIFF_BETWEEN_BEACONS_IN_MILLIS:2e3},BEACON_ERROR_COUNT_MAX:3,BEACON_LIMITS:{MAX_URL_SIZE:16384,MAX_NUMBER_CAPABILITIES:75,MAX_NUMER_TESTS:25},Events:{sendData:"sendData"},DATA_TRANSFER_MODES:{AUTO:0,INSECURE:1,SECURE:2},BEACON_SEND_STATES:{IDLE:0,SENDING:1,SEND_SUCCESS:2,SEND_ERROR:3,EXPIRED:4},PROBABILITY_MAPPING:{PERCENT_100:100,PERCENT_90:90,PERCENT_50:50,PERCENT_10:10,PERCENT_0:0},PLAYSTATES_MAPPING_IDS:{0:"CONNECTING",1:"BUFFERING",2:"CANPLAY",3:"PLAYING",4:"PAUSE",5:"SEEKING",6:"STOP",7:"ENDED",8:"STALLED",9:"ABORT",10:"ERROR",11:"SWITCHED"},ERRORS_SEVERITIES_MAPPING_IDS:{0:"INFORMATION",1:"WARNING",2:"ERROR",3:"CRITICAL"}}}function Defaults(){return{HBBTV_VERSION_SETTINGS:[{version:"1.0",id:1,transferModeId:1,disable:!1},{version:"1.5",id:2,transferModeId:0,disable:!1},{version:"1.X",id:100,transferModeId:1,disable:!1},{version:"2.0",id:3,transferModeId:2,disable:!1},{version:"2.0.1",id:4,transferModeId:2,disable:!1},{version:"2.0.2",id:5,transferModeId:2,disable:!1},{version:"2.0.3",id:6,transferModeId:2,disable:!1},{version:"2.X",id:200,transferModeId:2,disable:!1}],FEATURE_PERCENTAGES:{cdnDetection:10}}}function Models(){return{createBeacon:function(e){var t={};if(t.sid=e.data.getData().sessionId,t.rn=e.data.getData().requestNumber,t.t=(new Date).getTime()-e.data.getData().initialisationTimeStamp,e.utils.isNotEmptyArray(e.data.getData().playStates)){t.ps=[];for(var i=0;i<e.data.getData().playStates.length;i++)t.ps.push(e.data.getData().playStates[i].getBeaconPlayState())}if(e.utils.isNotEmptyArray(e.data.getData().errors)){t.es=[];for(var i=0;i<e.data.getData().errors.length;i++)t.es.push(e.data.getData().errors[i].getBeaconError())}if(e.utils.isNotEmptyArray(e.data.getData().customData)){t.cd=[];for(var i=0;i<e.data.getData().customData.length;i++)t.cd.push(e.data.getData().customData[i].getBeaconCustomData())}e.utils.isNotEmptyArray(e.data.getData().networkMeasurements)&&(t.netthr=_self.models.getBeaconNetworkTroughput(e.data.getData().networkMeasurements),t.netrtt=_self.models.getBeaconRoundTripTime(e.data.getData().networkMeasurements));if(e.utils.isNotEmptyArray(e.data.getData().contentDeliveryNetworks)){t.cdns=[];for(var i=0;i<e.data.getData().contentDeliveryNetworks.length;i++)t.cdns.push(e.data.getData().contentDeliveryNetworks[i].getBeaconContentDeliverNetwork())}e.utils.isDefined(e.data.getData().drmModelData)&&(t.drm=e.data.getData().drmModelData.getBeaconDrmInformation());e.utils.isDefined(e.data.getData().periodModelData)&&(t.per=e.data.getData().periodModelData.getBeaconPeriodInformation());e.utils.isDefined(e.data.getData().contentDeliveryNetworkHeader)&&(t.cdn=e.data.getData().contentDeliveryNetworkHeader.getBeaconCdnHeader());e.state.getState().running||(t.sr=!1);e.state.getState().terminated&&(t.st=!0);0===e.data.getData().requestNumber&&(t.it=e.data.getData().initialisationTimeStamp,t.mu=e.data.getData().mediaUrl,t.mt=e.data.getData().mediaTitle,t.ms=e.data.getData().mediaSeries,t.mc=e.data.getData().mediaCategory,t.mid=e.data.getData().mediaId,t.tid=e.data.getData().termId,t.crid=e.data.getData().crid,t.av=e.data.getData().applicationVersion,t.an=e.data.getData().applicationName,t.lv=e.constants.LIBRARY_VERSION);if(_self.utils.isNotEmptyArray(e.data.getData().tests)){t.tests=[];var s=_self.constants.BEACON_LIMITS.MAX_NUMER_TESTS;e.data.getData().tests.length<_self.constants.BEACON_LIMITS.MAX_NUMER_TESTS&&(s=e.data.getData().tests.length);for(var i=0;i<s&&!_self.utils.maxBeaconLengthReached(t);i++)t.tests.push(e.data.getData().tests[i].getBeaconTestData())}if(_self.utils.isNotEmptyArray(e.data.getData().capabilities)){t.caps=[];var a=_self.constants.BEACON_LIMITS.MAX_NUMBER_CAPABILITIES;e.data.getData().capabilities.length<a&&(a=e.data.getData().capabilities.length);for(var i=0;i<a&&!_self.utils.maxBeaconLengthReached(t);i++)t.caps.push(e.data.getData().capabilities[i].getBeaconCapability())}return{getUriEncodedData:function(){return encodeURIComponent(JSON.stringify(t))},getBeaconData:function(){return t}}},createPlayState:function(e,t,i,s){var a={_sent:!1,playState:"",playStateId:null,time:null,currentPlaybackPositionInMillis:null,currentPlaybackRateInKbps:null};a.playState=_self.constants.PLAYSTATES_MAPPING_IDS[t],a.playStateId=t,a.time=(new Date).getTime()-e,_self.utils.isNotNegativeNumber(i)&&(a.currentPlaybackPositionInMillis=Math.round(i));_self.utils.isNotNegativeNumber(s)&&void 0!==s&&(a.currentPlaybackRateInKbps=Math.round(s));return{isSent:function(){return a._sent},getPlayState:function(){return a},getBeaconPlayState:function(){var e={t:a.time,id:a.playStateId};return _self.utils.isDefined(a.currentPlaybackPositionInMillis)&&(e.pp=a.currentPlaybackPositionInMillis),_self.utils.isDefined(a.currentPlaybackRateInKbps)&&(e.pr=a.currentPlaybackRateInKbps),a._sent=!0,e}}},createError:function(e,t,i,s){var a={_sent:!1,errorSeverity:null,errorSeverityId:null,time:null,errorMessage:null,errorDescription:null};a.errorSeverity=_self.constants.ERRORS_SEVERITIES_MAPPING_IDS[t],a.errorSeverityId=t,a.time=(new Date).getTime()-e,_self.utils.isNotNegativeNumber(a.time)||(a.time=0);_self.utils.isNotEmptyString(i)?a.errorMessage=i:a.errorMessage=_self.constants.EINBLIQIO_ERRORS.ERROR_ERROR_MESSAGE_NOT_DEFINED;_self.utils.isNotEmptyString(s)&&(a.errorDescription=s);return{isSent:function(){return a._sent},getError:function(){return a},getBeaconError:function(){var e={t:a.time,sid:a.errorSeverityId,em:a.errorMessage};_self.utils.isNotEmptyString(a.errorDescription)&&(e.ed=a.errorDescription);return a._sent=!0,e}}},createContentDeliveryNetworkHeader:function(e,t){var i={_sent:!1,name:null,cdnEdgeIp:null};_self.utils.isNotEmptyString(e)&&(i.name=e);_self.utils.isNotEmptyString(t)&&(i.cdnEdgeIp=t);return{isSent:function(){return i._sent},getCdnHeader:function(){return i},getBeaconCdnHeader:function(){var e={};_self.utils.isNotEmptyString(i.name)&&(e.n=i.name);_self.utils.isNotEmptyString(i.cdnEdgeIp)&&(e.ceip=i.cdnEdgeIp);return i._sent=!0,e}}},createContentDeliveryNetwork:function(e,t){var i={_sent:!1,time:null,url:null};_self.utils.isNotNegativeNumber(e)&&(i.time=(new Date).getTime()-e,i.url=t);return{isSent:function(){return i._sent},getContentDeliveryNetwork:function(){return i},getBeaconContentDeliverNetwork:function(){var e={t:i.time,u:i.url};return i._sent=!0,e}}},createCustomData:function(e,t){var i={_sent:!1,time:null,customData:null};i.time=e>0?(new Date).getTime()-e:0;return i.customData=t,{isSent:function(){return i._sent},getCustomData:function(){return i},getBeaconCustomData:function(){var e={t:i.time,cd:i.customData};return i._sent=!0,e}}},createNetworkMeasurement:function(e){var t={_sent:!1,start:null,firstByte:null,end:null,totalBytes:null,type:null,throughput:null,roundTripTime:null};t.start=e.requestStartTimeStamp,t.firstByte=e.requestFirstByteTimeStamp,t.end=e.requestEndTimeStamp,t.totalBytes=e.totalBytes,t.type=e.mediaType;var i=t.end-t.firstByte;return t.throughput=Math.round(8*t.totalBytes/i),t.roundTripTime=e.requestFirstByteTimeStamp-e.requestStartTimeStamp,{isSent:function(){return t._sent},getNetworkMeasurement:function(){return t}}},getBeaconNetworkTroughput:function(e){for(var t=[],i=0;i<e.length;i++){var s=e[i].getNetworkMeasurement();s._sent=!0,t.push(s.throughput)}return{min:(t=_self.utils.getSortedArray(t))[0],max:t[t.length-1],avg:_self.utils.getAverage(t),med:_self.utils.getMedian(t),cur:e[e.length-1].getNetworkMeasurement().throughput,std:_self.utils.getStandardDeviation(t)}},getBeaconRoundTripTime:function(e){for(var t=[],i=0;i<e.length;i++){var s=e[i].getNetworkMeasurement();s._sent=!0,t.push(s.roundTripTime)}return t=_self.utils.getSortedArray(t),{avg:_self.utils.getAverage(t),med:_self.utils.getMedian(t),std:_self.utils.getStandardDeviation(t)}},createDrmModel:function(e){var t=e;return t._sent=!1,{isSent:function(){return t._sent},getDrmInformation:function(){return t},getBeaconDrmInformation:function(){var e={};if(_self.utils.isDefined(t.systemString)&&(e.ss=t.systemString),_self.utils.isDefined(t.audioCapabilities)&&t.audioCapabilities.length>0){e.ac=[];for(var i=0;i<t.audioCapabilities.length;i++){var s={};_self.utils.isNotEmptyString(t.audioCapabilities[i].contentType)&&(s.ct=t.audioCapabilities[i].contentType),_self.utils.isNotEmptyString(t.audioCapabilities[i].encryptionScheme)&&(s.es=t.audioCapabilities[i].encryptionScheme),_self.utils.isNotEmptyString(t.audioCapabilities[i].robustness)&&(s.r=t.audioCapabilities[i].robustness),e.ac.push(s)}}if(_self.utils.isDefined(t.videoCapabilities)&&t.videoCapabilities.length>0)for(e.vc=[],i=0;i<t.videoCapabilities.length;i++){var a={};_self.utils.isNotEmptyString(t.videoCapabilities[i].contentType)&&(a.ct=t.videoCapabilities[i].contentType),_self.utils.isNotEmptyString(t.videoCapabilities[i].encryptionScheme)&&(a.es=t.videoCapabilities[i].encryptionScheme),_self.utils.isNotEmptyString(t.videoCapabilities[i].robustness)&&(a.r=t.videoCapabilities[i].robustness),e.vc.push(a)}return _self.utils.isDefined(t.encryptionTypes)&&(e.tn=t.encryptionTypes),t._sent=!0,e}}},createPeriodModel:function(e){var t={_sent:!1,periodId:_self.utils.createSessionId(),periodModel:e};return{isSent:function(){return t._sent},getPeriodInformation:function(){return t},getBeaconPeriodInformation:function(){var e={};return _self.utils.isNotEmptyString(t.periodId)&&(e.pid=t.periodId),_self.utils.isTrueBool(t.periodModel.isLast)&&(e.il=t.periodModel.isLast),_self.utils.isNotNegativeNumber(t.periodModel.relativeStartInMillis)&&(e.rs=t.periodModel.relativeStartInMillis),_self.utils.isNotNegativeNumber(t.periodModel.relativeEndInMillis)&&(e.re=t.periodModel.relativeEndInMillis),_self.utils.isNotEmptyString(t.periodModel.name)&&(e.n=t.periodModel.name),t._sent=!0,e}}},createTest:function(e,t,i,s){var a={_sent:!1,name:null,test:null,success:null,reason:null};_self.utils.isNotEmptyString(e)&&(a.name=e);_self.utils.isNotEmptyString(t)&&(a.test=t);_self.utils.isDefined(i)&&(a.success=i);_self.utils.isNotEmptyString(s)&&(a.reason=s);return{isSent:function(){return a._sent},getTest:function(){return a},getBeaconTestData:function(){var e={n:a.name,t:a.test,s:a.success};return _self.utils.isNotEmptyString(a.reason)&&(e.r=a.reason),a._sent=!0,e}}},createCapability:function(e){var t={_sent:!1,type:e.nodeName,name:e.getAttribute("name"),mimeType:e.getAttribute("type"),transport:e.getAttribute("transport"),drmSystem:"",format:"",displayFormatWidth:"",displayFormatHeight:"",displayFormatFrameRate:"",displayFormatBitDepth:"",displayFormatColorimetry:"",displaySizeWidth:"",displaySizeHeight:"",displaySizeMeasurementType:"",hdr:e.getAttribute("hdr")};_self.utils.isNotEmptyString(t.type)&&"drm"===t.type.toLowerCase()&&(t.drmSystem=e.innerHTML);_self.utils.isNotEmptyString(t.type)&&"broadcast"===t.type.toLowerCase()&&(t.format=e.innerHTML);_self.utils.isNotEmptyString(t.type)&&"video_display_format"===t.type.toLowerCase()&&(t.displayFormatWidth=e.getAttribute("width"),t.displayFormatHeight=e.getAttribute("height"),t.displayFormatFrameRate=e.getAttribute("frame_rate"),t.displayFormatBitDepth=e.getAttribute("bit_depth"),t.displayFormatColorimetry=e.getAttribute("colorimetry"));_self.utils.isNotEmptyString(t.type)&&"display_size"===t.type.toLowerCase()&&(t.displaySizeWidth=e.getAttribute("width"),t.displaySizeHeight=e.getAttribute("height"),t.displaySizeMeasurementType=e.getAttribute("measurement_type"));return{isSent:function(){return t._sent},getCapability:function(){return t},getBeaconCapability:function(){var e={};return _self.utils.isNotEmptyString(t.type)&&(e.t=t.type),_self.utils.isNotEmptyString(t.name)&&(e.n=t.name),_self.utils.isNotEmptyString(t.mimeType)&&(e.mt=t.mimeType),_self.utils.isNotEmptyString(t.transport)&&(e.tr=t.transport),_self.utils.isNotEmptyString(t.drmSystem)&&(e.drms=t.drmSystem),_self.utils.isNotEmptyString(t.format)&&(e.form=t.format),_self.utils.isNotEmptyString(t.displayFormatWidth)&&(e.dfw=t.displayFormatWidth),_self.utils.isNotEmptyString(t.displayFormatHeight)&&(e.dfh=t.displayFormatHeight),_self.utils.isNotEmptyString(t.displayFormatFrameRate)&&(e.dff=t.displayFormatFrameRate),_self.utils.isNotEmptyString(t.displayFormatBitDepth)&&(e.dfbd=t.displayFormatBitDepth),_self.utils.isNotEmptyString(t.displayFormatColorimetry)&&(e.dfc=t.displayFormatColorimetry),_self.utils.isNotEmptyString(t.displaySizeWidth)&&(e.dsw=t.displaySizeWidth),_self.utils.isNotEmptyString(t.displaySizeHeight)&&(e.dsh=t.displaySizeHeight),_self.utils.isNotEmptyString(t.displaySizeMeasurementType)&&(e.dsmt=t.displaySizeMeasurementType),_self.utils.isNotEmptyString(t.hdr)&&(e.hdr=t.hdr),t._sent=!0,e}}}}}function Tests(){return{testCapabilities:function(){_self.utils.isTrueBool(_self.state.getState().tests.xmlHttpRequest.active)&&e()},testXmlHttpRequest:e};function e(e){var t=_self.state.getState().tests.xmlHttpRequest.name,i="";try{if(i="namespace defined",!_self.utils.isDefined(XMLHttpRequest))return void _self.data.addTest(_self.models.createTest(t,i,!1));_self.data.addTest(_self.models.createTest(t,i,!0)),i="instance defined";var s=new XMLHttpRequest;if(!_self.utils.isDefined(s)||_self.utils.isTrueBool(e))return void _self.data.addTest(_self.models.createTest(t,i,!1));if(_self.data.addTest(_self.models.createTest(t,i,!0)),i="instance functions defined",!_self.utils.isDefined(s.open)||!_self.utils.isDefined(s.send))return void _self.data.addTest(_self.models.createTest(t,i,!1));_self.data.addTest(_self.models.createTest(t,i,!0))}catch(e){var a="unknown exception";_self.utils.isDefined(e)&&_self.utils.isNotEmptyString(e.message)&&(a=e.message),_self.data.addTest(_self.models.createTest(t,i,!1,a))}}}function Capabilities(){return{addXmlCapabilities:function(){try{var e=document.getElementsByTagName("object");if(!_self.utils.isNotEmptyArray(e))return;for(var t=null,i=0;i<e.length;i++){var s=e[i];if(_self.utils.isDefined(s)&&_self.utils.isNotEmptyString(s.type)&&"application/oipfcapabilities"===s.type.toLowerCase()&&_self.utils.isDefined(s.xmlCapabilities)){t=s.xmlCapabilities;break}}if(!_self.utils.isDefined(t))return;if(!_self.utils.isNotEmptyArray(t.childNodes))return;if(!_self.utils.isDefined(t.childNodes[0])||"profilelist"!==t.childNodes[0].nodeName)return;for(var a=t.childNodes[0],i=0;i<a.childNodes.length;i++){var n=a.childNodes[i];_self.utils.isDefined(n)&&_self.utils.isDefined(n.attributes)&&0!==n.attributes.length&&_self.data.addCapability(_self.models.createCapability(n))}}catch(e){var r="";_self.utils.isDefined(e)&&_self.utils.isNotEmptyString(e.message)&&(r=e.message),_self.data.addError("exception adding capability",EinbliqIo.ErrorSeverityIds.WARNING,r)}}}}function Utils(){return{isDefined:EinbliqIo.Helpers().isDefined,isNotEmptyString:EinbliqIo.Helpers().isNotEmptyString,isNotEmptyArray:EinbliqIo.Helpers().isNotEmptyArray,isNotNegativeNumber:EinbliqIo.Helpers().isNotNegativeNumber,isTrueBool:EinbliqIo.Helpers().isTrueBool,isBoolean:EinbliqIo.Helpers().isBoolean,getSortedArray:getSortedArray,getAverage:getAverage,getMedian:getMedian,getStandardDeviation:getStandardDeviation,isCustomerIdLength:isCustomerIdLength,createSessionId:createSessionId,createServerUrl:createServerUrl,createCdnInfoUrl:createCdnInfoUrl,s4:s4,getNextTimerIntervalInMillis:getNextTimerIntervalInMillis,getCdnInformation:getCdnInformation,maxBeaconLengthReached:maxBeaconLengthReached,isValidPlayStateId:isValidPlayStateId,isValidErrorSeverityId:isValidErrorSeverityId,isValidNetworkMeasurement:isValidNetworkMeasurement,getOwnVersionSettings:getOwnVersionSettings,isValidHbbTVConfigTransfer:isValidHbbTVConfigTransfer,getConfigTransferId:getConfigTransferId,isValidFeatureValue:isValidFeatureValue,getFeaturePercentageByValue:getFeaturePercentageByValue,enableByPercentage:enableByPercentage,jsonPolyfill:jsonPolyfill,getNotSentList:getNotSentList,getNotSentObject:getNotSentObject};function getSortedArray(e){for(var t,i=e.length,s=0;s<i;s++){t=!1;for(var a=0;a<i-1;a++)if(e[a]>e[a+1]){var n=e[a];e[a]=e[a+1],e[a+1]=n,t=!0}if(!t)break}return e}function getAverage(e){for(var t=-1,i=0,s=0;s<e.length;s++)i+=e[s];return e.length>0&&(t=Math.round(i/e.length)),t}function getMedian(e){if(0==e.length)return-1;if(e.length%2==1)return e[Math.floor(e.length/2)];var t=e[e.length/2-1],i=e[e.length/2];return Math.round((t+i)/2)}function getStandardDeviation(e){for(var t=0,i=0,s=0;s<e.length;s++)t+=e[s];e.length>0&&(i=t/e.length);var a=[];for(s=0;s<e.length;s++)a.push(e[s]-i);var n=[];for(s=0;s<a.length;s++)n.push(a[s]*a[s]);var r=0;for(s=0;s<n.length;s++)r+=n[s];var o=0;return e.length>0&&(o=r/e.length),Math.round(Math.sqrt(o))}function isCustomerIdLength(e){return!(!_self.utils.isNotEmptyString(e)||e.length!==_self.constants.CUSTOMER_ID_LENGTH)}function createSessionId(){return s4()+s4()+"-"+s4()+"-"+s4()+"-"+s4()+"-"+s4()+s4()+s4()}function createServerUrl(e,t){var i="https://";return t===_self.constants.DATA_TRANSFER_MODES.INSECURE&&(_self.data.addError(_self.constants.EINBLIQIO_ERRORS.SENDER_SWITCH_TO_UNSECURE_TRANSFER,EinbliqIo.ErrorSeverityIds.WARNING),i="http://"),i+e+"."+_self.constants.SERVER_DOMAIN+"/qos/"+_self.constants.LIBRARY_TYPE+"?x="}function createCdnInfoUrl(e,t){var i="?";return e.indexOf(i)>=0&&(i="&"),e+i+_self.constants.CDN_URL_NO_CACHE_PREFIX+t.toString()}function s4(){return Math.floor(65536*(1+Math.random())).toString(16).substring(1)}function getNextTimerIntervalInMillis(e){if(0===e.data.getData().requestNumber)return e.constants.SESSION_INTERVAL.REQUEST_NUMBER_INTERVAL_MAPPINGS.TWO*e.constants.TIME.SECONDS_IN_MILLIS;if(1===e.data.getData().requestNumber)return e.constants.SESSION_INTERVAL.REQUEST_NUMBER_INTERVAL_MAPPINGS.ONE*e.constants.TIME.SECONDS_IN_MILLIS;if(2===e.data.getData().requestNumber)return e.constants.SESSION_INTERVAL.REQUEST_NUMBER_INTERVAL_MAPPINGS.TWO*e.constants.TIME.SECONDS_IN_MILLIS;if(3===e.data.getData().requestNumber)return e.constants.SESSION_INTERVAL.REQUEST_NUMBER_INTERVAL_MAPPINGS.THREE*e.constants.TIME.SECONDS_IN_MILLIS;var t=Date.now(),i=Math.floor((t-e.data.getData().initialisationTimeStamp)/6e4),s=e.data.getData().initialisationTimeStamp+(i+1)*e.constants.SESSION_INTERVAL.REQUEST_NUMBER_INTERVAL_MAPPINGS.DEFAULT*1e3-t;return e.utils.isNotNegativeNumber(s)?s<=1e3*e.constants.SESSION_INTERVAL.REQUEST_NUMBER_INTERVAL_MAPPINGS.TWO?s+1e3*e.constants.SESSION_INTERVAL.REQUEST_NUMBER_INTERVAL_MAPPINGS.DEFAULT:s:s<=0?1:1e3*e.constants.SESSION_INTERVAL.REQUEST_NUMBER_INTERVAL_MAPPINGS.DEFAULT}function getCdnInformation(e){return!!e.state.getState().getCdnInfo&&!(e.data.getData().requestNumber>=1&&e.data.getData().requestNumber<=3)}function maxBeaconLengthReached(e){return _self.constants.BEACON_LIMITS.MAX_URL_SIZE-encodeURIComponent(JSON.stringify(e)).length<100}function isValidPlayStateId(e){return e===EinbliqIo.PlayStateIds.CONNECTING||e===EinbliqIo.PlayStateIds.BUFFERING||e===EinbliqIo.PlayStateIds.CANPLAY||e===EinbliqIo.PlayStateIds.PLAYING||e===EinbliqIo.PlayStateIds.PAUSE||e===EinbliqIo.PlayStateIds.SEEKING||e===EinbliqIo.PlayStateIds.STOP||e===EinbliqIo.PlayStateIds.ENDED||e===EinbliqIo.PlayStateIds.STALLED||e===EinbliqIo.PlayStateIds.ABORT||e===EinbliqIo.PlayStateIds.ERROR||e===EinbliqIo.PlayStateIds.SWITCHED}function isValidErrorSeverityId(e){return e===EinbliqIo.ErrorSeverityIds.INFORMATION||e===EinbliqIo.ErrorSeverityIds.WARNING||e===EinbliqIo.ErrorSeverityIds.ERROR||e===EinbliqIo.ErrorSeverityIds.CRITICAL}function isValidNetworkMeasurement(e){return _self.utils.isNotNegativeNumber(e.requestStartTimeStamp)&&e.requestStartTimeStamp>0&&_self.utils.isNotNegativeNumber(e.requestFirstByteTimeStamp)&&e.requestFirstByteTimeStamp>0&&_self.utils.isNotNegativeNumber(e.requestEndTimeStamp)&&e.requestEndTimeStamp>0&&_self.utils.isNotNegativeNumber(e.totalBytes)&&e.totalBytes>0&&_self.utils.isNotEmptyString(e.mediaType)&&"video"==e.mediaType&&e.requestEndTimeStamp-e.requestStartTimeStamp>12&&e.totalBytes>1e4}function getOwnVersionSettings(){var e=_self.defaults.HBBTV_VERSION_SETTINGS[0];if(!_self.utils.isDefined(navigator)||!_self.utils.isNotEmptyString(navigator.userAgent))return e;var t=navigator.userAgent.toLowerCase();if(-1==t.indexOf("hbbtv/"))return e;var i=t.indexOf("hbbtv/")+6,s=t.substring(i,i+6).split(".");if(!s||3!=s.length)return e;var a=parseInt(s[1]);if(!_self.utils.isNotNegativeNumber(a)||0==a)return e;for(var n=0;n<_self.defaults.HBBTV_VERSION_SETTINGS.length;n++){var r=_self.defaults.HBBTV_VERSION_SETTINGS[n];if(r.id===a)return r}return _self.defaults.HBBTV_VERSION_SETTINGS[7]}function isValidHbbTVConfigTransfer(e){return!!_self.utils.isNotEmptyString(e)&&("auto"===e||"insecure"===e||"secure"===e)}function getConfigTransferId(e){return"auto"==e?_self.constants.DATA_TRANSFER_MODES.AUTO:"insecure"==e?_self.constants.DATA_TRANSFER_MODES.INSECURE:_self.constants.DATA_TRANSFER_MODES.SECURE}function isValidFeatureValue(e){return!!_self.utils.isNotEmptyString(e)&&("all"===e||"90%"===e||"50%"===e||"10%"===e||"none"===e)}function getFeaturePercentageByValue(e){return"all"===e?_self.constants.PROBABILITY_MAPPING.PERCENT_100:"90%"===e?_self.constants.PROBABILITY_MAPPING.PERCENT_90:"50%"===e?_self.constants.PROBABILITY_MAPPING.PERCENT_50:"10%"===e?_self.constants.PROBABILITY_MAPPING.PERCENT_10:_self.constants.PROBABILITY_MAPPING.PERCENT_0}function enableByPercentage(e){if(e===_self.constants.PROBABILITY_MAPPING.PERCENT_0)return!1;if(e===_self.constants.PROBABILITY_MAPPING.PERCENT_100)return!0;var t=Math.floor(100*Math.random())+1;return e===_self.constants.PROBABILITY_MAPPING.PERCENT_10&&t<=10||e===_self.constants.PROBABILITY_MAPPING.PERCENT_50&&t<=50||e===_self.constants.PROBABILITY_MAPPING.PERCENT_90&&t<=90}function getNotSentList(e){for(var t=[],i=0;i<e.length;i++){var s=e[i];s.isSent()||t.push(s)}return t}function getNotSentObject(e){return e.isSent()?null:e}function jsonPolyfill(){JSON||(JSON={}),function(){function f(e){return e<10?"0"+e:e}"function"!=typeof Date.prototype.toJSON&&(Date.prototype.toJSON=function(e){return isFinite(this.valueOf())?this.getUTCFullYear()+"-"+f(this.getUTCMonth()+1)+"-"+f(this.getUTCDate())+"T"+f(this.getUTCHours())+":"+f(this.getUTCMinutes())+":"+f(this.getUTCSeconds())+"Z":null},String.prototype.toJSON=Number.prototype.toJSON=Boolean.prototype.toJSON=function(e){return this.valueOf()});var cx=/[\u0000\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,escapable=/[\\\"\x00-\x1f\x7f-\x9f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,gap,indent,meta={"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"},rep;function quote(e){return escapable.lastIndex=0,escapable.test(e)?'"'+e.replace(escapable,function(e){var t=meta[e];return"string"==typeof t?t:"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)})+'"':'"'+e+'"'}function str(e,t){var i,s,a,n,r,o=gap,l=t[e];switch(l&&"object"==typeof l&&"function"==typeof l.toJSON&&(l=l.toJSON(e)),"function"==typeof rep&&(l=rep.call(t,e,l)),typeof l){case"string":return quote(l);case"number":return isFinite(l)?String(l):"null";case"boolean":case"null":return String(l);case"object":if(!l)return"null";if(gap+=indent,r=[],"[object Array]"===Object.prototype.toString.apply(l)){for(n=l.length,i=0;i<n;i+=1)r[i]=str(i,l)||"null";return a=0===r.length?"[]":gap?"[\n"+gap+r.join(",\n"+gap)+"\n"+o+"]":"["+r.join(",")+"]",gap=o,a}if(rep&&"object"==typeof rep)for(n=rep.length,i=0;i<n;i+=1)"string"==typeof(s=rep[i])&&(a=str(s,l))&&r.push(quote(s)+(gap?": ":":")+a);else for(s in l)Object.hasOwnProperty.call(l,s)&&(a=str(s,l))&&r.push(quote(s)+(gap?": ":":")+a);return a=0===r.length?"{}":gap?"{\n"+gap+r.join(",\n"+gap)+"\n"+o+"}":"{"+r.join(",")+"}",gap=o,a}}"function"!=typeof JSON.stringify&&(JSON.stringify=function(e,t,i){var s;if(gap="",indent="","number"==typeof i)for(s=0;s<i;s+=1)indent+=" ";else"string"==typeof i&&(indent=i);if(rep=t,t&&"function"!=typeof t&&("object"!=typeof t||"number"!=typeof t.length))throw new Error("JSON.stringify");return str("",{"":e})}),"function"!=typeof JSON.parse&&(JSON.parse=function(text,reviver){var j;function walk(e,t){var i,s,a=e[t];if(a&&"object"==typeof a)for(i in a)Object.hasOwnProperty.call(a,i)&&(void 0!==(s=walk(a,i))?a[i]=s:delete a[i]);return reviver.call(e,t,a)}if(text=String(text),cx.lastIndex=0,cx.test(text)&&(text=text.replace(cx,function(e){return"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)})),/^[\],:{}\s]*$/.test(text.replace(/\\(?:["\\\/bfnrt]|u[0-9a-fA-F]{4})/g,"@").replace(/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,"]").replace(/(?:^|:|,)(?:\s*\[)+/g,"")))return j=eval("("+text+")"),"function"==typeof reviver?walk({"":j},""):j;throw new SyntaxError("JSON.parse")})}()}}},Helpers:function(){return{isDefined:e,isNotEmptyString:function(t){return e(t)&&"string"==typeof t&&!(0===t.length)},isNotEmptyArray:function(i){return e(i)&&"object"==typeof i&&t(i.length)&&i.length>0},isNotNegativeNumber:t,isBoolean:function(t){return e(t)&&"boolean"==typeof t},isTrueBool:function(t){return e(t)&&!0===t}};function e(e){return!(null==e)}function t(t){return e(t)&&"number"==typeof t&&t>=0}},LOGGER:{log:function(){}}};
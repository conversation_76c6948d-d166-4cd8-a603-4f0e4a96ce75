import 'styles/root.scss';
import 'styles/app.scss';

import { LanguageResponseBody } from '@apptypes/language';
import { DebugOverlay } from '@components/Debug/DebugOverlay';
import { LoginPage } from '@components/Login/LoginPage';
import { OfflineAlert } from '@components/OfflineAlert/OfflineAlert';
import { PackagedOfflineAlert } from '@components/OfflineAlert/PackagedOfflineAlert';
import { PageLayout } from '@components/PageLayout/PageLayout';
import { ErrorPage } from '@components/Pages/ErrorPage/ErrorPage';
import { Page } from '@components/Pages/Page';
import { InformationPage } from '@components/Pages/SettingsPages/Information/InformationPage';
import { InterfacePage } from '@components/Pages/SettingsPages/Interface/InterfacePage';
import { PersonalisationPage } from '@components/Pages/SettingsPages/PersonalisationPage';
import { PrivacyPage } from '@components/Pages/SettingsPages/Privacy/PrivacyPage';
import { SettingsPage } from '@components/Pages/SettingsPages/SettingsPage';
import { TutorialPage } from '@components/Pages/SettingsPages/Tutorial/TutorialPage';
import { TvGuidePage } from '@components/Pages/TvGuidePage';
import { RootLayout } from '@components/RootLayout/RootLayout';
import { FullScreenSpinner } from '@components/Spinner/FullScreenSpinner';
import { Toaster } from '@components/ToastNotification/Toaster';
import { Verification } from '@components/Verification/Verification';
import { verificationLoader } from '@components/Verification/verificationLoader';
import { setDefaultCookies } from '@features/defaultcookies/defaultcookies';
import { printKey } from '@features/keys/printKey';
import { isDebug } from '@features/queryParams/queryParamsLookup';
import { useWindowKeyDownListener } from '@hooks/useWindowKeyDownListener';
import { init, setKeyMap } from '@noriginmedia/norigin-spatial-navigation';
import { BookmarksContextProvider } from '@providers/BookmarksContext';
import GlobalContextProvider from '@providers/GlobalContextProvider';
import { ModalProvider } from '@providers/ModalContext';
import MouseProvider from '@providers/MouseProvider';
import { authenticationPageLoader } from '@routes/authenticationPageLoader';
import { AuthenticationRoute } from '@routes/AuthenticationRoute';
import { collectionLoader } from '@routes/collectionLoader';
import { loginPageLoader } from '@routes/loginPageLoader';
import { myArtePageLoader } from '@routes/myArtePageLoader';
import { pageLayoutLoader } from '@routes/pageLayoutLoader';
import { pageLoader } from '@routes/pageLoader';
import { programLoader } from '@routes/programLoader';
import { ProtectedRoute } from '@routes/ProtectedRoute';
import { getDeeplinkRedirectionRoute } from '@routes/route';
import { informationLoader, personalisationLoader, settingsLoader } from '@routes/settingsLoader';
import { tvGuideLoader } from '@routes/tvGuideLoader';
import { videoLoader } from '@routes/videoLoader';
import { Tracking } from '@tracking/Tracking';
import { isHosted } from '@util/platform';
import { KeyboardEvent, useEffect } from 'react';
import { init as rrcInit, setKeyMap as rrcSetKeyMap } from 'react-remote-controller';
import { RouterProvider } from 'react-router-dom';
import { createRouter, getKeyMap } from 'target';
import { addOnlineOfflineListener } from 'target';

import { ROUTES } from './constants';
import { getGeoLocation } from './i18n';

const showDebugOverlay = isDebug();

setDefaultCookies();

const App = () => {
  const router = createRouter(
    [
      {
        path: '/',
        element: (
          <MouseProvider>
            <BookmarksContextProvider>
              <ModalProvider>
                <RootLayout />
                <Toaster />
                {isHosted() ? <OfflineAlert /> : <PackagedOfflineAlert />}
              </ModalProvider>
            </BookmarksContextProvider>
          </MouseProvider>
        ),
        errorElement: <ErrorPage />,
        children: [
          { ...getDeeplinkRedirectionRoute() },
          {
            path: '/',
            element: <PageLayout />,
            loader: pageLayoutLoader,
            children: [
              {
                path: '/',
                element: <Page />,
                loader: pageLoader,
              },
              {
                path: `${ROUTES.TV_GUIDE}/:day?`,
                element: <TvGuidePage />,
                loader: tvGuideLoader,
              },
              {
                path: ROUTES.SETTINGS.ROOT,
                element: <SettingsPage />,
                loader: settingsLoader,
                children: [
                  {
                    index: true,
                    element: <InterfacePage />,
                  },
                  {
                    path: ROUTES.SETTINGS.INTERFACE,
                    element: <InterfacePage />,
                  },
                  {
                    path: ROUTES.SETTINGS.PERSONALISATION,
                    element: <PersonalisationPage />,
                    loader: personalisationLoader,
                  },
                  {
                    path: ROUTES.SETTINGS.TUTORIAL,
                    element: <TutorialPage />,
                  },
                  {
                    path: ROUTES.SETTINGS.PRIVACY,
                    element: <PrivacyPage />,
                  },
                  {
                    path: ROUTES.SETTINGS.INFORMATION,
                    element: <InformationPage />,
                    loader: informationLoader,
                  },
                ],
              },
              {
                path: ROUTES.MYARTE.LOGIN,
                element: <LoginPage />,
                loader: loginPageLoader,
              },
              {
                path: ROUTES.MYARTE.AUTHENTICATE,
                element: <AuthenticationRoute />,
                loader: authenticationPageLoader,
              },
              {
                path: ROUTES.MYARTE.ROOT,
                element: <ProtectedRoute />,
                loader: myArtePageLoader,
              },
              {
                path: `${ROUTES.PAGE}/:pageId`,
                element: <Page />,
                loader: pageLoader,
              },
              {
                path: `/${ROUTES.COLLECTION}/:collectionId`,
                element: <Page />,
                loader: collectionLoader,
              },
              {
                path: `${ROUTES.PROGRAM}/:programId`,
                element: <Page />,
                loader: programLoader,
              },
            ],
          },
          {
            path: ROUTES.ERROR,
            element: <ErrorPage />,
          },
          {
            path: `${ROUTES.VERIFICATION}/:videoId`,
            loader: verificationLoader,
            element: <Verification />,
          },
          {
            path: `${ROUTES.VIDEO}/:videoId`,
            loader: videoLoader,
            async lazy() {
              const { ForceMountedPlayer } = await import(/* webpackChunkName: "player" */ './routes/withForcedMount');
              return {
                Component: ForceMountedPlayer,
              };
            },
          },
        ],
      },
    ],
    { basename: process.env.REACT_ROUTER_BASE_PATH },
  );

  Tracking.init(showDebugOverlay);

  // Setup spatial navigation
  const keyMap = getKeyMap();
  init();
  setKeyMap(keyMap);
  // Setup react remote controller lib
  rrcInit({});
  rrcSetKeyMap(keyMap);

  useEffect(() => {
    addOnlineOfflineListener();
    console.log('[userAgent]:', navigator.userAgent);
    console.log('[hash]:', process.env.COMMIT_HASH);
    console.log('[version]:', process.env.VERSION);
    console.log('[resolution]:', `${window.screen?.width}x${window.screen?.height}`);
    if (showDebugOverlay) {
      getGeoLocation().then((geoLocation: LanguageResponseBody) => {
        console.log(`geo location ${geoLocation.country}`);
      });
    }
  }, []);

  useWindowKeyDownListener((e: KeyboardEvent) => printKey(e, keyMap));

  return (
    <GlobalContextProvider>
      {showDebugOverlay && <DebugOverlay />}
      <FullScreenSpinner />
      <RouterProvider router={router}></RouterProvider>
    </GlobalContextProvider>
  );
};

export { App };

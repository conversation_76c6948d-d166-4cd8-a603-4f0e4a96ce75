@use '~styles/globals' as *;

@font-face {
  font-family: 'BarnaRegular';
  src: url('/static/fonts/Barna-Regular.ttf') format('truetype');
}

@font-face {
  font-family: 'BarnaBold';
  src: url('/static/fonts/Barna-Bold.ttf') format('truetype');
}

html,
body {
  margin: 0;
  font-family: $font-family-regular;
  font-size: calc(1280 / 1920 * $root-px * 1px);
  color: $white;
  overflow: hidden;

  @media (min-width: 1920px) {
    font-size: calc($root-px * 1px);
  }
}

#root {
  width: 1280px;
  height: 720px;
  overflow: hidden;
  background-color: $primary-background;

  @media (min-width: 1920px) {
    width: 1920px;
    height: 1080px;
  }
}

@use '~styles/globals' as *;

$subtitle-shadow-color: #010100;
$subtitle-shadow-size: 4px;
$subtitle-font-size: px-to-rem(54);

// ensure 720 resolution
// this is necessary for some targets that require a separate 720 build e.g. webos
// note the build process adds the class to the body element
body.res-720 {
  font-size: calc(1280 / 1920 * $root-px * 1px);

  #root {
    width: 1280px;
    height: 720px;
  }
}

h2 {
  font-size: px-to-rem(42);
  font-family: $font-family-bold;
}

h3 {
  font-size: px-to-rem(32);
  font-family: $font-family-bold;
}

p {
  font-size: px-to-rem(30);
  font-family: $font-family-regular;
}

a {
  text-decoration: none;
  color: inherit;
}

#dashVideoSubtitles div div {
  top: auto !important;
  right: auto !important;
  bottom: px-to-rem(20) !important;
  left: auto !important;
  background-color: transparent !important;
}

#dashVideoSubtitles div div div {
  font-size: $subtitle-font-size !important;
  display: inline-block !important;
  line-height: 1.2 !important;
  background-color: transparent !important;
  color: #fefe01 !important;
  text-shadow: 0 0 $subtitle-shadow-size $subtitle-shadow-color, 0 0 $subtitle-shadow-size $subtitle-shadow-color,
    0 0 $subtitle-shadow-size $subtitle-shadow-color;
}

@use '~styles/functions' as f;
@use '~styles/variables' as v;

/**
 * Note this is CSS3 which may not work on all devices.
 */
@mixin line-clamp($max_lines) {
  display: -webkit-box;
  -webkit-line-clamp: $max_lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

@mixin icon-size($size) {
  width: $size;
  height: $size;
  background-size: $size;
}

@mixin icon-url($path, $name, $focused, $sizeHD, $sizeFHD) {
  background-image: url($path + f.icon-name($name, $focused, $sizeHD));

  @media (min-width: 1920px) {
    background-image: url($path + f.icon-name($name, $focused, $sizeFHD));
  }
}

@mixin icon-XL($path, $name) {
  &.XL {
    @include icon-url(
      $path,
      $name,
      false,
      f.strip-unit(map-get(v.$icon-size-map, xl-hd)),
      f.strip-unit(map-get(v.$icon-size-map, xl-fhd))
    );
  }
}

@mixin icon-XL-focusable($path, $name) {
  @include icon-XL($path, $name);

  &.is-focused {
    &.XL {
      @include icon-url(
        $path,
        $name,
        true,
        f.strip-unit(map-get(v.$icon-size-map, xl-hd)),
        f.strip-unit(map-get(v.$icon-size-map, xl-fhd))
      );
    }
  }
}

@mixin icon-L($path, $name) {
  &.L {
    @include icon-url(
      $path,
      $name,
      false,
      f.strip-unit(map-get(v.$icon-size-map, l-hd)),
      f.strip-unit(map-get(v.$icon-size-map, l-fhd))
    );
  }
}

@mixin icon-ML($path, $name) {
  &.ML {
    @include icon-url(
      $path,
      $name,
      false,
      f.strip-unit(map-get(v.$icon-size-map, ml-hd)),
      f.strip-unit(map-get(v.$icon-size-map, ml-fhd))
    );
  }
}

@mixin icon-M($path, $name) {
  &.M {
    @include icon-url(
      $path,
      $name,
      false,
      f.strip-unit(map-get(v.$icon-size-map, m-hd)),
      f.strip-unit(map-get(v.$icon-size-map, m-fhd))
    );
  }
}

@mixin icon-M-focusable($path, $name) {
  @include icon-M($path, $name);

  &.is-focused {
    &.M {
      @include icon-url(
        $path,
        $name,
        true,
        f.strip-unit(map-get(v.$icon-size-map, m-hd)),
        f.strip-unit(map-get(v.$icon-size-map, m-fhd))
      );
    }
  }
}

@mixin icon-S($path, $name) {
  &.S {
    @include icon-url(
      $path,
      $name,
      false,
      f.strip-unit(map-get(v.$icon-size-map, s-hd)),
      f.strip-unit(map-get(v.$icon-size-map, s-fhd))
    );
  }
}

@mixin icon-S-focusable($path, $name) {
  @include icon-S($path, $name);

  &.is-focused {
    &.S {
      @include icon-url(
        $path,
        $name,
        true,
        f.strip-unit(map-get(v.$icon-size-map, s-hd)),
        f.strip-unit(map-get(v.$icon-size-map, s-fhd))
      );
    }
  }
}

@mixin icon-XS($path, $name) {
  &.XS {
    @include icon-url(
      $path,
      $name,
      false,
      f.strip-unit(map-get(v.$icon-size-map, xs-hd)),
      f.strip-unit(map-get(v.$icon-size-map, xs-fhd))
    );
  }
}

@mixin icon-XS-focusable($path, $name) {
  @include icon-XS($path, $name);

  &.is-focused {
    &.XS {
      @include icon-url(
        $path,
        $name,
        true,
        f.strip-unit(map-get(v.$icon-size-map, xs-hd)),
        f.strip-unit(map-get(v.$icon-size-map, xs-fhd))
      );
    }
  }
}

@mixin truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

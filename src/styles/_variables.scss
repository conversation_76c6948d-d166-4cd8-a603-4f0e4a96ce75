@use 'colors';
@use 'functions';

$focused-border-width: 3px;
$focused-border-width-fhd: 6px;
$focused-border-color: colors.$white;
$border-live-concert-color: colors.$pink;
$border-live-color: colors.$arte-highlight;
$border-width-live: 5px;

$theme-white-border-color: colors.$dark-grey;

$hero-image-width: 640px;
$hero-image-height: 360px;
$hero-image-width-fhd: 940px;
$hero-image-height-fhd: 530px;

$menu-width-collapsed: functions.px-to-rem(150);
$menu-width-expanded: functions.px-to-rem(380);

$player-button-background-color: rgba(155, 155, 155, 0.5);
$player-button-border-size: functions.px-to-rem(3);
$player-button-border: solid $player-button-border-size;
$player-button-border-color: colors.$white;
$player-button-dimension: functions.px-to-rem(100);

$title-font-size: functions.px-to-rem(70);

$rail-gap: functions.px-to-rem(60);
$rail-item-gap: 10px;
@media (min-width: 1920px) {
  $rail-item-gap: 16px;
}

$keyboard-view-width: functions.px-to-rem(380);
$settings-menu-width: functions.px-to-rem(420);
$settings-nav-button-width: functions.px-to-rem(300);

$zone-title-padding-top: functions.px-to-rem(16);
$zone-title-margin-bottom: functions.px-to-rem(16);

$icon-size-map: (
  xs-hd: 20px,
  xs-fhd: 32px,
  s-hd: 24px,
  s-fhd: 40px,
  m-hd: 32px,
  m-fhd: 46px,
  ml-hd: 32px,
  ml-fhd: 55px,
  l-hd: 40px,
  l-fhd: 55px,
  xl-hd: 40px,
  xl-fhd: 65px,
);

$spinner-size: functions.px-to-rem(80px);
$spinner-size-small: functions.px-to-rem(40px);

$tv-guide-menu-width: functions.px-to-rem(380);
$tv-guide-menu-item-height: functions.px-to-rem(110);
$tv-guide-menu-visible-items: 6;

$big_play_button_width: functions.px-to-rem(220);
$big_play_button_height: functions.px-to-rem(220);

$search-view-padding: functions.px-to-rem(58);
// Two different values to align with search tiles which are not the same size in hd and fhd
$search-header-width: functions.px-to-rem(1245);
$search-header-width-fhd: functions.px-to-rem(1202);

// teaser size
$teaser-horizontal-landscape-big-width: 384px + ($focused-border-width * 2);
$teaser-horizontal-landscape-big-width-fhd: 540px + ($focused-border-width-fhd * 2);
$teaser-horizontal-portrait-width: 235px + ($focused-border-width * 2);
$teaser-horizontal-portrait-width-fhd: 300px + ($focused-border-width-fhd * 2);
$teaser-horizontal-square-width: 125px + ($focused-border-width * 2);
$teaser-horizontal-square-width-fhd: 200px + ($focused-border-width-fhd * 2);
$teaser-horizontal-landscape-width: 265px + ($focused-border-width * 2);
$teaser-horizontal-landscape-width-fhd: 384px + ($focused-border-width-fhd * 2);
$teaser-horizontal-landscape-fixed-height: 335px;
$teaser-horizontal-landscape-fixed-height-fhd: 502px;

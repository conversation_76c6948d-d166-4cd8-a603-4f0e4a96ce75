import { NativeDeeplink } from '@apptypes/deeplink';
import { UTM_CAMPAIGN_KEY, UTM_MEDIUM_KEY } from '@features/queryParams/queryParamsConsts';
import { useModalContext } from '@providers/ModalContext';
import { getRoute, isVideoRouteByLocation } from '@routes/route';
import EventBus, { EventData } from '@util/EventBus';
import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { getSearchParams } from 'target';

import { EVENTS } from '../constants';
import { useCustomNavigate } from './useCustomNavigate';

const getQueryString = (deeplink: NativeDeeplink): string => {
  const searchParams = getSearchParams();
  searchParams.set(UTM_CAMPAIGN_KEY, deeplink.utmCampaign);
  searchParams.set(UTM_MEDIUM_KEY, deeplink.utmMedium);
  return `?${searchParams.toString()}`;
};

export function useNativeDeeplinks() {
  const navigate = useCustomNavigate();
  const location = useLocation();
  const { hideModal } = useModalContext();

  useEffect(() => {
    function handleNativeDeeplink<T>(eventData: EventData<T>) {
      const data: unknown = eventData[EVENTS.NATIVE_DEEPLINK];
      const deeplink = data as NativeDeeplink;
      const replace = isVideoRouteByLocation(location);
      const to = { pathname: getRoute(deeplink.id), search: getQueryString(deeplink) };
      hideModal();
      navigate(to, { replace: replace });
    }

    EventBus.on(EVENTS.NATIVE_DEEPLINK, handleNativeDeeplink);
    return () => {
      EventBus.off(EVENTS.NATIVE_DEEPLINK, handleNativeDeeplink);
    };
  }, [hideModal, location, navigate]);
}

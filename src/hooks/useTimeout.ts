import { useEffect, useRef } from 'react';

const useTimeout = (callback: () => void, delay: number | null) => {
  const savedCallback = useRef(callback);

  useEffect(() => {
    savedCallback.current = callback;
  }, [callback]);

  useEffect(() => {
    const func = () => {
      savedCallback.current();
    };
    if (delay !== null) {
      const id = setTimeout(func, delay);
      return () => clearTimeout(id);
    }
  }, [delay]);
};

export default useTimeout;

import { isMagentaTv } from '@features/queryParams/queryParamsLookup';
import { useModalContext } from '@providers/ModalContext';
import { useEffect, useState } from 'react';
import { getKeyMap } from 'target';

import { KEY_FAST_FWD, KEY_LEFT, KEY_REWIND, KEY_RIGHT } from '../../target/keys';
import { useInterval } from './useInterval';
import { useWindowKeyDownListener } from './useWindowKeyDownListener';
import { useWindowKeyUpListener } from './useWindowKeyUpListener';

const CANCEL_INTERVAL = null;

const SEEK_GAP_SECONDS = [10, 30, 90]; // seconds to jump when seeking continuously
const CONTINUOUS_SEEK_ALLOWED_INTERVAL = 300; // when key is continuously pressed seek every 100ms
const CONTINUOUS_SEEK_GAP_INCREASE_INTERVAL = 600; // when key is continuously pressed increase seek gap every 300ms
const KEY_UP_THRESHOLD = 400; // when key is pressed for less than 180ms do not seek continuously
const KEY_PRESS_LIMIT = 2; // when more than 2 consecutive keys are pressed, increase the seek gap
const KEY_REPEAT_PRESS_INTERVAL = 1200; // when more than 3 consecutive keys are pressed within 800 ms, increase the seek gap

const rightKeysToListen = {
  focused: [KEY_RIGHT, KEY_FAST_FWD],
  unfocused: [KEY_FAST_FWD],
};

const leftKeysToListen = {
  focused: [KEY_LEFT, KEY_REWIND],
  unfocused: [KEY_REWIND],
};

const getRepeatPressInterval = () => {
  if (isMagentaTv()) {
    return 1500;
  }
  return KEY_REPEAT_PRESS_INTERVAL;
};

const getKeyUpThreshold = () => {
  if (isMagentaTv()) {
    return 700;
  }
  return KEY_UP_THRESHOLD;
};

export const useContinuousKeyPress = (attributes, callback) => {
  const { duration, focused, shouldReset, seeking } = attributes;

  const [keysToListen, setKeysToListen] = useState({
    right: [],
    left: [],
  });
  const [fakeDirection, setFakeDirection] = useState<string>('');
  const [keyUpTimestamp, setKeyUpTimestamp] = useState<number>(-10);
  const [seekGap, setSeekGap] = useState<number>(0);
  const [countKeyPresses, setCountKeyPresses] = useState<number>(0);

  const [allowedContinuouslySeekInterval, setAllowedContinuouslySeekInterval] = useState<number | null>(
    CANCEL_INTERVAL,
  );
  const [continuousIncreaseGapInterval, setContinuousIncreaseGapInterval] = useState<number | null>(CANCEL_INTERVAL);
  const [countKeyPressInterval, setCountKeyPressInterval] = useState<number | null>(CANCEL_INTERVAL);
  const { modalOpen } = useModalContext();

  useEffect(() => {
    if (shouldReset) {
      setSeekGap(0);
      setContinuousIncreaseGapInterval(CANCEL_INTERVAL);
    }
  }, [shouldReset]);

  useEffect(() => {
    if (!seeking) {
      setSeekGap(0);
    }
  }, [seeking]);

  useEffect(() => {
    setAllowedContinuouslySeekInterval(CANCEL_INTERVAL);
    setContinuousIncreaseGapInterval(CANCEL_INTERVAL);
    setCountKeyPressInterval(CANCEL_INTERVAL);
    if (focused) {
      setKeysToListen({
        right: rightKeysToListen.focused,
        left: leftKeysToListen.focused,
      });
    } else {
      setKeysToListen({
        right: rightKeysToListen.unfocused,
        left: leftKeysToListen.unfocused,
      });
    }
  }, [focused]);

  useWindowKeyDownListener((e) => {
    if (duration === 0) return;
    if (modalOpen) return;

    const { keyCode } = e;
    const direction = detectDirection(keyCode);

    if (direction === KEY_RIGHT || direction === KEY_LEFT) {
      setFakeDirection(direction);
      keyUpTimestamp === -1 && setKeyUpTimestamp(Date.now());
      setAllowedContinuouslySeekInterval(CONTINUOUS_SEEK_ALLOWED_INTERVAL);
      setContinuousIncreaseGapInterval(CONTINUOUS_SEEK_GAP_INCREASE_INTERVAL);
      setCountKeyPressInterval(getRepeatPressInterval());
    }
  });

  useWindowKeyUpListener((e) => {
    if (duration === 0) return;
    if (modalOpen) return;

    const { keyCode } = e;
    const direction = detectDirection(keyCode);
    if (direction === KEY_RIGHT || direction === KEY_LEFT) {
      const keyUpDiff = Date.now() - keyUpTimestamp;
      if (keyUpDiff < getKeyUpThreshold()) {
        triggerSeek();
      }
      setCountKeyPresses(countKeyPresses + 1);
      setKeyUpTimestamp(-1);
      setAllowedContinuouslySeekInterval(CANCEL_INTERVAL);
      setContinuousIncreaseGapInterval(CANCEL_INTERVAL);
    }
  });

  const detectDirection = (keyCode) => {
    const keyMap = getKeyMap();

    if (keysToListen.right.some((key) => keyMap[key] === keyCode)) {
      return KEY_RIGHT;
    }

    if (keysToListen.left.some((key) => keyMap[key] === keyCode)) {
      return KEY_LEFT;
    }
  };

  const triggerSeek = () => {
    callback({ fakeDirection, seekJump: SEEK_GAP_SECONDS[seekGap], seekGap });
  };

  useInterval(() => {
    triggerSeek();
  }, allowedContinuouslySeekInterval);

  useInterval(() => {
    incrementSeekGap();
  }, continuousIncreaseGapInterval);

  useInterval(() => {
    countKeyPresses >= KEY_PRESS_LIMIT && incrementSeekGap();
    setCountKeyPresses(0);
    setCountKeyPressInterval(null);
  }, countKeyPressInterval);

  const incrementSeekGap = () => seekGap < SEEK_GAP_SECONDS.length - 1 && setSeekGap(seekGap + 1);
};

import debounce from 'lodash/debounce';
import { DependencyList, EffectCallback, useEffect, useRef } from 'react';

export function useLazyEffect(effect: EffectCallback, deps: DependencyList = [], wait = 300) {
  const cleanupFunc = useRef<void | (() => void)>();

  useEffect(() => {
    const debouncedEffect = debounce(() => {
      if (cleanupFunc.current) {
        cleanupFunc.current();
      }

      cleanupFunc.current = effect();
    }, wait);

    debouncedEffect();

    return () => {
      debouncedEffect.cancel();

      if (cleanupFunc.current) {
        cleanupFunc.current();
      }
    };
  }, [deps, effect, wait]);
}

import { useEffect, useState } from 'react';
import { useRemoteController } from 'react-remote-controller';
import { getKeyMap } from 'target';

type ValidKey = 'down' | 'left' | 'right';
type KeySequence = ValidKey[];

const KEY_SEQUENCE: KeySequence = ['down', 'down', 'down', 'left', 'right', 'left', 'right'];
const SEQUENCE = KEY_SEQUENCE.join('');
const TIMEOUT = 2000;

export function useKeySequence() {
  const [started, setStarted] = useState(false);
  const [sequence, setSequence] = useState('');
  const [unlocked, setUnlocked] = useState(false);

  function addKey(key: ValidKey) {
    if (unlocked) return;
    if (!started) setStarted(true);
    setSequence((prevState) => {
      return `${prevState}${key}`;
    });
  }

  function reset() {
    setSequence('');
    setUnlocked(false);
    setStarted(false);
  }

  useEffect(() => {
    const timeoutId = setTimeout(reset, TIMEOUT);
    return () => clearTimeout(timeoutId);
  }, [started, unlocked]);

  useEffect(() => {
    if (!unlocked && sequence === SEQUENCE) setUnlocked(true);
  }, [sequence, unlocked]);

  useRemoteController<ReturnType<typeof getKeyMap>>({
    listenTo: {
      down: () => addKey('down'),
      left: () => addKey('left'),
      right: () => addKey('right'),
    },
  });

  return {
    unlocked,
  };
}

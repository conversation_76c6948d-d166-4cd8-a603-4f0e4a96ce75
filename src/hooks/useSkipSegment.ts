import { useCallback, useEffect, useState } from 'react';

import { useSeek, useVideoContext } from '../components/Video/';
import { Segment, Segments } from '../components/Video/types/Segmets';
import { isObjectEmpty } from '../util/object';
import { VideoObjectEvent } from '../videoplayer/video-object/types';

const useSkipSegment = () => {
  const { loadedStream, videoObject, isPlaying, setFakeSeeking, setSkipSeekingTarget } = useVideoContext();
  const { immediateScrubTo } = useSeek();

  const [skipSegmentActive, setSkipSegmentActive] = useState<Segment | object>({});
  const [foundActiveSegment, setFoundActiveSegment] = useState<boolean>(false);

  const computeActiveSegment = useCallback(
    (time: number, duration: number) => {
      if (duration === 0 || !time) return;

      if (loadedStream?.segments) {
        const segments: Segments = loadedStream?.segments as Segments;

        const activeSegment = segments.find((item) => time >= item.begin && time <= item.end);

        if (activeSegment) {
          if (
            isObjectEmpty(skipSegmentActive) ||
            activeSegment.type !== skipSegmentActive?.type ||
            activeSegment.begin !== skipSegmentActive?.begin ||
            activeSegment.end !== skipSegmentActive?.end
          ) {
            setSkipSegmentActive(activeSegment);
          }
          setFoundActiveSegment(true);
        } else {
          setFoundActiveSegment(false);
          if (!isObjectEmpty(skipSegmentActive)) {
            setSkipSegmentActive({});
          }
        }
      }
    },
    [loadedStream, skipSegmentActive],
  );

  const onTimeUpdate = useCallback(
    (event: Event) => {
      const customEvent = event as CustomEvent;
      const { time, duration } = customEvent?.detail;

      computeActiveSegment(time, duration);
    },
    [computeActiveSegment],
  );

  useEffect(() => {
    videoObject?.on(VideoObjectEvent.TIMEUPDATE, onTimeUpdate);

    return () => {
      videoObject?.off(VideoObjectEvent.TIMEUPDATE, onTimeUpdate);
    };
  }, [videoObject, onTimeUpdate]);

  useEffect(() => {
    if (!foundActiveSegment && !isObjectEmpty(skipSegmentActive)) {
      setSkipSegmentActive({});
    }
  }, [skipSegmentActive, foundActiveSegment, isPlaying]);

  const triggerSeek = () => {
    setFakeSeeking(true);
    setSkipSeekingTarget(skipSegmentActive.end + 1);
    immediateScrubTo(skipSegmentActive.end + 1);
    setFoundActiveSegment(false);
    setSkipSegmentActive({});
  };

  return {
    skipSegmentActive,
    triggerSeek,
  };
};

export { useSkipSegment };

import { useCallback, useContext } from 'react';

import { BookmarksContext } from '../providers/BookmarksContext';
import { Zone } from '../types';

export const useZones = (zones: Zone[]) => {
  const { bookmarks } = useContext(BookmarksContext);

  const zonesExist = useCallback(() => {
    const bookmarksInPageTemplate = zones.some((zone) => zone.authenticatedContent === 'sso-favorites');
    const bookmarksShown = bookmarksInPageTemplate && bookmarks?.length;
    const zonesOtherThanBookmarksExist = zones.some((zone) => zone.authenticatedContent !== 'sso-favorites');
    const thereAreSomeZones = bookmarksShown || zonesOtherThanBookmarksExist;
    return thereAreSomeZones;
  }, [bookmarks, zones]);

  return { zonesExist };
};

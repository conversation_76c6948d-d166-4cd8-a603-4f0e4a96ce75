import { getPersistedQueryString } from '@features/queryParams/persistedQueryParams';
import { emitOfflineEvent, isOnlineBackupCheck } from '@util/offlineEvent';
import { useCallback } from 'react';
import { NavigateOptions, Path, To, useNavigate } from 'react-router-dom';

/**
 * A replacement for useNavigate hook. It's needed for persisting the query string.
 * @returns A navigate function which also persists the query string.
 */

export const useCustomNavigate = () => {
  const navigate = useNavigate();

  const navigateCallback = useCallback(
    async (a1: unknown, a2?: unknown) => {
      function customNavigate(to: To, options?: NavigateOptions): void;
      function customNavigate(delta: number): void;
      function customNavigate(arg1: unknown, arg2?: NavigateOptions) {
        if (typeof arg1 === 'number') {
          // navigate a number of steps back
          navigate(arg1);
        } else if (typeof arg1 === 'string') {
          // navigate forward persisting the query string
          navigate(`${arg1}${getPersistedQueryString()}`, arg2);
        } else if (isPath(arg1)) {
          // navigate forward overriding the query string
          navigate(`${arg1.pathname}${arg1.search}`, arg2);
        }
      }

      const isBackupOffline = await isOnlineBackupCheck();
      if (!isBackupOffline) {
        emitOfflineEvent(true);
        return;
      }
      return customNavigate(a1 as To, a2 as NavigateOptions);
    },
    [navigate],
  );

  return navigateCallback;
};

function isPath(arg: unknown): arg is Path {
  return (<Path>arg).search !== undefined;
}

import { useEffect, useState } from 'react';

// Each scroll trigger event will scroll the content by 60% of the container height
const SCROLL_AMOUNT = 0.6;

const useContentScrolling = (contentContainerRef, contentRef, stickRef, barRef, content) => {
  const [contentYOffset, setContentYOffset] = useState(0);
  const [scrollBarYOffset, setScrollBarYOffset] = useState(0);
  const [shouldShowScrollbar, setShouldShowScrollbar] = useState(false);

  const scroll = (pDirection) => {
    if (!contentContainerRef.current || !contentRef.current) return;

    const containerHeight = contentContainerRef.current.clientHeight;
    const contentHeight = contentRef.current.clientHeight;

    if (contentHeight < containerHeight) return;

    if (!stickRef.current || !barRef.current) return;

    const stickHeight = stickRef.current.clientHeight;
    const barHeight = barRef.current.clientHeight;

    const scrollStep = Math.round(containerHeight * SCROLL_AMOUNT);
    const bottomContentLimit = containerHeight - contentHeight - barHeight;
    const computedStyle = window.getComputedStyle(barRef.current);
    const barBottomLimit =
      stickHeight - barHeight - parseFloat(computedStyle.marginTop) - parseFloat(computedStyle.marginBottom);
    let newContentYOffset;

    if (pDirection === 'down') {
      newContentYOffset = contentYOffset - scrollStep;
      if (newContentYOffset < containerHeight - contentHeight) newContentYOffset = bottomContentLimit;
    }

    if (pDirection === 'up') {
      newContentYOffset = contentYOffset + scrollStep;
      if (newContentYOffset > 0) newContentYOffset = 0;
    }

    const perScrolled = Math.abs(Math.round((newContentYOffset / bottomContentLimit) * 100));

    setContentYOffset(Math.round(newContentYOffset));
    setScrollBarYOffset(Math.round((barBottomLimit * perScrolled) / 100));
  };

  useEffect(() => {
    if (!contentContainerRef.current || !contentRef.current) return;

    const containerHeight = contentContainerRef.current.clientHeight;
    const contentHeight = contentRef.current.clientHeight;

    contentHeight > containerHeight && setShouldShowScrollbar(true);
  }, [content, contentContainerRef, contentRef]);

  return { contentYOffset, scrollBarYOffset, scroll, shouldShowScrollbar };
};

export { useContentScrolling };

import './styles/root.scss';

import { useEffect } from 'react';
import { init, setKeyMap, useRemoteController } from 'react-remote-controller';
import { exitApp, getKeyMap } from 'target';

interface ErrorProps {
  title: string;
  description: string;
}

export function AppError(props: ErrorProps) {
  const { title, description } = props;

  const keyMap = getKeyMap();
  init({});
  setKeyMap(keyMap);

  useRemoteController<ReturnType<typeof getKeyMap>>({
    listenTo: {
      back: () => {
        exitApp();
      },
    },
  });

  useEffect(() => {
    const element = document.getElementById('splash');
    if (element) element.remove();
  }, []);

  return (
    <div style={{ maxWidth: '600px', margin: '0 auto', paddingTop: '160px', textAlign: 'center' }}>
      <h1 style={{ fontFamily: 'BarnaBold', fontSize: '64px' }}>{title}</h1>
      <p style={{ fontSize: '32px' }}>{description}</p>
    </div>
  );
}

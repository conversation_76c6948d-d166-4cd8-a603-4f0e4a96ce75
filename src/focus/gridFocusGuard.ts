import { ITeaserResponse } from '@apptypes/ITeaserResponse';
import { computeKeyboardFocusKey } from '@components/Keyboard/keyboardConfig';
import { UseFocusableResult } from '@noriginmedia/norigin-spatial-navigation';
import { getSearchKeyboardNavExitKeyRight } from '@util/NavHistory';

import { FOCUS_KEY_PURGE_HISTORY_BUTTON } from './focusKeys';

export const gridPageFocusGuard = (teaser: ITeaserResponse, direction: string, index: number, rowLength: number) => {
  switch (direction) {
    case 'left':
    case 'down':
    case 'right':
      return true;
    case 'up':
      if (index < rowLength) {
        return false;
      }
      return true;
    default:
      return false;
  }
};

export const myHistoryPageFocusGuard = (
  teaser: ITeaserResponse,
  direction: string,
  index: number,
  rowLength: number,
  setFocus: UseFocusableResult['setFocus'],
) => {
  switch (direction) {
    case 'up':
      if (teaser.emptyState) {
        return false;
      }
      if (index < rowLength) {
        setFocus(FOCUS_KEY_PURGE_HISTORY_BUTTON);
        return false;
      }
      return true;
    default:
      return gridPageFocusGuard(teaser, direction, index, rowLength);
  }
};

export const searchGridFocusGuard = (
  teaser: ITeaserResponse,
  direction: string,
  index: number,
  rowLength: number,
  setFocus: UseFocusableResult['setFocus'],
) => {
  switch (direction) {
    case 'up':
    case 'down':
    case 'right':
      return true;
    case 'left':
      if (index % rowLength) {
        return true;
      } else {
        setFocus(computeKeyboardFocusKey(getSearchKeyboardNavExitKeyRight()) || '');
        return false;
      }
    default:
      return false;
  }
};

import classNames, { Argument } from 'classnames';

const setFocusClass = (styles: Record<string, string>, properties: IFocusState) => ({
  [styles['is-active']]: properties.isActive,
  [styles['is-focused']]: properties.isFocused,
  [styles['is-disabled']]: properties.isDisabled,
});

export const focusClassNames = (styles: Record<string, string>, properties: IFocusState, ...arguments_: Argument[]) =>
  classNames(arguments_, setFocusClass(styles, properties));

export interface IFocusState {
  isFocused?: boolean;
  isActive?: boolean;
  isDisabled?: boolean;
}

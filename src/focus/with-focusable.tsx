import { KeyPressDetails, useFocusable, UseFocusableConfig } from '@noriginmedia/norigin-spatial-navigation';
import { MouseContext } from '@providers/MouseProvider';
import { ComponentProps, ForwardRefExoticComponent, FunctionComponent, useContext, useEffect, useState } from 'react';
import { config } from 'target';

import { IFocusState } from './focus';

export interface Focusable<T> extends UseFocusableConfig<T>, IFocusState {
  onKeyPress?: (details: KeyPressDetails, properties: T) => boolean;
  onFocusChange?: (isFocused: boolean) => void;
  focusOnMount?: boolean;
  onClick?: (event: Event) => void;
}

export const withFocusable = <
  T extends ForwardRefExoticComponent<ComponentProps<T>>,
  ResolvedProperties = JSX.LibraryManagedAttributes<T, Focusable<T> & Omit<ComponentProps<T>, keyof Focusable<T>>>,
>(
  Component: T,
) => {
  const displayName = `Focusable(${Component.displayName || Component.name})`;

  const WrappedComponent: FunctionComponent<ResolvedProperties & Focusable<T>> = ({
    onFocusChange,
    isDisabled,
    focusOnMount = false,
    ...rest
  }) => {
    const { isMouseActive } = useContext(MouseContext);

    const { ref, focused, focusKey, focusSelf } = useFocusable({
      focusable: !isDisabled,
      ...rest,
    });

    const [isHovered, setIsHovered] = useState(false);

    useEffect(() => {
      const handleMouseEnter = () => {
        setIsHovered(true);
        focusSelf();
      };

      const handleMouseLeave = () => {
        setIsHovered(false);
      };

      const element = ref.current;
      if (element && config.hasMouseSupport) {
        element.addEventListener('mouseenter', handleMouseEnter);
        element.addEventListener('mouseleave', handleMouseLeave);
      }

      return () => {
        if (element && config.hasMouseSupport) {
          element.removeEventListener('mouseenter', handleMouseEnter);
          element.removeEventListener('mouseleave', handleMouseLeave);
        }
      };
    }, [ref, focusSelf]);

    useEffect(() => {
      onFocusChange?.(focused);
    }, [onFocusChange, focused]);

    useEffect(() => {
      if (focusOnMount) {
        focusSelf();
      }
    }, [focusSelf, focusOnMount]);

    return (
      <Component
        ref={ref}
        testIdFocused={focused}
        focusKey={focusKey}
        isFocused={(focused && !isMouseActive) || (isMouseActive && isHovered)}
        isDisabled={isDisabled}
        {...(rest as JSX.LibraryManagedAttributes<T, ComponentProps<T>>)}
      />
    );
  };

  WrappedComponent.displayName = displayName;

  return WrappedComponent;
};

import { PAGE_IDS, PageId } from '@constants';

export const teaserFocusKey = (zoneIndex: number, teaserIndex: number) =>
  `focus-key-zone-${zoneIndex}-teaser-${teaserIndex}`;
export const menuFocusKey = (pageType: PageId) => `focus-key-menu-${pageType}`;
export const searchGenreFocusKey = (index: number) => `focus-key-search-genre-${index}`;

export const tvGuideMenuFocusKey = (menuItem: number) => `focus-key-tv-guide-menu-${menuItem}`;
export const settingsMenuFocusKey = (menuItem: number) => `focus-key-settings-menu-${menuItem}`;
export const FOCUS_KEY_MODAL = 'FOCUS_KEY_MODAL';
export const FOCUS_KEY_FIRST_SEARCH_GENRE = searchGenreFocusKey(0);
export const FOCUS_KEY_MENU_SEARCH = menuFocusKey(PAGE_IDS.SEARCH_HOME);
export const FOCUS_KEY_MENU_MYARTE = menuFocusKey(PAGE_IDS.MYARTE);
export const FOCUS_KEY_MENU_QUIT = menuFocusKey(PAGE_IDS.QUIT);
/**
 * Use the primary focus key for elements that need to gain focus when users leave the side menu.
 */
export const FOCUS_KEY_PRIMARY = 'FOCUS_KEY_PRIMARY';
export const FOCUS_KEY_OFFLINE_MODAL = 'FOCUS_KEY_OFFLINE_MODAL';
export const FOCUS_KEY_PRIMARY_RIGHTHAND = 'FOCUS_KEY_PRIMARY_RIGHTHAND';
export const FOCUS_KEY_PRIMARY_LEFTHAND = 'FOCUS_KEY_PRIMARY_LEFTHAND';
export const FOCUS_KEY_PLAYER_BACK = 'FOCUS_KEY_PLAYER_BACK';
export const FOCUS_KEY_PLAYER_SKIP = 'FOCUS_KEY_PLAYER_SKIP';
export const FOCUS_KEY_PLAYER_PREVIOUS_PROGRAM = 'FOCUS_KEY_PLAYER_PREVIOUS_PROGRAM';
export const FOCUS_KEY_PLAYER_FAVOURITE = 'FOCUS_KEY_PLAYER_FAVOURITE';
export const FOCUS_KEY_PLAYER_INFO = 'FOCUS_KEY_PLAYER_INFO';
export const FOCUS_KEY_PLAYER_NEXT_PROGRAM = 'FOCUS_KEY_PLAYER_NEXT_PROGRAM';
export const FOCUS_KEY_PLAYER_TIME_BAR = 'FOCUS_KEY_PLAYER_TIME_BAR';
export const FOCUS_KEY_PLAYER_AUDIO_SUBTITLES = 'FOCUS_KEY_PLAYER_AUDIO_SUBTITLES';
export const FOCUS_KEY_PURGE_HISTORY_BUTTON = 'FOCUS_KEY_PURGE_HISTORY_BUTTON';
export const FOCUS_KEY_SEARCH_EDITORIAL_CONTENT = 'FOCUS_KEY_SEARCH_EDITORIAL_CONTENT';
export const FOCUS_KEY_SIDE_MENU = 'FOCUS_KEY_SIDE_MENU';
export const FOCUS_KEY_GRID_VIEW = 'FOCUS_KEY_GRID_VIEW';
export const FOCUS_TV_GUIDE_PROGRAM = 'FOCUS_TV_GUIDE_PROGRAM';

export const FOCUS_KEY_AGE_CONFIRMATION = 'FOCUS_KEY_AGE_CONFIRMATION';
export const FOCUS_KEY_AGE_CONFIRMATION_ERROR = 'FOCUS_KEY_AGE_CONFIRMATION_ERROR';

export const FOCUS_KEY_TOP_TEASER_BOOKMARK = 'FOCUS_KEY_TOP_TEASER_BOOKMARK';
export const FOCUS_KEY_TOP_TEASER_MORE_INFO = 'FOCUS_KEY_TOP_TEASER_MORE_INFO';
export const FOCUS_KEY_TOP_TEASER_TRAILER = 'FOCUS_KEY_TOP_TEASER_TRAILER';

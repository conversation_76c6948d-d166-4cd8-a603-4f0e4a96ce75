import { useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import { useEffect } from 'react';

/**
 * A hook that sets focus to a specified key on unmount
 * @param returnFocusKey a key to which the focus will be returned
 * @param testFocusKey if specified, the hook additionally checks if the current focus key starts with the test focus key
 */
export function useReturnFocusOnUnmount(returnFocusKey: string, testFocusKey?: string) {
  const { setFocus, getCurrentFocusKey } = useFocusable();

  useEffect(() => {
    // return focus on unmount
    return () => {
      if (!testFocusKey || getCurrentFocusKey().startsWith(testFocusKey)) {
        setFocus(returnFocusKey);
      }
    };
  }, [getCurrentFocusKey, returnFocusKey, setFocus, testFocusKey]);
}

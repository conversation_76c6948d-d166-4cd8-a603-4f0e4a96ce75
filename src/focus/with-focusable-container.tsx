import {
  FocusContext,
  KeyPressDetails,
  useFocusable,
  UseFocusableConfig,
} from '@noriginmedia/norigin-spatial-navigation';
import { ComponentProps, ForwardRefExoticComponent, FunctionComponent, useEffect } from 'react';

import { IFocusState } from './focus';

interface FocusableContainer<T> extends UseFocusableConfig<T>, IFocusState {
  onKeyPress?: (details: KeyPressDetails, properties: T) => boolean;
  onChildFocusChange?: (hasFocusedChild: boolean) => void;
  focusOnMount?: boolean;
}

const withFocusableContainer = <
  T extends ForwardRefExoticComponent<ComponentProps<T>>,
  ResolvedProperties = JSX.LibraryManagedAttributes<
    T,
    FocusableContainer<T> & Omit<ComponentProps<T>, keyof FocusableContainer<T>>
  >,
>(
  Component: T,
) => {
  const displayName = `FocusableContainer(${Component.displayName || Component.name})`;

  const WrappedComponent: FunctionComponent<ResolvedProperties & FocusableContainer<T>> = ({
    onChildFocusChange,
    focusOnMount = false,
    isDisabled,
    ...rest
  }) => {
    const { ref, focusKey, focusSelf, hasFocusedChild, focused } = useFocusable({
      trackChildren: onChildFocusChange !== undefined,
      ...rest,
    });

    useEffect(() => onChildFocusChange?.(hasFocusedChild), [onChildFocusChange, hasFocusedChild]);

    useEffect(() => {
      if (focusOnMount) {
        focusSelf();
      }
    }, [focusOnMount, focusSelf]);

    return (
      <FocusContext.Provider value={focusKey}>
        <Component
          ref={ref}
          testIdFocused={focused || hasFocusedChild}
          focusKey={focusKey}
          isFocused={focused || hasFocusedChild}
          isDisabled={isDisabled}
          {...(rest as JSX.LibraryManagedAttributes<T, ComponentProps<T>>)}
        />
      </FocusContext.Provider>
    );
  };

  WrappedComponent.displayName = displayName;

  return WrappedComponent;
};

export { withFocusableContainer };

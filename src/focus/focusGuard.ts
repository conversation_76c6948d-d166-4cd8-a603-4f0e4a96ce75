import { UseFocusableResult } from '@noriginmedia/norigin-spatial-navigation';

import { noSearchResultsFound } from '../components/Search/searchUtil';
import { ISearchResults } from '../types';
import { FOCUS_KEY_FIRST_SEARCH_GENRE, FOCUS_KEY_PRIMARY, FOCUS_KEY_SIDE_MENU, tvGuideMenuFocusKey } from './focusKeys';

export type TeaserFocusGuardOptions = ISearchResults;

/**
 * Default movement in all directions. Focus will not get lost upon pressing a non-directional button.
 * @param direction 'up', 'down', 'left' or 'right'
 * @returns true for all directions, false otherwise
 */
export const defaultFocusGuard = (direction: string) => {
  switch (direction) {
    case 'up':
    case 'right':
    case 'down':
    case 'left':
      return true;
    default:
      return false;
  }
};

// FIXME: Find out why our setup of the spatial navigation library does not work out of the box and remove this function
/**
 * Prevents the focus from getting lost when navigating up or left on a teaser
 * @param direction one of 'up', 'down', 'left' or 'right'
 * @param zoneIndex 0-based index of a zone which contains the focused teaser
 * @param teaserIndex 0-based index of the focused teaser
 * @param setFocus a function that can set the focus on a new element
 * @returns false if a move was intercepted, true otherwise
 */
export const teaserFocusGuard = (
  direction: string,
  zoneIndex: number,
  teaserIndex: number,
  setFocus: UseFocusableResult['setFocus'],
) => {
  switch (direction) {
    case 'up':
      switch (zoneIndex) {
        case 0:
          return false;
        default:
          return true;
      }
    case 'left':
      switch (teaserIndex) {
        case 0:
          setFocus(FOCUS_KEY_SIDE_MENU);
          return false;
        default:
          return true;
      }
    case 'down':
    case 'right':
      return true;
    default:
      return false;
  }
};

export const searchTeaserFocusGuard = (
  direction: string,
  zoneIndex: number,
  teaserIndex: number,
  setFocus: UseFocusableResult['setFocus'],
  options: TeaserFocusGuardOptions,
) => {
  switch (direction) {
    case 'up':
      switch (zoneIndex) {
        case 0:
          if (noSearchResultsFound(options)) {
            setFocus(FOCUS_KEY_FIRST_SEARCH_GENRE);
          }
          return false;
        default:
          return true;
      }
    case 'left':
      switch (teaserIndex) {
        case 0:
          setFocus(FOCUS_KEY_PRIMARY);
          return false;
        default:
          return true;
      }
    case 'down':
    case 'right':
      return true;
    default:
      return false;
  }
};

export const tvGuideTeaserFocusGuard = (
  direction: string,
  zoneIndex: number,
  teaserIndex: number,
  setFocus: UseFocusableResult['setFocus'],
) => {
  switch (direction) {
    case 'up':
      return teaserIndex ? true : false;
    case 'left':
      setFocus(tvGuideMenuFocusKey(zoneIndex));
      return false;
    case 'down':
      return true;
    case 'right':
      return false;
    default:
      return false;
  }
};

export const myArteTeaserFocusGuard = (
  direction: string,
  zoneIndex: number,
  teaserIndex: number,
  setFocus: UseFocusableResult['setFocus'],
) => {
  switch (direction) {
    case 'up':
      switch (zoneIndex) {
        case 0:
          setFocus(FOCUS_KEY_PRIMARY);
          return false;
        default:
          return true;
      }
    case 'left':
      switch (teaserIndex) {
        case 0:
          setFocus(FOCUS_KEY_SIDE_MENU);
          return false;
        default:
          return true;
      }
    case 'down':
    case 'right':
      return true;
    default:
      return false;
  }
};

export const livePlayerButtonFocusGuard = (direction: string) => {
  switch (direction) {
    case 'right':
    case 'down':
    case 'left':
      return true;
    default:
      return false;
  }
};

import { getRootElement } from '@util/getRootElement';
import { config } from 'target';
import type { AuthErrorMessage } from 'target/ITargetConfig';

import { AppError } from './AppError';

export function init(): void {
  const { title, description }: AuthErrorMessage = config.authErrorMessage as NonNullable<AuthErrorMessage>;
  const root = getRootElement();
  if (root) root.render(<AppError title={title} description={description} />);
}

import { ZoneTemplate } from '@apptypes/PageResponseBody';

export enum Target {
  TIZEN = 'tizen',
  WEBOS = 'webos',
  HTML5 = 'html5',
  HBBTV = 'hbbtv',
  ORANGE = 'orange',
}

export const QUERY_STRING_TEST_STREAM = 'stream';
export const QUERY_STRING_TEST_STREAM_LIVE = 'live';
export const QUERY_STRING_LOOKUP_LANGUAGE = 'lang';
export const FALLBACK_LANGUAGE = 'en';

export const ROUTES = {
  ROOT: '/',
  PAGE: '/page',
  PROGRAM: '/program',
  COLLECTION: '/collection',
  TV_GUIDE: '/page/tv_guide',
  VERIFICATION: '/verification',
  VIDEO: '/video',
  ERROR: '/errorpage',

  MY_VIDEOS: '/page/my-videos',

  MYARTE: {
    ROOT: '/page/myarte',
    AUTHENTICATE: '/page/myarte/authenticate',
    LOGIN: '/page/myarte/login',
  },

  SETTINGS: {
    ROOT: '/page/settings',
    INTERFACE: '/page/settings/interface',
    PERSONALISATION: '/page/settings/personalisation',
    TUTORIAL: '/page/settings/tutorial',
    PRIVACY: '/page/settings/privacy',
    INFORMATION: '/page/settings/information',
  },
};

export const PROD_HOST = 'smarttv.arte.tv';

export const EVENTS = {
  ERROR: 'error',
  NATIVE_DEEPLINK: 'nativeDeeplink',
  OFFLINE: 'offline',
  SHOW_FULL_SCREEN_SPINNER: 'showFullScreenSpinner',
  APP_TO_FOREGROUND: 'appToForeground',
  APP_TO_BACKGROUND: 'appToBackground',
  PLAYER_BACK: 'playerBack',
  TOAST_ADD: 'toastAdd',
  TOAST_REMOVE: 'toastRemove',
};

export const THEMES: { [key: string]: string } = {
  WHITE: 'theme-white',
  INFO: 'theme-info',
  SHOWEMPTYZONE: 'showEmptyZone',
};

export const PAGE_IDS = {
  SEARCH_HOME: 'search_home',
  HOME: 'home',
  ARTE_CONCERT: 'arte_concert',
  TV_GUIDE: 'tv_guide',
  MYARTE: 'myarte',
  SETTINGS: 'settings',
  QUIT: 'quit',
  MY_VIDEOS: 'my_videos',
  MY_FAVORITES: 'my_favorites',
  MY_HISTORY: 'my_history',
  MY_RESUME: 'my_resume',
} as const;

export type PageId = (typeof PAGE_IDS)[keyof typeof PAGE_IDS];

export type Environment = 'DEV' | 'PREPROD' | 'PROD';

export const ZONE_TEMPLATES = {
  HORIZONTAL_HIGHLIGHTED: 'horizontalSelectedHighlighted-landscape',
  HORIZONTAL_PORTRAIT: 'horizontal-portrait',
  HORIZONTAL_SQUARE: 'horizontal-square',
  VERTICAL_LANDSCAPE: 'vertical-landscape',
  HORIZONTAL_LANDSCAPE: 'horizontal-landscape',
  HORIZONTAL_LANDSCAPE_BIG: 'horizontal-landscapeBig',
  HORIZONTAL_LANDSCAPE_BIG_SUBTITLE: 'horizontal-landscapeBigWithSubtitle',
  EVENT_RIGHT: 'event-textOnRightSide',
  EVENT_LEFT: 'event-textOnLeftSide',
  SINGLE_COLLECTION: 'single-collectionContent',
  SINGLE_PAGE_HEADER: 'single-pageHeader',
  SINGLE_PROGRAM: 'single-programContent',
  TABLEVIEW_GUIDE: 'tableview-guide',
} as const;

export const VALID_ZONE_TEMPLATES = [
  ZONE_TEMPLATES.HORIZONTAL_HIGHLIGHTED,
  ZONE_TEMPLATES.HORIZONTAL_PORTRAIT,
  ZONE_TEMPLATES.HORIZONTAL_SQUARE,
  ZONE_TEMPLATES.VERTICAL_LANDSCAPE,
  ZONE_TEMPLATES.HORIZONTAL_LANDSCAPE,
  ZONE_TEMPLATES.HORIZONTAL_LANDSCAPE_BIG,
  ZONE_TEMPLATES.HORIZONTAL_LANDSCAPE_BIG_SUBTITLE,
  ZONE_TEMPLATES.EVENT_RIGHT,
  ZONE_TEMPLATES.EVENT_LEFT,
  ZONE_TEMPLATES.SINGLE_COLLECTION,
  ZONE_TEMPLATES.SINGLE_PAGE_HEADER,
  ZONE_TEMPLATES.SINGLE_PROGRAM,
  ZONE_TEMPLATES.TABLEVIEW_GUIDE,
];

export const SINGLE_TEASER_TEMPLATES: Partial<ZoneTemplate[]> = [
  ZONE_TEMPLATES.HORIZONTAL_HIGHLIGHTED,
  ZONE_TEMPLATES.SINGLE_COLLECTION,
  ZONE_TEMPLATES.SINGLE_PROGRAM,
  ZONE_TEMPLATES.EVENT_RIGHT,
  ZONE_TEMPLATES.EVENT_LEFT,
];

export const TEST_ID = {
  PLAYER: {
    FAVOURITE: 'player-favourite-button',
    NEXT: 'player-next-button',
    BACK: 'player-back-button',
    INFO: 'player-info-button',
    INFO_MODAL: 'player-info-modal',
    PLAY_PAUSE: 'player-play-pause-button',
    PREV: 'player-prev-button',
    VERSION: 'player-version-button',
    CURRENT_TIME: 'player-current-time',
    DURATION: 'player-duration',
    PROGRESS_BAR: 'player-progress-bar',
    MENU: 'player-menu',
  },
  EXIT: {
    MODAL: 'exit-modal',
    ACCEPT: 'exit-modal-accept-button',
    REJECT: 'exit-modal-reject-button',
  },
  COOKIE: {
    VIEW: 'cookie-view',
    ACCEPT: 'cookie-accept-button',
    REJECT: 'cookie-reject-button',
    MODIFY: 'cookie-modify-button',
  },
  AUTH: {
    LOGIN: 'login-button',
    LOGOUT: 'logout-button',
  },
  PAGES: {
    ERROR: 'error-page',
  },
  KEYBOARD: {
    BACKSPACE: 'keyboard-backspace-button',
  },
  MODAL: {
    TITLE: 'modal-title',
    DESRIPTION: 'modal-description',
  },
  VERIFICATION: {
    AGE_WARNING: 'verification-age-warning',
    AGE_WARNING_16: 'verification-age-warning-16',
  },
  SEARCH: {
    QUERY: 'search-query',
    NO_RESULTS: 'search-no-results',
    RESULTS: 'search-results',
  },
  MAIN_NAVIGATION: 'main-nav',
  TOP_TEASER_PLAY: 'top-teaser-play-button',
};

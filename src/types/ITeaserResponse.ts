import { AvailabilityCore } from './ITeaserProperties';
import { ZoneTemplate } from './PageResponseBody';

interface ISticker {
  code?: string;
  label?: string;
}

type AudioVersionCode = 'AD' | 'STM' | 'VF' | 'OV' | 'VO' | 'UT' | 'ST' | 'DE';

interface Kind {
  code?: string;
  label?: string | null;
  isCollection?: boolean;
}

interface AudioVersion {
  code: AudioVersionCode;
  label: string;
}

interface ITeaserResponse {
  id: string;
  audioVersions: AudioVersion[];
  callToAction: string;
  clip: string;
  deeplink?: string;
  description: string;
  fullDescription: string;
  duration: string;
  image: string;
  isCollection: boolean;
  item_id: string;
  program_id: string;
  showItemTitle: boolean;
  stickers: ISticker[];
  subtitle: string;
  template: ZoneTemplate;
  title: string;
  trailer: string;
  type: string;
  playable?: boolean;
  availability?: AvailabilityCore | null;
  placeholderMode?: boolean;
  trackingPixel?: string;
  kind?: Kind;
  code?: string;
}
export type { AudioVersionCode, ITeaserResponse, ISticker, AudioVersion };

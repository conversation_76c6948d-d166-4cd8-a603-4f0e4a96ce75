import { ZoneLink } from '@apptypes/PageResponseBody';
import { TeaserListProperties } from '@components/TeaserList/TeaserList';
import { UseFocusableConfig, UseFocusableResult } from '@noriginmedia/norigin-spatial-navigation';
import { MouseEventHandler } from 'react';

import { IFocusState } from '../focus';
import { ITeaserResponse } from './ITeaserResponse';

export type Availability = {
  label: string;
  upcomingDate: string;
  type: string;
  start: string;
  end: string;
  remainingDays: number;
};

export type AvailabilityCore = {
  start: string;
  end: string;
  type?: string;
  hasVideoStreams?: boolean;
  broadcastBegin?: string;
  displayDate?: string;
  availabilityRights?: Availability;
  videoId?: string;
};

export interface ITeaserProperties
  extends ITeaserResponse,
    IFocusState,
    Pick<TeaserListProperties, 'template' | 'zoneIndex' | 'lastZone' | 'authenticatedContent'>,
    Pick<UseFocusableConfig, 'focusKey' | 'onFocus' | 'onEnterPress' | 'onArrowPress'> {
  availability: Availability | null;
  /**
   * 0-based horizontal position
   */
  position: number;
  setFocus: UseFocusableResult['setFocus'];
  className?: string;
  onMouseClick?: MouseEventHandler<HTMLAnchorElement> | undefined;
  trailer: string;
  clip: string;
  label?: string;
  watchOrResumeLabel: string;
  viewedProgress: number;
  imageWidth?: string;
  imageHeight?: string;
  landscapeImage?: string;
  landscapeImageWidth?: string;
  landscapeImageHeight?: string;
  placeholderMode?: boolean;
  isLiveWebTemplate?: boolean;
  isLivestreamWebTemplate?: boolean;
  broadcastDates: string[];
  theme?: string;
  fixedHeight?: boolean;
  link?: ZoneLink;
  emptyState?: boolean;
  testIdFocused?: boolean;
  genre?: Genre;
  shouldBlockExtraKeys?: boolean;
  dataKind?: string;
}

export interface Genre {
  genreName: string;
  deeplink: string;
  label: string;
}

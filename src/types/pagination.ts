import { ITeaserResponse } from './ITeaserResponse';
import { ZoneTemplate } from './PageResponseBody';

export interface IInitialResults {
  results: ITeaserResponse[];
  showItemTitle: boolean;
  showZoneTitle?: boolean;
  template?: ZoneTemplate;
  title?: string;
  pages?: number;
  id: string;
}

export interface IPageResults {
  data: ITeaserResponse[];
}

export const DefaultInitialResults: IInitialResults = {
  results: [],
  id: '',
  showItemTitle: false,
};
export const DefaultPageResults: IPageResults = { data: [] };

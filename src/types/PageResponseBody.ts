import { VALID_ZONE_TEMPLATES } from '@constants';

import { ITeaserResponse } from './ITeaserResponse';
import { Me, MeData, UserData } from './SSOResponse';
import { UserContentType } from './UserContentType';

type ZoneTemplate = (typeof VALID_ZONE_TEMPLATES)[number];

type Zone = {
  horizontalIndex: number;
  verticalIndex: number;
  authenticatedContent?: UserContentType;
  id: string;
  showItemTitle: boolean;
  showZoneTitle: boolean;
  teaser_count: number;
  // hydrated in a separate api call start
  teaserList: ITeaserResponse[];
  meta: Me;
  // hydrated in a separate api call end
  template: ZoneTemplate;
  title: string;
  theme?: string;
  link?: ZoneLink;
  pages: number;
  code?: string;
};

type ZoneLink = {
  page: string;
  title: string;
  deeplink: string;
};

type serverSideTrackingPageType = {
  abv: string;
  category?: string;
  id: string;
  language: string;
  query?: string;
  subcategories?: string;
  url: string;
};
type serverSideTrackingContent = {
  category?: string;
  id?: string;
  kind?: string;
  slug?: string;
  subcategory?: string;
};

type serverSideTrackingType = {
  content?: serverSideTrackingContent;
  page: serverSideTrackingPageType;
};

type StatsType = {
  serverSideTracking: serverSideTrackingType;
};

type PageResponseBody = {
  type: string;
  title: string;
  description: string;
  id: string;
  zones: Zone[];
  message?: string;
  crc?: string;
  stats?: StatsType;
  meData: MeData;
  userData?: UserData;
};

export type { PageResponseBody };
export type { Zone };
export type { ZoneTemplate };
export type { serverSideTrackingType };
export type { ZoneLink };

export type PlayListResponseBody = {
  data: PlayListData;
};

export type PlayListData = {
  id: string;
  type: string;
  attributes: Attributes;
};

export type Attributes = {
  provider: string;
  metadata: Metadata;
  items: Item[];
};

export type Item = {
  providerId: string;
  title: string;
  subtitle: null;
  images: Image[];
  link: Link;
  config: Config;
  duration: Duration;
  current: boolean;
  live: boolean;
  beginRounded: null;
  description: string;
};

export type Config = {
  url: string;
  replay: string;
  playlist: string;
};

export type Duration = {
  seconds: number;
};

export type Image = {
  caption: null;
  url: string;
};

export type Link = {
  url: string;
  deeplink: string;
  videoOnDemand: null;
};

export type Metadata = {
  providerId: string;
  language: string;
  title: string;
  subtitle: null;
  description: string;
  images: Image[];
  link: Link;
  config: Config;
  duration: Duration;
  episodic: boolean;
};

import { Genre, ITeaserProperties } from './ITeaserProperties';
import { ISticker } from './ITeaserResponse';
import { serverSideTrackingType } from './PageResponseBody';

export interface ITvGuideTeaserProperties extends ITeaserProperties {
  data: ITvGuideDayData[];
  stats: {
    serverSideTracking: serverSideTrackingType;
  };
}

export interface ITvGuideDayData {
  start: string;
  genre: Genre;
  live?: ISticker;
  prime?: boolean;
  information: string;
}

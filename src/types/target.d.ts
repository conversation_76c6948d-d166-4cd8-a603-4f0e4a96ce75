declare module 'target' {
  export function getKeyMap(): { [index: string]: number | number[] };
  export const updateKeys: () => void;
  export const createRouter: (
    routes: import('react-router-dom').RouteObject[],
    opts?: {
      basename?: string;
    },
  ) => import('@remix-run/router').Router;
  export const getSearchParams: () => URLSearchParams;
  export const confirmExitOnBack: () => boolean;
  export const exitApp: () => void;
  export const isExitEnabled: () => boolean;
  export const addOnlineOfflineListener: () => boolean;
  export const isReady: () => boolean;
  export const reloadApp: (relaunchQueryParams?: string) => void;
  export const showMessageOnReload: () => boolean;
  export const onLanguageDetected: () => void;
  export const hasColorButtonSupport: () => boolean;
  export const thirdPartyAuth: {
    auth: () => Promise<boolean | undefined>;
    enabled: boolean;
  };
  export const config: import('target/ITargetConfig').ITargetConfig;
  export const normalizeParams: (
    params: Readonly<import('react-router-dom').Params>,
  ) => Readonly<import('react-router-dom').Params>;
}

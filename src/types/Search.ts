import { ITeaserResponse } from './ITeaserResponse';
import { Zone } from './PageResponseBody';
import { DefaultInitialResults, IInitialResults } from './pagination';

export interface ISearchResponse {
  zones: SearchZone[];
}

export type SearchZone = Zone & {
  items: ITeaserResponse[];
};

export interface ISearchResults extends IInitialResults {
  genre: IGenre;
  query: string;
}

export const enum GenreType {
  Documentaries = 1, // for convenience, numbers match the genre param in the search request
  Movies,
  TvSeries,
  TvShows,
  Concerts,
}

export interface IGenre {
  value?: GenreType;
  translationKey: string;
}

export const DefaultGenre: IGenre = { translationKey: 'all', value: undefined };
export const DefaultSearchResults: ISearchResults = {
  ...DefaultInitialResults,
  query: '',
  genre: DefaultGenre,
};

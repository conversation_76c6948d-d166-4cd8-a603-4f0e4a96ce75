// https://github.com/ArteGEIE/API-Player/blob/develop/doc/v2/config.md

import { Availability } from '@apptypes/ITeaserProperties';

import { TrackingPlayerContextType } from './../tracking/types';
import { ISticker } from './ITeaserResponse';

type Duration = {
  seconds: number;
};

type Image = {
  caption: string;
  url: string;
};

type Segment = {
  type: 'SUMMARY' | 'OPENING_CREDITS' | 'END_CREDITS';
  begin: number;
  end: number;
};

type Version = {
  label: string;
  shortLabel: string;
  eStat: { ml5: string };
};

type Quality = {
  code: string;
  label: string;
};

export interface AgfType {
  type: string;
  assetid: string;
  program: string;
  title: string;
  length: number;
  nol_c0: string;
  nol_c2: string;
  nol_c5: string;
  nol_c7: string;
  nol_c9: string;
  nol_c10: string;
  nol_c12: string;
  nol_c15: string;
  nol_c18: string;
}

export type Stat = {
  serverSideTracking: TrackingPlayerContextType;
  agf?: AgfType;
  eStat?: Record<string, unknown>;
  push?: Record<string, unknown>;
  mediametrie?: Record<string, unknown>;
  arte?: Record<string, unknown>;
  // add other known properties as needed
};

export type Metadata = {
  providerId: string;
  language: string;
  title: string;
  subtitle: string;
  description: string;
  images: Image[];
  duration: Duration;
  durationReplay: Duration;
  episodic: boolean;
};

export enum StreamProtocolEnum {
  DASH = 'DASH',
  API_DASH_MA = 'API_DASH_MA',
  DASH_MA = 'DASH_MA',
  DASH_UHD = 'DASH_UHD',
  DASH_CUHD = 'DASH_CUHD',
}

export type StreamProtocol = keyof typeof StreamProtocolEnum;

export type Stream = {
  url: string;
  versions: Version[];
  mainQuality: Quality;
  slot: number;
  protocol: StreamProtocol;
  segments: Segment[];
};

export type Warning = {
  code: 'WARNING_AGE_RESTRICTION_12' | 'WARNING_AGE_RESTRICTION_16' | 'WARNING_AGE_RESTRICTION_18';
  title: string;
  message: string;
};

export type Error = {
  code: 'ERROR_AGE_RESTRICTION_16' | 'ERROR_AGE_RESTRICTION_18';
  title: string;
  message: string;
};

export type AvailabilityRights = {
  begin: string;
  end: string;
};

type Geoblocking = {
  userCountryCode: string;
};

type Restriction = {
  geoblocking: Geoblocking;
};

export type Attributes = {
  provider: string;
  metadata: Metadata;
  live: boolean;
  streams: Stream[];
  autoplay: boolean;
  warnings: Warning[];
  error: Error;
  stickers: ISticker[];
  stat: Stat;
  rights: AvailabilityRights;
  availability: Availability;
  isLiveWebTemplate?: boolean;
  isLivestreamWebTemplate?: boolean;
  restriction: Restriction;
};

export type VideoData = {
  id: string;
  attributes: Attributes;
};

export type VideoResponseBody = {
  data: VideoData;
};

import { CustomError } from '@errors/CustomError';

import { Me, MeData, NextEpisode } from './SSOResponse';

export interface IUserHistory {
  proxy: boolean;
  debug: boolean;
  getAnonymousToken: () => Promise<void>;
  getMe: () => Promise<Me>;
  getMeData: () => Promise<MeData>;
  getNextEpisode: (programId: string) => Promise<NextEpisode>;
  addToFavourites: (programId: string) => Promise<Response>;
  removeFromFavourites: (programId: string) => Promise<Response>;
  clearHistory: () => void;
  history: History[];
  expireCookie: number;
  requestUserToken: (code: string) => Promise<Response>;
  setUserHistory: (programId: string, position: number, duration: number, failback: (e: CustomError) => void) => void;
  url: URL;
}

export interface History {
  programId: string;
  lastviewed: Lastviewed;
}

export interface Lastviewed {
  timecode: number;
  progress: number;
}

export interface URL {
  favorites: string;
  magazine: string;
  clearHistory: string;
  history: string;
  history_proxy: string;
  personalzone: string;
  nextEpisode: string;
  nextEpisode_proxy: string;
  meData: string;
  meData_proxy: string;
  me: string;
}

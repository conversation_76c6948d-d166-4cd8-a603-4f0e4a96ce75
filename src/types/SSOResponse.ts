export type MeData = {
  meta: MeDataMeta;
  favorites: unknown[];
  subscriptions: unknown[];
  lastvieweds: MeDataLastViewed[];
  nextEpisodes: MeDataNextEpisode[];
};

export type UserData = {
  email?: string;
  avatar: Avatar;
};

export type FeatureFlagsApiResponse = {
  type: string;
  version: string;
  'feature-flags': FeatureFlags;
  crc: number;
};

export type FeatureFlags = {
  [feature: string]: FeatureFlag;
};

export type FeatureFlag = {
  DEV: boolean;
  LOCAL: boolean;
  PREPROD: boolean;
  PROD: boolean;
  blocked_devices?: string[];
};

type Avatar = {
  avatarId?: string;
  images?: AvatarImage[];
};

type AvatarImage = {
  url: string;
  w: string;
  h: string;
};

export type MeDataLastViewed = {
  programId: string;
  timecode: number;
  progress: number;
  updatedAt: Date;
};

export type MeDataMeta = {
  updatedAt: string;
};

export type MeDataNextEpisode = {
  seriesId: string;
  seasonId: string;
  nextEpisodeId: string;
  season: number;
  episode: number;
  totalEpisodes: number;
  seasonProgress: number;
};

export type NextEpisode = {
  meta: NextEpisodeMeta;
  data: NextEpisodeData[];
};

export type NextEpisodeData = {
  type: string;
  id: string;
  kind: string;
  programId: string;
  language: string;
  url: string;
  title: string;
  subtitle: string;
  images: NextEpisodeImage[];
  markings: unknown[];
  geoblocking: string;
  warning: null;
  description: string;
  shortDescription: string;
  beginsAt: Date;
  expireAt: Date;
  availability: null;
  duration: number;
  durationSeconds: number;
  video_url: string;
  player: NextEpisodePlayer;
  playable: boolean;
  stickers: unknown[];
  durationLabel: string;
  season: number;
  episode: number;
  totalEpisodes: number;
  episodeLabel: string;
  available: boolean;
  trackingPixel: string;
  favorite: null;
  lastviewed: NextEpisodeLastViewed;
};

export type NextEpisodeImage = {
  url: string;
  format: string;
  width: number;
  height: number;
  alternateResolutions: unknown[];
};

export type NextEpisodeLastViewed = {
  is: boolean;
  timecode: number;
  progress: number;
};

export type NextEpisodePlayer = {
  config: string;
};

export type NextEpisodeMeta = {
  totalCount: number;
  page: number;
  pages: number;
  limit: number;
  saveInterval: number;
};

export type AuthToken = {
  access_token: string;
  expires_in: Date;
  refresh_expires_in: Date;
  refresh_token: string;
  token_type: string;
  'not-before-policy': number;
  session_state: string;
  scope: string;
};

export type Me = {
  meta: MeMeta;
  data: MeDatum[];
};

export type MeDatum = {
  ageVerificationToken: string | null;
  uid: string;
};

export type MeMeta = {
  totalCount: number;
  page: number;
  pages: number;
};

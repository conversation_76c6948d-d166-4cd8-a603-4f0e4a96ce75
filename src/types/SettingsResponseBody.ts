interface SettingsResponseBody {
  settingsMenuItems: string[];
  routes: string[];
}

interface SettingsPage {
  title: string;
}

interface InformationSettingsPage extends SettingsPage {
  version: string;
  application: string;
  titles: InformationTitle[];
}

interface InformationTitlesResponse {
  titles: InformationTitle[];
  type: string;
  message?: string;
}

interface InformationTitle {
  txt: string;
  url?: string;
}

export type {
  SettingsResponseBody,
  SettingsPage,
  InformationSettingsPage,
  InformationTitlesResponse,
  InformationTitle,
};

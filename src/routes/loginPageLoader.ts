import i18n from '../i18n';

const loginPageLoader = async () => {
  return {
    title: i18n.t('login_page1_BrandName'),
    subtitle: i18n.t('login_page1_Description'),
    connect_message: i18n.t('login_page1_AlreadyAMember'),
    connect_button: i18n.t('login'),
    qrCodeTitle: i18n.t('login_page1_NotAMemberYet'),
    qrCodeMessage: i18n.t('login_page1_signin_description'),
  };
};

export { loginPageLoader };

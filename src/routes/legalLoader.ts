import { middlewareUrl } from '@util/url';

import { ApiRequestFailError, failOnInformationPageResponseError } from '../errors';
import { LegalModalResponseBody } from '../types/LegalModalResponseBody';

const legalLoader = async (uri: string): Promise<LegalModalResponseBody> => {
  const url = middlewareUrl(`/${uri}`);
  const response = await fetch(url).catch(() => {
    throw new ApiRequestFailError(url);
  });
  const data = await response.json();
  failOnInformationPageResponseError(data);
  return data as LegalModalResponseBody;
};

export { legalLoader };

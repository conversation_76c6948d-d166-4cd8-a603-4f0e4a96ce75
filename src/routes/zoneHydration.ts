import { PAGE_IDS, ZONE_TEMPLATES } from '@constants';
import { THEMES } from '@constants';
import { SsoRequestForbiddenError } from '@errors/SsoRequestForbiddenError';
import { SsoRequestUnauthorizedError } from '@errors/SsoRequestUnauthorizedError';
import { anonPersonalisationAllowed } from '@util/anonPersonalisationAllowed';
import { isLoggedIn } from '@util/cookies';
import { isCollection } from '@util/isCollection';
import { getLastViewedData } from '@util/meData';
import { getStoredZoneTeasers } from '@util/NavHistory';
import { middlewareUrl } from '@util/url';
import i18n from 'i18next';

import { ApiRequestFailError, logError, MyArtePageMissingContentError } from '../errors';
import { getNextEpisode, getUserContent } from '../features/usercontent/userContentData';
import {
  ITeaserProperties,
  ITeaserResponse,
  ITvGuideDayData,
  ITvGuideTeaserProperties,
  PageResponseBody,
  Zone,
} from '../types';
import { Me, MeData } from '../types/SSOResponse';
import { MY_ARTE_PAGE_ZONE_IDS } from './myArtePageLoader';

type Hydrate = (zone: Zone) => Promise<undefined | Zone>;

function canReturnEmptyZone(page?: PageResponseBody, zone?: Zone): boolean {
  if (zone?.theme === THEMES.SHOWEMPTYZONE) {
    return true;
  }

  switch (page?.id) {
    case PAGE_IDS.MY_FAVORITES:
    case PAGE_IDS.MY_HISTORY:
    case PAGE_IDS.MY_RESUME:
      return true;
    default:
      return false;
  }
}

function getWatchOrResumeLabel(progress: number): string {
  return progress > 0.05 && progress < 0.95 ? i18n.t('continueWatching') : i18n.t('watch');
}

function initMetadata(): Partial<ITeaserProperties> {
  const metadata: Partial<ITeaserProperties> = {};
  metadata.watchOrResumeLabel = i18n.t('watch');
  return metadata;
}

async function getWatchOrResumeMetadataFromNextEpisode(item: ITeaserResponse | ITvGuideTeaserProperties) {
  const metadata = initMetadata();

  if (item.template === 'horizontalSelectedHighlighted-landscape') {
    // if this component has a collection item we just show the 'Watch' label
    return metadata;
  }

  let nextEpisode;
  try {
    nextEpisode = await getNextEpisode(isCollection(item) ? item.item_id : item.program_id).catch((reason) => {
      throw reason;
    });
  } catch (error) {
    switch (true) {
      case error instanceof SsoRequestUnauthorizedError:
        throw error;
      default:
        logError(error, 'WARNING');
    }
  }

  if (!nextEpisode) return metadata;

  if (!nextEpisode.data[0]) {
    logError(new Error('[zonehydration] No data for ' + item.item_id), 'WARNING');
    return metadata;
  }

  const { episode, totalEpisodes, programId, lastviewed } = nextEpisode.data[0];
  const { progress } = lastviewed;
  const labelPrefix = getWatchOrResumeLabel(progress);
  const episodeCount = episode && totalEpisodes ? `(${episode}/${totalEpisodes})` : null;
  metadata.watchOrResumeLabel = episodeCount ? `${labelPrefix} (${episode}/${totalEpisodes})` : labelPrefix;
  metadata.program_id = programId;
  return metadata;
}

async function getWatchOrResumeMetadataFromMeData(item: ITeaserResponse, meData: MeData) {
  const metadata = initMetadata();

  if (!meData || !anonPersonalisationAllowed()) return metadata;

  const lastViewedData = getLastViewedData(item.program_id, meData.lastvieweds);
  metadata.watchOrResumeLabel = getWatchOrResumeLabel(lastViewedData?.progress);
  return metadata;
}

async function getWatchOrResumeMetadata(item: ITeaserResponse, meData: MeData) {
  if (isCollection(item)) return await getWatchOrResumeMetadataFromNextEpisode(item);
  return await getWatchOrResumeMetadataFromMeData(item, meData);
}

export const addTeaserProgress = (
  teasers: ITeaserProperties[] | ITvGuideTeaserProperties[] | ITeaserResponse[],
  meData: MeData,
) => {
  let lastviewedsMap: Map<string, number>;

  if (meData && meData.lastvieweds) {
    lastviewedsMap = new Map(meData.lastvieweds.map((item) => [item.programId, item.progress]));
  }
  let viewedProgress: number | undefined = 0;
  return teasers.map((item: ITeaserResponse | ITvGuideTeaserProperties) => {
    viewedProgress = 0;
    if ((isLoggedIn() || anonPersonalisationAllowed()) && lastviewedsMap && lastviewedsMap.has(item.program_id)) {
      viewedProgress = lastviewedsMap.get(item.program_id);
    }
    return { ...item, viewedProgress };
  });
};

const addZoneData = (zone: Zone) => {
  return zone.teaserList.map((item: ITeaserResponse) => {
    return { ...item, template: zone.template, showItemTitle: zone.showItemTitle, code: zone.code };
  });
};

const getHydratePromise = (zone: Zone) => {
  if (zone.authenticatedContent) {
    // Return undefined to indicate there's nothing to hydrate in case personalisation is disabled
    // A call to a personal zone with personalization disabled would return 401
    return anonPersonalisationAllowed() ? hydratePersonalZone : undefined;
  }
  return hydrateCuratedZone;
};

export const hydrateZone = async (page?: PageResponseBody, zone: Zone, meData: MeData) => {
  const hydrateZone: Hydrate = getHydratePromise(zone);

  if (!hydrateZone) {
    return undefined;
  }

  const zoneResponse = await hydrateZone(zone);

  if (!zoneResponse.data?.length) {
    // no teasers in the response
    if (canReturnEmptyZone(page, zone)) {
      // allow showing an empty zone for this theme
      zone.teaserList = [];
      return zone;
    } else {
      return undefined;
    }
  }

  // restore paginated teasers so we don't have to load them again
  const storedTeasers = getStoredZoneTeasers(page?.id, zone.id);
  zone.teaserList = storedTeasers ? [...storedTeasers] : zoneResponse.data;

  zone.teaserList = addTeaserProgress(zone.teaserList, meData);
  zone.teaserList = addZoneData(zone);

  switch (zone.template) {
    case ZONE_TEMPLATES.EVENT_RIGHT:
    case ZONE_TEMPLATES.EVENT_LEFT:
    case ZONE_TEMPLATES.SINGLE_COLLECTION:
    case ZONE_TEMPLATES.SINGLE_PROGRAM:
    case ZONE_TEMPLATES.HORIZONTAL_HIGHLIGHTED:
      const watchOrResumeMetadata = await getWatchOrResumeMetadata(zone.teaserList[0], meData);
      zone.teaserList[0] = { ...zone.teaserList[0], ...watchOrResumeMetadata };
      break;
  }

  if (!zone.meta) {
    zone.meta = zoneResponse?.meta as Me;
  }

  return zone;
};

export const hydrateTvGuideDay = async (tvGuideDay: ITvGuideDayData, meData: MeData) => {
  for (let i = 0; i < tvGuideDay.data.length; i++) {
    const watchOrResumeMetadata = await getWatchOrResumeMetadata(tvGuideDay.data[i], meData);
    tvGuideDay.data[i] = { ...tvGuideDay.data[i], ...watchOrResumeMetadata };
  }
  return {
    data: [...addTeaserProgress(tvGuideDay.data, meData)],
    stats: tvGuideDay?.stats,
    code: tvGuideDay?.code,
  };
};

const hydrateCuratedZone = async (zone: Zone) => {
  const url = middlewareUrl(`/zones/${zone.id}`);
  return zone.teaser_count ? fetch(url).then((response) => response.json()) : undefined;
};

const hydratePersonalZone = async (zone: Zone) =>
  getUserContent(zone.authenticatedContent).catch((reason) => {
    switch (true) {
      case reason instanceof SsoRequestForbiddenError:
        logError(reason, 'WARNING');
        break;
      case reason instanceof SsoRequestUnauthorizedError:
        return new Promise((resolve, reject) => {
          reject(reason);
        });
      case MY_ARTE_PAGE_ZONE_IDS.includes(zone.id):
        return new Promise((resolve, reject) => {
          reject(new MyArtePageMissingContentError());
        });
      default:
        logError(new ApiRequestFailError('Loading a personal zone failed.'), 'WARNING');
    }
  });

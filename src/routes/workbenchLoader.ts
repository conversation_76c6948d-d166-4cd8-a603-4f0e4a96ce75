import { middlewareUrl } from '@util/url';

import { ApiRequestFailError } from '../errors';

export interface WorkbenchResponse {
  workbench: {
    [target: string]: {
      urls: WorkbenchURL[];
    };
  };
}

export interface WorkbenchURL {
  title: string;
  url: string;
}

export async function workbenchLoader() {
  const url = middlewareUrl('/workbench', { omitLang: true });
  const response = await fetch(url).catch(() => {
    throw new ApiRequestFailError(url);
  });
  const data: WorkbenchResponse = await response.json();
  const target = process.env.TARGET || '';
  return data?.workbench[target]?.urls || [];
}

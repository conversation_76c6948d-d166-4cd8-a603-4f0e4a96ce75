import { Player } from '@components/Player/Player';
import React from 'react';
import { useLoaderData } from 'react-router-dom';

/**
 * A HOC that forces a component to get unmounted and mounted again upon a loader response change
 * @param WrappedComponent component to unmount and mount again
 * @param loaderKey name of the key in the loader response data to check if a value was changed
 * @returns WrappedComponent component with a react key
 */
function withForcedMount<T>(WrappedComponent: React.ComponentType<T>, loaderKey: string) {
  const displayName = WrappedComponent.displayName || WrappedComponent.name || 'Component';

  const ForceMountedComponent = (props: T) => {
    // get loader data
    // TODO strongly type
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const loaderData = useLoaderData() as any;
    // get a new value based on loaderKey and use it as a react key
    // the component will be unmounted and mounted again every time loaderData[loaderKey] is changed
    const keyDefiningValue = loaderData[loaderKey];

    return <WrappedComponent key={keyDefiningValue} {...props} />;
  };

  ForceMountedComponent.displayName = `withForcedMount(${displayName})`;

  return ForceMountedComponent;
}

export const ForceMountedPlayer = withForcedMount(Player, 'videoId');

import { buildPageId } from '@util/buildPageId';
import { userHistoryScheduler } from '@util/userHistoryScheduler';
import { Params } from 'react-router-dom';

import { CustomError, logError, SsoRequestUnauthorizedError } from '../errors';
import { getMeData } from '../features/usercontent/userContentData';
import { PageResponseBody, Zone } from '../types';
import { MeData } from '../types/SSOResponse';
import { hydrateZone } from './zoneHydration';

export const loadMeData = async () => {
  let meData = await getMeData().catch((reason) => {
    switch (true) {
      case reason instanceof SsoRequestUnauthorizedError:
        throw reason;
      default:
        logError(reason, 'WARNING');
    }
  });
  const modifiedMeData = userHistoryScheduler.feedIntoLastViewed(meData);
  if (modifiedMeData) {
    meData = modifiedMeData;
  }
  return meData as MeData;
};

const hydrateZones = async (page: PageResponseBody | null, meData: MeData) => {
  if (!page.zones || !page.zones.length) return [];

  const hydratedZonePromises = await Promise.allSettled(page.zones.map((zone) => hydrateZone(page, zone, meData)));

  hydratedZonePromises.forEach((promise) => {
    if (promise.status === 'rejected' && promise.reason instanceof CustomError) {
      throw promise.reason;
    }
  });

  return hydratedZonePromises
    .filter((promise): promise is PromiseFulfilledResult<Zone> => promise.status === 'fulfilled' && promise.value)
    .map((result) => result.value);
};

export const hydratePage = async (page: PageResponseBody, params: Params<string>) => {
  const meData = await loadMeData();
  meData?.lastvieweds && userHistoryScheduler.startQueueInterval(true, meData?.lastvieweds);
  const pageWithId = { ...page, id: buildPageId(params, page?.zones) };

  return {
    ...pageWithId,
    zones: await hydrateZones(pageWithId, meData),
    meData: meData,
  };
};

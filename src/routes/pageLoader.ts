import { ContentPageData } from '@apptypes/ContentPageData';
import { PAGE_IDS, PageId } from '@constants';
import { hasFeatureFlag } from '@featureflags/featureFlags';
import { Tracking } from '@tracking/Tracking';
import { buildPageId } from '@util/buildPageId';
import { middlewareUrl } from '@util/url';
import { LoaderFunctionArgs } from 'react-router-dom';

import { failOnPageResponseError } from '../errors';
import { PageResponseBody } from '../types';
import { fetchCachedData, fetchUrl, getCachedResponse, storeCachedResponse } from '../util/CacheHistory';
import { hydratePage } from './pageHydration';

// FIXME
//  note we filter zones here in order to test feature flags by only displaying
//  event teasers for a given target and environment
//  this should be removed once testing is complete
function filterZones(hydratedPage: PageResponseBody) {
  const showEventTeaser = hasFeatureFlag('SMARTTV_FEATURE_FLAGS_EVENT_TEASER');
  return {
    ...hydratedPage,
    zones: hydratedPage.zones.filter((zone) =>
      zone.template === 'event-textOnRightSide' || zone.template === 'event-textOnLeftSide' ? showEventTeaser : true,
    ),
  };
}

function shouldHaveTitle(type: PageResponseBody['type'], id: PageId) {
  switch (type) {
    case 'collection':
    case 'program':
      return false;
    default:
      return id !== PAGE_IDS.HOME && id !== PAGE_IDS.SEARCH_HOME && id !== PAGE_IDS.MYARTE;
  }
}

const pageLoader = async ({ params }: LoaderFunctionArgs): Promise<ContentPageData> => {
  const pageId = buildPageId(params);
  const url = middlewareUrl(`/skeletons/pages/${pageId}`);

  const cachedResponse = getCachedResponse(url);
  let dehydratedPage: PageResponseBody | null = cachedResponse ? await fetchCachedData(cachedResponse) : null;

  if (!dehydratedPage) {
    dehydratedPage = await fetchUrl(url);
    failOnPageResponseError(dehydratedPage);
    storeCachedResponse(url, dehydratedPage);
  }

  const hydratedPage = await hydratePage(dehydratedPage, params);
  const filtered = filterZones(hydratedPage);
  const showTitle = shouldHaveTitle(hydratedPage.type, hydratedPage.id as PageId);

  Tracking.trackPageView(hydratedPage);

  return { showTitle, ...filtered };
};

export { pageLoader };

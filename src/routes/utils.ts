import { getPlayList } from '../data';
import { ApiRequestFailError, logError } from '../errors';
import { PlayListData, PlayListResponseBody } from '../types/PlayListResponseBody';
import { ApiEnv, getOverriddenApiEnv } from '../util/apiOverride';

function getSubdomainForEnv(env: ApiEnv) {
  switch (env) {
    case 'dev':
      return 'smarttv-dev';
    case 'preprod':
      return 'smarttv-preprod';
    case 'prod':
      return 'smarttv';
  }
}

type ApiType = 'middleware' | 'player';

export function getBaseUrl(type: ApiType) {
  const apiDefinedInEnv = type === 'middleware' ? process.env.API_BASE_URL : process.env.PLAYER_API_BASE_URL;
  const apiParam = type === 'middleware' ? 'tvmid' : 'playerapi';

  if (!apiDefinedInEnv) {
    throw new Error(`Api for ${type} not defined in .env`);
  }

  const overriddenEnv = getOverriddenApiEnv(apiParam);
  if (!overriddenEnv) return apiDefinedInEnv;

  const subDomain = getSubdomainForEnv(overriddenEnv);
  return apiDefinedInEnv.replace(/^(http?:\/\/)?(smarttv(-dev|-preprod)?)/, `$1${subDomain}`);
}

export const getPlaylistData = async (videoId: string) => {
  let playlistData;
  try {
    const playlistResponse: PlayListResponseBody = await getPlayList(videoId);
    playlistData = playlistResponse.data as PlayListData;
  } catch (e) {
    logError(new Error('cannot load playlist'), 'WARNING');
    return null;
  }

  return playlistData;
};

export const fetchWithRetry = async (
  url: string,
  pRetried: boolean = false,
  requestInit?: RequestInit,
): Promise<Response> => {
  try {
    const response = await fetch(url, requestInit);

    if (!response.ok) {
      if (response.status >= 500 && response.status < 600 && !pRetried) {
        return await fetchWithRetry(url, true, requestInit);
      } else {
        throw new ApiRequestFailError(url);
      }
    }

    return response;
  } catch (error) {
    throw new ApiRequestFailError(url);
  }
};

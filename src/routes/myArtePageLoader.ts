import { ZONE_TEMPLATES } from '@constants';
import { hydratePage } from '@routes/pageHydration';
import { getStaticUserPageData } from '@tracking/static/userPageData';
import { Tracking } from '@tracking/Tracking';

import i18n from '../i18n';
import { PageResponseBody, UserContentType } from '../types';

const MY_ARTE_FAVOURITES_ZONE_ID = 'myarte-favourites';
const MY_ARTE_HISTORY_ZONE_ID = 'myarte-personal';
const MY_ARTE_PAGE_ZONE_IDS = [MY_ARTE_FAVOURITES_ZONE_ID, MY_ARTE_HISTORY_ZONE_ID];

const getUserZone = (type: UserContentType) => {
  let title, id; // id can be anything as it only provides a react key
  switch (type) {
    case 'sso-favorites':
      title = i18n.t('myFavorites');
      id = MY_ARTE_FAVOURITES_ZONE_ID;
      break;
    case 'sso-personalzone':
      title = i18n.t('myHistory');
      id = MY_ARTE_HISTORY_ZONE_ID;
      break;
    default:
      title = 'unknown title';
      id = 'unknown id';
  }

  return {
    authenticatedContent: type,
    id,
    showItemTitle: true,
    showZoneTitle: true,
    template: ZONE_TEMPLATES.HORIZONTAL_LANDSCAPE,
    title,
    teaser_count: 0,
    teaserList: [],
  };
};

const getMyArtePage = () => {
  const favouritesZone = getUserZone('sso-favorites');
  const personalZone = getUserZone('sso-personalzone');
  const zones = [favouritesZone, personalZone];

  return {
    id: 'myarte',
    type: 'page',
    title: i18n.t('myArte'),
    description: '',
    zones,
  };
};

const myArtePageLoader = async (): Promise<PageResponseBody | Response> => {
  const lang = i18n.language;
  const pageData = getStaticUserPageData('MY_FAVORITES', lang);
  Tracking.trackPageView(pageData);
  return await hydratePage(getMyArtePage(), {});
};

export { MY_ARTE_PAGE_ZONE_IDS, myArtePageLoader };

import i18n from 'i18next';
import { thirdPartyAuth } from 'target';

import { SettingsResponseBody } from '../../types';
import { hasUserToken } from '../../util/cookies';

const settingsLoader = (): SettingsResponseBody => {
  const isAuthenticated = hasUserToken();
  const isThirdParty = thirdPartyAuth.enabled;
  const items: Array<[string, string]> = [];

  items.push(['settings__display', 'interface']);

  if (!isAuthenticated) {
    items.push(['settings__personalisation', 'personalisation']);
  }

  items.push(['settings__playback', 'tutorial']);

  if (!isThirdParty) {
    items.push(['settings__privacy', 'privacy']);
  }

  items.push(['settings__informations', 'information']);

  return {
    settingsMenuItems: items.map(([key]) => i18n.t(key)),
    routes: items.map(([, route]) => route),
  };
};

export { settingsLoader };

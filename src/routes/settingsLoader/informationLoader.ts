import { InformationTitlesResponse } from '@apptypes/SettingsResponseBody';
import { getTargetAndSubsetName } from '@util/target';
import { middlewareUrl } from '@util/url';

import { ApiRequestFailError, failOnInformationPageResponseError } from '../../errors';
import i18n from '../../i18n';

const informationLoader = async () => {
  const url = middlewareUrl(`/information_titles`);
  const response = await fetch(url).catch(() => {
    throw new ApiRequestFailError(url);
  });
  const data: InformationTitlesResponse = await response.json();
  failOnInformationPageResponseError(data);
  data.titles = [...data.titles, { txt: i18n.t('settings__help'), url: 'help' }];
  return {
    version: process.env.VERSION,
    application: getTargetAndSubsetName(),
    titles: data.titles,
  };
};

export { informationLoader };

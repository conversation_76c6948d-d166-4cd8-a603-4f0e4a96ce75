import { fetchCachedData, fetchUrl, getCachedResponse, storeCachedResponse } from '@util/CacheHistory';
import { middlewareUrl } from '@util/url';
import { LoaderFunctionArgs } from 'react-router-dom';
import { normalizeParams } from 'target';

import { failOnPageResponseError } from '../errors';
import { Tracking } from '../tracking/Tracking';
import { PageResponseBody } from '../types';
import { hydratePage } from './pageHydration';

const collectionLoader = async ({ params }: LoaderFunctionArgs): Promise<PageResponseBody | null> => {
  const normalizedParams = normalizeParams(params);
  const url = middlewareUrl(`/skeletons/collections/${normalizedParams.collectionId}`);

  const cachedResponse = getCachedResponse(url);
  let dehydratedPage: PageResponseBody | null = cachedResponse ? await fetchCachedData(cachedResponse) : null;

  if (!dehydratedPage) {
    dehydratedPage = await fetchUrl(url);
    failOnPageResponseError(dehydratedPage);
    storeCachedResponse(url, dehydratedPage);
  }

  const hydratedPage = await hydratePage(dehydratedPage, normalizedParams);
  Tracking.trackPageView(hydratedPage);

  return hydratedPage;
};

export { collectionLoader };

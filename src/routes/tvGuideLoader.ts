import { ITvGuideDayData } from '@apptypes/ITvGuideTeaserProperties';
import { getTvGuideDayData } from '@components/TvGuide/getTvGuideDayData';
import { Tracking } from '@tracking/Tracking';
import { isTimeBefore } from '@util/isTimeBefore';
import { middlewareUrl } from '@util/url';
import { LoaderFunctionArgs } from 'react-router-dom';

import { ApiRequestFailError } from '../errors';
import { ITvGuideTeaserProperties, TvGuideResponseBody, Zone } from '../types';

const tvGuideLoader = async ({
  params,
}: LoaderFunctionArgs): Promise<{
  tvGuidePage: TvGuideResponseBody;
  tvGuideDayData: ITvGuideDayData[];
  activeDay: number;
  liveTeaserIndex: number;
  primeTeaserIndex: number;
}> => {
  const activeDay = params.day ? Number(params.day) : 20;
  const url = middlewareUrl(`/skeletons/pages/tv_guide`);
  const response = await fetch(url).catch(() => {
    throw new ApiRequestFailError(url);
  });
  const tvGuidePage = (await response.json()) as TvGuideResponseBody;

  const tvGuideDay = (await getTvGuideDayData(tvGuidePage.items[activeDay])) as unknown as ITvGuideTeaserProperties[];
  const fullParsableComposedZonesInUsage = [];
  for (const idx in tvGuidePage.items) {
    fullParsableComposedZonesInUsage.push({
      title: `${tvGuidePage?.id} - ${tvGuidePage.items[idx].date}`,
      template: 'tableview-guide',
      teaserList: [] as Zone[],
      code: tvGuideDay.code,
    });

    if (tvGuidePage.items[idx].date === tvGuidePage.items[activeDay].date) {
      fullParsableComposedZonesInUsage[fullParsableComposedZonesInUsage.length - 1].teaserList = [...tvGuideDay.data];
    }
  }

  Tracking.trackPageView(tvGuideDay);
  Tracking.setZonesInUsage(fullParsableComposedZonesInUsage as unknown as Zone[]);

  const tvGuideDayData = tvGuideDay.data;
  const primeTeaserIndex =
    tvGuideDayData.findIndex((teaser) => teaser.stickers.find((sticker) => sticker.code === 'PRIME_TIME')) || 0;

  let liveTeaserIndex = -1;
  for (let idx = 0; idx < tvGuideDayData.length; idx++) {
    const teaser = tvGuideDayData[idx];
    if (!isTimeBefore(teaser)) {
      liveTeaserIndex = idx;
    } else {
      if (liveTeaserIndex !== -1) break;
    }
  }

  return { tvGuidePage, tvGuideDayData, activeDay, liveTeaserIndex, primeTeaserIndex, tvGuideDay };
};

export { tvGuideLoader };

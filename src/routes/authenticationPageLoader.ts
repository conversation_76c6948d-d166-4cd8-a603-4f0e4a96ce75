import { getStaticUserPageData } from '@tracking/static/userPageData';
import { Tracking } from '@tracking/Tracking';
import i18n from 'i18next';

const authenticationPageLoader = async () => {
  const lang = i18n.language;
  const pageData = getStaticUserPageData('LOGIN', lang);

  Tracking.trackPageView(pageData);

  return {
    title: i18n.t('login'),
    qrCodeMessage: i18n.t('login_page2_step1'),
    connectMessage: i18n.t('login_page2_step2'),
    networkErrorMessage: i18n.t('error__generic'),
    unauthorizedErrorMessage: i18n.t('error__login_invalid'),
  };
};

export { authenticationPageLoader };

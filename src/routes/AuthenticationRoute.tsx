import { CustomNavigate } from '@components/CustomNavigate/CustomNavigate';
import { AuthenticationPage } from '@components/Login/Authentication/AuthenticationPage';
import { isLoggedIn } from '@util/cookies';

import { ROUTES } from '../constants';

export function AuthenticationRoute() {
  if (isLoggedIn()) return <CustomNavigate path={ROUTES.MYARTE.ROOT} replace />;
  return <AuthenticationPage />;
}

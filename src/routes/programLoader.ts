import { middlewareUrl } from '@util/url';
import { LoaderFunctionArgs } from 'react-router-dom';

import { failOnPageResponseError } from '../errors';
import { Tracking } from '../tracking/Tracking';
import { PageResponseBody } from '../types';
import { fetchCachedData, fetchUrl, getCachedResponse, storeCachedResponse } from '../util/CacheHistory';
import { hydratePage } from './pageHydration';

const programLoader = async ({ params }: LoaderFunctionArgs): Promise<PageResponseBody | null> => {
  const url = middlewareUrl(`/skeletons/programs/${params.programId}`);

  const cachedResponse = getCachedResponse(url);
  let dehydratedPage: PageResponseBody | null = cachedResponse ? await fetchCachedData(cachedResponse) : null;

  if (!dehydratedPage) {
    dehydratedPage = await fetchUrl(url);
    failOnPageResponseError(dehydratedPage);
    storeCachedResponse(url, dehydratedPage);
  }

  const hydratedPage = await hydratePage(dehydratedPage, params);

  Tracking.trackPageView(hydratedPage);

  return hydratedPage;
};

export { programLoader };

import { CustomNavigate } from '@components/CustomNavigate/CustomNavigate';
import { MyAccount } from '@components/Pages/MyAccount/MyAccount';
import { MyArtePage } from '@components/Pages/MyArtePage';
import { GlobalContext } from '@providers/GlobalContextProvider';
import { isLoggedIn } from '@util/cookies';
import { FEATURE_FLAG, getFeatureFlag } from '@util/getFeatureFlag';
import { useContext } from 'react';

import { ROUTES } from '../constants';

export function ProtectedRoute() {
  const { featureFlags } = useContext(GlobalContext);
  if (!isLoggedIn()) return <CustomNavigate path={ROUTES.MYARTE.LOGIN} replace />;

  const shouldUseMyAccount = getFeatureFlag(featureFlags, FEATURE_FLAG.SMARTTV_FEATURE_FLAGS_MY_ACCOUNT);
  return shouldUseMyAccount ? <MyAccount /> : <MyArtePage />;
}

import { MenuResponseBody } from '@apptypes/MenuResponseBody';
import { getFeatureFlags, getMenu } from '@data/source';

export async function pageLayoutLoader() {
  const menu: MenuResponseBody | void = await getMenu();
  const featureFlags = await getFeatureFlags();

  const parsedMenuItems = menu?.items.map((item) => {
    return {
      label: item.label,
      page: item.page.toLowerCase(),
    };
  });

  const items = parsedMenuItems || [];

  return { items, featureFlags };
}

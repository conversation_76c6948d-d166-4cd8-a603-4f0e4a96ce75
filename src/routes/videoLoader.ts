import { Attributes } from '@apptypes/VideoResponseBody';
import * as dashjs from 'dashjs';
import { LoaderFunctionArgs } from 'react-router-dom';
import { normalizeParams } from 'target';

import { PlayListData } from '../types/PlayListResponseBody';
import { getPlaylistData } from './utils';

export type VideoLoaderResponse = {
  attributes: Attributes;
  playlistData: PlayListData | undefined;
  videoId: string;
};

const videoLoader = async ({ params }: LoaderFunctionArgs): Promise<VideoLoaderResponse | Response> => {
  const normalizedParams = normalizeParams(params);
  const videoId = normalizedParams.videoId || '';

  if (!window.dashjs) {
    window.dashjs = dashjs;
  }

  const playlistData = await getPlaylistData(videoId);

  return {
    playlistData,
    videoId: videoId,
  };
};

export { videoLoader };

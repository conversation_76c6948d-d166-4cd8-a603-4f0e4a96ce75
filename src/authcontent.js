import { SsoRequestForbiddenError } from '@errors/SsoRequestForbiddenError';
import { SsoRequestUnauthorizedError } from '@errors/SsoRequestUnauthorizedError';
import { getSsoUrl } from '@util/getSsoUrl';
import i18n from 'i18next';

import { logError } from './errors';
import { userHistory as History } from './features/usercontent/userContentData';

const AuthContent = function (proxy, debug) {
  // eslint-disable-next-line @typescript-eslint/no-this-alias
  var self = this;

  self.proxy = false;

  if (proxy === true) {
    self.proxy = true;
  }

  self.debug = false;

  if (debug === true) {
    self.debug = true;
  }

  self.get_authcontent_zone = function (authenticated_content, queryString, callback, failback, reference) {
    const lang = i18n.language;
    let authenticated_content_tag;
    switch (authenticated_content) {
      case 'sso-personalzone':
        authenticated_content_tag = 'personalzone';
        break;
      case 'sso-favorites':
        authenticated_content_tag = 'favorites';
        break;
      case 'sso-history':
        authenticated_content_tag = 'lastvieweds';
        break;
      default:
        const unsupportedAuthenticatedContent = `Unknown authenticated content ${authenticated_content}`;
        logError(unsupportedAuthenticatedContent, 'WARNING');
        failback(unsupportedAuthenticatedContent);
        return;
    }

    if (self.debug) {
      console.log('[authcontent] get_authcontent_zone ', authenticated_content, queryString);
    }

    const query = queryString ? `?${queryString}` : '';

    if (!self.proxy) {
      var url = getSsoUrl(`/${authenticated_content_tag}/${lang}${query}`);
    } else {
      var url =
        'http://dev56.teravolt.it/hab/arte_proxy/authcontent.php?authenticated_content=' +
        authenticated_content_tag +
        '&lang=' +
        lang;
    }

    var auth_token = 'Bearer ' + History.getToken();
    var headersData = {
      client: 'hbbtv',
      authorization: auth_token,
    };

    fetch(url, {
      method: 'GET',
      headers: headersData,
    })
      .then((response) => {
        switch (response.status) {
          case 401:
            throw new SsoRequestUnauthorizedError();
          case 503:
            throw new SsoRequestForbiddenError();
          default:
            return response.json();
        }
      })
      .then((response) => {
        response.authenticatedContent = authenticated_content;

        if (reference) {
          response.reference = reference;
        }

        // fix some field -> same fieldnames/structure as normal ASM "zone" responses
        for (var t = 0; t < response.data.length; t++) {
          response.data[t].kind = {
            isCollection: response.data[t].id.substr(0, 3) == 'RC-',
          };
          response.data[t].image =
            response.data[t].images[0].url.substr(0, 1 + response.data[t].images[0].url.lastIndexOf('/')) + '__SIZE__';
          response.data[t].program_id = response.data[t].programId;
          response.data[t].duration =
            response.data[t].duration != null ? response.data[t].duration + ' Min.' : response.data[t].durationLabel;
        }

        if (self.debug) {
          console.log('[authcontent] authcontent_zone data', response);
        }

        if (callback && typeof callback == 'function') {
          if (self.debug) {
            console.log('[authcontent] calling callback...');
          }

          callback(response);
        }
      })
      .catch((e) => {
        if (typeof failback === 'function') {
          failback(e);
        }
      });

    return true;
  };
};

export { AuthContent };

import { useCustomNavigate } from '@hooks/useCustomNavigate';
import { useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import { useCallback, useEffect, useState } from 'react';
import { useLocation, useRouteError } from 'react-router-dom';
import { confirmExitOnBack, exitApp, isExitEnabled } from 'target';

import { getExitModal } from '../../components/Modal/modal';
import { InvalidPageError, LiveStreamUnavailable } from '../../errors';
import { useModalContext } from '../../providers/ModalContext';
import { isVerificationRouteByLocation, isVideoRouteByLocation } from '../../routes/route';
import { isAppEntryPoint } from '../appentrypoint';

export const useBackJourney = () => {
  const { setFocus, getCurrentFocusKey } = useFocusable();
  const location = useLocation();
  const { modalOpen, showModal, hideModal } = useModalContext();
  const navigate = useCustomNavigate();
  const error = useRouteError() as Error;
  const [isNavigating, setIsNavigating] = useState(false);

  useEffect(
    function onLocationChanged() {
      setIsNavigating(false);
    },
    [location],
  );

  const handleBack = useCallback(() => {
    if (isNavigating) return;

    const isVideoPage = isVideoRouteByLocation(location);
    const isVerificationPage = isVerificationRouteByLocation(location);
    const stepsBack: number | undefined = location?.state?.stepsBack;

    function startNavigating(delta: number) {
      setIsNavigating(true);
      navigate(delta);
    }

    if (error && location?.pathname) {
      switch (true) {
        case error instanceof LiveStreamUnavailable:
          navigate(-1);
          return;

        case error instanceof InvalidPageError:
          const isDeeplinkedPage = location?.state?.isDeeplinkRedirect;

          if (isDeeplinkedPage) {
            // Nowhere else to go. The deeplinked page is invalid.
            isExitEnabled() && exitApp();
          } else {
            navigate(-1);
          }
          return;

        default:
      }
    }

    if (isVerificationPage) {
      isAppEntryPoint(location) ? exitApp() : startNavigating(-1);
      return;
    }

    if (modalOpen && isAppEntryPoint(location)) {
      return;
    }

    if (!modalOpen && !isAppEntryPoint(location)) {
      const steps = (() => {
        switch (true) {
          case !!stepsBack:
            return (stepsBack as number) * -1;
          default:
            return -1;
        }
      })();

      startNavigating(steps);
      return;
    }

    if (!isExitEnabled() || modalOpen) {
      return;
    }

    // Quit the app from a video page
    if (isVideoPage) {
      exitApp();
      return;
    }

    // Quit the app from pages other than the video page
    if (confirmExitOnBack()) {
      showModal({
        content: getExitModal(hideModal, setFocus, getCurrentFocusKey()),
      });
    } else {
      exitApp();
    }
  }, [getCurrentFocusKey, hideModal, location, modalOpen, navigate, setFocus, showModal, error, isNavigating]);

  return { handleBack };
};

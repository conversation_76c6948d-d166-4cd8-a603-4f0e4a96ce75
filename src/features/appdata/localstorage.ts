import { logError } from '../../errors';

const LOCAL_STORAGE_EXPIRY_TS_POSTFIX = '_exp';

const removeFromLocalStorage = (name: string): void => {
  try {
    localStorage.removeItem(name);
    localStorage.removeItem(name + LOCAL_STORAGE_EXPIRY_TS_POSTFIX);
  } catch (e) {
    logError(e, 'WARNING');
  }
};

const removeEverythingFromLocalStorage = (except: string[] = []): void => {
  try {
    // localStorage.clear();
    for (const key in localStorage) {
      if (
        except.includes(key) ||
        except.includes(key.substring(0, key.length - LOCAL_STORAGE_EXPIRY_TS_POSTFIX.length))
      ) {
        continue;
      }
      delete localStorage[key];
    }
  } catch (e) {
    logError(e, 'WARNING');
  }
};

const readFromLocalStorage = (name: string): string | null => {
  // try to read localstorage "name" and - if it exists - check expiry timestamp
  try {
    const localStorageExpire = localStorage.getItem(name + LOCAL_STORAGE_EXPIRY_TS_POSTFIX);

    if (localStorageExpire && parseInt(localStorageExpire) > 0 && parseInt(localStorageExpire) < Date.now()) {
      // local storage has expired -> remove it
      removeFromLocalStorage(name);
      return '';
    } else {
      return localStorage.getItem(name);
    }
  } catch (e) {
    logError(e, 'WARNING');
    return null;
  }
};

const writeToLocalStorage = (name: string, value: string, expiryDate: Date): void => {
  try {
    localStorage.setItem(name, value);
    localStorage.setItem(name + LOCAL_STORAGE_EXPIRY_TS_POSTFIX, expiryDate.getTime().toString());
  } catch (e) {
    logError(e, 'WARNING');
  }
};

const getAllLocalStorageData = (): Map<string, string> => {
  const localStorageMap = new Map<string, string>();
  try {
    Object.keys(localStorage).forEach(function (key) {
      localStorageMap.set(key, localStorage.getItem(key) as string);
    });
  } catch (e) {
    logError(e, 'WARNING');
  }
  return localStorageMap;
};

export {
  removeFromLocalStorage,
  removeEverythingFromLocalStorage,
  readFromLocalStorage,
  writeToLocalStorage,
  getAllLocalStorageData,
};

import { COOKIE_ANON_TOKEN, COOKIE_TOKENS_TRANSFERRED } from '../../util/cookies';
import { getAllCookies, readCookie, removeAllCookies, removeCookie, writeCookie } from './cookie';
import {
  getAllLocalStorageData,
  readFromLocalStorage,
  removeEverythingFromLocalStorage,
  removeFromLocalStorage,
  writeToLocalStorage,
} from './localstorage';

const readAppData = (name: string): string | null => {
  return readFromLocalStorage(name) || readCookie(name);
};

const writeAppData = (name: string, value: boolean | string, days = 365 * 5): void => {
  const expiryDate = new Date();
  expiryDate.setTime(expiryDate.getTime() + days * 24 * 60 * 60 * 1000);

  writeCookie(name, value.toString(), expiryDate);
  writeToLocalStorage(name, value.toString(), expiryDate);
};

const eraseAppData = (name: string): void => {
  removeCookie(name);
  removeFromLocalStorage(name);
};

const eraseAllAppData = (): void => {
  const nonRemovableData = [COOKIE_TOKENS_TRANSFERRED, COOKIE_ANON_TOKEN];
  removeAllCookies(nonRemovableData.slice());
  removeEverythingFromLocalStorage(nonRemovableData.slice());
};

const getAllAppData = (): Map<string, string> => {
  const cookieMap = getAllCookies();
  const localStorageMap = getAllLocalStorageData();
  // Merge and remove duplicates
  return new Map([...Array.from(cookieMap.entries()), ...Array.from(localStorageMap.entries())]);
};

export { readAppData, writeAppData, eraseAppData, eraseAllAppData, getAllAppData };

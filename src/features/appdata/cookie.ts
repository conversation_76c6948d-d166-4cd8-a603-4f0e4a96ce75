const removeCookie = (name: string): void => {
  const yesterday = new Date();
  yesterday.setTime(yesterday.getTime() - 1 * 24 * 60 * 60 * 1000);

  writeCookie(name, '', yesterday, true); // Deletes a cookie from .arte.tv
  // Deletes a cookie without specifying a domain.
  // It's needed to delete cookies set by 3rd party libs which save cookies using a full domain e.g. smarttv-preprod.arte.tv/
  writeCookie(name, '', yesterday, false);
};

const removeAllCookies = (except: string[] = []): void => {
  const cookies = document.cookie.split('; ');
  for (const i in cookies) {
    const cookieName = cookies[i].split('=')[0];
    if (except.includes(cookieName)) {
      continue;
    }
    removeCookie(cookieName);
  }
};

const readCookie = (name: string): string | null => {
  const nameEQ = name + '=';
  const cookiesArray = document.cookie.split(';');

  for (let i = 0; i < cookiesArray.length; i++) {
    let cookie = cookiesArray[i];

    while (cookie.charAt(0) === ' ') {
      cookie = cookie.substring(1, cookie.length);
    }

    if (cookie.indexOf(nameEQ) === 0) {
      return cookie.substring(nameEQ.length, cookie.length);
    }
  }

  return null;
};

const cookieDomain = process.env.NODE_ENV === 'development' ? location.hostname : '.arte.tv';

const writeCookie = (name: string, value: string, expiryDate: Date, useDomain = true): void => {
  const expires = '; expires=' + expiryDate.toUTCString();
  const domain = useDomain ? '; domain=' + cookieDomain : '';

  document.cookie = name + '=' + value + expires + domain + '; path=/;';
};

const getAllCookies = (): Map<string, string> => {
  const cookieArray: [string, string][] = document.cookie
    .split(';')
    .map((cookiePairString) => cookiePairString.trim().split('=') as [string, string]);
  const cookieMap: Map<string, string> = new Map(cookieArray);
  return cookieMap;
};

export { removeCookie, removeAllCookies, readCookie, writeCookie, getAllCookies };

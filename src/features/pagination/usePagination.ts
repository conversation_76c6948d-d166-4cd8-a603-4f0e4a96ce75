import { Bookmark } from '@apptypes/bookmarks';
import { DefaultPageResults, IInitialResults, IPageResults } from '@apptypes/pagination';
import { MeData } from '@apptypes/SSOResponse';
import { BookmarksContext } from '@providers/BookmarksContext';
import { loadMeData } from '@routes/pageHydration';
import { addTeaserProgress } from '@routes/zoneHydration';
import { storePaginationCurrentPage, storePaginationResult } from '@util/NavHistory';
import { useCallback, useContext, useEffect, useState } from 'react';

export const usePagination = <T, I extends IInitialResults>(
  fetchPage: (id: string, queryString: string) => Promise<void | T>,
  defaultResults: I,
  getQueryParams?: (results: I) => URLSearchParams,
) => {
  const [currentPaginationPage, setCurrentPaginationPage] = useState<number>(1);
  const [initialResults, setInitialResults] = useState<I>(defaultResults);
  const [pageResults, setPageResults] = useState<IPageResults>(DefaultPageResults);
  const [busyFetchingPage, setBusyFetchingPage] = useState<boolean>(false);
  const { addNextBookmarksPage } = useContext(BookmarksContext);
  const [meData, setMeData] = useState<MeData | null>(null);
  const [nextPageBookmarks, setNextPageBookmarks] = useState<Bookmark[]>();
  const [pageId, setPageId] = useState<string | null>(null);

  useEffect(() => {
    async function getMeData() {
      const data = await loadMeData();
      setMeData(data);
    }
    getMeData();
  }, []);

  useEffect(() => {
    // Add bookmarks when the next bookmarks page is loaded
    if (nextPageBookmarks) {
      addNextBookmarksPage(nextPageBookmarks);
    }
  }, [addNextBookmarksPage, nextPageBookmarks]);

  const fetchNextPage = () => {
    if (currentPaginationPage >= 1 && currentPaginationPage < initialResults?.pages && !busyFetchingPage) {
      setCurrentPaginationPage(currentPaginationPage + 1);
    }
  };

  const fetchResults = useCallback(
    async (id: string, currentPage: number, queryString: string) => {
      const searchParams = new URLSearchParams();
      searchParams.set('page', currentPage.toString());
      const query = queryString ? `${searchParams.toString()}&${queryString}` : searchParams.toString();

      const pageResponse = await fetchPage(id, query);
      const data = pageResponse?.data;
      if (data) {
        const hydratedData = meData ? addTeaserProgress(data, meData) : data;

        if (id === 'sso-favorites') {
          // Update bookmarks context with loaded data
          setNextPageBookmarks(hydratedData);
        }

        setPageResults((prevResults) => {
          const newData = { data: [...prevResults.data, ...hydratedData] };
          pageId && storePaginationResult(newData, pageId);
          return newData;
        });
      }
    },
    [fetchPage, meData, pageId],
  );

  useEffect(() => {
    if (currentPaginationPage === 1) return;

    setBusyFetchingPage(true);

    const queryParams = getQueryParams ? getQueryParams(initialResults) : '';
    fetchResults(initialResults.id, currentPaginationPage, queryParams.toString());
    pageId && storePaginationCurrentPage(currentPaginationPage, pageId);
  }, [currentPaginationPage, pageId, initialResults, fetchResults, getQueryParams]);

  const setBusyFetchingPageWithDelay = useCallback(() => {
    const timer = setTimeout(() => {
      setBusyFetchingPage(false);
    }, 1500);
    return () => clearTimeout(timer);
  }, [setBusyFetchingPage]);

  useEffect(() => {
    setBusyFetchingPageWithDelay();
  }, [pageResults, setBusyFetchingPageWithDelay]);

  useEffect(() => {
    setBusyFetchingPage(false);
  }, [initialResults, setCurrentPaginationPage, setPageResults]);

  return {
    initialResults,
    pageResults,
    fetchNextPage,
    setInitialResults,
    busyFetchingPage,
    setPageResults,
    currentPaginationPage,
    setCurrentPaginationPage,
    setPageId,
  };
};

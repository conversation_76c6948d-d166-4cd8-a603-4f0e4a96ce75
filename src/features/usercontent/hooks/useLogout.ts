import { eraseAppData } from '@features/appdata/appdata';
import { BookmarksContext } from '@providers/BookmarksContext';
import { GlobalContext } from '@providers/GlobalContextProvider';
import { resetCacheHistory } from '@util/CacheHistory';
import { COOKIE_USER_TOKEN, deleteLegacyAnonToken, deleteLegacyUserToken } from '@util/cookies';
import { resetNavHistory } from '@util/NavHistory';
import { useCallback, useContext } from 'react';

export const useLogout = () => {
  const { initBookmarks } = useContext(BookmarksContext);
  const { setUserData } = useContext(GlobalContext);

  const logout = useCallback(() => {
    resetNavHistory();
    resetCacheHistory();
    initBookmarks([]);
    deleteLegacyUserToken();
    deleteLegacyAnonToken();
    eraseAppData(COOKIE_USER_TOKEN);
    setUserData(undefined);
  }, [initBookmarks, setUserData]);

  return { logout };
};

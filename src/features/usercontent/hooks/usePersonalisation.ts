import { BookmarksContext } from '@providers/BookmarksContext';
import { resetCacheHistory } from '@util/CacheHistory';
import { rejectPersonalisationCookies } from '@util/cookies';
import { userHistoryScheduler } from '@util/userHistoryScheduler';
import { useCallback, useContext } from 'react';

export const usePersonalisation = () => {
  const { initBookmarks } = useContext(BookmarksContext);

  const disablePersonalisation = useCallback(() => {
    rejectPersonalisationCookies();
    userHistoryScheduler.reset();
    initBookmarks([]);
    resetCacheHistory();
  }, [initBookmarks]);

  return { disablePersonalisation };
};

import { AuthContent } from '../../authcontent';
import { UserContentType } from '../../types';
import { Me, MeData, NextEpisode, UserData } from '../../types/SSOResponse';
import { IUserHistory } from '../../types/userHistory';
import { UserHistory } from '../../userhistory';
import { COOKIE_ANON_TOKEN, COOKIE_PERSONALISATION } from '../../util/cookies';
import EventBus from '../../util/EventBus';
import { eraseAppData } from '../appdata/appdata';

const proxy = process.env.USER_HISTORY_PROXY === 'true' || false;
const debug = process.env.NODE_ENV === 'development';
const authContent = new AuthContent(proxy, debug);

export const userHistory: IUserHistory = new UserHistory(proxy, debug);

EventBus.on(COOKIE_PERSONALISATION, async (data) => {
  const { cookieValue } = data;
  cookieValue ? await getMeData() : eraseAppData(COOKIE_ANON_TOKEN);
});

export const getUserContent = async <T>(type: UserContentType | string, queryString = ''): Promise<T> => {
  await userHistory.getAnonymousToken();
  return new Promise<T>((resolve, reject) => {
    authContent.get_authcontent_zone(type, queryString, (content: T) => resolve(content), reject);
  });
};

export const getMe = async (): Promise<Me | UserData> => {
  return userHistory.getMe();
};

export const getMeData = async (): Promise<MeData> => {
  await userHistory.getAnonymousToken();

  return userHistory.getMeData();
};

export const requestUserToken = async (code: string): Promise<Response> => {
  return userHistory.requestUserToken(code);
};

export const getNextEpisode = async (programId: string): Promise<NextEpisode> => {
  return userHistory.getNextEpisode(programId);
};

export const addToFavourites = async (programId: string): Promise<Response> => {
  return userHistory.addToFavourites(programId);
};

export const removeFromFavourites = async (programId: string): Promise<Response> => {
  return userHistory.removeFromFavourites(programId);
};

export const clearHistory = async (): Promise<void> => {
  return userHistory.clearHistory();
};

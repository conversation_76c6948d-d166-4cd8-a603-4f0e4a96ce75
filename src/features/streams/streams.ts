import { getLastViewedData } from '@util/meData';
import { MeDataLastViewed } from 'src/types/SSOResponse';

import { Stream } from '../../types';

export const getStartTime = (id: string, lastViewedItems?: MeDataLastViewed[]): number => {
  const lastViewed = getLastViewedData(id, lastViewedItems);
  if (!lastViewed || !lastViewed?.progress) {
    return 0;
  }

  const canResume = lastViewed && lastViewed.progress >= 0.01 && lastViewed.progress <= 0.95;
  return canResume ? lastViewed.timecode : 0;
};

export const hasVersions = (stream?: Stream): boolean => !!stream && stream.versions.length > 0;

import { getSearchParams } from 'target';

import { QUERY_STRING_LOOKUP_LANGUAGE } from '../../constants';

const defaultRelaunchQueryParams = (): URLSearchParams => {
  const searchParams = getSearchParams();
  searchParams.delete('type');
  searchParams.delete('id');
  searchParams.delete(QUERY_STRING_LOOKUP_LANGUAGE);
  return searchParams;
};

const debugRelaunchQueryParams = (): URLSearchParams => {
  const debugParams = defaultRelaunchQueryParams();
  debugParams.set('debug', 'true');
  return debugParams;
};

export { defaultRelaunchQueryParams, debugRelaunchQueryParams };

import { getSearchParams } from 'target';

import { QUERY_STRING_TEST_STREAM, QUERY_STRING_TEST_STREAM_LIVE } from '../../constants';
import {
  CHANNEL_77_QUERY_PARAM,
  DEBUG_QUERY_PARAM,
  UTM_CAMPAIGN_KEY,
  UTM_MEDIUM_KEY,
  UTM_SOURCE_KEY,
  UTM_SOURCE_VALUE,
} from './queryParamsConsts';

// A module for query string lookup.
// Need to read a query string param? Add a function below to abstract out the key and values of a param you need.

const isChannel77 = () => getSearchParams().get(CHANNEL_77_QUERY_PARAM.key) === CHANNEL_77_QUERY_PARAM.value;

const isDebug = () => getSearchParams().get(DEBUG_QUERY_PARAM.key) === DEBUG_QUERY_PARAM.value;

// utm
const getUtmCampaign = () => getSearchParams().get(UTM_CAMPAIGN_KEY);
const getUtmMedium = () => getSearchParams().get(UTM_MEDIUM_KEY);
const getUtmSource = () => getSearchParams().get(UTM_SOURCE_KEY);

// platforms
const isMagentaTv = () => getSearchParams().get(UTM_SOURCE_KEY) === UTM_SOURCE_VALUE.MAGENTA_TV;
const isSky = () => getSearchParams().get(UTM_SOURCE_KEY) === UTM_SOURCE_VALUE.SKY;
const isSkyDe = () => getSearchParams().get(UTM_SOURCE_KEY) === UTM_SOURCE_VALUE.SKYDE;
const isRoku = () => getSearchParams().get(UTM_SOURCE_KEY) === UTM_SOURCE_VALUE.ROKU;
const hasPanasonicTvQueryParam = () => getSearchParams().get(UTM_SOURCE_KEY) === UTM_SOURCE_VALUE.PANASONIC;
const hasTitanosQueryParam = () => getSearchParams().get(UTM_SOURCE_KEY) === UTM_SOURCE_VALUE.TITANOS;
const hasVidaaTvQueryParam = () => getSearchParams().get(UTM_SOURCE_KEY) === UTM_SOURCE_VALUE.VIDAA;

// test stream
const getTestStream = () => getSearchParams().get(QUERY_STRING_TEST_STREAM);
const isLiveTestStream = () => getSearchParams().get(QUERY_STRING_TEST_STREAM_LIVE) === 'true';

export {
  isChannel77,
  isDebug,
  getUtmCampaign,
  getUtmMedium,
  getUtmSource,
  isMagentaTv,
  isRoku,
  isSky,
  isSkyDe,
  getTestStream,
  isLiveTestStream,
  hasPanasonicTvQueryParam,
  hasTitanosQueryParam,
  hasVidaaTvQueryParam,
};

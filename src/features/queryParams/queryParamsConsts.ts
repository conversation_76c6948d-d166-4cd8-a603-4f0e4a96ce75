const CHANNEL_77_QUERY_PARAM = {
  key: 'app',
  value: 'portalfr',
};

const DEBUG_QUERY_PARAM = {
  key: 'debug',
  value: 'true',
};

const REMAP_COLOR_KEYS_QUERY_PARAM = {
  key: 'remap_color_keys',
  value: 'true',
};

const UTM_CAMPAIGN_KEY = 'utm_campaign';
export type UTM_CAMPAIGN_VALUE = 'thumbnail_app' | `search_${string}` | `preview_${string}`;

const UTM_MEDIUM_KEY = 'utm_medium';
enum UTM_MEDIUM_VALUE {
  SMARTHUB = 'smart_hub',
  SMARTHUB_PREVIEW = 'smart_hub_preview',
  UNIVERSAL_SEARCH = 'universal_search',
}

const UTM_SOURCE_KEY = 'utm_source';
enum UTM_SOURCE_VALUE {
  MAGENTA_TV = 'magenta_tv',
  ROKU = 'roku',
  TITANOS = 'titanos',
  TIZEN = 'tizen',
  PANASONIC = 'panasonic',
  VIDAA = 'vidaa',
  WEBOS = 'webos',
  SKY = 'sky',
  SKYDE = 'skyde',
}

export {
  CHANNEL_77_QUERY_PARAM,
  DEBUG_QUERY_PARAM,
  REMAP_COLOR_KEYS_QUERY_PARAM,
  UTM_CAMPAIGN_KEY,
  UTM_MEDIUM_KEY,
  UTM_MEDIUM_VALUE,
  UTM_SOURCE_KEY,
  UTM_SOURCE_VALUE,
};

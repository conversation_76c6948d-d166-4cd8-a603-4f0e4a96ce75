import { Location } from 'react-router-dom';

import { isVerificationRouteByLocation } from '../../routes/route';

const EntryPoints = {
  INITIAL: 'default',
  VERIFICATION: 'verification',
};

let entryPoint = EntryPoints.INITIAL;

const isVerificationEntryPoint = () => entryPoint === EntryPoints.VERIFICATION;
const isLocationTheEntryPoint = (location: Location) => location.key === entryPoint;

export const isInitialEntryPoint = () => entryPoint === EntryPoints.INITIAL;
export const isAppEntryPoint = (location: Location) => isVerificationEntryPoint() || isLocationTheEntryPoint(location);

export const setEntryPoint = (location: Location) => {
  if (!isInitialEntryPoint()) {
    console.warn('Entry point can be changed only once');
    return;
  }

  entryPoint = isVerificationRouteByLocation(location) ? EntryPoints.VERIFICATION : location.key;
};

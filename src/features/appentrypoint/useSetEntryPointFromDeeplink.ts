import { useCustomNavigate } from '@hooks/useCustomNavigate';
import { useEffect, useRef } from 'react';
import { Location, useLocation } from 'react-router-dom';

import { getRouteFromQueryString, isVerificationRouteByLocation } from '../../routes/route';
import { isInitialEntryPoint, setEntryPoint } from './appEntryPoint';

function isDeeplinkToAgeVerificationRoute(location: Location, previousPathname: string) {
  return isVerificationRouteByLocation(location) && !previousPathname;
}

export const useSetEntryPointFromDeeplink = () => {
  const navigate = useCustomNavigate();
  const location = useLocation();
  const previousPathname = useRef('');
  const previousRouteFromQueryString = useRef('');
  useEffect(() => {
    /**
     * prevent deep-linking to age verification route.
     * this is not something a user can do but we can refresh on that page and
     * that does not represent a valid user journey and may cause confusion so
     * we navigate home in this instance
     */
    if (isDeeplinkToAgeVerificationRoute(location, previousPathname.current)) {
      navigate('/', { replace: true });
      return;
    }

    // The check below works ok with a browser router.
    // It will need some modifications if you decide to use different routers e.g a memory router.
    if (previousPathname.current === '/' && previousRouteFromQueryString.current) {
      if (isInitialEntryPoint()) {
        setEntryPoint(location);
      }
    }
    previousPathname.current = location.pathname;
    previousRouteFromQueryString.current = getRouteFromQueryString();
  }, [location, navigate]);
};

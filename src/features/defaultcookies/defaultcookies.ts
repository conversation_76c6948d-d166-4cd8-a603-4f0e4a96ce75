import {
  areTokensTransferred,
  deleteLegacyAnonToken,
  deleteLegacyLanguage,
  deleteLegacyUserToken,
  deleteLegacyVideoQualityCookie,
  getLegacyAnonToken,
  getLegacyLanguage,
  getLegacyUserToken,
  getLegacyVideoQualityCookie,
  getUuid,
  getVideoQualityCookie,
  hasAnonymousToken,
  hasLanguage,
  hasUserToken,
  setAnonymousToken,
  setLanguage,
  setTokensTransferred,
  setUserToken,
  setVideoQualityCookie,
} from '@util/cookies';
import { VideoQuality } from '@util/videoQuality';

/**
 * Copies and deletes legacy cookies.
 */
const transferLegacyCookies = () => {
  if (getLegacyLanguage() && !hasLanguage()) {
    setLanguage(getLegacyLanguage() as string);
    deleteLegacyLanguage();
  }

  if (getLegacyVideoQualityCookie() && !getVideoQualityCookie()) {
    setVideoQualityCookie(getLegacyVideoQualityCookie() as VideoQuality);
    deleteLegacyVideoQualityCookie();
  }

  if (!areTokensTransferred()) {
    if (getLegacyUserToken() && !hasUserToken()) {
      setUserToken(getLegacyUserToken() as string);
      deleteLegacyUserToken();
      deleteLegacyAnonToken();
    } else {
      if (getLegacyAnonToken() && !hasAnonymousToken()) {
        setAnonymousToken(getLegacyAnonToken() as string);
        deleteLegacyAnonToken();
      }
    }
    setTokensTransferred();
  }
};

const setDefaultCookies = () => {
  transferLegacyCookies();

  getUuid();

  // Set a default video quality cookie if it does not exist
  if (!getVideoQualityCookie()) {
    setVideoQualityCookie(VideoQuality.MAX);
  }
};

export { setDefaultCookies };

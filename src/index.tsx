// import polyfills
import 'react-app-polyfill/ie9';
import 'react-app-polyfill/stable';
import 'abortcontroller-polyfill';
import 'window-location-origin';
import './i18n';
import './styles/reset.css';

import { overrideApiEnv } from '@util/apiOverride';
import { config, thirdPartyAuth } from 'target';

import { init as initApp } from './AppIndex';
import { init as initError } from './AppIndexError';

function init() {
  overrideApiEnv();

  if (config.hasAuthErrorQueryParams) {
    initError();
  } else {
    if (thirdPartyAuth.enabled) {
      thirdPartyAuth.auth().then((result) => {
        if (result) {
          initApp();
        }
      });
    } else {
      initApp();
    }
  }
}

init();

# SmartTV Deployments from GitHub Repository

## Available Workflows

The main SmartTV Repository holds the Meta-Workflow named [`📺 SmartTV Meta Deploy`](../.github/workflows/smarttv-meta-deploy.yml).

All SmartTV Flavor Repositories hold the Workflow named [`📺 SmartTV Deploy`](https://github.com/ArteGEIE/workflow-spreader/blob/master/workflows/hbbtv/smarttv-deploy.yml).

## Configuration

### Main `ArteGEIE/SmartTV` Repository

#### Configuring Flavors

The main `ArteGEIE/SmartTV` Repository must have a list of all Flavor Repositories. This is done directly in the Meta-Workflow YAML file, by setting the list as a JSON array format under the `SMARTTV_FLAVORS` environment variable :

```yaml
env:
  SMARTTV_FLAVORS: '["SmartTV-HbbTV","SmartTV-HTML5-v4","SmartTV-Orange","SmartTV-Panasonic-v4","SmartTV-Philips-v4","SmartTV-Sky","SmartTV-Tizen","SmartTV-WebOS"]'
```

Note that the `ArteGEIE` Organization is missing. It's normal as it's automatically added by the Workflow itself.

#### Configuring Build Targets

The project [`package.json`](../package.json) must have a build script for each registered flavor :

```json
{
  // ...
  "scripts": {
    // if no resolution-specific builds
    "build:{flavor_code}": "{command}",                            // for development environment
    "build:{flavor_code}:preproduction": "{command}",              // for preproduction environment
    "build:{flavor_code}:production": "{command}",                 // for production environment
    // if resolution-specific builds
    "build:{flavor_code}:{resolution}": "{command}",               // for development environment and a specific resolution
    "build:{flavor_code}:preproduction:{resolution}": "{command}", // for preproduction environment and a specific resolution
    "build:{flavor_code}:production:{resolution}": "{command}",    // for production environment and a specific resolution
  }
  // ...
}
```

The project [`Makefile`](../Makefile) must have these targets for each registered flavor :

| `Makefile` Target                             | Description                                                                                                               |
| --------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------- |
| `build-{flavor}-{environment}`                | Build the project `{flavor}` for a specific `{environment}`.                                                              |
| `deploy-{flavor}-{environment}`               | Deploys the project `{flavor}` on `{environment}`. Only for non-resolution variants flavors.                              |
| `deploy-{flavor}-{resolution}-{environment}`  | Deploys the project `{flavor}` on `{environment}` for a specific `{resolution}`.                                          |
| `release-{flavor}-{environment}`              | Create a Release tarball for project `{flavor}` for a specific `{environment}`. Only for non-resolution variants flavors. |
| `release-{flavor}-{resolution}-{environment}` | Create a Release tarball for project `{flavor}` for a specific `{environment}` and `{resolution}`.                        |

For example : 

```
build-hbbtv-dev
deploy-hbbtv-dev
deploy-webos-720-dev
release-tizen-dev
```

#### Generating `.env`

The `.env` file for each flavor is automatically generated by the `set-build-config` target in [`Makefile`](../Makefile).

It will try to detect the `{flavor}`, `{resolution}` and `{environment}` from the context of `make release-{flavor}-{environment}`.

For flavors with no resolution variants :
  1. `.infrastructure/env/.env` file : generic `.env` file shared across all flavors
  2. `.infrastructure/env/.env.{flavor}.{environment}` : specific `.env` file for `{flavor}` and `{environment}`

```bash
# Example
#   flavor      : tizen
#   environment : dev
cat .infrastructure/env/.env .infrastructure/env/.env.tizen.dev > .env
```

For flavors with resolution variants :
  1. `.infrastructure/env/.env` file : generic `.env` file shared across all flavors
  2. `.infrastructure/env/.env.{flavor}.{resolution}.{environment}` : specific `.env` file for `{flavor}`, `{environment}` and `{resolution}`

```bash
# Example
#   flavor      : webos
#   resolution  : 720
#   environment : dev
cat .infrastructure/env/.env .infrastructure/env/.env.webos.720.dev > .env
```

#### Environments

The main `ArteGEIE/SmartTV` Repository has only three registered environments : 
 - `dev`
 - `preprod`
 - `prod`

### Flavor Repositories

Each Flavor Repositories must have at least one Repository Variable `SMARTTV_FLAVOR` holding the flavor short-code, used in target names in the `Makefile`.

Additionaly, if a Flavor has multiple resolution variants, a second Repository Variable `SMARTTV_RESOLUTIONS` has to be set, holding a list of resolution variants in JSON array format.

Here are the current values :

| Repository                                                                        | `SMARTTV_FLAVOR` | `SMARTTV_RESOLUTIONS` |
| --------------------------------------------------------------------------------- | ---------------- | --------------------- |
| [ArteGEIE/SmartTV-HbbTV](https://github.com/ArteGEIE/SmartTV-HbbTV)               | `hbbtv`          | N/A                   |
| [ArteGEIE/SmartTV-HTML5-v4](https://github.com/ArteGEIE/SmartTV-HTML5-v4)         | `html5`          | N/A                   |
| [ArteGEIE/SmartTV-Orange](https://github.com/ArteGEIE/SmartTV-Orange)             | `orange`        | N/A                   |
| [ArteGEIE/SmartTV-Panasonic-v4](https://github.com/ArteGEIE/SmartTV-Panasonic-v4) | `panasonic`      | N/A                   |
| [ArteGEIE/SmartTV-Philips-v4](https://github.com/ArteGEIE/SmartTV-Philips-v4)     | `philips`        | N/A                   |
| [ArteGEIE/SmartTV-Sky](https://github.com/ArteGEIE/SmartTV-Sky)                   | `sky`            | N/A                   |
| [ArteGEIE/SmartTV-Tizen](https://github.com/ArteGEIE/SmartTV-Tizen)               | `tizen`          | N/A                   |
| [ArteGEIE/SmartTV-WebOS](https://github.com/ArteGEIE/SmartTV-WebOS)               | `webos`          | `["720","1080"]`      |

#### Environments

All Flavor Repositories have at least three registered environments :
 - `dev`
 - `preprod`
 - `prod`

Some Flavors have resolution variants, so they hold more than three environments. Will be added :
 - `dev-{resolution}`
 - `preprod-{resolution}`
 - `prod-{resolution}`

These resolution environments can be used to trigger a deployment only for a specific resolution.  
In these Repositories, if you trigger a deployment on `dev` environment, it will in reality deploy all resolution variants on `dev-{resolution}` environments.  

## Triggers

The Deployment of SmartTV can be triggered by multiple ways detailed below.

### Directly from `ArteGEIE/SmartTV` Repository

The Meta-Workflow in `ArteGEIE/SmartTV` Repository will call each Deploy Workflow in Flavor Repositories.

#### Automatic Triggers

1. Commiting/Merging a PR to `main` : will trigger all flavors to deploy to `preprod` environments.
2. Commiting/Merging a PR to `develop` : will trigger all flavors to deploy to `dev` environments.
3. Publishing a new Release : will trigger all flavors to deploy to `prod` environments.

#### Manual Trigger

You can also trigger the meta-workflow manually with the two mandatory inputs :
  - `ref` : Reference of the `ArteGEIE/SmartTV` Repository (tag, branch name, commit sha1)
  - `environment` : One of the available environments (`dev`, `preprod` or `prod`)

### From Flavor Repositories

The meta-workflow triggers each flavor repository deploy workflow, but you can also trigger these manually.  

#### Manual Trigger

Once on the Workflow run form, fill it out with :
  - `ref` : Reference of the `ArteGEIE/SmartTV` Repository (tag, branch name, commit sha1)
  - `environment` : On of the available environments

## Flow

```mermaid
flowchart LR
    M[ArteGEIE/SmartTV]

    M-->F1
    M-->F2
    M-->F3
    M-->F4
    M-->F5
    M-->F6
    M-->F7
    M-->F8

    subgraph Flavors
        subgraph HbbTV
            F3[ArteGEIE/SmartTV-HbbTV]
            F3-->F3E1
            F3-->F3E2
            F3-->F3E3
            subgraph F3E[Environments]
                F3E1[dev]
                F3E2[preprod]
                F3E3[prod]
            end
        end

        subgraph Tizen
            F1[ArteGEIE/SmartTV-Tizen]
            F1-->F1E1
            F1-->F1E2
            F1-->F1E3
            subgraph F1E[Environments]
                F1E1[dev]
                F1E2[preprod]
                F1E3[prod]
            end
        end

        subgraph HTML5
            F2[ArteGEIE/SmartTV-HTML5-v4]
            F2-->F2E1
            F2-->F2E2
            F2-->F2E3
            subgraph F2E[Environments]
                F2E1[dev]
                F2E2[preprod]
                F2E3[prod]
            end
        end

        subgraph WebOS
            F4[ArteGEIE/SmartTV-WebOS]
            F4-->F4E1R1
            F4-->F4E1R2
            F4-->F4E2R1
            F4-->F4E2R2
            F4-->F4E3R1
            F4-->F4E3R2
            subgraph F4E[Environments]
                subgraph F4E1[dev]
                    F4E1R1[720]
                    F4E1R2[1080]
                end
                subgraph F4E2[preprod]
                    F4E2R1[720]
                    F4E2R2[1080]
                end
                subgraph F4E3[prod]
                    F4E3R1[720]
                    F4E3R2[1080]
                end
            end
        end

        subgraph Sky
            F5[ArteGEIE/SmartTV-Sky]
            F5-->F5E1
            F5-->F5E2
            F5-->F5E3
            subgraph F5E[Environments]
                F5E1[dev]
                F5E2[preprod]
                F5E3[prod]
            end
        end

        subgraph Orange
            F6[ArteGEIE/SmartTV-Orange]
            F6-->F6E1
            F6-->F6E2
            F6-->F6E3
            subgraph F6E[Environments]
                F6E1[dev]
                F6E2[preprod]
                F6E3[prod]
            end
        end

        subgraph Philips
            F7[ArteGEIE/SmartTV-Philips-v4]
            F7-->F7E1
            F7-->F7E2
            F7-->F7E3
            subgraph F7E[Environments]
                F7E1[dev]
                F7E2[preprod]
                F7E3[prod]
            end
        end

        subgraph Panasonic
            F8[ArteGEIE/SmartTV-Panasonic-v4]
            F8-->F8E1
            F8-->F8E2
            F8-->F8E3
            subgraph F8E[Environments]
                F8E1[dev]
                F8E2[preprod]
                F8E3[prod]
            end
        end
    end
```

---
EOF

const { execSync } = require('child_process');
const featureFlags = require('../featureFlags.json');

function getEnv() {
  return process.env.NODE_ENV || 'development';
}

function getTarget() {
  // default to html5 target - this prevents any confusion if we accidentally
  // run `npm run dev` locally and there is no target specified
  if (!process.env.TARGET) process.env.TARGET = 'html5';
  return process.env.TARGET;
}

function getBranchName() {
  try {
    return execSync('git rev-parse --abbrev-ref HEAD').toString().trim();
  } catch (error) {
    console.error('Error retrieving branch name:', error);
    return '';
  }
}

function getCommitHash() {
  try {
    return execSync('git rev-parse --short HEAD').toString().trim();
  } catch (error) {
    console.error('Error retrieving commit hash:', error);
    return '';
  }
}

/**
 * Returns an array of feature flags for the current target and environment.
 *
 * @return {Array<string>}
 */
function getFeatureFlags() {
  const target = getTarget();
  const env = getEnv();
  const allowedFeatureFlags = [];
  for (const flag in featureFlags) {
    if (featureFlags[flag]?.[target]?.[env]) allowedFeatureFlags.push(flag);
  }
  return allowedFeatureFlags;
}

module.exports = {
  getBranchName,
  getCommitHash,
  getEnv,
  getFeatureFlags,
  getTarget,
};

# Webpack Configuration

This is a basic file structure for a webpack configuration that includes files for development, production, and base configurations. The intention is to iterate on this config structure to implement MVP where we can produce builds for different targets.

```
webpack
├── base.config.js
├── development.config.js
├── paths.js
├── plugins
│   ├── dotenv-webpack.config.js
│   ├── html-webpack-plugin.config.js
│   └── terser-webpack-plugin.config.js
└── production.config.js
```

## Files

### base.config.js

This file contains the base configuration for webpack. It specifies the entry point and output destination of the project, as well as any loaders or plugins that need to be used. This file is used as a starting point for the development and production configurations.

### development.config.js

This file contains the configuration for webpack when building the project in development mode. It extends the base configuration and specifies additional settings for development, such as source maps and dev server.

### plugins/dotenv-webpack.config.js

This file contains the configuration for the dotenv-webpack plugin. This plugin loads environment variables from a .env file into process.env, making them available at runtime.

### plugins/html-webpack-plugin.config.js

This file contains the configuration for the html-webpack-plugin. This plugin generates an HTML file for the project with the correct script and link tags automatically injected.

### plugins/terser-webpack-plugin.config.js

This file contains the configuration for the terser-webpack-plugin. This plugin minifies the JavaScript code for production builds.

### production.config.js

This file contains the configuration for webpack when building the project in production mode. It extends the base configuration and specifies additional settings for production, such as optimizations for file size and caching.

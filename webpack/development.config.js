const { merge } = require('webpack-merge');

const baseConfig = require('./base.config.js');
const HtmlWebpackPlugin = require('./plugins/html-webpack-plugin.config.js');
const Dotenv = require('./plugins/dotenv-webpack.config.js');
const DefinePlugin = require('./plugins/define-plugin.config.js');
const CopyWebpackPlugin = require('./plugins/copy-webpack-plugin.config.js');

module.exports = () => {
  const config = merge(baseConfig(), {
    mode: 'development',
    devServer: {
      historyApiFallback: true,
      client: {
        overlay: false,
      },
    },
    plugins: [CopyWebpackPlugin, Dotenv, HtmlWebpackPlugin, DefinePlugin],
  });

  return config;
};

const { merge } = require('webpack-merge');
const path = require('path');
const { getTarget } = require('./utils');
const loadPresets = require('./presets/loadPresets');

const paths = {
  src: '../src',
  dist: '../dist',
  styles: '../src/styles/',
  assets: '../src/assets/',
};

const target = getTarget();

module.exports = () => {
  const config = {
    entry: path.join(__dirname, paths.src, 'index.tsx'),
    output: {
      path: path.resolve(__dirname, paths.dist),
      publicPath: process.env.REACT_ROUTER_BASE_PATH || '/',
    },
    module: {
      rules: [
        {
          test: /\.(js|ts)x?$/,
          exclude: /@babel(?:\/|\\{1,2})runtime|core-js/,
          loader: 'babel-loader',
        },
        {
          test: /\.css$/i,
          use: ['style-loader', 'css-loader'],
        },
        {
          test: /\.(scss|sass)$/,
          use: ['style-loader', 'css-loader', 'sass-loader'],
        },
        {
          test: /\.(png|jp(e*)g|svg|gif)$/,
          type: 'asset',
          parser: {
            dataUrlCondition: {
              // only inline images of maxSize
              // this is important as inlined images will bloat the main bundle
              maxSize: 2 * 1024, // 2KB
            },
          },
        },
        {
          test: /\.json$/,
          use: 'json-loader',
          type: 'javascript/auto',
        },
        {
          test: /\.(ttf)$/,
          type: 'asset/resource',
          generator: {
            filename: 'static/fonts/[name][ext]',
          },
        },
      ],
    },
    resolve: {
      extensions: ['.tsx', '.ts', '.js', '.jsx'],
      alias: {
        '@apptypes': path.resolve(__dirname, '../src/types/'),
        '@assets': path.resolve(__dirname, '../src/assets/'),
        '@components': path.resolve(__dirname, '../src/components/'),
        '@constants': path.resolve(__dirname, '../src/constants.ts'),
        '@data': path.resolve(__dirname, '../src/data/'),
        '@errors': path.resolve(__dirname, '../src/errors/'),
        '@featureflags': path.resolve(__dirname, '../src/featureflags/'),
        '@features': path.resolve(__dirname, '../src/features/'),
        '@hooks': path.resolve(__dirname, '../src/hooks/'),
        '@i18n': path.resolve(__dirname, '../src/i18n.ts'),
        '@libraries': path.resolve(__dirname, '../src/libraries/'),
        '@providers': path.resolve(__dirname, '../src/providers/'),
        '@routes': path.resolve(__dirname, '../src/routes/'),
        '@tracking': path.resolve(__dirname, '../src/tracking/'),
        '@util': path.resolve(__dirname, '../src/util/'),
        '@videoplayer': path.resolve(__dirname, '../src/videoplayer/'),
        styles: path.resolve(__dirname, paths.styles),
        target: path.resolve(__dirname, `../target/${target}/`),
      },
    },
  };

  return merge(config, loadPresets());
};

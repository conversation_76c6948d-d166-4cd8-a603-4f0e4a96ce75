require('dotenv').config();
const HtmlWebpackPlugin = require('html-webpack-plugin');
const path = require('path');
const { getTarget, getCommitHash, getBranchName, getEnv } = require('../utils');
const { getHeadFragment, getBodyFragment } = require('../../target/fragments/fragments.ts');

const target = getTarget();
console.log('target', target);
const templatePath = path.join(__dirname, `../../target/${target}/index.html`);

const metadata =
  getEnv() !== 'production'
    ? {
        hash: getCommitHash(),
        target: getTarget(),
        branch: getBranchName(),
      }
    : undefined;

module.exports = new HtmlWebpackPlugin({
  template: templatePath,
  inject: 'body',
  bodyClass: process.env.RESOLUTION === '720' ? 'res-720' : '',
  basePath: process.env.REACT_ROUTER_BASE_PATH,
  headFragment: getHeadFragment(),
  bodyFragment: getBodyFragment(metadata),
  scriptLoading: 'blocking',
  minify: {
    useShortDoctype: false,
  },
});

const CopyWebpackPlugin = require('copy-webpack-plugin');
const path = require('path');

module.exports = new CopyWebpackPlugin({
  patterns: [
    { from: path.resolve(__dirname, '../../locales'), to: 'locales' },
    { from: path.resolve(__dirname, '../../src/libraries/nielsen.js'), to: 'libraries/nielsen.js' },
    { from: path.resolve(__dirname, '../../src/libraries/mu-7.2.js'), to: 'libraries/mu-7.2.js' },
    { from: path.resolve(__dirname, '../../src/libraries/vtt.min.js'), to: 'libraries/vtt.min.js' },
    {
      from: path.resolve(__dirname, '../../src/libraries/einbliqio.hbbtv.v1.4.0.min.js'),
      to: 'libraries/einbliqio.hbbtv.v1.4.0.min.js',
    },

    { from: path.resolve(__dirname, '../../src/assets/img/gifnocache/'), to: 'gifnocache' },
    { from: path.resolve(__dirname, '../../src/assets/img/arte-logo.png'), to: 'img/arte-logo.png' },
    {
      from: path.resolve(__dirname, '../../src/assets/img/arte-sample-rectangle.png'),
      to: 'img/arte-sample-rectangle.png',
    },
    { from: path.resolve(__dirname, '../../src/assets/img/no-user-icon.png'), to: 'img/no-user-icon.png' },
    { from: path.resolve(__dirname, '../../src/assets/img/icon/tada.png'), to: 'img/tada.png' },
  ],
});

const webpack = require('webpack');
const { getCommitHash, getBranchName, getFeatureFlags } = require('../utils');
const packageJson = require('../../package.json');

module.exports = new webpack.DefinePlugin({
  'process.env.TARGET': JSON.stringify(process.env.TARGET),
  'process.env.BRANCH_NAME': JSON.stringify(getBranchName()),
  'process.env.COMMIT_HASH': JSON.stringify(getCommitHash()),
  'process.env.FEATURE_FLAGS': JSON.stringify(getFeatureFlags()),
  'process.env.VERSION': JSON.stringify(packageJson.version),
});

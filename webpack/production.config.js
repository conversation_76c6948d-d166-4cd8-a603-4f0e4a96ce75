require('dotenv').config();

const { merge } = require('webpack-merge');
const baseConfig = require('./base.config.js');
const Dotenv = require('./plugins/dotenv-webpack.config.js');
const HtmlWebpackPlugin = require('./plugins/html-webpack-plugin.config.js');
const TerserPlugin = require('./plugins/terser-webpack-plugin.config.js');
const DefinePlugin = require('./plugins/define-plugin.config');
const CopyWebpackPlugin = require('./plugins/copy-webpack-plugin.config.js');
const { getEnv } = require('./utils');

module.exports = () => {
  return merge(baseConfig(), {
    mode: getEnv() === 'production' ? 'production' : 'development',
    output: {
      filename: '[name].[chunkhash].js',
      publicPath: process.env.REACT_ROUTER_BASE_PATH,
    },
    optimization: {
      minimize: true,
      minimizer: [TerserPlugin],
      splitChunks: {
        chunks: 'all',
      },
    },
    plugins: [CopyWebpackPlugin, Dotenv, HtmlWebpackPlugin, DefinePlugin],
  });
};

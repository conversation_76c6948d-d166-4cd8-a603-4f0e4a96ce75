# SmartTV

SmartTV (v11+) application

## Building the Application

By default, the application is built using the `html5` target. To start the development server, run the following command:

```
npm run dev
```

To build the application for a specific target, set the `TARGET` environment variable followed by the target name before running the development server command. For example:

```
TARGET=hbbtv npm run dev       # build for HbbTV
TARGET=tizen npm run dev       # build for Tizen
TARGET=webos npm run dev       # build for webOS
```

This will set the `TARGET` environment variable to the desired target and then run the development server command. This approach allows you to easily switch between different targets during development.

## webOS

For webOS development on a device you need to install the CLI tools on your PC and the developer mode app on your TV.

### webOS TV CLI

The webOS TV CLI (Command Line Interface) is a tool that allows developers to package, install, and launch applications on LG webOS TVs. Follow the [instructions](https://webostv.developer.lge.com/develop/tools/cli-installation) to download and install.

### Developer mode app

The Developer Mode app is a tool that simplifies the installation, debugging, and testing of webOS TV apps directly on the TV. It acts as a bridge between your TV and PC, enabling a seamless connection for efficient development workflows. Follow the [installation instructions](https://webostv.developer.lge.com/develop/getting-started/developer-mode-app) to install the app on your TV and connect the TV to your PC.

### Package and install the app on a device

Note that the app is a hosted web app, you can read more about the web app types for webOS [here](https://webostv.developer.lge.com/develop/getting-started/web-app-types#hosted-web-app).

For local development add the url of your local build in your `.env` file e.g.

```shell
WEBOS_HOSTED_URL=http://************:8080/
```

The `WEBOS_HOSTED_URL` variable will be used in the package that gets installed on the TV allowing you to run your local app on the device.

To package the app run...

```shell
npm run package:webos
```

The output of the script will be in `/bin` and is an `.ipk` file; this is the file you need install on the TV.

### Installing app on TV

Firstly make sure your `webOS` build is running...

```shell
npm run dev:webos
```

Now, assuming your PC and TV are connected, install the app using the CLI tools...

```shell
ares-install -d <device_name> <path_to_ipkj>
```

For example, if you named the device `lg` and the path to the `.ipk` is `bin/arte-webos-1080.ipk`...

```shell
ares-install -d lg bin/arte-webos-1080.ipk
```

Once installed, launch the app...

```shell
ares-launch com.arte.app -d lg
```

To close the app...

```shell
ares-launch com.arte.app -cd lg
```

For remote debugging run the following command, the output will give you a url, open in a browser for remote debugging.

```shell
ares-inspect com.arte.app -d lg
```

### Generate ipk files for all envs

**Note that these are not the production ipk files, they are used for QA and developer convenience.**

Generate ipk files for all environments and resolutions with the following command...

```shell
npm run package:webos:all
```

Files are written to `/ipk`. Each package has a unique id so that all of them can be installed on a tv at once. The logo for each package shows the resolution and environment to make it clear what you are testing.

Assuming you have the webOS TV CLI tools setup you can install all the packages as follows....

```shell
ares-install -d <device_name> ipk/arte-webos-dev-1080.ipk
ares-install -d <device_name> ipk/arte-webos-dev-720.ipk
ares-install -d <device_name> ipk/arte-webos-preprod-1080.ipk
ares-install -d <device_name> ipk/arte-webos-preprod-720.ipk
ares-install -d <device_name> ipk/arte-webos-prod-1080.ipk
ares-install -d <device_name> ipk/arte-webos-prod-720.ipk
ares-install -d <device_name> ipk/arte-webos-qa-1080.ipk
ares-install -d <device_name> ipk/arte-webos-qa-720.ipk
```

## Tests

### Unit Tests

We use [vitest](https://vitest.dev/) for unit tests.

```shell
npm test
```

## Environment variable substitution for client-side and server-side

A single `.env` file is used to store environment variables, this file should not be committed to the repository. Environment variables specific to staging and production environments etc will be defined separately as part of the CI/CD processes.

### Create a .env file

Copy the example file provided in the repository as a starting point:

```bash
cp .env.example .env
```

Update the values in `.env` as needed for your local development environment.

### Client-side variable substitution with dotenv-webpack

Client-side variable substitution involves injecting environment variables into client-side code during the build process. This is achieved using `dotenv-webpack`. During the build process, `dotenv-webpack` will load the environment variables from the `.env` file and make them available in your client-side code.

### Server-side variable substitution with dotenv

Server-side variable substitution involves using the `dotenv` package to load environment variables into your server-side code - for our purposes this is necessary for build scripts e.g. running a script via node as part of the build process. Once the environment variables are loaded, you can access them in your server-side code using `process.env.VARIABLE_NAME`.

## Playback

To make a successful player config call, the request must come from one of the accepted origins e.g arte.tv or arte.local. For development purposes either edit the hosts file on your local machine or use a browser extension to avoid cross origin errors.

`DashJS` is being used, see `package.json` for current version.

## Feature Flags

Feature flags are defined in `featureFlags.json`. There is an entry for each feature flag. Each feature flag will list the targets and the corresponding environments.

```json
    "SMARTTV_FEATURE_FLAGS_EVENT_TEASER": {
        "hbbtv": {
            "development": true,
            "preproduction": true,
            "production": false
        },
        "webos": {
            "development": false,
            "preproduction": false,
            "production": false
        }
    }
```

Please note that environments set to `false` are included here for clarity, even though they don't require explicit listing.

### Usage

Feature flag data is parsed and filtered during the build process. On the client side, you can access the feature flags for the specified target and environment using `process.env.FEATURE_FLAGS`. To simplify usage, a helper utility is available at `src/featureflags/featureFlags.ts`.

When adding a new feature flag to `featureFlags.json`, make sure to add it in `featureFlags.ts` for type safety.

## Deploy

For informations about how SmartTV Project is deployed, please read the [dedicated documentation](docs/README.Deploy.md).

## Query string parameters

Test application behaviour via query string parameters.

### lang

Test the application with different languages via the `lang` parameter.

- `?lang=fr`
- `?lang=de`

See `src/i18n.ts` for a list of supported languages.

### debug

Launch the application with a debug overlay.

- `?debug=true`

### remap_color_keys

Remaps colour keys to use keyboard (b, g, r, y) when testing in browser.

- `?remap_color_keys=true`

### tvmid

Request middleware data from different environments via the `tvmid` parameter.

- `?tvmid=dev`
- `?tvmid=preprod`
- `?tvmid=prod`

### playerapi

Make player requests using different endpoints via the `playerapi` parameter.

- `?playerapi=dev`
- `?playerapi=preprod`
- `?playerapi=prod`

### app

`app=portalfr`

Channel 77 in French TV, is a dedicated channel where users can launch the app directly. We identify this via a query param: `app=portalfr`

In this case we do not display an exit button on the main menu and the back button journey to exit the app is disabled. To leave the app the user has to change the channel.

### emac

Setting a query param `emac` allows the middleware to request either prepod or prod emac environment.

- `?emac=preprod`
- `?emac=prod`

### stream

For testing purposes we can test DASH streams via query params. Using this will overwrite the stream so selecting any content in the UI will result in playing the stream specified in the query param.

- `?stream=https%3A%2F%2Farte-uhd-cmafhls.akamaized.net%2Ftests%2Ftest_new_encodes%2F115000%2F115000%2F115045-000-A_v1%2F115045-000-A_VF.mpd`

For a live stream add the following.

- `?live=true`

### sso

Use preprod sso: http://smarttv-preprod.arte.tv/api/sso/v3

- `?sso=preprod`

Note that you can test this as an anonymous user but will not be able to login. Logging in requires getting a code from a different environment and you must be on the Arte VPN.

### error and description

**NOTE** these params apply to the **ORANGE TARGET ONLY** and are different in that they are not used for modifying behaviour for testing purposes, rather they inform the application to behave differently at startup and display an error message to prevent the user from seeing any content.

The Orange authentication workflow requires the client to be redirected through an 'authentication portal', this portal will check the network to verify that the client is an Orange customer.

If an error occurs with authentication, the user can be redirected to the SmartTV app with specific query params added to the url e.g.

- `?error=access_denied&error_description=login%20failed`

When these params are detected at startup the application shows an error message only and does not load any content.

## Webpack Bundle Analyzer

This will help you:

- Realize what's really inside your bundle
- Find out what modules make up the most of its size
- Find modules that got there by mistake
- Optimize it!

```
npm run build:analyze
```

## Spinner

The spinner uses a **progressive enhancement** approach.

- **Modern Devices:**  
  Uses CSS animations with hardware-accelerated transforms for smooth, efficient rotation.

- **Fallback for Limited Devices:**  
  Displays an optimized animated GIF spinner when CSS animations or transforms are unsupported.

## Fallback GIF Details

Created with ImageMagick...

```
magick -size 64x64 xc:#151515 -stroke white -strokewidth 3 -fill none \
  -set delay 5 \
  -loop 0 \
  \( -clone 0 -draw "arc 8,8 56,56 0,90" \) \
  \( -clone 0 -draw "arc 8,8 56,56 30,120" \) \
  \( -clone 0 -draw "arc 8,8 56,56 60,150" \) \
  \( -clone 0 -draw "arc 8,8 56,56 90,180" \) \
  \( -clone 0 -draw "arc 8,8 56,56 120,210" \) \
  \( -clone 0 -draw "arc 8,8 56,56 150,240" \) \
  \( -clone 0 -draw "arc 8,8 56,56 180,270" \) \
  \( -clone 0 -draw "arc 8,8 56,56 210,300" \) \
  \( -clone 0 -draw "arc 8,8 56,56 240,330" \) \
  \( -clone 0 -draw "arc 8,8 56,56 270,0"   \) \
  \( -clone 0 -draw "arc 8,8 56,56 300,30"  \) \
  \( -clone 0 -draw "arc 8,8 56,56 330,60"  \) \
  -delete 0 spinner.gif
```

{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "react-jsx", "baseUrl": "./", "paths": {"@apptypes/*": ["src/types/*"], "@assets/*": ["src/assets/*"], "@components/*": ["src/components/*"], "@constants": ["src/constants.ts"], "@data/*": ["src/data/*"], "@errors/*": ["src/errors/*"], "@featureflags/*": ["src/featureflags/*"], "@features/*": ["src/features/*"], "@hooks/*": ["src/hooks/*"], "@i18n/*": ["src/i18n.ts"], "@libraries/*": ["src/libraries/*"], "@providers/*": ["src/providers/*"], "@routes/*": ["src/routes/*"], "@styles/*": ["src/styles/*"], "@tracking/*": ["src/tracking/*"], "@util/*": ["src/util/*"], "@videoplayer/*": ["src/videoplayer/*"]}}, "include": ["src", "src/types/target", "./node_modules/hbbtv-typings"], "exclude": ["**/*.test.ts", "**/*.karma.ts"]}
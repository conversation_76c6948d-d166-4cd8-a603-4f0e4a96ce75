#!/bin/bash
############################################
################### USAGE ##################
############################################
# destRootPath:
# 		path to project root on destination server
#			fyi: a directory called "releases" will automatically be created in destRootPath
# user:
# 		ssh user to use
#
# Usage: deploy.sh IP[:PORT] [destinationPath]
#		deploy.sh HOSTNAME[:PORT] [destinationPath]

destRootPath="/data/www/root/static-dev.arte.tv/static/hbbtvv11"
user="arte"

############################################
####### DO NOT CHANGE ANYTHING BELOW #######
############################################
set -e

distantServer=$1
destRootPath=$2
releasesPath="$destRootPath/releases"
releasesWanted=3

if [ -z $distantServer ] || [ -z $destRootPath ]; then
	echo "Missing parameter"
	echo "Usage: deploy.sh HOSTNAME[:PORT] FOLDER_TO_DEPLOY_TO ENVIRONMENT_TO_DEPLOY_TO"
	exit 1
fi

now=$(date +'%Y%m%d%H%M%S')

# replace paths in index.html to point to `releases/timestamped` dir
# e.g
# <script src="/static/hbbtvv11/current/827.88193778124a5baccd42.js"></script>
# becomes...
# <script src="/static/hbbtvv11/releases/20240229134239/827.88193778124a5baccd42.js"></script>
pathToIndex="./index.html"
if [ ! -f "$pathToIndex" ]; then
  echo "The specified path to index.html ($pathToIndex) does not exist."
  exit 1
fi
pathToReplace="current"
replaceWith="releases\/${now}"
sed -i "s/\(src=\"[^\"]*\/\)${pathToReplace}\/\([^\"]*\"\)/\1${replaceWith}\/\2/g" "${pathToIndex}"

# Starting deployment
echo "# Starting deployment to $releasesPath/$now"
rsync --exclude 'ci' --exclude "Makefile" --rsync-path="mkdir -p $releasesPath && rsync" -vFarl --progress . $user@$distantServer:$releasesPath/$now

# Creating symlink
echo "# Creating symlink in $releasesPath/$now"
ssh $user@$distantServer "ln -sfn $releasesPath/$now $destRootPath/current"

# Removing surplus releases
directoriesTotal=$(ssh $user@$distantServer "ls -l $releasesPath | grep -c ^d")
if [ $releasesWanted -gt 0 ] && [ "$directoriesTotal" -gt "$releasesWanted" ]; then
	AmountOfDirectoriesToRemove=`expr $directoriesTotal - $releasesWanted`
	echo "# Removing $AmountOfDirectoriesToRemove legacy release(s)"
	ssh $user@$distantServer "cd $releasesPath && ls -r |tail -n-$AmountOfDirectoriesToRemove |xargs rm -Rf"
fi

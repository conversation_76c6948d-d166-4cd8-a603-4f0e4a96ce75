##################################################
## Github Action Workflow File
## Designed for ArteGEIE
##
## Description : Checks Conventional Naming for Commit Messages & Pull Requests Titles
##
## In order to ease changelog generation, we need to check commits. We have decided to relay on conventional commits:
##   https://www.conventionalcommits.org/en/v1.0.0/.
##
## This workflow will also check that every PR contains an associated JIRA ticket.
## Some exceptions are present (see below).
##
## For more details on how this workflow works, please refer to the GitHub Action :
##   https://github.com/ArteGEIE/github-actions-common/tree/master/check-git-conventions
##
## Each project using this workflow can override default configurations by setting variables in .infrastructure/env/.env file:
##
##     INFRA_JIRA_REFERENCE_MANDATORY : Make Jira Ticket Reference in PR title mandatory
##                                      Allowed values : true or false (strings)
##                                      Default value : true
##
##     INFRA_IGNORE_ROBOTS_COMMITS    : Ignore Robot Commits
##                                      Allowed values : true or false (strings)
##                                      Default value : true
##

name: "Check conventional naming"

on:
  pull_request:
    types: [opened, synchronize, edited]

jobs:
  check-conventions:
    name: Check Conventional Naming
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Get Configurations
        id: check_configuration
        run: |
          if [ -f .infrastructure/env/.env ]; then
            source .infrastructure/env/.env
          fi

          echo "jira_reference_mandatory=${INFRA_JIRA_REFERENCE_MANDATORY:-true}" >> $GITHUB_OUTPUT
          echo "ignore_robots_commits=${INFRA_IGNORE_ROBOTS_COMMITS:-true}"       >> $GITHUB_OUTPUT

      - name: Check Conventions
        uses: ArteGEIE/github-actions-common/check-git-conventions@0.29
        with:
          jira_reference_mandatory: ${{ steps.check_configuration.outputs.jira_reference_mandatory }}
          ignore_robots_commits: ${{ steps.check_configuration.outputs.ignore_robots_commits }}

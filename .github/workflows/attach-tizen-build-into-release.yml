##################################################
## GitHub Action Workflow File
## Designed for ArteGEIE
##
## Description :
##   This workflow attachs a Tizen build into GitHub release.
##

name: '📺 SmartTV Attach Tizen build into GitHub Release'

on:
  workflow_dispatch:
  release:
    types: [published]

env:
  TIZEN_STUDIO_VERSION: 5.1

jobs:
  build_tizen_app:
    name: Build Tizen App
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Setup
        run: |
          TIZEN_STUDIO="${GITHUB_WORKSPACE}/tizen-studio"
          INSTALLER="${GITHUB_WORKSPACE}/tizen-studio_${TIZEN_STUDIO_VERSION}.bin"
          echo "TIZEN_STUDIO=${TIZEN_STUDIO}" >> $GITHUB_ENV
          echo "INSTALLER=${INSTALLER}" >> $GITHUB_ENV

      - name: <PERSON>ache Tizen Studio Installer
        id: cache-tizen-studio-installer
        uses: actions/cache@v4
        with:
          path: ${{ env.INSTALLER }}
          key: tizen-studio-installer-${{ env.TIZEN_STUDIO_VERSION }}

      - name: Download Tizen Studio Installer
        if: steps.cache-tizen-studio-installer.outputs.cache-hit != 'true'
        run: |
          wget -nc -O "$INSTALLER"  "http://download.tizen.org/sdk/Installer/tizen-studio_${TIZEN_STUDIO_VERSION}/web-cli_Tizen_Studio_${TIZEN_STUDIO_VERSION}_ubuntu-64.bin"

      - name: Install Tizen Studio
        run: |
          chmod a+x "$INSTALLER"
          "$INSTALLER" --accept-license $TIZEN_STUDIO

          echo "$TIZEN_STUDIO/tools/ide/bin" >> $GITHUB_PATH
          echo "TIZEN STUDIO VERSION: ${TIZEN_STUDIO_VERSION}" >> $GITHUB_STEP_SUMMARY

      - uses: actions/setup-node@v4
        with:
          node-version: 18

      - name: Create Tizen profiles.xml for signature
        env:
          AUTHOR_CERT: ${{ secrets.TIZEN_AUTHOR_CERT }}
          AUTHOR_KEY: ${{ secrets.TIZEN_AUTHOR_KEY }}
          AUTHOR_PASSWORD: ${{ secrets.TIZEN_AUTHOR_PASSWORD }}
          DISTRIBUTOR_CERT: ${{ secrets.TIZEN_DISTRIBUTOR_CERT }}
          DISTRIBUTOR_KEY: ${{ secrets.TIZEN_DISTRIBUTOR_KEY }}
          DISTRIBUTOR_PASSWORD: ${{ secrets.TIZEN_DISTRIBUTOR_PASSWORD }}
          PRIVILEGE: public
        run: |
          AUTHOR_CERT_PATH="$GITHUB_WORKSPACE/author-cert.cer"
          echo -n "${AUTHOR_CERT}" | base64 -d >"$AUTHOR_CERT_PATH"

          AUTHOR_KEY_PATH="$GITHUB_WORKSPACE/author-key.p12"
          echo -n "${AUTHOR_KEY}" | base64 -d >"$AUTHOR_KEY_PATH"

          DISTRIBUTOR_CERT_PATH="$GITHUB_WORKSPACE/distributor-cert.cer"
          echo -n "${DISTRIBUTOR_CERT}" | base64 -d >"$DISTRIBUTOR_CERT_PATH"

          DISTRIBUTOR_KEY_PATH="$GITHUB_WORKSPACE/distributor-key.p12"
          echo -n "${DISTRIBUTOR_KEY}" | base64 -d >"$DISTRIBUTOR_KEY_PATH"

          GLOBAL_PROFILES_PATH="$(tizen cli-config -l | grep -i 'default.profiles.path=' | sed 's/^default\.profiles\.path=//g')"
          echo "GLOBAL_PROFILES_PATH=${GLOBAL_PROFILES_PATH}" >> $GITHUB_ENV

          cat <<EOF >"$GLOBAL_PROFILES_PATH"
          <?xml version="1.0" encoding="UTF-8" standalone="no"?>
          <profiles active="Arte" version="3.1">
              <profile name="Arte">
                  <profileitem ca="$AUTHOR_CERT_PATH" distributor="0" key="$AUTHOR_KEY_PATH" password="$AUTHOR_PASSWORD" rootca=""/>
                  <profileitem ca="$DISTRIBUTOR_CERT_PATH" distributor="1" key="$DISTRIBUTOR_KEY_PATH" password="$DISTRIBUTOR_PASSWORD" rootca=""/>
                  <profileitem ca="" distributor="2" key="" password="" rootca=""/>
              </profile>
          </profiles>
          EOF

          chmod a-w "$GLOBAL_PROFILES_PATH"

      - name: Build app
        env:
          TARGET: tizen
        run: |
          DOTENV_PATH="${GITHUB_WORKSPACE}/.env"
          cat "${GITHUB_WORKSPACE}/.infrastructure/env/.env" > "${DOTENV_PATH}"
          cat "${GITHUB_WORKSPACE}/.infrastructure/env/.env.tizen.prod" >> "${DOTENV_PATH}"
          sed -i 's/REACT_ROUTER_BASE_PATH=".*/REACT_ROUTER_BASE_PATH=""/g' "${DOTENV_PATH}"

          # Replace target version for dist/config.xml
          sed -i "s/to: \['.*/to: ['${GITHUB_REF_NAME}'],/g" "${GITHUB_WORKSPACE}/scripts/tizen/create-project.js"

          npm install
          make build-tizen-prod
          npm run package:tizen

          echo "----------------------------------"
          echo "dist/config.xml"
          echo "----------------------------------"
          cat "${GITHUB_WORKSPACE}/dist/config.xml"
          echo "----------------------------------"

          # Display build and error log
          if [ -f tizen-studio-data/cli/logs/cli.log ]; then
            cat tizen-studio-data/cli/logs/cli.log
          fi

      - uses: actions/upload-artifact@v4
        with:
          name: Arte.wgt
          path: dist/.buildResult/ARTE.wgt

      - name: Attach build to release assets
        if: github.event_name == 'release' && github.event.action == 'published'
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          echo "Attaching build to release ${{ github.ref_name }} ..."
          gh release upload ${{ github.ref_name }} dist/.buildResult/ARTE.wgt

      - name: Cleanup temporary files
        run: |
          rm -rf dist
          rm -rf $GLOBAL_PROFILES_PATH

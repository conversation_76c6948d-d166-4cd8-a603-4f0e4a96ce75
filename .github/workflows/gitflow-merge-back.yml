##################################################
## Github Action Workflow File
## Designed for ArteGEIE
##
## Description :
##
## This job creates a "merge back" PR on develop
## when a release in published.
##

name: "Merge back master on develop"


on:
  release:
    types: [published]

jobs:
  merge-back-pr:
    name: Create "merge back" PR on develop
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - uses: ArteGEIE/github-actions-common/debug@0.29

      - name: Create "merge back" PR
        run: |
          source .infrastructure/env/.env 2> /dev/null || true
          MAIN_BRANCH_NAME=${INFRA_MAIN_BRANCH_NAME:-master}

          git fetch

          DIFFS=$(git diff origin/develop...origin/${MAIN_BRANCH_NAME} | wc -c)
          if [ "${DIFFS}" -eq 0 ]; then
            echo "Merge back aborted, no changes needed to be merged into develop"
            exit 0;
          fi

          HEAD_BRANCH="merge-back-${RELEASE_TITLE:?}"

          git switch "${MAIN_BRANCH_NAME}"
          git switch -c "${HEAD_BRANCH}"
          git push -uf origin "${HEAD_BRANCH}"

          gh pr create \
            --base develop \
            --head "${HEAD_BRANCH}" \
            --title "Merge back: \"${RELEASE_TITLE:?}\"" \
            --body "
          _This PR was automatically created_
          see: [original release](${RELEASE_URL:?})
          " || true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          RELEASE_TITLE: ${{ github.event.release.name }}
          RELEASE_URL: ${{ github.event.release.url }}

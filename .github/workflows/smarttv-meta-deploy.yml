##################################################
## GitHub Action Workflow File
## Designed for ArteGEIE
##
## Description :
##   This workflow allows SmartTV deployment for all project flavors from the main Repository.
##

name: "📺 SmartTV Meta Deploy"

on:
  push:
    branches:
      - main
      - develop
      - qa
      - mirror-develop
  release:
    types:
      - published
  workflow_dispatch:
    inputs:
      ref:
        description: 'Commit SHA, tag or branch name to deploy'
        required: true
        default: 'develop'
      environment:
        type: environment
        description: 'Target Environment where the Deployment will be done'
        required: true

env:
  SMARTTV_FLAVORS: '["SmartTV-HbbTV","SmartTV-WebOS","SmartTV-Tizen","SmartTV-HTML5-v4","SmartTV-Sky","SmartTV-Orange","SmartTV-Philips-v4","SmartTV-Panasonic-v4"]'
  DEPLOY_WORKFLOW: 'smarttv-deploy.yml'

jobs:
  prepare:
    name: "🧰 Prepare Deployment"
    runs-on: ubuntu-latest
    outputs:
      repo_ref: ${{ steps.summary.outputs.repo_ref }}
      environment: ${{ steps.summary.outputs.environment }}
      flavors: ${{ steps.summary.outputs.flavors }}
    steps:
      - name: "📋 Deployment Summary"
        id: summary
        run: |
          # Check the trigger for this Workflow
          if [ "${EVENT_NAME}" == "workflow_dispatch" ]; then
            TRIGGER="manual"

            # Grab variables from Event Inputs
            REPO_REF="${{ github.event.inputs.ref }}"
            ENVIRONMENT="${{ github.event.inputs.environment }}"

            # Check if the Repository Ref is valid
            TARGET_EXISTS=$(gh api /repos/${{ github.repository }}/commits/${REPO_REF} 2> /dev/null > /dev/null \
              && echo "true" || echo "false")

            if [ "${TARGET_EXISTS}" == "false" ]; then
              echo "::error::Input Branch Name refers to an unexisting branch or SHA1 of ${{ github.repository }} Repository : ${REPO_REF}"
              exit 1
            fi

          else
            TRIGGER="auto"
            REPO_REF="${BRANCH_REF##*/}"

            if [[ "${EVENT_NAME}" == "release" && "${EVENT_ACTION}" == "published" ]]; then
              ENVIRONMENT="prod"
            else
              if [[ "${REPO_REF}" == "develop" || "${REPO_REF}" == "mirror-develop" ]]; then
                ENVIRONMENT="dev"
              elif [[ "${REPO_REF}" == "main" || "${REPO_REF}" =~ ^release/ ]]; then
                ENVIRONMENT="preprod"
              elif [ "${REPO_REF}" == "qa" ]; then
                ENVIRONMENT="qa"
              else
                echo "::error::Unknown Repository Reference : ${REPO_REF}"
                exit 1
              fi
            fi
          fi

          echo "| Deployment Summary  ||"                                                                                  >> $GITHUB_STEP_SUMMARY
          echo "| ------------------- | ------------------------------------------------------------------------------- |" >> $GITHUB_STEP_SUMMARY
          echo "| SmartTV Flavors     | \`$(echo "${SMARTTV_FLAVORS}" | jq -r 'join("` `")')\`                          |" >> $GITHUB_STEP_SUMMARY
          echo "| SmartTV Referential | [\`${REPO_REF}\`](https://github.com/${{ github.repository }}/tree/${REPO_REF}) |" >> $GITHUB_STEP_SUMMARY
          echo "| Trigger Type        | \`${TRIGGER}\`                                                                  |" >> $GITHUB_STEP_SUMMARY
          echo "| Target Environments | \`${ENVIRONMENT}\`                                                              |" >> $GITHUB_STEP_SUMMARY

          echo "environment=${ENVIRONMENT}"                           >> $GITHUB_OUTPUT
          echo "repo_ref=${REPO_REF}"                                 >> $GITHUB_OUTPUT
          echo "flavors=$(echo '${{ env.SMARTTV_FLAVORS }}' | jq -c)" >> $GITHUB_OUTPUT

          echo "A Summary has been added to the workflow run."
        env:
          GITHUB_TOKEN: ${{ secrets.GH_PAT_READ_PRIVATE_REPO }}
          BRANCH_REF: ${{ github.ref }}
          EVENT_NAME: ${{ github.event_name }}
          EVENT_ACTION: ${{ github.event.action }}

  deploy:
    name: "🚀 Deploy Flavor ${{ matrix.SMARTTV_FLAVOR }} to ${{ needs.prepare.outputs.environment }}"
    needs:
      - prepare
    strategy:
      fail-fast: false
      matrix:
        SMARTTV_FLAVOR: ${{ fromJson(needs.prepare.outputs.flavors) }}
    environment:
      name: ${{ needs.prepare.outputs.environment }}
    runs-on: ubuntu-latest
    steps:
      - name: "📡 Triggering Deploy Workflow"
        run: |
          echo '{"ref":"${{ needs.prepare.outputs.repo_ref }}","environment":"${{ needs.prepare.outputs.environment }}"}' | gh workflow run ${{ env.DEPLOY_WORKFLOW }} -R ArteGEIE/${{ matrix.SMARTTV_FLAVOR }} -r main --json
        env:
          GITHUB_TOKEN: ${{ secrets.GH_PAT_WRITE_GIT_TOOLS }}

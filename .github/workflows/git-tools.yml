##################################################
## Github Action Workflow File
## Designed for ArteGEIE
##
## Description : Handle automatic Squash or Rebase commands when a PR receives a comment with "/rebase" or "/squash".
##
## Checks the PR comment when a PR receives a new comment or an existing comment is edited.
## If the message has "/rebase" or "/squash" in the body, the action is triggered.
##
## Only works on opened PR. Closed and Merged statuses are ignored.
## The rebase action may fail if <PERSON><PERSON><PERSON> thinks the branch is not rebaseable without a human intervention.
##
## Two actions are available :
##
##   1. "/rebase"
##      Runs "Rebase head branch on base branch" step
##      Rebases the branch from the base branch then adds a comment in the PR to notify the change.
##
##      Limitations :
##        - Cannot rebase develop nor master branches.
##
##   2. "/squash"
##      Runs "Squash PR's commits" step
##      All commits are soft reseted then squashed into a single commit.
##      The PR receives a comment when the change is made.
##
## In all cases, if an error occured, a message is posted into the PR to alert the users.
##

name: "Git tools"

on:
  issue_comment:
    types: [created, edited]

jobs:
  git-rebase-squash:
    name: Rebase or squash PR
    if: |
      github.event.issue.pull_request != '' &&
      (contains(github.event.comment.body, '/rebase') ||
      contains(github.event.comment.body, '/squash'))
    runs-on: ubuntu-latest
    env:
      GITHUB_TOKEN: ${{ secrets.GH_PAT_WRITE_GIT_TOOLS }}
    steps:
      - uses: actions/checkout@v3
        with:
          token: ${{ secrets.GH_PAT_WRITE_GIT_TOOLS }}

      - name: Resolve PR information
        id: resolve_pr_information
        run: |
          source .infrastructure/env/.env 2> /dev/null || true
          MAIN_BRANCH_NAME=${INFRA_MAIN_BRANCH_NAME:=master}

          PR_INFO=$(gh api -X GET "${PR_URL}")

          if [ $(echo "${PR_INFO}" | jq -r '.state') != 'open' ]; then
            echo "::error::Cannot rebase: PR is closed or merged."
            exit 1;
          fi

          if [ $(echo "${PR_INFO}" | jq -r '.rebaseable') != true ]; then
            echo "::error::Github thinks this PR is not rebaseable."
            exit 1;
          fi

          HEAD_BRANCH=$( echo "${PR_INFO}" | jq -r '.head.ref')
          BASE_BRANCH=$( echo "${PR_INFO}" | jq -r '.base.ref')
          PR_NUMBER=$( echo "${PR_INFO}" | jq -r '.number')
          PR_TITLE=$( echo "${PR_INFO}" | jq -r '.title')
          COMMITS_NUMBER=$( echo "${PR_INFO}" | jq -r '.commits')

          if [ "${HEAD_BRANCH}" = "${MAIN_BRANCH_NAME}" ] || [ "${HEAD_BRANCH}" = 'develop' ]; then
            echo "::error::Cannot rebase develop or ${MAIN_BRANCH_NAME}."
            exit 1;
          fi

          echo "head_branch=${HEAD_BRANCH}"       >> $GITHUB_OUTPUT
          echo "base_branch=${BASE_BRANCH}"       >> $GITHUB_OUTPUT
          echo "pr_number=${PR_NUMBER}"           >> $GITHUB_OUTPUT
          echo "pr_title=${PR_TITLE}"             >> $GITHUB_OUTPUT
          echo "commits_number=${COMMITS_NUMBER}" >> $GITHUB_OUTPUT

        env:
          PR_URL: ${{ github.event.issue.pull_request.url }}

      - name: Initialize mandatory git config
        run: |
          git config user.name "${USER_LOGIN}"
          git config user.email ${USER_LOGIN}@no-reply.github.com
        env:
          USER_LOGIN: ${{ github.event.sender.login }}

      - name: Rebase head branch on base branch
        if: contains(github.event.comment.body, '/rebase')
        run: |
          git fetch --unshallow
          git checkout ${HEAD_BRANCH}

          git rebase origin/${BASE_BRANCH}
          git push --force-with-lease origin

          gh pr comment ${PR_NUMBER} -b ":heavy_check_mark: PR rebased with success!"
        env:
          HEAD_BRANCH: ${{ steps.resolve_pr_information.outputs.head_branch }}
          BASE_BRANCH: ${{ steps.resolve_pr_information.outputs.base_branch }}
          PR_NUMBER: ${{ steps.resolve_pr_information.outputs.pr_number }}

      - name: Squash PR's commits
        if: contains(github.event.comment.body, '/squash')
        run: |
          git fetch --unshallow
          git checkout ${HEAD_BRANCH}

          git reset --soft HEAD~${COMMITS_NUMBER}
          git commit -m "${PR_TITLE}"
          git push --force-with-lease origin

          gh pr comment ${PR_NUMBER} -b \
            ":heavy_check_mark: PR squashed ${COMMITS_NUMBER} commits with success!"
        env:
          HEAD_BRANCH: ${{ steps.resolve_pr_information.outputs.head_branch }}
          COMMITS_NUMBER: ${{ steps.resolve_pr_information.outputs.commits_number }}
          PR_TITLE: ${{ steps.resolve_pr_information.outputs.pr_title }}
          PR_NUMBER: ${{ steps.resolve_pr_information.outputs.pr_number }}

      - name: Feedback for failure
        if: ${{ failure() }}
        run: |
          if echo "${COMMENT_BODY}" | grep -q "\/rebase"; then
            MESSAGE="PR rebase failed!"
          fi

          if echo "${COMMENT_BODY}" | grep -q "\/squash"; then
            MESSAGE="PR squash failed!"
          fi

          MESSAGE+=" - [see failing run]" \
          "(https://github.com/${GITHUB_REPOSITORY}/actions/runs/${GITHUB_RUN_ID}?check_suite_focus=true)"

          gh pr comment ${PR_NUMBER} -b ":x: ${MESSAGE}"
        env:
          PR_NUMBER: ${{ steps.resolve_pr_information.outputs.pr_number }}
          COMMENT_BODY: ${{ github.event.comment.body }}

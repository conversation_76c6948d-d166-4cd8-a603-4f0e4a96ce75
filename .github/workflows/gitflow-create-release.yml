##################################################
## Github Action Workflow File
## Designed for ArteGEIE
##
## Description :
##   When triggered from Github UI - creates a Release Pull Request starting from develop branch
##     An input "commit" can be provided to create the release from a specific commit
##   When triggered from PR Merge into master - creates a Release Draft :
##     The semver is updated accordingly : 'minor' if merging a release, else 'patch'
##
## Manual Trigger from the "Run Workflow" button in Github UI
## Automatic Trigger when a Pull Request is closed
##
## Creates a version bump PR from a new branch into the new release branch
##

name: "Create a release (gitflow)"
on:
  workflow_dispatch:
    inputs:
      commit:
        required: false
        type: string
        description: |
          ⚠️ This workflow will create a release branch and version bump PR.
          ⚠️ MINOR release will be created by default if no other option is selected.
          -----
          Release on a specific commit?
      major:
        required: false
        type: boolean
        description: "Create a major release?"
        default: false
      hotfix:
        required: false
        type: boolean
        description: "Create a hotfix release?"
        default: false

  pull_request:
    types:
      - closed

jobs:
  create-release-pr:
    if: github.event_name == 'workflow_dispatch'
    name: Create release PR
    runs-on: ubuntu-latest
    outputs:
      release_branch: ${{ steps.create_release.outputs.branch_name }}
    steps:
      - name: Validate branch
        env:
          REF_BRANCH: ${{ github.event.ref }}
        run: |
            if [ ${REF_BRANCH} != 'refs/heads/develop' ]; then
              echo "::error::A release can be created only from develop branch."
              exit 1;
            fi

      - name: Checkout repository
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Debug action
        uses: ArteGEIE/github-actions-common/debug@0.29

      - name: Load environment variables and determine release level
        id: load_env_var
        env:
          MAJOR_RELEASE: ${{ github.event.inputs.major }}
          HOTFIX: ${{ github.event.inputs.hotfix }}
        run: |
          source .infrastructure/env/.env 2> /dev/null || true

          if [[ "${MAJOR_RELEASE}" == 'true' && "${HOTFIX}" == 'true' ]]; then
            echo "::error::Cannot select both major and hotfix. Please select only one."
            exit 1
          fi

          RELEASE_LEVEL=minor
          if [[ "${MAJOR_RELEASE}" == 'true' ]]; then
            RELEASE_LEVEL=major
          elif [[ "${HOTFIX}" == 'true' ]]; then
            RELEASE_LEVEL=patch
          fi

          echo "main_branch_name=${INFRA_MAIN_BRANCH_NAME:-master}" >> $GITHUB_OUTPUT
          echo "release_level=${RELEASE_LEVEL}" >> $GITHUB_OUTPUT

      - name: Create release branch and PR
        id: create_release
        uses: ArteGEIE/github-actions-common/create-release@0.29
        with:
          release_level: ${{ steps.load_env_var.outputs.release_level }}
          release_type: pull_request
          main_branch_name: ${{ steps.load_env_var.outputs.main_branch_name }}

  bump-version:
    name: Bump version and create PR
    runs-on: ubuntu-latest
    needs: create-release-pr
    if: github.event_name == 'workflow_dispatch'
    steps:
      - name: Checkout 'develop' branch
        uses: actions/checkout@v3
        with:
          ref: develop
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Bump version, commit, and push new branch
        id: bump_version
        run: |
          MAJOR="${{ github.event.inputs.major }}"
          HOTFIX="${{ github.event.inputs.hotfix }}"

          CURRENT_VERSION=$(jq -r '.version' package.json)
          IFS='.' read -r major minor patch <<< "$CURRENT_VERSION"

          if [[ "$MAJOR" == 'true' ]]; then
            major=$((major + 1))
            minor=0
            patch=0
          elif [[ "$HOTFIX" == 'true' ]]; then
            patch=$((patch + 1))
          else
            minor=$((minor + 1))
            patch=0
          fi

          NEW_VERSION="$major.$minor.$patch"
          echo "Bumping version from $CURRENT_VERSION to $NEW_VERSION"

          jq ".version = \"$NEW_VERSION\"" package.json > package.json.tmp && mv package.json.tmp package.json
          if [ -f package-lock.json ]; then
            npm ci
          fi

          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"

          BRANCH_NAME="chore/bump-version-$NEW_VERSION"
          git checkout -b $BRANCH_NAME
          git add package.json package-lock.json
          git commit -m "chore: bump version to $NEW_VERSION (TVAPPS-0000)"

          export HUSKY=0
          git push --set-upstream origin $BRANCH_NAME

          echo "branch=$BRANCH_NAME" >> $GITHUB_OUTPUT
          echo "new_version=$NEW_VERSION" >> $GITHUB_OUTPUT

      - name: Set release branch env
        id: set_release_branch
        run: |
          # Derive release branch
          if [[ "${{ github.event.inputs.major }}" == 'true' ]]; then
            LEVEL="major"
          elif [[ "${{ github.event.inputs.hotfix }}" == 'true' ]]; then
            LEVEL="patch"
          else
            LEVEL="minor"
          fi
          BRANCH="release/${LEVEL}/${{ steps.bump_version.outputs.new_version }}"
          echo "branch=$BRANCH" >> $GITHUB_OUTPUT

      - name: Create Pull Request to the release branch
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          HEAD_BRANCH: ${{ steps.bump_version.outputs.branch }}
          BASE_BRANCH: ${{ steps.set_release_branch.outputs.branch }}
          NEW_VERSION: ${{ steps.bump_version.outputs.new_version }}
        run: |
          # Adding a small delay to ensure the release branch is fully available via the API
          echo "Waiting 10 seconds for branch to propagate..."
          sleep 10
          echo "Creating PR from '${HEAD_BRANCH}' to target branch '${BASE_BRANCH}'..."
          gh pr create \
            --title "chore: Version bump to ${NEW_VERSION} (TVAPPS-0000)" \
            --body "This PR bumps the release version to ${NEW_VERSION} and merges it into the release branch." \
            --head "${HEAD_BRANCH}" \
            --base "${BASE_BRANCH}" \
            --label "Release Version Bump"

  create-release-draft:
    name: Create release draft
    runs-on: ubuntu-latest
    if: |
      github.event_name == 'pull_request'
      && github.event.pull_request.merged == true
      && github.event.pull_request.head.ref != 'develop'
      && ( github.event.pull_request.base.ref == 'master' || github.event.pull_request.base.ref == 'main')
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Debug action
        uses: ArteGEIE/github-actions-common/debug@0.29

      - name: Load env var
        id: load_env_var
        env:
          IS_MAJOR_RELEASE: ${{ startsWith(github.event.pull_request.head.ref, 'release/major') }}
          IS_MINOR_RELEASE: ${{ startsWith(github.event.pull_request.head.ref, 'release/minor') }}
          IS_HOTFIX_RELEASE: ${{ startsWith(github.event.pull_request.head.ref, 'release/hotfix') }}
        run: |
          source .infrastructure/env/.env 2> /dev/null || true

          if [ 'true' = "${IS_MAJOR_RELEASE}" ]; then
            RELEASE_LEVEL='major'
          elif [ 'true' = "${IS_MINOR_RELEASE}" ]; then
            RELEASE_LEVEL='minor'
          else
            RELEASE_LEVEL='patch'
          fi

          echo "main_branch_name=${INFRA_MAIN_BRANCH_NAME:-master}" >> $GITHUB_OUTPUT
          echo "release_level=${RELEASE_LEVEL}" >> $GITHUB_OUTPUT

      - name: Create release draft
        uses: ArteGEIE/github-actions-common/create-release@0.29
        with:
          release_level: ${{ steps.load_env_var.outputs.release_level }}
          release_type: draft
          main_branch_name: ${{ steps.load_env_var.outputs.main_branch_name }}

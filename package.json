{"name": "ARTE", "version": "11.24.0", "description": "", "main": "index.tsx", "engines": {"node": ">=18"}, "scripts": {"test": "vitest", "prepare": "husky install", "dev": "webpack serve --config webpack/development.config.js", "dev:hbbtv": "TARGET=hbbtv npm run dev", "dev:html5": "TARGET=html5 npm run dev", "dev:orange": "TARGET=orange npm run dev", "dev:tizen": "TARGET=tizen npm run dev", "dev:webos": "TARGET=webos npm run dev", "build:analyze": "NODE_ENV=production PRESETS=analyze npm run build:html5", "build:hbbtv": "TARGET=hbbtv webpack --config webpack/production.config.js", "build:hbbtv:preproduction": "NODE_ENV=preproduction npm run build:hbbtv", "build:hbbtv:production": "NODE_ENV=production npm run  build:hbbtv", "build:html5": "TARGET=html5 webpack --config webpack/production.config.js", "build:html5:preproduction": "NODE_ENV=preproduction npm run build:html5", "build:html5:production": "NODE_ENV=production npm run build:html5", "build:orange": "TARGET=orange webpack --config webpack/production.config.js", "build:orange:preproduction": "NODE_ENV=preproduction npm run build:orange", "build:orange:production": "NODE_ENV=production npm run build:orange", "build:panasonic": "TARGET=panasonic webpack --config webpack/production.config.js", "build:panasonic:preproduction": "NODE_ENV=preproduction npm run build:panasonic", "build:panasonic:production": "NODE_ENV=production npm run build:panasonic", "build:philips": "TARGET=philips webpack --config webpack/production.config.js", "build:philips:preproduction": "NODE_ENV=preproduction npm run build:philips", "build:philips:production": "NODE_ENV=production npm run build:philips", "build:sky": "TARGET=sky webpack --config webpack/production.config.js", "build:sky:preproduction": "NODE_ENV=preproduction npm run build:sky", "build:sky:production": "NODE_ENV=production npm run build:sky", "build:tizen": "TARGET=tizen webpack --config webpack/tizen.config.js", "build:tizen:preproduction": "NODE_ENV=preproduction npm run build:tizen", "build:tizen:production": "NODE_ENV=production npm run build:tizen", "build:webos": "TARGET=webos webpack --config webpack/production.config.js", "build:webos:1080": "RESOLUTION=1080 npm run build:webos", "build:webos:720": "RESOLUTION=720 npm run build:webos", "build:webos:preproduction:1080": "RESOLUTION=1080 NODE_ENV=preproduction npm run build:webos", "build:webos:preproduction:720": "RESOLUTION=720 NODE_ENV=preproduction npm run build:webos", "build:webos:production:1080": "RESOLUTION=1080 NODE_ENV=production npm run build:webos", "build:webos:production:720": "RESOLUTION=720 NODE_ENV=production npm run build:webos", "package:tizen": "node scripts/tizen/create-project.js && node scripts/tizen/package.js", "package:webos:1080": "RESOLUTION=1080 node scripts/webos/webos.js", "package:webos:720": "RESOLUTION=720 node scripts/webos/webos.js", "package:webos:all": "node scripts/webos/generate-ipk.js", "format:eslint": "eslint --ext .js,.jsx,.ts,.tsx .", "format:eslint:fix": "eslint --fix --ext .js,.jsx,.ts,.tsx .", "format:css:lint": "stylelint 'src/**/*.{css,scss}'", "format:css:lint:fix": "stylelint 'src/**/*.{css,scss}' --fix", "format:css:prettier": "prettier --check 'src/**/*.{css,scss}'", "format:css:prettier:fix": "prettier --write 'src/**/*.{css,scss}'", "format:css": "concurrently 'npm run format:css:lint' 'npm run format:css:prettier'", "format:css:fix": "concurrently 'npm run format:css:lint:fix' 'npm run format:css:prettier:fix'", "format:all": "concurrently 'npm run format:eslint' 'npm run format:css'", "format:all:fix": "concurrently 'npm run format:eslint:fix' 'npm run format:css:fix'", "ts:error-tracker": "node scripts/ts-error-tracker/ts-error-tracker.js", "translate": "node scripts/getTranslations.mjs"}, "author": "", "license": "ISC", "dependencies": {"@noriginmedia/norigin-spatial-navigation": "^1.2.0", "@ungap/event-target": "^0.2.3", "abortcontroller-polyfill": "^1.7.5", "classnames": "^2.3.2", "core-js": "^3.30.0", "crypto-js": "^4.2.0", "css-loader": "^6.7.3", "dashjs": "^5.0.3", "hbbtv-typings": "^1.5.2", "i18next": "^23.2.11", "i18next-browser-languagedetector": "^7.1.0", "i18next-http-backend": "^2.2.1", "lodash.debounce": "^4.0.8", "react": "^18.2.0", "react-app-polyfill": "^3.0.0", "react-dom": "^18.2.0", "react-i18next": "^13.0.2", "react-remote-controller": "^0.0.2", "react-router-dom": "^6.10.0", "style-loader": "^3.3.2", "uuid": "^9.0.1", "window-location-origin": "^0.1.0"}, "devDependencies": {"@babel/core": "^7.21.4", "@babel/preset-env": "^7.21.4", "@babel/preset-react": "^7.18.6", "@babel/preset-typescript": "^7.21.4", "@lokalise/node-api": "^11.1.0", "@types/css-modules": "^1.0.2", "@types/lodash.debounce": "^4.0.7", "@types/react": "^18.0.33", "@types/react-dom": "^18.0.11", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^6.19.1", "@typescript-eslint/parser": "^6.19.1", "axios": "^1.5.0", "babel-loader": "^9.1.2", "child_process": "^1.0.2", "concurrently": "^8.0.1", "copy-webpack-plugin": "^11.0.0", "dotenv": "^16.0.3", "dotenv-webpack": "^8.0.1", "eslint": "^8.38.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-local-rules": "^3.0.2", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-simple-import-sort": "^12.0.0", "eslint-plugin-unused-imports": "^3.0.0", "happy-dom": "^13.8.2", "html-webpack-plugin": "^5.5.0", "husky": "^8.0.3", "json-loader": "^0.5.7", "prettier": "^2.8.7", "replace-in-file": "^6.3.5", "sass": "^1.61.0", "sass-loader": "^13.2.2", "shelljs": "^0.8.5", "stylelint": "^13.13.0", "stylelint-config-prettier": "^9.0.5", "stylelint-config-standard": "^21.0.0", "terser-webpack-plugin": "^5.3.7", "ts-loader": "^9.4.2", "typescript": "^5.0.3", "unzip-stream": "^0.3.1", "vite-tsconfig-paths": "^4.3.2", "vitest": "^1.2.1", "webpack": "^5.77.0", "webpack-bundle-analyzer": "^4.9.1", "webpack-cli": "^5.0.1", "webpack-dev-server": "^4.13.2", "yoctocolors-cjs": "^2.1.2"}, "browserslist": ["firefox >= 10"]}
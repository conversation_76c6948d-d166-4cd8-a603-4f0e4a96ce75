<!DOCTYPE html>
<html>
<head>
    <title>Remote Content Module POC</title>
    <style>
        body { margin: 0; overflow: hidden; background-color: black; font-size: 1.5rem;}

        #tvModelInfo {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
        }

        #connectionInfo {
            position: absolute;
            top: 80px;
            left: 20px;
            color: white;
        }
    </style>
</head>
<body>
    <div id="tvModelInfo"></div>
    <div id="connectionInfo"></div>

    <script type="text/javascript">
        const tvModelInfoDiv = document.getElementById('tvModelInfo');
        const connectionDiv = document.getElementById('connectionInfo');

        // Function to generate a unique ID for message correlation
        function generateUuid() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }

        // Store pending requests to match responses
        const pendingRequests = {};

        // Function to send a message to the Launcher
        function sendMessageToLauncher(type, payload) {
            const correlationId = generateUuid();
            const message = {
                type: type,
                correlationId: correlationId,
                ...payload
            };
            window.parent.postMessage(message, '*'); // Specify targetOrigin in a real app
            return new Promise((resolve, reject) => {
                pendingRequests[correlationId] = { resolve, reject, timeout: setTimeout(() => {
                    delete pendingRequests[correlationId];
                    reject(new Error('Message response timed out for correlationId: ' + correlationId));
                }, 5000) }; // 5-second timeout
            });
        }

        window.addEventListener('message', (event) => {
            // Ensure messages are from a trusted origin in a real application
            // For this POC, all origins are allowed
            console.log("Remote Content Module received message:", event.data);

            if (event.data && event.data.type === 'tizenApiResult') {
                const { api, result, correlationId } = event.data;
                if (pendingRequests[correlationId]) {
                    clearTimeout(pendingRequests[correlationId].timeout);
                    pendingRequests[correlationId].resolve(result);
                    delete pendingRequests[correlationId];
                }
            }
        });

        sendMessageToLauncher('tizenApiCall', { api: 'webapis.productinfo.getRealModel' })
            .then(model => {
                console.log("Received TV Model:", model);
                tvModelInfoDiv.textContent = `TV Model: ${model}`;
            })
            .catch(error => {
                console.error("Error getting TV model:", error);
                tvModelInfoDiv.textContent = `TV Model: Error - ${error.message}`;
            });

        sendMessageToLauncher('tizenApiCall', { api: 'webapis.network.isConnectedToGateway' })
            .then(connected => {
                console.log("Received online status:", connected);
                connectionDiv.textContent = `TV Connected to network: ${connected}`;
            })
            .catch(error => {
                console.error("Error getting online status:", error);
                connectionDiv.textContent = `TV Connection Error - ${error.message}`;
            });
    </script>
</body>
</html>

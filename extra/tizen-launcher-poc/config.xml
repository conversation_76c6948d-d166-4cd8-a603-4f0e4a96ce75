<?xml version="1.0" encoding="UTF-8"?>
<widget xmlns:tizen="http://tizen.org/ns/widgets" xmlns="http://www.w3.org/ns/widgets" id="http://arte.tv/arte" version="1.0.0" viewmodes="maximized">
    <access origin="*" subdomains="true"></access>
    <tizen:app-control>
        <tizen:src name="index.html" reload="disable"/>
        <tizen:operation name="http://samsung.com/appcontrol/operation/eden_resume"/>
    </tizen:app-control>
    <tizen:application id="qfAU4KnqUO.ArteLauncherPOC" package="qfAU4KnqUO" required_version="2.3"/>
    <content src="index.html"/>
    <feature name="http://tizen.org/feature/screen.size.all"/>
    <icon src="icon.png"/>
    <tizen:metadata key="http://samsung.com/tv/metadata/devel.api.version" value="2.4"/>
    <tizen:metadata key="http://samsung.com/tv/metadata/multitasking.support" value="true"/>
    <tizen:metadata key="http://samsung.com/tv/metadata/prelaunch.support" value="true"/>
    <name>Arte Launcher POC</name>
    <tizen:privilege name="http://tizen.org/privilege/application.launch"/>
    <tizen:privilege name="http://tizen.org/api/filesystem"/>
    <tizen:privilege name="http://tizen.org/privilege/filesystem.read"/>
    <tizen:privilege name="http://tizen.org/privilege/filesystem.write"/>
    <tizen:privilege name="http://tizen.org/privilege/internet"/>
    <tizen:privilege name="http://tizen.org/privilege/tv.inputdevice"/>
    <tizen:privilege name="http://developer.samsung.com/privilege/network.public"/>
    <tizen:privilege name="http://developer.samsung.com/privilege/productinfo"/>
    <tizen:profile name="tv-samsung"/>
    <tizen:setting/>
    <tizen:setting screen-orientation="landscape"/>
</widget>

const URL_PREPROD = 'https://smarttv-preprod.arte.tv/static/smartTV-html5-subset/current/';
const URL_API_CALL_TEST = 'https://dev56.teravolt.it/pik/arte_hosted_poc/';

let remoteContentFrame;

function insertIframeWithDelay() {
  setTimeout(function () {
    remoteContentFrame = document.createElement('iframe');
    remoteContentFrame.id = 'remoteContentFrame';
    remoteContentFrame.src = URL_PREPROD;
    document.body.appendChild(remoteContentFrame);
    remoteContentFrame.focus();

    setupMessageListener();
  }, 100);
}

function setupMessageListener() {
  window.addEventListener('message', function (event) {
    // Ensure messages are from a trusted origin in a real application
    // For this POC, all origins are allowed
    console.log('Launcher received message:', event.data);

    if (event.data && event.data.type === 'tizenApiCall') {
      const apiName = event.data.api;
      let result = null;

      try {
        switch (apiName) {
          case 'webapis.productinfo.getRealModel':
            result = window.webapis.productinfo.getRealModel();
            break;
          case 'webapis.network.isConnectedToGateway':
            result = webapis.network.isConnectedToGateway();
            break;
          default:
            console.warn('Unknown Tizen API requested:', apiName);
            result = 'Error: Unknown API';
        }
      } catch (e) {
        console.error('Error executing Tizen API:', apiName, e);
        result = 'Error: ' + e.message;
      }

      // Send the result back
      remoteContentFrame.contentWindow.postMessage(
        {
          type: 'tizenApiResult',
          api: apiName,
          result: result,
          correlationId: event.data.correlationId,
        },
        '*',
      ); // Specify targetOrigin in a real app
    }
  });
}

insertIframeWithDelay();

{"AD_DELAY_BEFORE_VIDEO_LABEL1": "Votre vidéo commence dans", "A_Z_MAGAZINE": "Émissions A-Z", "BACK": "Retour", "BACK_TO_TOP": "Retour haut de page", "BANNER_PANEL_HIGHLIGHTS": "Notre sélection", "BANNER_PANEL_NEXT_PROGRAM_TITLE": "Voir un autre programme ?", "BANNER_PANEL_RECOMMENDATIONS_TITLE": "Vous pourriez aimer aussi", "BANNER_POPUP_BLUE_LABEL": "Bouton bleu", "BANNER_POPUP_BLUE_TITLE": "Revenir au début du programme", "BANNER_POPUP_RED_LABEL": "Bouton rouge", "BANNER_POPUP_RED_TITLE": "Accéder à l'application ARTE", "CATEGORIES": "Catégories", "CLOSE_CONFIRMATION": "Êtes-vous sûr de vouloir quitter l'application ?", "CONFIRM": "Confirmer", "COOKIE_SETTING": "<PERSON><PERSON><PERSON> les cookies", "DA": "2160p", "DASHBOARD_EMPTY": "C'est un peu vide par ici !<br />• Ajoutez des vidéos à votre liste en sélectionnant le cœur dans le player<br />• <PERSON>bonnez-vous aux émissions en sélectionnant \"S'abonner\" dans chaque émission<br />• Et retrouvez ici les vidéos que vous avez visionnées", "DIGIT": "<sup>ème</sup>", "SECOND": "<sup>nd</sup>", "add": "Ajouter", "alert__login": "Pour ajouter cette vidéo à votre liste, connectez-vous à MonARTE", "alert__message_interface_loading": "Changement de langue en cours, l'application va redémarrer.", "alert__personalisation": "Vos données de lectures et vos favoris sont enregistrés localement et anonymement sur votre TV. Conservez ces informations en créant un compte MonARTE sur arte.tv.", "all": "<PERSON>ut", "allVideos": "Toutes les vidéos", "application__ending_description": "Dans un souci de vous garantir la meilleure expérience possible, nous vous informons que l’application ARTE n’est plus disponible sur cet appareil. Retrouvez nos programmes sur votre ordinateur, smartphone, tablette ou TV connectée.", "application__ending_title": "Arrêt de l'application ARTE sur T-Entertain", "audiodescription": "Audiodescription", "availability__livestream_day": "Livestream le {{date}} à {{hour}}", "availability__livestream_hours": "Livestream à {{hour}}", "availability__remaining_days": "{\"one\":\"Dernier jour\",\"other\":\"Plus que {{DD}} jours\"}", "availability__vod_date": "Disponible le {{date}}", "availability__vod_hour": "Disponible à {{hour}}", "back": "Retour", "broadcast__availability_day": "À l'antenne le {{date}} à {{hour}}", "broadcast__availability_same_day": "À l'antenne à {{hour}}", "button__credits": "Crédits", "button__skip_endCredits": "Passer le générique de fin", "button__skip_nextEpisode": "Épisode suivant", "button__skip_openingCredits": "Passer le générique", "button__skip_summary": "<PERSON><PERSON> le résumé", "call_to_action__live": "Regarder en direct", "call_to_action__livestream": "Regarder en livestream", "cancel": "Annuler", "close": "<PERSON><PERSON><PERSON>", "collection": "Collection", "concerts": "Concerts", "continueWatching": "Reprendre", "cookies__audience": "Cookies de mesure d'audience", "cookies__audience_desc": "Ces cookies nous permettent de mesurer le trafic et la consommation des contenus vidéo afin d’assurer un suivi de la qualité de notre offre numérique et d’améliorer l’expérience utilisateur. S’opposer à la collecte de ces données n’empêche pas la mesure d’audience mais réduit sa précision. Les statistiques fournies par les solutions d’audience à ARTE sont strictement anonymes et ne contiennent aucune donnée personnelle.", "cookies__personalisation": "Fonctionnalités de personnalisation", "cookies__personalisation_desc": "Retrouvez vos vidéos en cours de lecture et votre liste de favoris grâce aux données enregistrées anonymement dans votre navigateur. Il vous suffit pour conserver ces informations de vous connecter à MonARTE ou de créer un compte.", "cookies__pop-up_accept": "J'accepte", "cookies__pop-up_cookies-choice": "Je choisis mes cookies", "cookies__pop-up_cta": "Je modifie mes préférences", "cookies__pop-up_delete-confirmation": "Les cookies ont bien été supprimés", "cookies__pop-up_desc": "En entrant dans l’application, vous acceptez le dépôt de cookies destinés à mesurer nos audiences et à recueillir des informations sur votre navigation. Ces données nous permettent d’améliorer l’expérience utilisateur. Vous pouvez changer vos préférences à tout moment dans les paramètres.", "cookies__pop-up_title": "À vous de choisir !", "cookies__technical": "Cookies techniques", "cookies__technical_desc": "Nous utilisons des cookies techniques afin d’évaluer la qualité de l’expérience de nos utilisateurs et de veiller au bon fonctionnement de nos applications. En refusant ces cookies, vous ne nous permettrez plus de détecter les éventuels dysfonctionnements que vous pourriez rencontrer sur nos interfaces.", "credits": "Crédits", "delete": "<PERSON><PERSON><PERSON><PERSON>", "direct": "Direct", "discover": "Découvrir", "documentaries": "Documentaires", "error__generic": "Une erreur est survenue.", "error__generic_2": "Oups ! Un problème technique nous empêche d'afficher cette page.", "error__geoblocking": "Cette vidéo n'est pas disponible dans votre pays.", "error__geoblocking_2": "[%key_id:354595800%]", "error__login_invalid": "Le code semble invalide ou a expiré", "error__no-stream": "Pas de vidéo disponible", "error__offline-text": "Veuillez vérifier votre connexion internet.", "error__offline-title": "Vous êtes déconnecté", "error__try_again": "Une erreur est survenue. Veuillez réessayer.", "extract": "EXTRAIT", "favorites__add": "Ajouter aux favoris", "favorites__delete": "Supprimer des favoris", "films": "Films", "friday": "<PERSON><PERSON><PERSON><PERSON>", "fsk__confirm_age_button_step2": "J'ai confirmé mon âge", "fsk__confirm_age_error": "La vérification d'âge a échoué", "fsk__confirm_age_step1": "1. Confirmez votre âge en scannant le code QR ou en vous rendant sur arte.tv/age-verification", "fsk__confirm_age_step2": "2. V<PERSON> avez confirmé votre âge ? Cliquez sur le bouton ci-dessous.", "greenButton": "Voir les options", "help__audiodescription": "Version en audiodescription (pour les malvoyants)", "help__button-cookies": "Mes cookies", "help__button-cookies-delete": "Effacer les cookies", "help__button-logs": "Mes logs", "help__button-test": "Lancer un test", "help__geoloc": "Géolocalisation", "help__hearingImpaired": "Version sous-titrée pour les malentendants", "help__platform": "Plateforme", "help__qr-code": "Scannez le code QR et posez-nous vos questions :", "help__show_data_text": "Outils et assistance :", "help__tv-info": "Outils et assistance :", "help__url": "arte.tv/fr/faq-tv", "help__version": "Version", "help__vostf": "Version originale sous-titrée en français (VOSTF)", "help_button": "Besoin d’aide ?", "home": "Accueil", "hours": "<PERSON><PERSON>", "i18n__english": "English", "i18n__french": "Français", "i18n__german": "De<PERSON>ch", "i18n__polish": "<PERSON><PERSON>", "i18n__romanian": "Română", "i18n__spanish": "Español", "informations": "Informations", "italian": "Italiano", "keyboard__space": "Espace", "languages": "<PERSON><PERSON>", "lastChance": "<PERSON><PERSON><PERSON> chance", "lastDay": "<PERSON><PERSON> jour", "live__availability": "Disponible en direct : ", "loading": "Chargement en cours", "loading__tests": "Please wait while your application conducts tests to enhance your experience.", "login": "Se connecter", "login__code": "Entrez le code", "login__error_too_short": "Le champ doit comporter au moins 8 caractères", "login__how_to": "Rendez-vous sur arte.tv/tvlogin depuis votre smartphone ou votre ordinateur, connectez-vous et utilisez le code qui apparaît pour accéder à votre compte MonARTE depuis votre TV connectée.", "login_page1_AlreadyAMember": "Déjà membre ?", "login_page1_BrandName": "MonARTE", "login_page1_Description": "Retrouvez vos favoris et votre historique sur votre TV", "login_page1_NotAMemberYet": "Pas encore de compte ?", "login_page1_signin_description": "Créez votre compte sur votre téléphone en scannant le QR code suivant", "login_page2_step1": "Rendez-vous sur arte.tv/tvlogin ou scannez le QR code suivant pour obtenir votre code de connexion", "login_page2_step2": "Saisissez votre code de connexion", "logout": "Me déconnecter", "magazines": "Émissions", "minutes_short": "min", "monday": "<PERSON><PERSON>", "moreInfos": "Plus d'informations", "moreInfos__short": "Infos", "more_information": "Plus d'options", "more_information_program": "[%key_id:*********%]", "myAccount": "Mon compte", "myArte": "MonARTE", "myFavorites": "<PERSON> <PERSON>e", "myHistory": "Mon historique", "myHistory__confirmation_popup_title": "Voulez-vous vraiment vider votre historique ?", "myHistory__purgehistory_cta": "Vider mon historique", "myMagazine": "Mes émissions", "myVideos__empty_favorites": "Aucune vidéo dans votre liste", "myVideos__empty_history": "Votre historique est vide", "myVideos__empty_personalzone": "Votre liste est vide", "my_account__loggedin_confirmation": "Vous êtes connecté", "my_videos": "<PERSON>s vid<PERSON>", "my_videos__empty_state_description": "\"Mes vidéos\" rassemble vos vidéos en cours de lecture, vos favoris et votre historique. <PERSON><PERSON><PERSON><PERSON> une vidéo ou ajoutez un programme à vos favoris pour commencer à utiliser votre espace.", "my_videos__empty_state_title": "arte.tv sur tous vos écrans", "my_videos__emptystate_title": "Profitez de votre espace \"Mes vidéos\"", "next": "Suivant", "nextBroadcast": "À la télévision le {{day}} {{date}} à {{hour}}", "no": "Non", "notifications__reminder_add": "M'envoyer un rappel", "notifications__reminder_add_feedback": "L'alerte a bien été activée. Vous recevrez un e-mail lorsque ce programme sera disponible.", "notifications__reminder_remove": "Désactiver le rappel", "notifications__reminder_remove_feedback": "Le rappel a été désactivé.", "ok": "OK", "pause": "Pause", "play": "Lecture", "play_all": "<PERSON><PERSON> regarder", "player__audio": "Audio", "player__countdown_vod_laterDay": "Un peu de patience, ce programme sera disponible le", "player__countdown_vod_sameDay": "Un peu de patience, ce programme sera disponible dans", "player__next": "Vidéo <PERSON>", "player__next_countdown": "Vid<PERSON><PERSON> suivante dans {{0}} secondes", "player__previous": "Vidéo précédente", "player__return_live": "Rev<PERSON>r au <PERSON>", "player__start_over_live": "<PERSON><PERSON><PERSON> au d<PERSON>", "player__subtitles": "Sous-titres", "player__subtitles_off": "Automatique", "player__versions": "Audio et sous-titres", "playlists": "Playlists", "portal__ending_text": "Vous avez été plus d’un million et demi à regarder Arte via la chaîne 77 !\\nL’expérimentation du portail touche à sa fin mais retrouvez tous les programmes via le bouton rouge de votre télécommande sur la chaîne 7 ou via arte.tv.", "portal__ending_title": "Arrêt du portail Arte sur la chaîne 77", "previous": "Précédent", "purge__favorites": "Souh<PERSON>ez-vous vider votre liste ?", "purge__history": "Souhaitez-vous vider votre historique ?", "quality": "Qualité", "quit": "<PERSON><PERSON><PERSON>", "quit__confirm": "Êtes-vous sûr de vouloir quitter l'application ?", "refuse": "Je refuse", "replay__availability": "Disponible du {{day}}/{{month}}/{{year}} au {{day}}/{{month}}/{{year}}", "resumeWatching": "<PERSON><PERSON><PERSON>", "retry": "<PERSON><PERSON><PERSON><PERSON>", "saturday": "<PERSON><PERSON>", "search": "Recherche", "search__mostRecent": "Les plus récentes", "search__mostViewed": "Les plus vues", "search__no-results": "Nous sommes dés<PERSON>, aucun résultat ne correspond à votre recherche \"{{0}}\"", "search__placeholder": "Rechercher un programme", "search__results": "Résultats", "season": "<PERSON><PERSON>", "seconds__short": "sec", "seeMore": "En voir plus", "series": "Séries", "settings": "Paramètres", "settings__autoplay_desc": "Lorsque vous arrivez à la fin d'une vidéo, la suivante se lance automatiquement.", "settings__autoplay_title": "Enchaînement automatique des vidéos", "settings__display": "Affichage", "settings__good_qaulity": "<PERSON><PERSON>", "settings__help": "Besoin d'aide ?", "settings__informations": "Informations", "settings__low_quality": "<PERSON><PERSON>", "settings__max_quality": "Maximale", "settings__personalisation": "Personnalisation", "settings__playback": "Lecture vidéo", "settings__playbackSpeed_desc": "Changez la vitesse de lecture des vidéos.", "settings__playbackSpeed_title": "Vitesse de lecture par défaut", "settings__player": "Lecture vidéo", "settings__privacy": "Vie privée", "settings__quality_desc": "Choisir une qualité plus faible permet de réduire la consommation de données.", "settings__quality_eco": "Économie de données", "settings__quality_good": "<PERSON><PERSON>", "settings__quality_max": "Maximale", "settings__quality_title": "Qualité des vidéos", "settings__quality_very_good": "<PERSON><PERSON><PERSON> bonne", "settings__sobriety_desc": "Ces options ont un impact sur votre consommation énergétique. Pour plus d'informations, rendez-vous sur arte.tv/sobriete-numerique", "settings__speed_desc": "TODO", "settings__speed_title": "TODO", "subscribe": "<PERSON>'abonner", "sunday": "<PERSON><PERSON><PERSON>", "thursday": "<PERSON><PERSON>", "time__seconds": "secondes", "tizen_tags": "culture,europe,replay,docu,séries,cinéma,musique", "today": "<PERSON><PERSON><PERSON>'hui", "tomorrow": "<PERSON><PERSON><PERSON>", "trailer": "Voir la bande-annonce", "trailer__watch": "Voir la bande-annonce", "tuesday": "<PERSON><PERSON>", "tvGuide": "Guide TV", "unsubscribe": "<PERSON> d<PERSON>ab<PERSON>ner", "upcoming": "Prochainement", "video": "Vidéo", "vo": "Version Originale", "watch": "Regarder", "wednesday": "<PERSON><PERSON><PERSON><PERSON>", "yes": "O<PERSON>", "yesterday": "<PERSON>er"}